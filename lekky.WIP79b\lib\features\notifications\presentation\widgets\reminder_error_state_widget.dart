// File: lib/features/notifications/presentation/widgets/reminder_error_state_widget.dart

import 'package:flutter/material.dart';
import 'dart:async';

/// Error types that can occur during reminder scheduling
enum ReminderErrorType {
  permission,
  scheduling,
  network,
  timeZone,
  storage,
  unknown,
}

/// A widget that displays an error state when reminder scheduling fails
class ReminderErrorStateWidget extends StatefulWidget {
  final ReminderErrorType errorType;
  final String message;
  final String? details;
  final VoidCallback? onRetry;
  final VoidCallback? onTroubleshoot;
  final VoidCallback? onDismiss;

  const ReminderErrorStateWidget({
    Key? key,
    required this.errorType,
    required this.message,
    this.details,
    this.onRetry,
    this.onTroubleshoot,
    this.onDismiss,
  }) : super(key: key);

  @override
  State<ReminderErrorStateWidget> createState() =>
      _ReminderErrorStateWidgetState();
}

class _ReminderErrorStateWidgetState extends State<ReminderErrorStateWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _shakeAnimation;
  bool _showDetails = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );

    _shakeAnimation = Tween<double>(begin: -5.0, end: 5.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.elasticIn,
      ),
    );

    // Play the shake animation once at the beginning
    _controller.forward().then((_) {
      _controller.reverse();
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.red.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.red.shade200),
        boxShadow: [
          BoxShadow(
            color: Colors.red.shade100.withOpacity(0.5),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildHeader(),
          _buildContent(),
          if (widget.details != null) _buildDetails(),
          _buildActions(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.red.shade100,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12),
        ),
      ),
      child: Row(
        children: [
          AnimatedBuilder(
            animation: _controller,
            builder: (context, child) {
              return Transform.translate(
                offset: Offset(_shakeAnimation.value, 0),
                child: child,
              );
            },
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.white,
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: Colors.red.shade300.withOpacity(0.3),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Icon(
                _getErrorIcon(),
                color: Colors.red.shade700,
                size: 24,
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _getErrorTitle(),
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                    color: Colors.red.shade900,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  'Reminder scheduling failed',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.red.shade800,
                  ),
                ),
              ],
            ),
          ),
          if (widget.onDismiss != null)
            IconButton(
              icon: const Icon(Icons.close),
              color: Colors.red.shade700,
              onPressed: widget.onDismiss,
            ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.message,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          _buildTroubleshootingTips(),
        ],
      ),
    );
  }

  Widget _buildTroubleshootingTips() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Troubleshooting Tips:',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 12,
            color: Colors.grey.shade700,
          ),
        ),
        const SizedBox(height: 8),
        ..._getTroubleshootingTips().map((tip) => _buildTip(tip)),
      ],
    );
  }

  Widget _buildTip(String tip) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            Icons.arrow_right,
            size: 16,
            color: Colors.grey.shade700,
          ),
          const SizedBox(width: 4),
          Expanded(
            child: Text(
              tip,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade700,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetails() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          InkWell(
            onTap: () {
              setState(() {
                _showDetails = !_showDetails;
              });
            },
            child: Row(
              children: [
                Icon(
                  _showDetails ? Icons.arrow_drop_down : Icons.arrow_right,
                  color: Colors.grey.shade700,
                ),
                Text(
                  'Technical Details',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey.shade700,
                  ),
                ),
              ],
            ),
          ),
          if (_showDetails)
            Container(
              margin: const EdgeInsets.only(left: 24, top: 8, bottom: 8),
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(4),
                border: Border.all(color: Colors.grey.shade300),
              ),
              child: Text(
                widget.details!,
                style: TextStyle(
                  fontSize: 10,
                  fontFamily: 'monospace',
                  color: Colors.grey.shade800,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildActions() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          if (widget.onTroubleshoot != null)
            TextButton(
              onPressed: widget.onTroubleshoot,
              child: const Text('Troubleshoot'),
            ),
          const SizedBox(width: 8),
          if (widget.onRetry != null)
            ElevatedButton(
              onPressed: () {
                // Play the shake animation again
                _controller.reset();
                _controller.forward().then((_) {
                  _controller.reverse();
                });

                widget.onRetry!();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red.shade700,
                foregroundColor: Colors.white,
              ),
              child: const Text('Try Again'),
            ),
        ],
      ),
    );
  }

  IconData _getErrorIcon() {
    switch (widget.errorType) {
      case ReminderErrorType.permission:
        return Icons.no_accounts;
      case ReminderErrorType.scheduling:
        return Icons.event_busy;
      case ReminderErrorType.network:
        return Icons.wifi_off;
      case ReminderErrorType.timeZone:
        return Icons.schedule;
      case ReminderErrorType.storage:
        return Icons.storage;
      case ReminderErrorType.unknown:
      default:
        return Icons.error_outline;
    }
  }

  String _getErrorTitle() {
    switch (widget.errorType) {
      case ReminderErrorType.permission:
        return 'Permission Error';
      case ReminderErrorType.scheduling:
        return 'Scheduling Error';
      case ReminderErrorType.network:
        return 'Network Error';
      case ReminderErrorType.timeZone:
        return 'Time Zone Error';
      case ReminderErrorType.storage:
        return 'Storage Error';
      case ReminderErrorType.unknown:
      default:
        return 'Error';
    }
  }

  List<String> _getTroubleshootingTips() {
    switch (widget.errorType) {
      case ReminderErrorType.permission:
        return [
          'Make sure notification permissions are enabled in your device settings',
          'Try reopening the app and granting permissions when prompted',
          'Check if app notifications are blocked in system settings',
        ];
      case ReminderErrorType.scheduling:
        return [
          'Ensure the selected time is in the future',
          'Try restarting the app and scheduling again',
          'Check if battery optimization is restricting background tasks',
        ];
      case ReminderErrorType.network:
        return [
          'Check your internet connection',
          'Try again when you have a stable connection',
          'If the problem persists, try setting reminders in offline mode',
        ];
      case ReminderErrorType.timeZone:
        return [
          'Ensure your device time zone settings are correct',
          'Try restarting the app after changing time zones',
          'Manually set the time zone in app settings',
        ];
      case ReminderErrorType.storage:
        return [
          'Check if your device has sufficient storage space',
          'Try clearing the app cache in system settings',
          'Restart your device and try again',
        ];
      case ReminderErrorType.unknown:
      default:
        return [
          'Try restarting the app',
          'Make sure your device is up to date',
          'If the problem persists, contact support',
        ];
    }
  }
}

/// Shows a reminder error state dialog
Future<void> showReminderErrorDialog(
  BuildContext context, {
  required ReminderErrorType errorType,
  required String message,
  String? details,
  VoidCallback? onRetry,
  VoidCallback? onTroubleshoot,
}) async {
  await showDialog<void>(
    context: context,
    barrierDismissible: true,
    builder: (context) => AlertDialog(
      contentPadding: EdgeInsets.zero,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      content: ReminderErrorStateWidget(
        errorType: errorType,
        message: message,
        details: details,
        onRetry: onRetry,
        onTroubleshoot: onTroubleshoot,
        onDismiss: () => Navigator.of(context).pop(),
      ),
    ),
  );
}

/// Shows a reminder error state snackbar
void showReminderErrorSnackbar(
  BuildContext context, {
  required ReminderErrorType errorType,
  required String message,
  VoidCallback? onRetry,
}) {
  ScaffoldMessenger.of(context).showSnackBar(
    SnackBar(
      backgroundColor: Colors.red.shade50,
      behavior: SnackBarBehavior.floating,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
        side: BorderSide(color: Colors.red.shade200),
      ),
      content: Row(
        children: [
          Icon(
            errorType == ReminderErrorType.permission
                ? Icons.no_accounts
                : Icons.error_outline,
            color: Colors.red.shade700,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              message,
              style: TextStyle(
                color: Colors.red.shade900,
                fontSize: 14,
              ),
            ),
          ),
          if (onRetry != null)
            TextButton(
              onPressed: onRetry,
              child: Text(
                'RETRY',
                style: TextStyle(
                  color: Colors.red.shade700,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
        ],
      ),
      duration: const Duration(seconds: 6),
    ),
  );
}
