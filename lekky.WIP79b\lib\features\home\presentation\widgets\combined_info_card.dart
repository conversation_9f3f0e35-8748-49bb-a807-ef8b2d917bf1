// File: lib/features/home/<USER>/widgets/combined_info_card.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../core/providers/notification_provider.dart';
import '../../../../core/shared_modules/settings_provider.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/utils/event_bus.dart';
import '../../../../core/utils/logger.dart';
import '../../../../core/widgets/app_card.dart';

/// A card that combines meter info and notifications with a divider
class CombinedInfoCard extends StatefulWidget {
  final String lastReadingDate;
  final String dateToTopUp;
  final VoidCallback? onTap;
  final bool isLoading;

  const CombinedInfoCard({
    super.key,
    required this.lastReadingDate,
    required this.dateToTopUp,
    this.onTap,
    this.isLoading = false,
  });

  @override
  State<CombinedInfoCard> createState() => _CombinedInfoCardState();
}

class _CombinedInfoCardState extends State<CombinedInfoCard> {
  // Format reminder text without next reminder information
  String _formatReminderText(String frequency, String time) {
    // Insert a newline character after the frequency to force wrapping
    return '$frequency\n@ $time';
  }

  // Helper method to get reminder settings asynchronously without next reminder info
  Future<Map<String, dynamic>> _getReminderSettings(
      NotificationProvider provider) async {
    try {
      // Initialize the provider first to ensure it's ready
      await provider.initialize();

      // Get reminder settings
      final bool remindersEnabled =
          await provider.areMeterReadingRemindersEnabled();

      // Get reminder frequency
      final int frequencyDays =
          await provider.getMeterReadingReminderFrequency();
      String reminderFrequency = 'Weekly'; // Default

      // Convert days to readable frequency
      if (frequencyDays == 1) {
        reminderFrequency = 'Daily';
      } else if (frequencyDays == 7) {
        reminderFrequency = 'Weekly';
      } else if (frequencyDays == 14) {
        reminderFrequency = 'Bi-weekly';
      } else if (frequencyDays == 30) {
        reminderFrequency = 'Monthly';
      }

      // Get reminder time
      final reminderTimeModel = await provider.getMeterReadingReminderTime();
      // Format time as 24-hour format (HH:MM)
      final hour = reminderTimeModel.timeOfDay.hour;
      final minute = reminderTimeModel.timeOfDay.minute;
      final reminderTime =
          '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}';

      return {
        'remindersEnabled': remindersEnabled,
        'reminderFrequency': reminderFrequency,
        'reminderTime': reminderTime,
      };
    } catch (e) {
      // Use logger instead of print
      logger.e('Error getting reminder settings', details: e.toString());
      // Return default values in case of error
      return {
        'remindersEnabled': false,
        'reminderFrequency': 'Weekly',
        'reminderTime': '09:00',
      };
    }
  }

  // Subscribe to settings changes
  @override
  void initState() {
    super.initState();

    // Listen for both data updates and settings changes
    EventBus().stream.listen((event) {
      if (event == EventType.dataUpdated ||
          event == EventType.settingsUpdated) {
        // Force a rebuild when data or settings are updated
        if (mounted) setState(() {});
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final labelColor =
        isDarkMode ? AppColors.lastReadingLabelDark : AppColors.primary;
    final valueColor =
        isDarkMode ? AppColors.valueTextDark : AppColors.onBackground;

    return AppCard(
      onTap: widget.onTap,
      padding: const EdgeInsets.all(16),
      child: widget.isLoading
          ? const Center(
              child: CircularProgressIndicator(),
            )
          : Column(
              children: [
                // Meter Info Section
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // Last Reading Column
                    Expanded(
                      child: Row(
                        children: [
                          const Icon(
                            Icons.calendar_today,
                            size: 20,
                            color: AppColors.primary,
                          ),
                          const SizedBox(width: 8),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Last Reading',
                                style: AppTextStyles.labelLarge.copyWith(
                                  color: labelColor,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                widget.lastReadingDate,
                                style: AppTextStyles.bodyLarge.copyWith(
                                  fontWeight: FontWeight.w500,
                                  color: valueColor,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    // Top Up By Column
                    Expanded(
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          const Icon(
                            Icons.access_time,
                            size: 20,
                            color: AppColors.primary,
                          ),
                          const SizedBox(width: 8),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Top Up Before',
                                style: AppTextStyles.labelLarge.copyWith(
                                  color: labelColor,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                widget.dateToTopUp,
                                style: AppTextStyles.bodyLarge.copyWith(
                                  fontWeight: FontWeight.w500,
                                  color: valueColor,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),

                // Divider between sections
                const SizedBox(height: 4),
                const Divider(),
                const SizedBox(height: 2),

                // Notifications Section
                Consumer<SettingsProvider>(
                  builder: (context, settingsProvider, _) {
                    // Get notification settings
                    final bool notificationsEnabled =
                        settingsProvider.notificationsEnabled;
                    final double alertThreshold =
                        settingsProvider.alertThreshold;
                    final int daysInAdvance = settingsProvider.daysInAdvance;

                    // Get currency symbol
                    final String currency = settingsProvider.currency;

                    // Get notification provider
                    final notificationProvider =
                        Provider.of<NotificationProvider>(context,
                            listen: true);

                    // Ensure the notification provider is initialized
                    notificationProvider.initialize();

                    // Use FutureBuilder for reminder settings
                    return FutureBuilder<Map<String, dynamic>>(
                      future: _getReminderSettings(notificationProvider),
                      builder: (context, snapshot) {
                        // Default values if data is still loading
                        bool remindersEnabled = false;
                        String reminderFrequency = 'Weekly';
                        String reminderTime = '7:00pm';

                        // Update with actual values if available
                        if (snapshot.hasData) {
                          remindersEnabled = snapshot.data!['remindersEnabled'];
                          reminderFrequency =
                              snapshot.data!['reminderFrequency'];
                          reminderTime = snapshot.data!['reminderTime'];
                        }

                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Fixed position titles row
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                // Notifications title
                                Row(
                                  children: [
                                    const Icon(
                                      Icons.show_chart,
                                      size: 20,
                                      color: AppColors.primary,
                                    ),
                                    const SizedBox(width: 8),
                                    Text(
                                      'Notifications:',
                                      style: AppTextStyles.labelLarge.copyWith(
                                        color: labelColor,
                                      ),
                                    ),
                                  ],
                                ),
                                // Meter Reminder title
                                Row(
                                  children: [
                                    const Icon(
                                      Icons.notifications,
                                      size: 20,
                                      color: AppColors.primary,
                                    ),
                                    const SizedBox(width: 8),
                                    Text(
                                      'Meter Reminder:',
                                      style: AppTextStyles.labelLarge.copyWith(
                                        color: labelColor,
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),

                            const SizedBox(height: 4),

                            // Content row
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                // Notification Details
                                Expanded(
                                  child: Padding(
                                    padding: const EdgeInsets.only(
                                        left: 28), // Icon width + spacing
                                    child: Text(
                                      // Check if notifications are enabled in settings
                                      notificationsEnabled
                                          ? '$currency${alertThreshold.toStringAsFixed(2)} alert\n$daysInAdvance days notice'
                                          : 'Not enabled',
                                      style: AppTextStyles.bodyLarge.copyWith(
                                        fontWeight: FontWeight.w500,
                                        color: valueColor,
                                      ),
                                      maxLines: 3,
                                      overflow: TextOverflow.ellipsis,
                                      key: const ValueKey(
                                          'notification_status_text'),
                                    ),
                                  ),
                                ),
                                // Meter Reading Reminder content
                                Expanded(
                                  child: Padding(
                                    padding: const EdgeInsets.only(
                                        left: 28), // Icon width + spacing
                                    child: Text(
                                      remindersEnabled
                                          ? _formatReminderText(
                                              reminderFrequency,
                                              reminderTime,
                                            )
                                          : 'Not enabled',
                                      style: AppTextStyles.bodyLarge.copyWith(
                                        fontWeight: FontWeight.w500,
                                        color: valueColor,
                                      ),
                                      maxLines: 3,
                                      overflow: TextOverflow.ellipsis,
                                      textAlign: TextAlign.right,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        );
                      },
                    );
                  },
                ),
              ],
            ),
    );
  }
}
