// File: lib/features/setup/presentation/screens/setup_screen.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/widgets/app_card.dart';
import '../../../../core/widgets/dialogs/confirmation_dialog.dart';
import '../../../../core/widgets/dialogs/progress_dialog.dart';
import '../../../../core/providers/theme_provider.dart';
import '../../../../core/shared_modules/shared_modules.dart' as shared;
import '../../../../core/extensions/context_extensions.dart';
import '../../../settings/presentation/widgets/notification_settings.dart';
import '../../../settings/presentation/widgets/meter_reading_reminder_settings.dart';
import '../controllers/setup_controller.dart';
import '../widgets/index.dart';

/// The setup screen of the app
class SetupScreen extends StatefulWidget {
  final bool isInitialSetup;

  const SetupScreen({
    super.key,
    this.isInitialSetup = true,
  });

  @override
  State<SetupScreen> createState() => _SetupScreenState();
}

class _SetupScreenState extends State<SetupScreen> {
  // Track if user has made a theme selection
  bool _hasUserSelectedTheme = false;

  @override
  void initState() {
    super.initState();
    // Initialize the controller
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Get the controller and initialize it
      final controller = context.read<SetupController>();
      controller.init();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    return Consumer<SetupController>(
      builder: (context, controller, _) {
        if (controller.isLoading) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        return CustomScrollView(
          slivers: [
            _buildAppBar(widget.isInitialSetup),
            SliverPadding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
              sliver: SliverList(
                delegate: SliverChildListDelegate([
                  // Region Settings Card
                  AppCard(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                Icons.public,
                                color: context.isDarkMode
                                    ? AppColors.primaryDark
                                    : AppColors.primary,
                                size: 24,
                              ),
                              const SizedBox(width: 12),
                              Text(
                                'Region Settings',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                  color: context.isDarkMode
                                      ? AppColors.primaryDark
                                      : AppColors.primary,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Configure your regional preferences',
                            style: AppTextStyles.bodyMedium.copyWith(
                              color: context.textColor,
                            ),
                          ),
                          const SizedBox(height: 16),

                          // Language Selector
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              shared.LanguageSelector(
                                currentValue: controller.config.language,
                                onChanged: (value) {
                                  controller.updateLanguage(value);
                                },
                                displayMode:
                                    shared.SettingsDisplayMode.expanded,
                                showHelperText: true,
                              ),
                            ],
                          ),

                          const SizedBox(height: 16),
                          const Divider(),
                          const SizedBox(height: 16),

                          // Currency Selector
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              shared.RadioCurrencySelectorAdapter(
                                currentValue: controller.config.meterUnit,
                                onChanged: (value) {
                                  controller.updateMeterUnit(value);
                                },
                                displayMode:
                                    shared.SettingsDisplayMode.expanded,
                                showHelperText: true,
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'Select your currency symbol from the list',
                                style: AppTextStyles.bodySmall.copyWith(
                                  color: context.secondaryTextColor,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // First Meter Reading Card
                  AppCard(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                Icons.speed,
                                color: context.isDarkMode
                                    ? AppColors.primaryDark
                                    : AppColors.primary,
                                size: 24,
                              ),
                              const SizedBox(width: 12),
                              Text(
                                'Meter Reading',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                  color: context.isDarkMode
                                      ? AppColors.primaryDark
                                      : AppColors.primary,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Enter your meter reading (optional)',
                            style: AppTextStyles.bodyMedium.copyWith(
                              color: context.textColor,
                            ),
                          ),
                          const SizedBox(height: 16),

                          // Initial Meter Credit Input
                          shared.InitialMeterCreditSelector(
                            currentValue: controller.config.initialMeterCredit,
                            onChanged: (value) {
                              controller.updateInitialMeterCredit(value);
                            },
                            currencySymbol: controller.config.meterUnit,
                            displayMode: shared.SettingsDisplayMode.expanded,
                            showHelperText: true,
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Alert Settings Card
                  AppCard(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                Icons.warning_amber,
                                color: context.isDarkMode
                                    ? AppColors.primaryDark
                                    : AppColors.primary,
                                size: 24,
                              ),
                              const SizedBox(width: 12),
                              Text(
                                'Alert Settings',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                  color: context.isDarkMode
                                      ? AppColors.primaryDark
                                      : AppColors.primary,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Configure when you want to be notified about low meter balance',
                            style: AppTextStyles.bodyMedium.copyWith(
                              color: context.textColor,
                            ),
                          ),
                          const SizedBox(height: 16),

                          // Alert Threshold
                          shared.AlertThresholdSelector(
                            currentValue: controller.config.alertThreshold,
                            onChanged: (value) {
                              controller.updateAlertThreshold(value);
                            },
                            currencySymbol: controller.config.meterUnit,
                            hasMeterReadings: false, // First-time setup
                            displayMode: shared.SettingsDisplayMode.expanded,
                            showHelperText: true,
                          ),

                          const SizedBox(height: 16),
                          const Divider(),
                          const SizedBox(height: 16),

                          // Days in Advance
                          shared.DaysInAdvanceSelector(
                            currentValue: controller.config.daysInAdvance,
                            onChanged: (value) {
                              controller.updateDaysInAdvance(value);
                            },
                            hasTotalAverage: false, // First-time setup
                            displayMode: shared.SettingsDisplayMode.expanded,
                            showHelperText: true,
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Display Settings Card
                  AppCard(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                Icons.palette_outlined,
                                color: context.isDarkMode
                                    ? AppColors.primaryDark
                                    : AppColors.primary,
                                size: 24,
                              ),
                              const SizedBox(width: 12),
                              Text(
                                'Date Settings',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                  color: context.isDarkMode
                                      ? AppColors.primaryDark
                                      : AppColors.primary,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Configure how dates are displayed in the app',
                            style: AppTextStyles.bodyMedium.copyWith(
                              color: context.textColor,
                            ),
                          ),
                          const SizedBox(height: 16),

                          // Date Format
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              shared.DateFormatSelector(
                                currentValue: controller.config.dateFormat,
                                onChanged: (value) {
                                  controller.updateDateFormat(value);
                                },
                                displayMode:
                                    shared.SettingsDisplayMode.expanded,
                                showHelperText: true,
                              ),
                            ],
                          ),

                          const SizedBox(height: 16),
                          const Divider(),
                          const SizedBox(height: 16),

                          // Date Info
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              shared.DateInfoSelector(
                                currentValue: controller.config.dateInfo,
                                onChanged: (value) {
                                  controller.updateDateInfo(value);
                                },
                                displayMode:
                                    shared.SettingsDisplayMode.expanded,
                                showHelperText: true,
                              ),
                            ],
                          ),

                          const SizedBox(height: 16),
                          const Divider(),
                          const SizedBox(height: 16),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Appearance Card
                  AppCard(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                Icons.color_lens_outlined,
                                color: context.isDarkMode
                                    ? AppColors.primaryDark
                                    : AppColors.primary,
                                size: 24,
                              ),
                              const SizedBox(width: 12),
                              Text(
                                'Appearance',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                  color: context.isDarkMode
                                      ? AppColors.primaryDark
                                      : AppColors.primary,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Choose light or dark mode for the app',
                            style: AppTextStyles.bodyMedium.copyWith(
                              color: context.textColor,
                            ),
                          ),
                          const SizedBox(height: 16),

                          // Use the same implementation as Settings - Appearance
                          Consumer<ThemeProvider>(
                            builder: (context, themeProvider, _) {
                              // If this is the first time, set the default to system
                              if (widget.isInitialSetup &&
                                  themeProvider.themeMode != ThemeMode.system &&
                                  !_hasUserSelectedTheme) {
                                // Use a post-frame callback to avoid setState during build
                                WidgetsBinding.instance
                                    .addPostFrameCallback((_) {
                                  themeProvider.setThemeMode(ThemeMode.system);
                                });
                              }

                              return Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: Column(
                                  children: ThemeMode.values.map((mode) {
                                    return RadioListTile<ThemeMode>(
                                      title: Row(
                                        children: [
                                          Icon(
                                            mode == ThemeMode.light
                                                ? Icons.wb_sunny
                                                : mode == ThemeMode.dark
                                                    ? Icons.nightlight_round
                                                    : Icons.settings_suggest,
                                            size: 16,
                                            color: context.isDarkMode
                                                ? AppColors.primaryDark
                                                : AppColors.primary,
                                          ),
                                          const SizedBox(width: 8),
                                          Text(
                                            mode == ThemeMode.light
                                                ? 'Light Mode'
                                                : mode == ThemeMode.dark
                                                    ? 'Dark Mode'
                                                    : 'System Default',
                                            style: TextStyle(
                                              fontSize: 13,
                                              color: context.textColor,
                                            ),
                                          ),
                                        ],
                                      ),
                                      value: mode,
                                      groupValue: themeProvider.themeMode,
                                      onChanged: (value) {
                                        if (value != null) {
                                          // Update theme mode and mark as user-selected
                                          themeProvider.setThemeMode(value);
                                          setState(() {
                                            _hasUserSelectedTheme = true;
                                          });
                                        }
                                      },
                                      dense: true,
                                      contentPadding:
                                          const EdgeInsets.symmetric(
                                              horizontal: 0),
                                      activeColor: context.isDarkMode
                                          ? AppColors.primaryDark
                                          : AppColors.primary,
                                    );
                                  }).toList(),
                                ),
                              );
                            },
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Notifications Card
                  AppCard(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                Icons.notifications,
                                color: context.isDarkMode
                                    ? AppColors.primaryDark
                                    : AppColors.primary,
                                size: 24,
                              ),
                              const SizedBox(width: 12),
                              Text(
                                'Notifications',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                  color: context.isDarkMode
                                      ? AppColors.primaryDark
                                      : AppColors.primary,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Configure notification preferences',
                            style: AppTextStyles.bodyMedium.copyWith(
                              color: context.textColor,
                            ),
                          ),
                          const SizedBox(height: 16),

                          // Notifications Settings with default on
                          const NotificationSettings(defaultOn: true),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Meter Reading Reminder Card
                  AppCard(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                Icons.timer_outlined,
                                color: context.isDarkMode
                                    ? AppColors.primaryDark
                                    : AppColors.primary,
                                size: 24,
                              ),
                              const SizedBox(width: 12),
                              Text(
                                'Reminders',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                  color: context.isDarkMode
                                      ? AppColors.primaryDark
                                      : AppColors.primary,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Get reminded to record your meter readings regularly',
                            style: AppTextStyles.bodyMedium.copyWith(
                              color: context.textColor,
                            ),
                          ),
                          const SizedBox(height: 16),

                          // Meter Reading Reminder Settings with default on
                          const MeterReadingReminderSettings(defaultOn: true),
                        ],
                      ),
                    ),
                  ),

                  // Actions
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    child: SetupActions(
                      onSave: _saveSettings,
                      onReset: controller.resetToDefault,
                      onLoad: widget.isInitialSetup ? _showLoadDialog : null,
                      isSaving: controller.isSaving,
                      isInitialSetup: widget.isInitialSetup,
                    ),
                  ),

                  // Error message if any
                  if (controller.error.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.only(top: 16),
                      child: Text(
                        controller.error,
                        style: AppTextStyles.bodyMedium.copyWith(
                          color: AppColors.error,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                ]),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildAppBar(bool isInitialSetup) {
    return SliverAppBar(
      toolbarHeight: 96, // Fixed height of 96px to match Homepage and Settings
      pinned: true,
      automaticallyImplyLeading: false, // Never show back button
      // Use fixed height Stack instead of FlexibleSpaceBar
      flexibleSpace: SizedBox(
        height: 96, // Fixed height of 96px
        child: Stack(
          children: [
            // Background gradient
            Positioned.fill(
              child: Container(
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: AppColors.setupGradient,
                  ),
                ),
              ),
            ),
            // Custom positioned title - matching Home screen style and position
            Positioned(
              top: 20,
              left: 20, // Exactly 20px from left edge to match Home screen
              child: Text(
                isInitialSetup ? 'Setup' : 'Settings', // Banner text
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 40, // Matching Home screen font size
                  fontWeight: FontWeight.bold,
                  shadows: [
                    Shadow(
                      offset: Offset(1.0, 1.0),
                      blurRadius: 3.0,
                      color: Color.fromARGB(128, 0, 0, 0),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _saveSettings() async {
    final controller = context.read<SetupController>();
    final success = await controller.saveSetup();

    if (success && widget.isInitialSetup) {
      // Navigate to home screen if this is the initial setup
      if (mounted) {
        Navigator.of(context).pushReplacementNamed('/home');
      }
    }
  }

  /// Shows a dialog to confirm loading meter data
  Future<void> _showLoadDialog() async {
    if (!mounted) return;

    final bool? confirmed = await ConfirmationDialog.show(
      context: context,
      title: 'Load Meter Data',
      message: 'This will load your previously saved meter data.',
      confirmText: 'Load',
      cancelText: 'Cancel',
      icon: Icons.file_download,
    );

    if (confirmed == true && mounted) {
      try {
        // Get the controller before the async operation
        final controller = Provider.of<SetupController>(context, listen: false);

        // Show loading indicator
        if (!mounted) return;

        await ProgressDialog.show(
          context: context,
          title: 'Loading Data',
          message: 'Please wait while we load your meter data...',
          barrierDismissible: false,
        );

        // Load the meter data
        final success = await controller.loadMeterData();

        // Check if the widget is still mounted
        if (!mounted) return;

        // Close loading dialog
        Navigator.of(context).pop();

        if (success) {
          // Show success message
          if (!mounted) return;

          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Meter data loaded successfully'),
              backgroundColor: Colors.green,
            ),
          );

          // Navigate to home screen if this is the initial setup
          if (widget.isInitialSetup && mounted) {
            Navigator.of(context).pushReplacementNamed('/home');
          }
        } else {
          // Show error message
          if (!mounted) return;

          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Failed to load meter data'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } catch (e) {
        // Handle any errors
        if (!mounted) return;

        // Close loading dialog if it's still open
        Navigator.of(context).pop();

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading meter data: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
