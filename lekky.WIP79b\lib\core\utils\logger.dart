// File: lib/core/utils/logger.dart
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'error_handler.dart';

/// Log level
enum LogLevel {
  /// Debug level - verbose information
  debug,

  /// Info level - general information
  info,

  /// Warning level - potential issues
  warning,

  /// Error level - errors that don't crash the app
  error,

  /// Fatal level - critical errors that may crash the app
  fatal,
}

/// Logger class for logging messages
class Logger {
  /// Singleton instance
  static final Logger _instance = Logger._internal();

  /// Factory constructor
  factory Logger() => _instance;

  /// Internal constructor
  Logger._internal();

  /// Whether to log to console
  bool _logToConsole = true;

  /// Whether to log to file
  bool _logToFile = false;

  /// Log file path
  String? _logFilePath;

  /// Initialize the logger
  Future<void> init({bool logToConsole = true, bool logToFile = false}) async {
    _logToConsole = logToConsole;
    _logToFile = logToFile;

    if (_logToFile) {
      final directory = await getApplicationDocumentsDirectory();
      final date = DateTime.now().toIso8601String().split('T')[0];
      _logFilePath = '${directory.path}/lekky_$date.log';
    }
  }

  /// Log a message
  void log(String message, {LogLevel level = LogLevel.info, dynamic details}) {
    final timestamp = DateTime.now().toIso8601String();
    final levelStr = level.toString().split('.').last.toUpperCase();
    final logMessage = '[$timestamp] $levelStr: $message';
    final detailsStr = details != null ? '\nDetails: $details' : '';
    final fullMessage = '$logMessage$detailsStr';

    if (_logToConsole && kDebugMode) {
      switch (level) {
        case LogLevel.debug:
          print('\x1B[37m$fullMessage\x1B[0m'); // White
          break;
        case LogLevel.info:
          print('\x1B[32m$fullMessage\x1B[0m'); // Green
          break;
        case LogLevel.warning:
          print('\x1B[33m$fullMessage\x1B[0m'); // Yellow
          break;
        case LogLevel.error:
          print('\x1B[31m$fullMessage\x1B[0m'); // Red
          break;
        case LogLevel.fatal:
          print('\x1B[35m$fullMessage\x1B[0m'); // Purple
          break;
      }
    }

    if (_logToFile && _logFilePath != null) {
      _writeToFile('$fullMessage\n');
    }

    // Add errors to the error handler
    if (level == LogLevel.error || level == LogLevel.fatal) {
      final errorType = level == LogLevel.fatal
          ? ErrorType.unknown
          : ErrorType.validation;
      final errorSeverity = level == LogLevel.fatal
          ? ErrorSeverity.high
          : ErrorSeverity.medium;
      
      ErrorHandler.addError(AppError(
        message: message,
        severity: errorSeverity,
        type: errorType,
        details: details,
      ));
    }
  }

  /// Log a debug message
  void d(String message, {dynamic details}) {
    log(message, level: LogLevel.debug, details: details);
  }

  /// Log an info message
  void i(String message, {dynamic details}) {
    log(message, level: LogLevel.info, details: details);
  }

  /// Log a warning message
  void w(String message, {dynamic details}) {
    log(message, level: LogLevel.warning, details: details);
  }

  /// Log an error message
  void e(String message, {dynamic details}) {
    log(message, level: LogLevel.error, details: details);
  }

  /// Log a fatal message
  void f(String message, {dynamic details}) {
    log(message, level: LogLevel.fatal, details: details);
  }

  /// Write to log file
  Future<void> _writeToFile(String message) async {
    try {
      if (_logFilePath != null) {
        final file = File(_logFilePath!);
        await file.writeAsString(message, mode: FileMode.append);
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error writing to log file: $e');
      }
    }
  }

  /// Get the log file path
  String? get logFilePath => _logFilePath;

  /// Clear the log file
  Future<void> clearLogFile() async {
    try {
      if (_logFilePath != null) {
        final file = File(_logFilePath!);
        if (await file.exists()) {
          await file.delete();
          await file.create();
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error clearing log file: $e');
      }
    }
  }

  /// Get the log file content
  Future<String> getLogFileContent() async {
    try {
      if (_logFilePath != null) {
        final file = File(_logFilePath!);
        if (await file.exists()) {
          return await file.readAsString();
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error reading log file: $e');
      }
    }
    return '';
  }
}

/// Global logger instance
final logger = Logger();
