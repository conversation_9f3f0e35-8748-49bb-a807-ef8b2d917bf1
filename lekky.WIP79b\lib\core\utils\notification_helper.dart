// File: lib/core/utils/notification_helper.dart
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:timezone/data/latest.dart' as tz_data;
import '../utils/logger.dart';

/// Helper class for notifications
class NotificationHelper {
  final FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin;
  static final NotificationHelper _instance = NotificationHelper._internal(
    FlutterLocalNotificationsPlugin(),
  );

  // Keys for storing notification-related data
  static const String _lastKnownTimeZoneKey = 'last_known_timezone';
  static const String _lastPermissionCheckKey = 'last_permission_check';
  static const String _notificationsEnabledKey = 'notifications_enabled_system';

  // In-memory cache of notification status
  bool _areNotificationsPermissionGranted = false;
  String? _currentTimeZone;

  // Constants for permission checking
  static const int _permissionCheckIntervalHours =
      24; // Check permissions once a day

  /// Private constructor
  NotificationHelper._internal(this._flutterLocalNotificationsPlugin);

  /// Factory constructor
  factory NotificationHelper() {
    return _instance;
  }

  /// Initialize the notification plugin
  static Future<void> initialize() async {
    try {
      // Initialize timezone
      tz_data.initializeTimeZones();

      // Get the current time zone and store it
      final String currentTimeZone = tz.local.name;
      await _instance._saveCurrentTimeZone(currentTimeZone);

      // Initialize notification settings
      const AndroidInitializationSettings initializationSettingsAndroid =
          AndroidInitializationSettings('@mipmap/ic_launcher');

      const DarwinInitializationSettings initializationSettingsIOS =
          DarwinInitializationSettings(
        requestAlertPermission:
            false, // Don't request permissions during initialization
        requestBadgePermission: false,
        requestSoundPermission: false,
      );

      const InitializationSettings initializationSettings =
          InitializationSettings(
        android: initializationSettingsAndroid,
        iOS: initializationSettingsIOS,
      );

      await _instance._flutterLocalNotificationsPlugin.initialize(
        initializationSettings,
        onDidReceiveNotificationResponse: _onDidReceiveNotificationResponse,
      );

      // Check notification permissions without requesting them
      await _instance.checkNotificationPermissions();

      // Check if time zone has changed since last run
      await _instance._checkTimeZoneChange();

      // Log initialization success with more details
      final permissionStatus = await _instance.areNotificationsEnabled();
      logger.i(
          'NotificationHelper initialized successfully. Permissions granted: $permissionStatus');
    } catch (e) {
      logger.e('Error initializing NotificationHelper', details: e.toString());
    }
  }

  /// Save the current time zone
  Future<void> _saveCurrentTimeZone(String timeZone) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final String? lastTimeZone = prefs.getString(_lastKnownTimeZoneKey);

      // Store the current time zone in memory
      _currentTimeZone = timeZone;

      // Only save if it's different from the last known time zone
      if (lastTimeZone != timeZone) {
        await prefs.setString(_lastKnownTimeZoneKey, timeZone);
        logger.i('Time zone saved: $timeZone (was: $lastTimeZone)');
      }
    } catch (e) {
      logger.e('Error saving time zone', details: e.toString());
    }
  }

  /// Check if the time zone has changed since the last run
  Future<bool> _checkTimeZoneChange() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final String? lastTimeZone = prefs.getString(_lastKnownTimeZoneKey);
      final String currentTimeZone = tz.local.name;

      // If we have a last known time zone and it's different from the current one
      if (lastTimeZone != null && lastTimeZone != currentTimeZone) {
        logger.i('Time zone changed from $lastTimeZone to $currentTimeZone');

        // Update the stored time zone
        await _saveCurrentTimeZone(currentTimeZone);
        return true;
      }
      return false;
    } catch (e) {
      logger.e('Error checking time zone change', details: e.toString());
      return false;
    }
  }

  /// Check if the time zone has changed and return the result
  Future<bool> hasTimeZoneChanged() async {
    return await _checkTimeZoneChange();
  }

  /// Get the current time zone
  String getCurrentTimeZone() {
    return _currentTimeZone ?? tz.local.name;
  }

  /// Check notification permissions and update internal state
  Future<bool> checkNotificationPermissions() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Check when we last verified permissions
      final lastCheck = prefs.getInt(_lastPermissionCheckKey);
      final now = DateTime.now().millisecondsSinceEpoch;

      // If we've checked recently, use the cached value
      if (lastCheck != null) {
        final hoursSinceLastCheck = (now - lastCheck) / (1000 * 60 * 60);

        if (hoursSinceLastCheck < _permissionCheckIntervalHours) {
          // Use cached permission status if recent
          _areNotificationsPermissionGranted =
              prefs.getBool(_notificationsEnabledKey) ?? false;

          logger.i(
              'Using cached notification permission status: $_areNotificationsPermissionGranted');
          return _areNotificationsPermissionGranted;
        }
      }

      // Check current permission status
      bool permissionGranted = false;

      // For Android, we need to check if notifications are enabled
      if (Platform.isAndroid) {
        // First try using permission_handler for Android 13+ (API 33+)
        try {
          final status = await Permission.notification.status;
          logger.i(
              'Notification permission status from permission_handler: $status');

          permissionGranted = status.isGranted;

          // Also check using flutter_local_notifications as a backup
          final AndroidFlutterLocalNotificationsPlugin? androidPlugin =
              _flutterLocalNotificationsPlugin
                  .resolvePlatformSpecificImplementation<
                      AndroidFlutterLocalNotificationsPlugin>();

          if (androidPlugin != null) {
            final bool? flnPermission =
                await androidPlugin.areNotificationsEnabled();

            // Log any discrepancy between the two methods
            if ((flnPermission ?? false) != permissionGranted) {
              logger.w(
                  'Discrepancy between permission_handler ($permissionGranted) and flutter_local_notifications (${flnPermission ?? false})');

              // If permission_handler says denied but flutter_local_notifications says granted,
              // trust flutter_local_notifications
              if (!permissionGranted && (flnPermission ?? false)) {
                permissionGranted = true;
                logger.i(
                    'Using flutter_local_notifications permission status instead');
              }
            }
          }
        } catch (e) {
          logger.e('Error checking permission with permission_handler',
              details: e.toString());

          // Fallback to flutter_local_notifications
          final AndroidFlutterLocalNotificationsPlugin? androidPlugin =
              _flutterLocalNotificationsPlugin
                  .resolvePlatformSpecificImplementation<
                      AndroidFlutterLocalNotificationsPlugin>();

          if (androidPlugin != null) {
            permissionGranted =
                await androidPlugin.areNotificationsEnabled() ?? false;
            logger.i(
                'Fallback to flutter_local_notifications: $permissionGranted');
          }
        }
      } else if (Platform.isIOS) {
        // For iOS, we need to check if notifications are authorized
        // But we should NOT request permissions here, just check status
        final IOSFlutterLocalNotificationsPlugin? iosPlugin =
            _flutterLocalNotificationsPlugin
                .resolvePlatformSpecificImplementation<
                    IOSFlutterLocalNotificationsPlugin>();

        if (iosPlugin != null) {
          // Check if we can get pending notifications as a way to check permission
          // without triggering a permission request
          try {
            await iosPlugin.pendingNotificationRequests();
            permissionGranted = true;
          } catch (e) {
            permissionGranted = false;
            logger.i('iOS notification permissions not granted');
          }
        }
      }

      // Update cache
      _areNotificationsPermissionGranted = permissionGranted;

      // Save to preferences
      await prefs.setInt(_lastPermissionCheckKey, now);
      await prefs.setBool(_notificationsEnabledKey, permissionGranted);

      logger.i('Notification permissions checked: $permissionGranted');
      return permissionGranted;
    } catch (e) {
      logger.e('Error checking notification permissions',
          details: e.toString());
      return false;
    }
  }

  // The requestPermissions method has been moved to line 270 with enhanced implementation

  /// Check if notifications are enabled
  Future<bool> areNotificationsEnabled() async {
    // First check our cached value
    if (_areNotificationsPermissionGranted) {
      return true;
    }

    // Otherwise, check the actual permission status
    return await checkNotificationPermissions();
  }

  /// Request notification permissions
  ///
  /// This method handles both Android and iOS permissions properly:
  /// - For Android <13: No runtime permission needed, just check if notifications are enabled
  /// - For Android 13+: Explicitly request POST_NOTIFICATIONS permission using permission_handler
  /// - For iOS: Request notification permissions via UNUserNotificationCenter
  Future<bool> requestPermissions() async {
    try {
      // For Android, we need to check if notifications are enabled
      if (Platform.isAndroid) {
        try {
          // First check if notifications are already enabled
          final AndroidFlutterLocalNotificationsPlugin? androidPlugin =
              _flutterLocalNotificationsPlugin
                  .resolvePlatformSpecificImplementation<
                      AndroidFlutterLocalNotificationsPlugin>();

          if (androidPlugin != null) {
            final bool? enabled = await androidPlugin.areNotificationsEnabled();
            if (enabled == true) {
              _areNotificationsPermissionGranted = true;

              // Save the result
              final prefs = await SharedPreferences.getInstance();
              await prefs.setInt(_lastPermissionCheckKey,
                  DateTime.now().millisecondsSinceEpoch);
              await prefs.setBool(
                  _notificationsEnabledKey, _areNotificationsPermissionGranted);

              logger.i('Notifications already enabled');
              return true;
            }
          }

          // For Android 13+ (API 33+), use permission_handler to request POST_NOTIFICATIONS permission
          // This provides better handling of permission states
          final status = await Permission.notification.status;
          logger.i('Current notification permission status: $status');

          if (status.isGranted) {
            _areNotificationsPermissionGranted = true;

            // Save the result
            final prefs = await SharedPreferences.getInstance();
            await prefs.setInt(
                _lastPermissionCheckKey, DateTime.now().millisecondsSinceEpoch);
            await prefs.setBool(
                _notificationsEnabledKey, _areNotificationsPermissionGranted);

            return true;
          }

          if (status.isDenied) {
            // Request permission using permission_handler
            final result = await Permission.notification.request();
            _areNotificationsPermissionGranted = result.isGranted;

            // Save the result
            final prefs = await SharedPreferences.getInstance();
            await prefs.setInt(
                _lastPermissionCheckKey, DateTime.now().millisecondsSinceEpoch);
            await prefs.setBool(
                _notificationsEnabledKey, _areNotificationsPermissionGranted);

            logger.i(
                'Permission request result: $result (granted: ${result.isGranted})');

            return result.isGranted;
          }

          if (status.isPermanentlyDenied) {
            logger.w(
                'Notification permission is permanently denied. User needs to enable it in app settings.');
            return false;
          }

          // Fallback to flutter_local_notifications for older Android versions
          if (androidPlugin != null) {
            final bool? result =
                await androidPlugin.requestNotificationsPermission();
            _areNotificationsPermissionGranted = result ?? false;

            // Save the result
            final prefs = await SharedPreferences.getInstance();
            await prefs.setInt(
                _lastPermissionCheckKey, DateTime.now().millisecondsSinceEpoch);
            await prefs.setBool(
                _notificationsEnabledKey, _areNotificationsPermissionGranted);

            logger.i(
                'Fallback permission request result: $_areNotificationsPermissionGranted');
            return _areNotificationsPermissionGranted;
          }
        } catch (e) {
          logger.e('Error requesting Android notification permissions',
              details: e.toString());
        }
      } else if (Platform.isIOS) {
        final IOSFlutterLocalNotificationsPlugin? iosPlugin =
            _flutterLocalNotificationsPlugin
                .resolvePlatformSpecificImplementation<
                    IOSFlutterLocalNotificationsPlugin>();

        if (iosPlugin != null) {
          // Request iOS notification permissions
          final bool? result = await iosPlugin.requestPermissions(
            alert: true,
            badge: true,
            sound: true,
          );

          _areNotificationsPermissionGranted = result ?? false;

          // Save the result
          final prefs = await SharedPreferences.getInstance();
          await prefs.setInt(
              _lastPermissionCheckKey, DateTime.now().millisecondsSinceEpoch);
          await prefs.setBool(
              _notificationsEnabledKey, _areNotificationsPermissionGranted);

          logger.i(
              'iOS notification permission request result: $_areNotificationsPermissionGranted');
          return _areNotificationsPermissionGranted;
        }
      }

      return false;
    } catch (e) {
      logger.e('Error requesting notification permissions',
          details: e.toString());
      return false;
    }
  }

  /// Check if notification permission is permanently denied
  /// This is useful to determine if we should show a button to open app settings
  Future<bool> isNotificationPermissionPermanentlyDenied() async {
    if (Platform.isAndroid) {
      final status = await Permission.notification.status;
      return status.isPermanentlyDenied;
    }
    return false;
  }

  /// Open app settings
  /// This should be called when permission is permanently denied
  Future<bool> openNotificationSettings() async {
    return await openAppSettings();
  }

  /// Show a notification
  Future<void> showNotification({
    required int id,
    required String title,
    required String body,
    String? payload,
  }) async {
    const AndroidNotificationDetails androidPlatformChannelSpecifics =
        AndroidNotificationDetails(
      'lekky_channel',
      'Lekky Notifications',
      channelDescription: 'Notifications from Lekky app',
      importance: Importance.max,
      priority: Priority.high,
      showWhen: true,
    );

    const DarwinNotificationDetails iOSPlatformChannelSpecifics =
        DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const NotificationDetails platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
      iOS: iOSPlatformChannelSpecifics,
    );

    await _flutterLocalNotificationsPlugin.show(
      id,
      title,
      body,
      platformChannelSpecifics,
      payload: payload,
    );
  }

  /// Schedule a notification with enhanced reliability
  Future<bool> scheduleNotification({
    required int id,
    required String title,
    required String body,
    required DateTime scheduledDate,
    String? payload,
    int maxRetries = 3,
  }) async {
    // First check if notifications are enabled
    final bool notificationsEnabled = await areNotificationsEnabled();
    if (!notificationsEnabled) {
      logger.w('Cannot schedule notification: notifications are not enabled');
      return false;
    }

    // Check if the scheduled date is in the past
    if (scheduledDate.isBefore(DateTime.now())) {
      logger.w('Cannot schedule notification: scheduled date is in the past');
      return false;
    }

    // Create notification details
    const AndroidNotificationDetails androidPlatformChannelSpecifics =
        AndroidNotificationDetails(
      'lekky_channel',
      'Lekky Notifications',
      channelDescription: 'Notifications from Lekky app',
      importance: Importance.max,
      priority: Priority.high,
      showWhen: true,
    );

    const DarwinNotificationDetails iOSPlatformChannelSpecifics =
        DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const NotificationDetails platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
      iOS: iOSPlatformChannelSpecifics,
    );

    // Get the current time zone
    final String currentTimeZone = getCurrentTimeZone();

    // Store the notification details for potential recovery
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('notification_$id',
        '$title|$body|${scheduledDate.millisecondsSinceEpoch}|$currentTimeZone');

    // Try to schedule the notification with retries
    for (int attempt = 0; attempt < maxRetries; attempt++) {
      try {
        // Convert the scheduled date to a time zone aware date
        final tz.TZDateTime zonedDate =
            tz.TZDateTime.from(scheduledDate, tz.local);

        // Schedule the notification
        await _flutterLocalNotificationsPlugin.zonedSchedule(
          id,
          title,
          body,
          zonedDate,
          platformChannelSpecifics,
          androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
          uiLocalNotificationDateInterpretation:
              UILocalNotificationDateInterpretation.absoluteTime,
          payload: payload,
        );

        logger.i(
            'Successfully scheduled notification $id for ${zonedDate.toString()}');
        return true;
      } catch (e) {
        logger.e(
            'Error scheduling notification (attempt ${attempt + 1}/$maxRetries)',
            details: e.toString());

        // Wait a bit before retrying
        if (attempt < maxRetries - 1) {
          await Future.delayed(Duration(milliseconds: 500 * (attempt + 1)));
        }
      }
    }

    logger.e('Failed to schedule notification after $maxRetries attempts');
    return false;
  }

  /// Reschedule a notification by ID
  Future<bool> rescheduleNotification(int id) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final String? notificationData = prefs.getString('notification_$id');

      if (notificationData == null) {
        logger.w('No data found for notification $id');
        return false;
      }

      final parts = notificationData.split('|');
      if (parts.length < 3) {
        logger.e('Invalid notification data format for ID $id');
        return false;
      }

      final String title = parts[0];
      final String body = parts[1];
      final int timestamp = int.parse(parts[2]);
      final String? storedTimeZone = parts.length > 3 ? parts[3] : null;

      // Check if the time zone has changed
      final String currentTimeZone = getCurrentTimeZone();
      final bool timeZoneChanged =
          storedTimeZone != null && storedTimeZone != currentTimeZone;

      if (timeZoneChanged) {
        logger.i(
            'Time zone changed from $storedTimeZone to $currentTimeZone, adjusting notification time');
      }

      // Create the scheduled date
      final DateTime scheduledDate =
          DateTime.fromMillisecondsSinceEpoch(timestamp);

      // Only reschedule if the date is in the future
      if (scheduledDate.isAfter(DateTime.now())) {
        return await scheduleNotification(
          id: id,
          title: title,
          body: body,
          scheduledDate: scheduledDate,
        );
      } else {
        logger.w(
            'Not rescheduling notification $id: scheduled date is in the past');
        return false;
      }
    } catch (e) {
      logger.e('Error rescheduling notification $id', details: e.toString());
      return false;
    }
  }

  /// Reschedule all pending notifications with enhanced reliability
  /// This method is called during app startup and after device reboot
  Future<int> rescheduleAllNotifications() async {
    try {
      // First check if notifications are enabled
      final bool notificationsEnabled = await areNotificationsEnabled();
      if (!notificationsEnabled) {
        logger.w(
            'Cannot reschedule notifications: notifications are not enabled');
        return 0;
      }

      logger.i('Starting to reschedule all pending notifications');

      final prefs = await SharedPreferences.getInstance();
      final Set<String> keys = prefs.getKeys();

      // Filter keys that start with 'notification_'
      final notificationKeys =
          keys.where((key) => key.startsWith('notification_')).toList();

      if (notificationKeys.isEmpty) {
        logger.i('No pending notifications found to reschedule');
        return 0;
      }

      logger.i(
          'Found ${notificationKeys.length} pending notifications to reschedule');
      int successCount = 0;
      int failureCount = 0;

      // First, cancel all existing notifications to avoid duplicates
      await cancelAllNotifications();
      logger.i('Cancelled all existing notifications before rescheduling');

      for (final key in notificationKeys) {
        try {
          final int id = int.parse(key.replaceFirst('notification_', ''));
          final bool success = await rescheduleNotification(id);

          if (success) {
            successCount++;
          } else {
            failureCount++;
          }
        } catch (e) {
          failureCount++;
          logger.e('Error rescheduling notification for key: $key',
              details: e.toString());
        }
      }

      logger.i(
          'Rescheduled $successCount/${notificationKeys.length} notifications (failed: $failureCount)');
      return successCount;
    } catch (e) {
      logger.e('Error rescheduling all notifications', details: e.toString());
      return 0;
    }
  }

  /// Cancel a notification
  Future<void> cancelNotification(int id) async {
    await _flutterLocalNotificationsPlugin.cancel(id);
  }

  /// Cancel all notifications
  Future<void> cancelAllNotifications() async {
    await _flutterLocalNotificationsPlugin.cancelAll();
  }

  /// Handle notification response
  static void _onDidReceiveNotificationResponse(NotificationResponse response) {
    // Handle notification tap
    if (response.payload != null) {
      // Navigate to the appropriate screen based on the payload
      debugPrint('Notification payload: ${response.payload}');
    }
  }

  /// Show a low balance notification
  Future<void> showLowBalanceNotification({
    required int id,
    required String meterUnit,
    required double balance,
  }) async {
    await showNotification(
      id: id,
      title: 'Low Balance',
      body:
          'Your balance is low: $meterUnit${balance.toStringAsFixed(2)}. Consider topping up soon.',
    );
  }

  /// Show a time to top up notification
  Future<void> showTimeToTopUpNotification({
    required int id,
    required String meterUnit,
    required double balance,
  }) async {
    await showNotification(
      id: id,
      title: 'Time to Top Up',
      body:
          'It\'s time to top up your meter. Your estimated balance is $meterUnit${balance.toStringAsFixed(2)}.',
    );
  }
}
