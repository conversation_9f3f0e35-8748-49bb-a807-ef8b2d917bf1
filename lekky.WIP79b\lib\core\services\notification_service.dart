// File: lib/core/services/notification_service.dart
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/date_to_top_up_result.dart';
import '../models/notification_model.dart';
import '../models/reminder_time_model.dart';
import '../utils/notification_helper.dart';
import '../utils/logger.dart' show logger;
import '../constants/app_constants.dart';

/// Service class for managing notifications
class NotificationService {
  // Singleton instance
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  // Key for storing notifications in SharedPreferences
  static const String _notificationsKey = 'lekky_notifications';
  static const String _notificationsEnabledKey = 'notifications_enabled';
  static const String _lowBalanceAlertsEnabledKey =
      'low_balance_alerts_enabled';
  static const String _topUpAlertsEnabledKey = 'top_up_alerts_enabled';
  static const String _invalidRecordAlertsEnabledKey =
      'invalid_record_alerts_enabled';
  static const String _meterReadingReminderEnabledKey =
      'meter_reading_reminder_enabled';
  static const String _meterReadingReminderFrequencyKey =
      'meter_reading_reminder_frequency';
  static const String _meterReadingReminderFrequencyEnabledKey =
      'meter_reading_reminder_frequency_enabled';
  static const String _meterReadingReminderLastDateKey =
      'meter_reading_reminder_last_date';
  static const String _meterReadingReminderTimeKey =
      'meter_reading_reminder_time';

  // Notification IDs
  static const int lowBalanceNotificationId = 1;
  static const int topUpReminderNotificationId = 2;
  static const int invalidRecordNotificationId = 3;
  static const int meterReadingReminderNotificationId = 4;
  static const int welcomeNotificationId = 5;
  static const int firstMeterReadingReminderNotificationId = 6;
  static const int notificationSystemActivationId = 7;
  static const int checkMeterNowNotificationId = 8;
  static const int groupedInvalidRecordsNotificationId = 9;

  // Notification Categories for grouping
  static const String _pendingInvalidRecordsKey = 'pending_invalid_records';

  // Keys for first-time user experience
  static const String _welcomeNotificationShownKey =
      'welcome_notification_shown';
  static const String _firstMeterReminderShownKey =
      'first_meter_reminder_shown';
  static const String _hasAnyMeterReadingsKey = 'has_any_meter_readings';
  static const String _notificationSystemActivatedKey =
      'notification_system_activated';

  // In-memory cache of notifications
  List<NotificationModel> _notifications = [];
  bool _isInitialized = false;
  final NotificationHelper _notificationHelper = NotificationHelper();

  /// Initialize the notification service
  Future<void> initialize() async {
    if (_isInitialized) return;

    await _loadNotifications();
    await NotificationHelper.initialize();
    _isInitialized = true;
  }

  /// Load notifications from SharedPreferences
  Future<void> _loadNotifications() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final notificationsJson = prefs.getStringList(_notificationsKey) ?? [];

      _notifications = notificationsJson.map((json) {
        final Map<String, dynamic> data = jsonDecode(json);
        return NotificationModel.fromMap(data);
      }).toList();

      // Sort notifications by timestamp (newest first)
      _notifications.sort((a, b) => b.timestamp.compareTo(a.timestamp));
    } catch (e) {
      logger.e('Error loading notifications: $e');
      _notifications = [];
    }
  }

  /// Save notifications to SharedPreferences
  Future<void> _saveNotifications() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final notificationsJson = _notifications.map((notification) {
        return jsonEncode(notification.toMap());
      }).toList();

      await prefs.setStringList(_notificationsKey, notificationsJson);
    } catch (e) {
      logger.e('Error saving notifications: $e');
    }
  }

  /// Add a new notification
  Future<void> addNotification({
    required String title,
    required String message,
    required NotificationPriority priority,
    String? actionType,
  }) async {
    await initialize();

    final notification = NotificationModel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      title: title,
      message: message,
      timestamp: DateTime.now(),
      priority: priority,
      actionType: actionType,
    );

    _notifications.insert(0, notification); // Add to the beginning of the list
    await _saveNotifications();
  }

  /// Mark a notification as read
  Future<void> markAsRead(String id) async {
    await initialize();

    final index =
        _notifications.indexWhere((notification) => notification.id == id);
    if (index != -1) {
      _notifications[index] = _notifications[index].copyWith(isRead: true);
      await _saveNotifications();
    }
  }

  /// Mark all notifications as read
  Future<void> markAllAsRead() async {
    await initialize();

    _notifications = _notifications
        .map((notification) => notification.copyWith(isRead: true))
        .toList();

    await _saveNotifications();
  }

  /// Clear all notifications
  Future<void> clearAll() async {
    await initialize();

    _notifications.clear();
    await _saveNotifications();
  }

  /// Remove a specific notification by ID
  Future<void> removeNotification(String id) async {
    await initialize();

    _notifications.removeWhere((notification) => notification.id == id);
    await _saveNotifications();
  }

  /// Get all notifications
  Future<List<NotificationModel>> getNotifications() async {
    await initialize();
    return List.unmodifiable(_notifications);
  }

  /// Check if there are any unread notifications
  Future<bool> hasUnreadNotifications() async {
    await initialize();
    return _notifications.any((notification) => !notification.isRead);
  }

  /// Get the count of unread notifications
  Future<int> getUnreadCount() async {
    await initialize();
    return _notifications.where((notification) => !notification.isRead).length;
  }

  /// Check if notifications are enabled
  Future<bool> areNotificationsEnabled() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_notificationsEnabledKey) ?? true;
  }

  /// Enable or disable notifications
  Future<void> setNotificationsEnabled(bool enabled) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_notificationsEnabledKey, enabled);

    if (!enabled) {
      await _notificationHelper.cancelAllNotifications();
    }
  }

  /// Check if low balance alerts are enabled
  Future<bool> areLowBalanceAlertsEnabled() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_lowBalanceAlertsEnabledKey) ?? true;
  }

  /// Enable or disable low balance alerts
  Future<void> setLowBalanceAlertsEnabled(bool enabled) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_lowBalanceAlertsEnabledKey, enabled);

    if (!enabled) {
      await _notificationHelper.cancelNotification(lowBalanceNotificationId);
    }
  }

  /// Check if top-up alerts are enabled
  Future<bool> areTopUpAlertsEnabled() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_topUpAlertsEnabledKey) ?? true;
  }

  /// Enable or disable top-up alerts
  Future<void> setTopUpAlertsEnabled(bool enabled) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_topUpAlertsEnabledKey, enabled);

    if (!enabled) {
      await _notificationHelper.cancelNotification(topUpReminderNotificationId);
    }
  }

  /// Check if invalid record alerts are enabled
  Future<bool> areInvalidRecordAlertsEnabled() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_invalidRecordAlertsEnabledKey) ?? true;
  }

  /// Enable or disable invalid record alerts
  Future<void> setInvalidRecordAlertsEnabled(bool enabled) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_invalidRecordAlertsEnabledKey, enabled);

    if (!enabled) {
      await _notificationHelper.cancelNotification(invalidRecordNotificationId);
    }
  }

  /// Show a low balance notification
  Future<void> showLowBalanceNotification({
    required String meterUnit,
    required double balance,
    required double threshold,
  }) async {
    await initialize();

    // Check if notifications and low balance alerts are enabled
    final notificationsEnabled = await areNotificationsEnabled();
    final lowBalanceAlertsEnabled = await areLowBalanceAlertsEnabled();

    if (!notificationsEnabled || !lowBalanceAlertsEnabled) {
      return;
    }

    final title = 'Low Balance Alert';
    final message =
        'Your balance is low: $meterUnit${balance.toStringAsFixed(2)}. '
        'You should top up soon as you are below your alert threshold of $meterUnit${threshold.toStringAsFixed(2)}.';

    // Add to in-app notifications
    await addNotification(
      title: title,
      message: message,
      priority: NotificationPriority.alert,
      actionType: 'low_balance',
    );

    // Show system notification
    await _notificationHelper.showNotification(
      id: lowBalanceNotificationId,
      title: title,
      body: message,
      payload: 'low_balance:$balance:$threshold',
    );
  }

  /// Show a time to top up notification with enhanced information
  Future<void> showTimeToTopUpNotification({
    required String meterUnit,
    required double balance,
    required int daysRemaining,
    ConfidenceLevel? confidenceLevel,
    double? daysSinceLastReading,
  }) async {
    await initialize();

    // Check if notifications and top-up alerts are enabled
    final notificationsEnabled = await areNotificationsEnabled();
    final topUpAlertsEnabled = await areTopUpAlertsEnabled();

    if (!notificationsEnabled || !topUpAlertsEnabled) {
      return;
    }

    String title = 'Time to Top Up';
    String message;

    // Handle immediate notification (already below threshold)
    if (daysRemaining <= 0) {
      title = 'Low Balance Alert';
      message =
          'Your estimated balance is now $meterUnit${balance.toStringAsFixed(2)}, '
          'which is below your alert threshold. Please top up soon.';
    } else {
      message =
          'Based on your usage, you should top up in $daysRemaining ${daysRemaining == 1 ? 'day' : 'days'}. '
          'Your estimated balance is $meterUnit${balance.toStringAsFixed(2)}.';
    }

    // Add confidence information based on reading age
    if (confidenceLevel != null &&
        daysSinceLastReading != null &&
        daysSinceLastReading > 1.0) {
      // Round to 1 decimal place for display
      final roundedDays = (daysSinceLastReading * 10).round() / 10;
      final daysText = roundedDays == 1.0
          ? '1 day'
          : (roundedDays == roundedDays.floor().toDouble()
              ? '${roundedDays.toInt()} days'
              : '${roundedDays.toStringAsFixed(1)} days');

      if (confidenceLevel == ConfidenceLevel.medium) {
        message += ' This estimate is based on a reading from $daysText ago.';
      } else if (confidenceLevel == ConfidenceLevel.low) {
        message +=
            ' This estimate is based on a reading from $daysText ago. For more accurate predictions, please submit a new meter reading.';
      }
    }

    // Add to in-app notifications
    await addNotification(
      title: title,
      message: message,
      priority: NotificationPriority.warning,
      actionType: 'top_up',
    );

    // Show system notification
    await _notificationHelper.showNotification(
      id: topUpReminderNotificationId,
      title: title,
      body: message,
      payload: 'top_up:$balance:$daysRemaining',
    );
  }

  /// Queue an invalid record notification for grouping
  /// This stores the notification to be sent later as part of a group
  Future<void> queueInvalidRecordNotification({
    required String message,
    required String date,
  }) async {
    await initialize();

    // Check if notifications and invalid record alerts are enabled
    final notificationsEnabled = await areNotificationsEnabled();
    final invalidRecordAlertsEnabled = await areInvalidRecordAlertsEnabled();

    if (!notificationsEnabled || !invalidRecordAlertsEnabled) {
      return;
    }

    // Get existing pending notifications
    final prefs = await SharedPreferences.getInstance();
    final pendingDates = prefs.getStringList(_pendingInvalidRecordsKey) ?? [];

    // Add this date if not already present
    if (!pendingDates.contains(date)) {
      pendingDates.add(date);
      await prefs.setStringList(_pendingInvalidRecordsKey, pendingDates);
      logger.i('Invalid record notification queued for date: $date');
    }
  }

  /// Show a grouped invalid record notification
  /// This sends a single notification for all queued invalid records
  Future<void> showGroupedInvalidRecordNotifications() async {
    await initialize();

    // Check if notifications and invalid record alerts are enabled
    final notificationsEnabled = await areNotificationsEnabled();
    final invalidRecordAlertsEnabled = await areInvalidRecordAlertsEnabled();

    if (!notificationsEnabled || !invalidRecordAlertsEnabled) {
      return;
    }

    // Get and clear pending notifications
    final prefs = await SharedPreferences.getInstance();
    final pendingDates = prefs.getStringList(_pendingInvalidRecordsKey) ?? [];

    if (pendingDates.isEmpty) {
      return;
    }

    // Clear the pending list
    await prefs.setStringList(_pendingInvalidRecordsKey, []);

    // Sort the dates for consistent display
    pendingDates.sort();

    // Create the consolidated message
    String message = 'You have invalid entries.\n';
    message += pendingDates.join(', ');
    message +=
        '\nPlease check the History screen or click on the invalid entry indicator.';

    final title = 'Invalid Entries Detected';

    // Add to in-app notifications
    await addNotification(
      title: title,
      message: message,
      priority: NotificationPriority.warning,
      actionType: 'invalid_record',
    );

    // Show system notification
    await _notificationHelper.showNotification(
      id: invalidRecordNotificationId,
      title: title,
      body: message,
      payload: 'invalid_record',
    );
  }

  /// Show an invalid record notification (legacy method for backward compatibility)
  Future<void> showInvalidRecordNotification({
    required String message,
  }) async {
    await initialize();

    // Check if notifications and invalid record alerts are enabled
    final notificationsEnabled = await areNotificationsEnabled();
    final invalidRecordAlertsEnabled = await areInvalidRecordAlertsEnabled();

    if (!notificationsEnabled || !invalidRecordAlertsEnabled) {
      return;
    }

    final title = 'Invalid Entries Detected';

    // Add to in-app notifications
    await addNotification(
      title: title,
      message: message,
      priority: NotificationPriority.warning,
      actionType: 'invalid_record',
    );

    // Show system notification
    await _notificationHelper.showNotification(
      id: invalidRecordNotificationId,
      title: title,
      body: message,
      payload: 'invalid_record',
    );

    logger.i('Invalid record notification shown');
  }

  /// Check if meter reading reminders are enabled
  Future<bool> areMeterReadingRemindersEnabled() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_meterReadingReminderEnabledKey) ?? false;
  }

  /// Enable or disable meter reading reminders
  Future<void> setMeterReadingRemindersEnabled(bool enabled) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_meterReadingReminderEnabledKey, enabled);

    if (!enabled) {
      await _notificationHelper
          .cancelNotification(meterReadingReminderNotificationId);
    } else {
      // Schedule the next reminder based on frequency
      final frequency = await getMeterReadingReminderFrequency();
      await scheduleMeterReadingReminder(frequency);
    }
  }

  /// Check if frequency options are enabled
  Future<bool> isFrequencyOptionsEnabled() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_meterReadingReminderFrequencyEnabledKey) ?? true;
  }

  /// Enable or disable frequency options
  Future<void> setFrequencyOptionsEnabled(bool enabled) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_meterReadingReminderFrequencyEnabledKey, enabled);
  }

  /// Get the meter reading reminder frequency in days
  Future<int> getMeterReadingReminderFrequency() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getInt(_meterReadingReminderFrequencyKey) ??
        7; // Default to weekly
  }

  /// Set the meter reading reminder frequency in days
  Future<void> setMeterReadingReminderFrequency(int days) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(_meterReadingReminderFrequencyKey, days);

    // Reschedule the reminder with the new frequency if enabled
    final enabled = await areMeterReadingRemindersEnabled();
    if (enabled) {
      await scheduleMeterReadingReminder(days);
    }
  }

  /// Get the last date a meter reading reminder was shown
  Future<DateTime?> getLastMeterReadingReminderDate() async {
    final prefs = await SharedPreferences.getInstance();
    final dateString = prefs.getString(_meterReadingReminderLastDateKey);
    return dateString != null ? DateTime.parse(dateString) : null;
  }

  /// Set the last date a meter reading reminder was shown
  Future<void> setLastMeterReadingReminderDate(DateTime date) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(
        _meterReadingReminderLastDateKey, date.toIso8601String());
  }

  /// Get the time of day for meter reading reminders
  Future<ReminderTimeModel> getMeterReadingReminderTime() async {
    final prefs = await SharedPreferences.getInstance();
    final timeString = prefs.getString(_meterReadingReminderTimeKey);

    if (timeString != null) {
      return ReminderTimeModel.fromString(timeString);
    }

    // Default to 7:00 PM if not set
    return ReminderTimeModel(
      timeOfDay: const TimeOfDay(hour: 19, minute: 0),
    );
  }

  /// Set the time of day for meter reading reminders
  Future<void> setMeterReadingReminderTime(ReminderTimeModel time) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_meterReadingReminderTimeKey, time.toTimeString());

    // Reschedule the reminder with the new time if enabled
    final enabled = await areMeterReadingRemindersEnabled();
    if (enabled) {
      final frequency = await getMeterReadingReminderFrequency();
      await scheduleMeterReadingReminder(frequency);
    }
  }

  /// Schedule a meter reading reminder based on frequency with enhanced reliability
  Future<bool> scheduleMeterReadingReminder(int frequencyDays) async {
    await initialize();

    // Check if notifications and meter reading reminders are enabled
    final notificationsEnabled = await areNotificationsEnabled();
    final remindersEnabled = await areMeterReadingRemindersEnabled();

    if (!notificationsEnabled || !remindersEnabled) {
      logger.i(
          'Skipping meter reading reminder scheduling: notifications enabled: $notificationsEnabled, reminders enabled: $remindersEnabled');
      return false;
    }

    // Check notification permissions
    final permissionsGranted =
        await _notificationHelper.checkNotificationPermissions();
    if (!permissionsGranted) {
      logger.w(
          'Cannot schedule meter reading reminder: notification permissions not granted');
      return false;
    }

    // Cancel any existing reminder
    await _notificationHelper
        .cancelNotification(meterReadingReminderNotificationId);

    // Get the preferred time of day for reminders
    final reminderTime = await getMeterReadingReminderTime();

    // Calculate the next reminder date
    final now = DateTime.now();
    DateTime baseDate;

    // If a specific date is selected and frequency is not daily, use that date
    if (reminderTime.selectedDate != null && frequencyDays > 1) {
      // Use the selected date from the reminder time model
      baseDate = DateTime(
        reminderTime.selectedDate!.year,
        reminderTime.selectedDate!.month,
        reminderTime.selectedDate!.day,
        reminderTime.timeOfDay.hour,
        reminderTime.timeOfDay.minute,
      );

      // If the selected date is in the past, add the frequency to it until it's in the future
      while (baseDate.isBefore(now)) {
        baseDate = baseDate.add(Duration(days: frequencyDays));
      }
    } else {
      // Use the standard calculation based on current date + frequency
      baseDate = now.add(Duration(days: frequencyDays));

      // Create a DateTime with the preferred time of day
      baseDate = DateTime(
        baseDate.year,
        baseDate.month,
        baseDate.day,
        reminderTime.timeOfDay.hour,
        reminderTime.timeOfDay.minute,
      );

      // If the calculated time is in the past, add one more day
      if (baseDate.isBefore(now)) {
        baseDate = baseDate.add(const Duration(days: 1));
      }
    }

    final finalReminderDate = baseDate;

    // Update the last reminder date
    await setLastMeterReadingReminderDate(now);

    // Check if time zone has changed
    final timeZoneChanged = await _notificationHelper.hasTimeZoneChanged();
    if (timeZoneChanged) {
      logger.i('Time zone has changed, adjusting reminder time');
    }

    // Schedule the notification with enhanced reliability
    final success = await _notificationHelper.scheduleNotification(
      id: meterReadingReminderNotificationId,
      title: 'Meter Reading Reminder',
      body:
          'It\'s time to record your meter reading. This helps track your usage accurately.',
      scheduledDate: finalReminderDate,
      payload: 'meter_reading_reminder:$frequencyDays',
    );

    if (success) {
      logger.i(
          'Successfully scheduled meter reading reminder for ${finalReminderDate.toString()}');

      // Save the next reminder date for UI display
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(
          'next_meter_reminder_date', finalReminderDate.millisecondsSinceEpoch);

      return true;
    } else {
      logger.e(
          'Failed to schedule meter reading reminder for ${finalReminderDate.toString()}');
      return false;
    }
  }

  /// Get the next scheduled meter reading reminder date
  Future<DateTime?> getNextMeterReadingReminderDate() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final int? timestamp = prefs.getInt('next_meter_reminder_date');

      if (timestamp != null) {
        return DateTime.fromMillisecondsSinceEpoch(timestamp);
      }

      return null;
    } catch (e) {
      logger.e('Error getting next meter reading reminder date',
          details: e.toString());
      return null;
    }
  }

  /// Show a meter reading reminder notification immediately
  Future<void> showMeterReadingReminderNotification() async {
    await initialize();

    // Check if notifications and meter reading reminders are enabled
    final notificationsEnabled = await areNotificationsEnabled();
    final remindersEnabled = await areMeterReadingRemindersEnabled();

    if (!notificationsEnabled || !remindersEnabled) {
      return;
    }

    const title = 'Meter Reading Reminder';
    const message =
        'It\'s time to record your meter reading. This helps track your usage accurately.';

    // Add to in-app notifications
    await addNotification(
      title: title,
      message: message,
      priority: NotificationPriority.info,
      actionType: 'meter_reading_reminder',
    );

    // Show system notification
    await _notificationHelper.showNotification(
      id: meterReadingReminderNotificationId,
      title: title,
      body: message,
      payload: 'meter_reading_reminder',
    );

    // Update the last reminder date
    await setLastMeterReadingReminderDate(DateTime.now());

    // Schedule the next reminder
    final frequency = await getMeterReadingReminderFrequency();
    await scheduleMeterReadingReminder(frequency);
  }

  /// Check if welcome notification has been shown
  Future<bool> hasWelcomeNotificationBeenShown() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_welcomeNotificationShownKey) ?? false;
  }

  /// Mark welcome notification as shown
  Future<void> setWelcomeNotificationShown() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_welcomeNotificationShownKey, true);
  }

  /// Show welcome notification for first-time users
  Future<void> showWelcomeNotification() async {
    await initialize();

    // Check if welcome notification has already been shown
    final hasBeenShown = await hasWelcomeNotificationBeenShown();
    if (hasBeenShown) {
      return;
    }

    // Check if notifications are enabled
    final notificationsEnabled = await areNotificationsEnabled();
    if (!notificationsEnabled) {
      return;
    }

    const title = 'Welcome to Lekky';
    const message =
        'Hi, thanks for using Lekky. Your notification messages can be found here for Top Up reminders etc.';

    // Add to in-app notifications
    await addNotification(
      title: title,
      message: message,
      priority: NotificationPriority.info,
      actionType: 'welcome',
    );

    // Show system notification
    await _notificationHelper.showNotification(
      id: welcomeNotificationId,
      title: title,
      body: message,
      payload: 'welcome',
    );

    // Mark welcome notification as shown
    await setWelcomeNotificationShown();

    logger.i('Welcome notification shown to first-time user');
  }

  /// Check if first meter reading reminder has been shown
  Future<bool> hasFirstMeterReminderBeenShown() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_firstMeterReminderShownKey) ?? false;
  }

  /// Mark first meter reading reminder as shown
  Future<void> setFirstMeterReminderShown() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_firstMeterReminderShownKey, true);
  }

  /// Show first meter reading reminder for new users
  Future<void> showFirstMeterReadingReminder() async {
    await initialize();

    // Check if first meter reminder has already been shown
    final hasBeenShown = await hasFirstMeterReminderBeenShown();
    if (hasBeenShown) {
      return;
    }

    // Check if notifications are enabled
    final notificationsEnabled = await areNotificationsEnabled();
    if (!notificationsEnabled) {
      return;
    }

    const title = 'Enter Your First Meter Reading';
    const message =
        'Enter your first meter reading to start tracking your usage and receive helpful alerts.';

    // Add to in-app notifications
    await addNotification(
      title: title,
      message: message,
      priority: NotificationPriority.info,
      actionType: 'first_meter_reading',
    );

    // Show system notification
    await _notificationHelper.showNotification(
      id: firstMeterReadingReminderNotificationId,
      title: title,
      body: message,
      payload: 'first_meter_reading',
    );

    // Mark first meter reminder as shown
    await setFirstMeterReminderShown();

    logger.i('First meter reading reminder shown to new user');
  }

  /// Check if notification system has been activated
  Future<bool> isNotificationSystemActivated() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_notificationSystemActivatedKey) ?? false;
  }

  /// Mark notification system as activated
  Future<void> setNotificationSystemActivated() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_notificationSystemActivatedKey, true);
  }

  /// Show notification system activation message
  Future<void> showNotificationSystemActivation() async {
    await initialize();

    // Check if notification system has already been activated
    final isActivated = await isNotificationSystemActivated();
    if (isActivated) {
      return;
    }

    // Check if notifications are enabled
    final notificationsEnabled = await areNotificationsEnabled();
    if (!notificationsEnabled) {
      return;
    }

    const title = 'Low Balance Alerts Activated';
    const message =
        'Low balance alerts are now active. You\'ll be notified when your balance falls below your alert threshold.';

    // Add to in-app notifications
    await addNotification(
      title: title,
      message: message,
      priority: NotificationPriority.info,
      actionType: 'system_activation',
    );

    // Show system notification
    await _notificationHelper.showNotification(
      id: notificationSystemActivationId,
      title: title,
      body: message,
      payload: 'system_activation',
    );

    // Mark notification system as activated
    await setNotificationSystemActivated();

    logger.i('Notification system activation message shown');
  }

  /// Set flag indicating user has meter readings
  Future<void> setHasMeterReadings(bool hasReadings) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_hasAnyMeterReadingsKey, hasReadings);

    // If this is the first meter reading, activate the notification system
    if (hasReadings) {
      final isActivated = await isNotificationSystemActivated();
      if (!isActivated) {
        await showNotificationSystemActivation();
      }
    }
  }

  /// Check if user has any meter readings
  Future<bool> hasMeterReadings() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_hasAnyMeterReadingsKey) ?? false;
  }

  /// Schedule first meter reading reminder for 24 hours after setup
  Future<void> scheduleFirstMeterReadingReminder() async {
    await initialize();

    // Check if first meter reminder has already been shown
    final hasBeenShown = await hasFirstMeterReminderBeenShown();
    if (hasBeenShown) {
      return;
    }

    // Check if notifications are enabled
    final notificationsEnabled = await areNotificationsEnabled();
    if (!notificationsEnabled) {
      return;
    }

    // Cancel any existing reminder
    await _notificationHelper
        .cancelNotification(firstMeterReadingReminderNotificationId);

    // Schedule for 24 hours from now
    final scheduledDate = DateTime.now().add(const Duration(hours: 24));

    // Schedule the notification
    await _notificationHelper.scheduleNotification(
      id: firstMeterReadingReminderNotificationId,
      title: 'Enter Your First Meter Reading',
      body:
          'Enter your first meter reading to start tracking your usage and receive helpful alerts.',
      scheduledDate: scheduledDate,
      payload: 'first_meter_reading',
    );

    logger.i(
        'First meter reading reminder scheduled for ${scheduledDate.toString()}');
  }

  /// Show a "Check your meter now" notification when projected balance may be below threshold
  Future<void> showCheckMeterNowNotification({
    required String meterUnit,
    required double projectedBalance,
    required double actualBalance,
    required double threshold,
    required double daysSinceLastReading,
    required double averageUsage,
  }) async {
    await initialize();

    // Check if notifications and low balance alerts are enabled
    final notificationsEnabled = await areNotificationsEnabled();
    final lowBalanceAlertsEnabled = await areLowBalanceAlertsEnabled();

    if (!notificationsEnabled || !lowBalanceAlertsEnabled) {
      return;
    }

    const title = 'Check Your Meter Now';
    // Format days since last reading for display
    final String daysText;
    final roundedDays = (daysSinceLastReading * 10).round() / 10;

    if (roundedDays == 1.0) {
      daysText = '1 day';
    } else if (roundedDays == roundedDays.floor().toDouble()) {
      // If it's a whole number, don't show decimal
      daysText = '${roundedDays.toInt()} days';
    } else {
      // Show one decimal place
      daysText = '${roundedDays.toStringAsFixed(1)} days';
    }

    final message =
        'Based on your average usage of $meterUnit${averageUsage.toStringAsFixed(2)}/day, '
        'your balance may have fallen below your alert threshold of $meterUnit${threshold.toStringAsFixed(2)}. '
        'It\'s been $daysText since your last reading. '
        'Your last recorded balance was $meterUnit${actualBalance.toStringAsFixed(2)}, '
        'but your projected balance is now $meterUnit${projectedBalance.toStringAsFixed(2)}.';

    // Add to in-app notifications
    await addNotification(
      title: title,
      message: message,
      priority: NotificationPriority.warning,
      actionType: 'check_meter',
    );

    // Show system notification
    await _notificationHelper.showNotification(
      id: checkMeterNowNotificationId,
      title: title,
      body: message,
      payload:
          'check_meter:$projectedBalance:$threshold:${daysSinceLastReading.toStringAsFixed(2)}',
    );

    logger.i('Check meter now notification shown');
  }
}
