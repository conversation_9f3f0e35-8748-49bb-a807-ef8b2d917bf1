// File: lib/features/cost/presentation/widgets/cost_period_selector.dart
import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/widgets/app_card.dart';
import '../../domain/models/cost_period.dart';

/// A widget for selecting the cost period
class CostPeriodSelector extends StatelessWidget {
  final CostPeriod selectedPeriod;
  final Function(CostPeriod) onPeriodChanged;

  const CostPeriodSelector({
    super.key,
    required this.selectedPeriod,
    required this.onPeriodChanged,
  });

  @override
  Widget build(BuildContext context) {
    return AppCard(
      padding: const EdgeInsets.all(8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          Flexible(
            child: _buildPeriodButton(
              context,
              CostPeriod.day,
              'Day',
            ),
          ),
          Flexible(
            child: _buildPeriodButton(
              context,
              CostPeriod.week,
              'Week',
            ),
          ),
          Flexible(
            child: _buildPeriodButton(
              context,
              CostPeriod.month,
              'Month',
            ),
          ),
          Flexible(
            child: _buildPeriodButton(
              context,
              CostPeriod.year,
              'Year',
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPeriodButton(
    BuildContext context,
    CostPeriod period,
    String label,
  ) {
    final isSelected = period == selectedPeriod;
    return ElevatedButton(
      onPressed: () => onPeriodChanged(period),
      style: ElevatedButton.styleFrom(
        backgroundColor:
            isSelected ? AppColors.primary : AppColors.surfaceVariant,
        foregroundColor: isSelected ? Colors.white : AppColors.onSurfaceVariant,
        elevation: isSelected ? 2 : 0,
        padding: const EdgeInsets.symmetric(
          horizontal: 8,
          vertical: 12,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      child: Text(label),
    );
  }
}
