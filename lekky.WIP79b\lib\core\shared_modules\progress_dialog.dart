// File: lib/core/shared_modules/progress_dialog.dart
import 'package:flutter/material.dart';

/// A dialog that shows progress during long operations
class ProgressDialog {
  final BuildContext context;
  final String title;
  final String message;
  final bool isDismissible;
  
  bool _isShowing = false;
  double _progress = 0.0;
  late BuildContext _dialogContext;
  
  ProgressDialog({
    required this.context,
    required this.title,
    required this.message,
    this.isDismissible = false,
  });
  
  /// Show the progress dialog
  void show() {
    if (_isShowing) {
      return;
    }
    
    _isShowing = true;
    _progress = 0.0;
    
    showDialog(
      context: context,
      barrierDismissible: isDismissible,
      builder: (BuildContext context) {
        _dialogContext = context;
        return _buildDialog();
      },
    );
  }
  
  /// Update the progress value (0.0 to 1.0)
  void updateProgress(double progress) {
    if (!_isShowing) {
      return;
    }
    
    _progress = progress.clamp(0.0, 1.0);
    
    // Rebuild the dialog with the new progress
    if (context.mounted) {
      Navigator.of(_dialogContext).pop();
      showDialog(
        context: context,
        barrierDismissible: isDismissible,
        builder: (BuildContext context) {
          _dialogContext = context;
          return _buildDialog();
        },
      );
    }
  }
  
  /// Dismiss the progress dialog
  void dismiss() {
    if (!_isShowing) {
      return;
    }
    
    _isShowing = false;
    
    if (context.mounted) {
      Navigator.of(_dialogContext).pop();
    }
  }
  
  /// Build the dialog widget
  Widget _buildDialog() {
    return AlertDialog(
      title: Text(title),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(message),
          const SizedBox(height: 16),
          LinearProgressIndicator(
            value: _progress,
            backgroundColor: Colors.grey[300],
            valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
          ),
          const SizedBox(height: 8),
          Text('${(_progress * 100).toInt()}%'),
        ],
      ),
      actions: isDismissible ? [
        TextButton(
          onPressed: dismiss,
          child: const Text('Cancel'),
        ),
      ] : null,
    );
  }
}
