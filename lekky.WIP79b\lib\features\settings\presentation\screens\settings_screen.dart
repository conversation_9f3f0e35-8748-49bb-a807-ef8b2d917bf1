// File: lib/features/settings/presentation/screens/settings_screen.dart
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/data/database/db_helper.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../../../../core/l10n/supported_locales.dart';
import '../../../../core/providers/localization_provider.dart';
import '../../../../core/shared_modules/settings_provider.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/utils/event_bus.dart';
import '../../../../core/utils/helpful_messages.dart';
import '../../../../core/utils/logger.dart';
import '../../../../core/widgets/dialogs/confirmation_dialog.dart';
import '../../../../core/widgets/message_banner.dart';
import '../../../../core/widgets/dialogs/progress_dialog.dart';
import '../../../../core/providers/theme_provider.dart';
import '../../../../core/shared_modules/shared_modules.dart' as shared;
import '../../data/paypal_service.dart';
import '../controllers/settings_controller.dart';
import '../widgets/expandable_settings_section.dart';
import '../widgets/notification_settings.dart';
import '../widgets/meter_reading_reminder_settings.dart';
import '../widgets/tips_tricks_dialog.dart';
import '../../../../main.dart' show routeObserver;

/// The settings screen of the app
class SettingsScreen extends StatelessWidget {
  final ValueNotifier<int>? tabIndexNotifier;
  final int? settingsTabIndex;

  const SettingsScreen({
    super.key,
    this.tabIndexNotifier,
    this.settingsTabIndex,
  });

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => SettingsController(
        settingsProvider: Provider.of<SettingsProvider>(context, listen: false),
      ),
      child: _SettingsScreenContent(
        tabIndexNotifier: tabIndexNotifier,
        settingsTabIndex: settingsTabIndex,
      ),
    );
  }
}

class _SettingsScreenContent extends StatefulWidget {
  final ValueNotifier<int>? tabIndexNotifier;
  final int? settingsTabIndex;

  const _SettingsScreenContent({
    this.tabIndexNotifier,
    this.settingsTabIndex,
  });

  @override
  State<_SettingsScreenContent> createState() => _SettingsScreenContentState();
}

class _SettingsScreenContentState extends State<_SettingsScreenContent>
    with RouteAware {
  // Logger for this class
  final logger = Logger();

  // Index for the current message to display
  int _currentMessageIndex = 0;

  // Event subscription
  StreamSubscription? _eventSubscription;

  // Persistent state variables for all toggle switches
  // Region settings
  bool _isCurrencyExpanded = false;
  bool _isLanguageExpanded = false;

  // Date settings
  bool _isDateFormatExpanded = false;
  bool _isDateInfoExpanded = false;

  // Appearance settings

  // Alerts & Notifications settings
  bool _isAlertThresholdExpanded = false;
  bool _isDaysInAdvanceExpanded = false;
  bool _isNotificationsExpanded = false;
  bool _isReadingRemindersExpanded = false;

  // About settings

  // Controllers for parent menus
  final _regionController = SettingsExpansionTileController();
  final _dateSettingsController = SettingsExpansionTileController();
  final _appearanceController = SettingsExpansionTileController();
  final _alertsNotificationsController = SettingsExpansionTileController();
  final _dataBackupController = SettingsExpansionTileController();
  final _aboutHelpController = SettingsExpansionTileController();
  final _donateController = SettingsExpansionTileController();
  final _testingController = SettingsExpansionTileController();

  @override
  void initState() {
    super.initState();

    // Ensure all menus are closed when opening the Settings screen
    _resetMenuStates();

    // Add listener to tab index changes if available
    if (widget.tabIndexNotifier != null) {
      widget.tabIndexNotifier!.addListener(_onTabChanged);
    }

    // Force reload settings when the screen is first shown
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        final controller =
            Provider.of<SettingsController>(context, listen: false);
        controller.reloadSettings();
      }
    });

    // Subscribe to EventBus events with more specific handling
    _eventSubscription = EventBus().stream.listen((event) {
      if (mounted) {
        switch (event) {
          case EventType.alertSettingsUpdated:
            // When alert settings are updated, only ensure the Alerts & Notifications menu stays expanded
            // Do NOT automatically expand the Reminders submenu
            logger.i(
                'Received alertSettingsUpdated event, keeping Alerts & Notifications menu expanded');
            setState(() {
              // Ensure the Alerts & Notifications menu stays expanded
              _alertsNotificationsController.expand();
              // Do not modify _isReadingRemindersExpanded here
            });
            break;

          case EventType.reminderSettingsUpdated:
            // When reminder settings are updated, check if we should expand the menus
            logger.i('Received reminderSettingsUpdated event');

            // Check if another parent menu is currently expanded
            // If so, respect the user's navigation choice and don't expand Alerts & Notifications
            bool shouldExpandAlertsMenu = true;

            // Check if any other parent menu is expanded
            if (_regionController.isExpanded ||
                _dateSettingsController.isExpanded ||
                _appearanceController.isExpanded ||
                _dataBackupController.isExpanded ||
                _aboutHelpController.isExpanded ||
                _donateController.isExpanded ||
                _testingController.isExpanded) {
              // Another parent menu is expanded, so don't expand Alerts & Notifications
              shouldExpandAlertsMenu = false;
              logger.i(
                  'Another parent menu is expanded, respecting user navigation');
            }

            if (shouldExpandAlertsMenu && mounted) {
              setState(() {
                // Only expand the Alerts & Notifications menu if no other parent menu is expanded
                _alertsNotificationsController.expand();

                // Only expand the Reading Reminders submenu if it was previously expanded
                // This preserves the user's submenu state
                logger.i('Expanding Alerts & Notifications menu');
              });
            }
            break;

          case EventType.dateSettingsUpdated:
            // When date settings are updated, ensure the Date Settings menu stays expanded
            logger.i(
                'Received dateSettingsUpdated event, keeping Date Settings menu expanded');
            setState(() {
              // Ensure the Date Settings menu stays expanded
              _dateSettingsController.expand();

              // Keep the appropriate date submenu expanded based on which one was open
              if (_isDateFormatExpanded || _isDateInfoExpanded) {
                // Keep the current state of the submenus
              } else {
                // If neither submenu was open, default to expanding the Date Format submenu
                _isDateFormatExpanded = true;
              }

              // No longer explicitly controlling meter reminders branch
            });
            break;

          case EventType.dataBackupMenuRequested:
            // When data backup menu is requested, ensure it's expanded
            logger.i(
                'Received dataBackupMenuRequested event, opening Data Backup menu');
            setState(() {
              // Close all other parent menus
              _closeAllParentMenusExcept('DataBackup');

              // Expand the Data Backup menu
              _dataBackupController.expand();
            });
            break;

          case EventType.settingsUpdated:
            // For generic settings updates, don't automatically expand any menus
            // This prevents unexpected menu expansions
            logger.i(
                'Received generic settingsUpdated event, not changing menu state');
            break;

          default:
            // No action needed for other event types
            break;
        }
      }
    });

    // Rotate messages every 10 seconds
    _startMessageRotation();
  }

  // Called when the tab index changes
  void _onTabChanged() {
    // If this is the Settings tab and it's becoming active
    if (widget.tabIndexNotifier != null &&
        widget.settingsTabIndex != null &&
        widget.tabIndexNotifier!.value == widget.settingsTabIndex &&
        mounted) {
      // Get the controller to reload settings
      final controller =
          Provider.of<SettingsController>(context, listen: false);

      // Force reload settings and ensure UI updates
      controller.reloadSettings().then((_) {
        // After settings are reloaded, force a UI refresh
        if (mounted) {
          setState(() {
            // Reset menu states and ensure UI reflects latest values
            _resetMenuStates();
          });
        }
      });
    }
  }

  // Resets all menu states by closing submenus and collapsing parent menus
  void _resetMenuStates() {
    // Close all submenus
    _closeAllMenus();

    // Collapse all parent menus
    _regionController.collapse();
    _dateSettingsController.collapse();
    _appearanceController.collapse();
    _alertsNotificationsController.collapse();
    _dataBackupController.collapse();
    _aboutHelpController.collapse();
    _donateController.collapse();
    _testingController.collapse();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // Subscribe to the RouteObserver
    final ModalRoute<dynamic>? modalRoute = ModalRoute.of(context);
    if (modalRoute != null) {
      routeObserver.subscribe(this, modalRoute);
    }
  }

  @override
  void didPopNext() {
    // Called when the screen becomes visible again (e.g., after popping another screen)
    if (mounted) {
      // Get the controller to reload settings
      final controller =
          Provider.of<SettingsController>(context, listen: false);

      // Force reload settings and ensure UI updates
      controller.reloadSettings().then((_) {
        // After settings are reloaded, force a UI refresh
        if (mounted) {
          setState(() {
            // Reset menu states and ensure UI reflects latest values
            _resetMenuStates();
          });
        }
      });
    }
  }

  // Method to close all menus and submenus
  void _closeAllMenus() {
    if (mounted) {
      setState(() {
        _isCurrencyExpanded = false;
        _isLanguageExpanded = false;
        _isDateFormatExpanded = false;
        _isDateInfoExpanded = false;
        _isAlertThresholdExpanded = false;
        _isDaysInAdvanceExpanded = false;
        _isNotificationsExpanded = false;
        _isReadingRemindersExpanded = false;
      });
    } else {
      // If not mounted, just update the variables without setState
      _isCurrencyExpanded = false;
      _isLanguageExpanded = false;
      _isDateFormatExpanded = false;
      _isDateInfoExpanded = false;
      _isAlertThresholdExpanded = false;
      _isDaysInAdvanceExpanded = false;
      _isNotificationsExpanded = false;
      _isReadingRemindersExpanded = false;
    }
  }

  // Method to close all parent menus except the one specified
  void _closeAllParentMenusExcept(String exceptMenu) {
    if (mounted) {
      setState(() {
        // Close all parent menus except the specified one
        if (exceptMenu != 'Region') {
          _regionController.collapse();
          _isCurrencyExpanded = false;
          _isLanguageExpanded = false;
        }

        if (exceptMenu != 'DateSettings') {
          _dateSettingsController.collapse();
          _isDateFormatExpanded = false;
          _isDateInfoExpanded = false;
        }

        if (exceptMenu != 'Appearance') {
          _appearanceController.collapse();
        }

        if (exceptMenu != 'AlertsNotifications') {
          _alertsNotificationsController.collapse();
          _isAlertThresholdExpanded = false;
          _isDaysInAdvanceExpanded = false;
          _isNotificationsExpanded = false;
          _isReadingRemindersExpanded = false;
        }

        if (exceptMenu != 'DataBackup') {
          _dataBackupController.collapse();
        }

        if (exceptMenu != 'AboutHelp') {
          _aboutHelpController.collapse();
        }

        if (exceptMenu != 'Donate') {
          _donateController.collapse();
        }

        if (exceptMenu != 'Testing') {
          _testingController.collapse();
        }
      });
    }
  }

  // Method to close all submenus within a parent menu except the one specified
  void _closeAllSubmenusExcept(String parentMenu, String exceptSubmenu) {
    if (mounted) {
      setState(() {
        // Close all submenus within the parent menu except the specified one
        if (parentMenu == 'Region') {
          if (exceptSubmenu != 'Currency') {
            _isCurrencyExpanded = false;
          }
          if (exceptSubmenu != 'Language') {
            _isLanguageExpanded = false;
          }
        } else if (parentMenu == 'DateSettings') {
          if (exceptSubmenu != 'DateFormat') {
            _isDateFormatExpanded = false;
          }
          if (exceptSubmenu != 'DateInfo') {
            _isDateInfoExpanded = false;
          }
        } else if (parentMenu == 'AlertsNotifications') {
          if (exceptSubmenu != 'AlertThreshold') {
            _isAlertThresholdExpanded = false;
          }
          if (exceptSubmenu != 'DaysInAdvance') {
            _isDaysInAdvanceExpanded = false;
          }
          if (exceptSubmenu != 'Notifications') {
            _isNotificationsExpanded = false;
          }
          if (exceptSubmenu != 'ReadingReminders') {
            _isReadingRemindersExpanded = false;
          }
        }
      });
    }
  }

  @override
  void dispose() {
    // Reset all menu states
    _resetMenuStates();

    // Remove tab index listener if it was added
    if (widget.tabIndexNotifier != null) {
      widget.tabIndexNotifier!.removeListener(_onTabChanged);
    }

    // Cancel the event subscription
    _eventSubscription?.cancel();

    // Unsubscribe from the RouteObserver
    routeObserver.unsubscribe(this);

    super.dispose();
  }

  void _startMessageRotation() {
    // Rotate messages every 10 seconds
    Future.delayed(const Duration(seconds: 10), () {
      if (mounted) {
        setState(() {
          _currentMessageIndex =
              (_currentMessageIndex + 1) % HelpfulMessages.allMessages.length;
        });
        _startMessageRotation(); // Schedule the next rotation
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor:
          Colors.transparent, // Match Homepage transparent background
      body: _buildBody(context),
    );
  }

  // Build the message banner with rotating helpful messages
  Widget _buildMessageBanner() {
    return MessageBanner(
      message: HelpfulMessages.allMessages[_currentMessageIndex],
    );
  }

  // Build the body of the screen
  Widget _buildBody(BuildContext context) {
    return Consumer<SettingsController>(
      builder: (context, controller, _) {
        return RefreshIndicator(
          onRefresh: () async {
            // Refresh settings
            await controller.reloadSettings();
          },
          child: CustomScrollView(
            slivers: [
              _buildAppBar(),
              SliverPadding(
                padding: const EdgeInsets.all(16),
                sliver: SliverList(
                  delegate: SliverChildListDelegate([
                    // Always show content, don't check isLoading
                    _buildRegionSettings(controller),
                    _buildAlertsAndNotificationsSettings(controller),
                    _buildDateSettings(controller),
                    _buildAppearanceSettings(),
                    _buildDataSettings(controller),
                    _buildAboutAndHelpSection(),
                    _buildDonateSection(),
                    _buildTestingSection(controller),
                    if (controller.error.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 16),
                        child: Text(
                          controller.error,
                          style: AppTextStyles.bodyMedium.copyWith(
                            color: AppColors.error,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                  ]),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // Build the app bar with the message bar positioned directly below it
  Widget _buildAppBar() {
    return SliverAppBar(
      toolbarHeight: 96, // Fixed height of 96px to match Homepage
      pinned: true,
      automaticallyImplyLeading: false, // Remove back arrow
      // Use fixed height Stack instead of FlexibleSpaceBar
      flexibleSpace: SizedBox(
        height: 96, // Fixed height of 96px
        child: Stack(
          children: [
            // Background gradient
            Positioned.fill(
              child: Container(
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: AppColors.settingsGradient,
                  ),
                ),
              ),
            ),
            // Custom positioned title - matching Home screen style and position
            const Positioned(
              top: 20,
              left: 20, // Exactly 20px from left edge to match Home screen
              child: Text(
                'Settings', // Banner text
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 40, // Matching Home screen font size
                  fontWeight: FontWeight.bold,
                  shadows: [
                    Shadow(
                      offset: Offset(1.0, 1.0),
                      blurRadius: 3.0,
                      color: Color.fromARGB(128, 0, 0, 0),
                    ),
                  ],
                ),
              ),
            ),
            // Back arrow positioned in line with "Settings" text and above Region toggle
            Positioned(
              top: 28, // Aligned with the "Settings" text
              left: 274, // Position to the left above the Region toggle switch
              child: IconButton(
                icon: const Icon(
                  Icons.arrow_back,
                  color: Colors.white,
                  size: 48, // 200% larger than standard icons
                ),
                onPressed: () {
                  // Navigate back to the Homepage
                  Navigator.of(context)
                      .pushReplacementNamed(AppConstants.routeHome);
                },
                tooltip: 'Back to Home',
                padding: EdgeInsets
                    .zero, // Remove padding to ensure proper positioning
                constraints:
                    const BoxConstraints(), // Remove constraints for better positioning
              ),
            ),
          ],
        ),
      ),
      // Remove notification button
      // Position the message bar directly below the banner with no spacing
      bottom: PreferredSize(
        // Use zero height to eliminate any spacing
        preferredSize: const Size.fromHeight(0),
        child: _buildMessageBanner(),
      ),
    );
  }

  Widget _buildRegionSettings(SettingsController controller) {
    return StatefulBuilder(
      builder: (context, setStateLocal) {
        return ExpandableSettingsSection(
          key: const Key('region_section'),
          title: 'Region',
          icon: Icons.public,
          initiallyExpanded: false,
          controller: _regionController,
          onExpansionChanged: (expanded) {
            if (expanded) {
              // When Region section is expanded, close all other parent sections
              _closeAllParentMenusExcept('Region');
            }
          },
          children: [
            // Language (now first)
            StreamBuilder<String>(
              stream: controller.language,
              initialData: controller
                  .languageValue, // Use controller value instead of hardcoded default
              builder: (context, snapshot) {
                final currentLanguage =
                    snapshot.data ?? controller.languageValue;
                final languages = shared.LanguageSelector.getLanguages();
                final languageName = languages.firstWhere(
                    (l) => l['code'] == currentLanguage,
                    orElse: () => {'name': 'English'})['name'];

                return Column(
                  children: [
                    Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12.0, vertical: 0),
                      child: Row(
                        children: [
                          // Content area (title and subtitle)
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  AppLocalizations.of(context)!.language,
                                  style: const TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                Text(
                                  '${AppLocalizations.of(context)!.current}: $languageName',
                                  style: const TextStyle(
                                    fontSize: 12,
                                    color: Colors.grey,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          // Toggle switch for submenu
                          Transform.scale(
                            scale: 0.8, // Scale down the switch by 20%
                            child: Switch(
                              value: _isLanguageExpanded,
                              onChanged: (value) {
                                setState(() {
                                  if (value) {
                                    // When Language submenu is expanded, close all other submenus in Region
                                    _closeAllSubmenusExcept(
                                        'Region', 'Language');
                                  }
                                  _isLanguageExpanded = value;
                                });
                              },
                              activeColor:
                                  Theme.of(context).colorScheme.primary,
                              materialTapTargetSize:
                                  MaterialTapTargetSize.shrinkWrap,
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Show language options when expanded
                    if (_isLanguageExpanded) ...[
                      const Divider(height: 1, thickness: 0.5),
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: shared.LanguageSelector(
                          currentValue: currentLanguage,
                          onChanged: (value) => controller.setLanguage(value),
                          displayMode: shared.SettingsDisplayMode.compact,
                          showHelperText: true,
                          showTitle: false,
                        ),
                      ),
                    ],
                  ],
                );
              },
            ),

            const Divider(height: 1, thickness: 0.5),

            // Currency (now second)
            StreamBuilder<String>(
              stream: controller.meterUnit,
              initialData: controller
                  .meterUnitValue, // Use controller value instead of hardcoded default
              builder: (context, snapshot) {
                final currentCurrency =
                    snapshot.data ?? controller.meterUnitValue;
                return Column(
                  children: [
                    Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12.0, vertical: 0),
                      child: Row(
                        children: [
                          // Content area (title and subtitle)
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                const Text(
                                  'Currency',
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                Text(
                                  'Current: $currentCurrency',
                                  style: const TextStyle(
                                    fontSize: 12,
                                    color: Colors.grey,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          // Toggle switch for submenu
                          Transform.scale(
                            scale: 0.8, // Scale down the switch by 20%
                            child: Switch(
                              value: _isCurrencyExpanded,
                              onChanged: (value) {
                                setState(() {
                                  if (value) {
                                    // When Currency submenu is expanded, close all other submenus in Region
                                    _closeAllSubmenusExcept(
                                        'Region', 'Currency');
                                  }
                                  _isCurrencyExpanded = value;
                                });
                              },
                              activeColor:
                                  Theme.of(context).colorScheme.primary,
                              materialTapTargetSize:
                                  MaterialTapTargetSize.shrinkWrap,
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Show currency options when expanded
                    if (_isCurrencyExpanded) ...[
                      const Divider(height: 1, thickness: 0.5),
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: shared.RadioCurrencySelectorAdapter(
                          currentValue: controller.meterUnitValue,
                          onChanged: (value) => controller.setMeterUnit(value),
                          displayMode: shared.SettingsDisplayMode.compact,
                          showHelperText: true,
                          showTitle: false,
                        ),
                      ),
                    ],
                  ],
                );
              },
            ),
          ],
        );
      },
    );
  }

  Widget _buildDateSettings(SettingsController controller) {
    return StatefulBuilder(builder: (context, setStateLocal) {
      return ExpandableSettingsSection(
        key: const Key('date_settings_section'),
        title: 'Date Settings',
        icon: Icons.calendar_today,
        initiallyExpanded: false,
        controller: _dateSettingsController,
        onExpansionChanged: (expanded) {
          if (expanded) {
            // When Date Settings section is expanded, close all other parent sections
            _closeAllParentMenusExcept('DateSettings');
          }
        },
        children: [
          // Date Format
          StreamBuilder<String>(
            stream: controller.dateFormat,
            initialData: controller
                .dateFormatValue, // Use controller value instead of hardcoded default
            builder: (context, snapshot) {
              final currentFormat = snapshot.data ?? controller.dateFormatValue;
              return Column(
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 12.0, vertical: 0),
                    child: Row(
                      children: [
                        // Content area (title and subtitle)
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const Text(
                                'Date Format',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              Text(
                                'Current: $currentFormat',
                                style: const TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey,
                                ),
                              ),
                            ],
                          ),
                        ),
                        // Toggle switch for submenu
                        Transform.scale(
                          scale: 0.8, // Scale down the switch by 20%
                          child: Switch(
                            value: _isDateFormatExpanded,
                            onChanged: (value) {
                              setState(() {
                                if (value) {
                                  // When Date Format submenu is expanded, close all other submenus in Date Settings
                                  _closeAllSubmenusExcept(
                                      'DateSettings', 'DateFormat');
                                }
                                _isDateFormatExpanded = value;
                              });
                            },
                            activeColor: Theme.of(context).colorScheme.primary,
                            materialTapTargetSize:
                                MaterialTapTargetSize.shrinkWrap,
                          ),
                        ),
                      ],
                    ),
                  ),
                  // Show date format options when expanded
                  if (_isDateFormatExpanded) ...[
                    const Divider(height: 1, thickness: 0.5),
                    Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: shared.DateFormatSelector(
                        currentValue: currentFormat,
                        onChanged: (value) {
                          controller.setDateFormat(value);
                          // Keep the menu open after value changes
                          setState(() {
                            _isDateFormatExpanded = true;
                          });
                        },
                        displayMode: shared.SettingsDisplayMode.compact,
                        showHelperText: true,
                        showTitle: false,
                      ),
                    ),
                  ],
                ],
              );
            },
          ),
          const Divider(height: 1, thickness: 0.5),

          // Date Info
          StreamBuilder<String>(
            stream: controller.dateInfo,
            initialData: controller
                .dateInfoValue, // Use controller value instead of hardcoded default
            builder: (context, snapshot) {
              final currentDateInfo = snapshot.data ?? controller.dateInfoValue;
              return Column(
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 12.0, vertical: 0),
                    child: Row(
                      children: [
                        // Content area (title and subtitle)
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const Text(
                                'Date Info',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              Text(
                                'Current: $currentDateInfo',
                                style: const TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey,
                                ),
                              ),
                            ],
                          ),
                        ),
                        // Toggle switch for submenu
                        Transform.scale(
                          scale: 0.8, // Scale down the switch by 20%
                          child: Switch(
                            value: _isDateInfoExpanded,
                            onChanged: (value) {
                              setState(() {
                                if (value) {
                                  // When Date Info submenu is expanded, close all other submenus in Date Settings
                                  _closeAllSubmenusExcept(
                                      'DateSettings', 'DateInfo');
                                }
                                _isDateInfoExpanded = value;
                              });
                            },
                            activeColor: Theme.of(context).colorScheme.primary,
                            materialTapTargetSize:
                                MaterialTapTargetSize.shrinkWrap,
                          ),
                        ),
                      ],
                    ),
                  ),
                  // Show date info options when expanded
                  if (_isDateInfoExpanded) ...[
                    const Divider(height: 1, thickness: 0.5),
                    Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: shared.DateInfoSelector(
                        currentValue: currentDateInfo,
                        onChanged: (value) {
                          controller.setDateInfo(value);
                          // Keep the menu open after value changes
                          setState(() {
                            _isDateInfoExpanded = true;
                          });
                        },
                        displayMode: shared.SettingsDisplayMode.compact,
                        showHelperText: true,
                        showTitle: false,
                      ),
                    ),
                  ],
                ],
              );
            },
          ),
          const Divider(height: 1, thickness: 0.5),
        ],
      );
    });
  }

  Widget _buildDataSettings(SettingsController controller) {
    return ExpandableSettingsSection(
      key: const Key('data_backup_section'),
      title: 'Data Backup',
      icon: Icons.storage,
      initiallyExpanded: false,
      controller: _dataBackupController,
      onExpansionChanged: (expanded) {
        if (expanded) {
          // When Data Backup section is expanded, close all other parent sections
          _closeAllParentMenusExcept('DataBackup');
        }
      },
      children: [
        ListTile(
          leading: const Icon(Icons.file_upload, color: Colors.blue),
          title: const Text('Export Data'),
          subtitle: const Text('Export your data to a CSV file'),
          trailing: const Icon(Icons.arrow_forward_ios, size: 12),
          dense: true,
          visualDensity: const VisualDensity(
              horizontal: -4, vertical: -4), // Further reduce height
          contentPadding: const EdgeInsets.symmetric(
              horizontal: 8.0, vertical: 0), // Reduced padding
          onTap: () => controller.exportData(context),
        ),
        const Divider(height: 1, thickness: 0.5),
        // Import Data with shared widget
        ListTile(
          leading: const Icon(Icons.file_download, color: Colors.green),
          title: const Text('Import Data'),
          subtitle: const Text('Import data from a CSV file'),
          trailing: const Icon(Icons.arrow_forward_ios, size: 12),
          dense: true,
          visualDensity: const VisualDensity(
              horizontal: -4, vertical: -4), // Further reduce height
          contentPadding: const EdgeInsets.symmetric(
              horizontal: 8.0, vertical: 0), // Reduced padding
          onTap: () => controller.importData(context),
        ),
        const Divider(height: 1, thickness: 0.5),
        ListTile(
          leading: const Icon(Icons.delete_forever, color: Colors.red),
          title: const Text('Clear All Data'),
          subtitle: const Text('Delete all your data'),
          trailing: const Icon(Icons.arrow_forward_ios, size: 12),
          dense: true,
          visualDensity: const VisualDensity(
              horizontal: -4, vertical: -4), // Further reduce height
          contentPadding: const EdgeInsets.symmetric(
              horizontal: 8.0, vertical: 0), // Reduced padding
          onTap: () => _showClearDataDialog(controller),
        ),
      ],
    );
  }

  Widget _buildTestingSection(SettingsController controller) {
    return ExpandableSettingsSection(
      key: const Key('testing_section'),
      title: 'For Testing Only',
      icon: Icons.bug_report,
      initiallyExpanded: false,
      controller: _testingController,
      onExpansionChanged: (expanded) {
        if (expanded) {
          // When Testing section is expanded, close all other parent sections
          _closeAllParentMenusExcept('Testing');
        }
      },
      children: [
        ListTile(
          leading: const Icon(Icons.app_registration, color: Colors.amber),
          title: const Text('Test Welcome & Setup'),
          subtitle:
              const Text('Backup data, reset app, and show welcome screen'),
          trailing: const Icon(Icons.arrow_forward_ios, size: 12),
          dense: true,
          visualDensity: const VisualDensity(
              horizontal: -4, vertical: -4), // Further reduce height
          contentPadding: const EdgeInsets.symmetric(
              horizontal: 8.0, vertical: 0), // Reduced padding
          onTap: () => _showTestWelcomeSetupDialog(controller),
        ),
        const Divider(height: 1, thickness: 0.5),
        ListTile(
          leading: const Icon(Icons.restart_alt, color: Colors.orange),
          title: const Text('Force Reset App'),
          subtitle: const Text('Direct reset for troubleshooting (no backup)'),
          trailing: const Icon(Icons.arrow_forward_ios, size: 12),
          dense: true,
          visualDensity: const VisualDensity(
              horizontal: -4, vertical: -4), // Further reduce height
          contentPadding: const EdgeInsets.symmetric(
              horizontal: 8.0, vertical: 0), // Reduced padding
          onTap: () => _showForceResetDialog(controller),
        ),
        const Divider(height: 1, thickness: 0.5),
        ListTile(
          leading: const Icon(Icons.warning_amber, color: Colors.deepOrange),
          title: const Text('Emergency Reset'),
          subtitle: const Text('Complete reset when nothing else works'),
          trailing: const Icon(Icons.arrow_forward_ios, size: 12),
          dense: true,
          visualDensity: const VisualDensity(
              horizontal: -4, vertical: -4), // Further reduce height
          contentPadding: const EdgeInsets.symmetric(
              horizontal: 8.0, vertical: 0), // Reduced padding
          onTap: () => _showEmergencyResetDialog(),
        ),
      ],
    );
  }

  Widget _buildAppearanceSettings() {
    final themeProvider = Provider.of<ThemeProvider>(context, listen: false);

    return ExpandableSettingsSection(
      key: const Key('appearance_section'),
      title: 'Appearance',
      icon: Icons.palette_outlined,
      initiallyExpanded: false,
      controller: _appearanceController,
      onExpansionChanged: (expanded) {
        if (expanded) {
          // When Appearance section is expanded, close all other parent sections
          _closeAllParentMenusExcept('Appearance');
        }
      },
      children: [
        // Show appearance options directly in the section
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: shared.AppearanceSelector(
            currentValue: themeProvider.themeMode,
            onChanged: (mode) {
              // Update theme mode
              final themeProvider =
                  Provider.of<ThemeProvider>(context, listen: false);
              themeProvider.setThemeMode(mode);
            },
            displayMode: shared.SettingsDisplayMode.compact,
            showHelperText: true,
            showTitle: false,
          ),
        ),
      ],
    );
  }

  Widget _buildAboutAndHelpSection() {
    return ExpandableSettingsSection(
      key: const Key('about_section'),
      title: 'About',
      icon: Icons.info_outline,
      initiallyExpanded: false,
      controller: _aboutHelpController,
      onExpansionChanged: (expanded) {
        if (expanded) {
          // When About section is expanded, close all other parent sections
          _closeAllParentMenusExcept('AboutHelp');
        }
      },
      children: [
        const ListTile(
          title: Text('Version'),
          subtitle: Text('1.0.1 beta'),
          dense: true,
          visualDensity: VisualDensity(
              horizontal: -4, vertical: -4), // Further reduce height
          contentPadding: EdgeInsets.symmetric(
              horizontal: 8.0, vertical: 0), // Reduced padding
        ),
        const Divider(height: 1, thickness: 0.5),
        ListTile(
          title: const Text('Upgrade'),
          subtitle: const Text('Premium features'),
          trailing: const Icon(Icons.arrow_forward_ios, size: 12),
          dense: true,
          visualDensity: const VisualDensity(
              horizontal: -4, vertical: -4), // Further reduce height
          contentPadding: const EdgeInsets.symmetric(
              horizontal: 8.0, vertical: 0), // Reduced padding
          onTap: () => _showUpgradeDialog(),
        ),
        const Divider(height: 1, thickness: 0.5),
        ListTile(
          title: const Text('Tips & Tricks'),
          subtitle: const Text('Learn how to get the most out of Lekky'),
          trailing: const Icon(Icons.arrow_forward_ios, size: 12),
          dense: true,
          visualDensity: const VisualDensity(
              horizontal: -4, vertical: -4), // Further reduce height
          contentPadding: const EdgeInsets.symmetric(
              horizontal: 8.0, vertical: 0), // Reduced padding
          onTap: () => _showTipsDialog(),
        ),
      ],
    );
  }

  Widget _buildDonateSection() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return ExpandableSettingsSection(
      key: const Key('donate_section'),
      title: 'Donate',
      icon: Icons.favorite,
      iconColor:
          isDarkMode ? AppColors.errorDark : AppColors.error, // Red heart icon
      initiallyExpanded: false,
      controller: _donateController,
      onExpansionChanged: (expanded) {
        if (expanded) {
          // When Donate section is expanded, close all other parent sections
          _closeAllParentMenusExcept('Donate');
        }
      },
      children: [
        ListTile(
          title: const Text('Support Lekky'),
          subtitle: const Text('Help us improve the app with a donation'),
          trailing: const Icon(Icons.arrow_forward_ios, size: 12),
          dense: true,
          visualDensity: const VisualDensity(
              horizontal: -4, vertical: -4), // Further reduce height
          contentPadding: const EdgeInsets.symmetric(
              horizontal: 8.0, vertical: 0), // Reduced padding
          onTap: () => _showDonateDialog(),
        ),
      ],
    );
  }

  Widget _buildAlertsAndNotificationsSettings(SettingsController controller) {
    return StatefulBuilder(
      builder: (context, setStateLocal) {
        return ExpandableSettingsSection(
          key: const Key('alerts_notifications_section'),
          title: 'Alerts & Notifications',
          icon: Icons.notifications,
          initiallyExpanded: false,
          controller: _alertsNotificationsController,
          onExpansionChanged: (expanded) {
            if (expanded) {
              // When Alerts & Notifications section is expanded, close all other parent sections
              _closeAllParentMenusExcept('AlertsNotifications');
            }
          },
          children: [
            // Alert Threshold
            StreamBuilder<double>(
              stream: controller.alertThreshold,
              initialData: controller
                  .alertThresholdValue, // Use controller value instead of hardcoded default
              builder: (context, snapshot) {
                // Force refresh when data changes
                final alertValue = snapshot.data?.toStringAsFixed(2) ??
                    controller.alertThresholdValue.toStringAsFixed(2);
                // Log the current value for debugging
                debugPrint('Alert Threshold StreamBuilder: $alertValue');

                return Column(
                  children: [
                    Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12.0, vertical: 0),
                      child: Row(
                        children: [
                          // Content area (title and subtitle)
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                const Text(
                                  'Alert Threshold',
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                Text(
                                  '${controller.meterUnitValue}$alertValue',
                                  style: const TextStyle(
                                    fontSize: 12,
                                    color: Colors.grey,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          // Toggle switch for submenu
                          Transform.scale(
                            scale: 0.8, // Scale down the switch by 20%
                            child: Switch(
                              value: _isAlertThresholdExpanded,
                              onChanged: (value) {
                                setState(() {
                                  if (value) {
                                    // When Alert Threshold submenu is expanded, close all other submenus in Alerts & Notifications
                                    _closeAllSubmenusExcept(
                                        'AlertsNotifications',
                                        'AlertThreshold');
                                  }
                                  _isAlertThresholdExpanded = value;
                                });
                              },
                              activeColor:
                                  Theme.of(context).colorScheme.primary,
                              materialTapTargetSize:
                                  MaterialTapTargetSize.shrinkWrap,
                            ),
                          ),
                        ],
                      ),
                    ),
                    // Show alert threshold options when expanded
                    if (_isAlertThresholdExpanded) ...[
                      const Divider(height: 1, thickness: 0.5),
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: shared.AlertThresholdSelector(
                          currentValue: double.tryParse(alertValue) ?? 5.0,
                          onChanged: (value) {
                            controller.setAlertThreshold(value);
                            // Keep the menu open after value changes
                            setState(() {
                              _isAlertThresholdExpanded = true;
                            });
                          },
                          currencySymbol: controller.meterUnitValue,
                          hasMeterReadings:
                              true, // Assume we have meter readings
                          displayMode: shared.SettingsDisplayMode.compact,
                          showHelperText: true,
                          showTitle: false,
                        ),
                      ),
                    ],
                  ],
                );
              },
            ),

            const Divider(height: 1, thickness: 0.5),

            // Days in Advance
            StreamBuilder<int>(
              stream: controller.daysInAdvance,
              initialData: controller
                  .daysInAdvanceValue, // Use controller value instead of hardcoded default
              builder: (context, snapshot) {
                // Force refresh when data changes
                final daysValue =
                    snapshot.data ?? controller.daysInAdvanceValue;
                // Log the current value for debugging
                debugPrint('Days in Advance StreamBuilder: $daysValue');

                return Column(
                  children: [
                    Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12.0, vertical: 0),
                      child: Row(
                        children: [
                          // Content area (title and subtitle)
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                const Text(
                                  'Days in Advance',
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                Text(
                                  '$daysValue days',
                                  style: const TextStyle(
                                    fontSize: 12,
                                    color: Colors.grey,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          // Toggle switch for submenu
                          Transform.scale(
                            scale: 0.8, // Scale down the switch by 20%
                            child: Switch(
                              value: _isDaysInAdvanceExpanded,
                              onChanged: (value) {
                                setState(() {
                                  if (value) {
                                    // When Days in Advance submenu is expanded, close all other submenus in Alerts & Notifications
                                    _closeAllSubmenusExcept(
                                        'AlertsNotifications', 'DaysInAdvance');
                                  }
                                  _isDaysInAdvanceExpanded = value;
                                });
                              },
                              activeColor:
                                  Theme.of(context).colorScheme.primary,
                              materialTapTargetSize:
                                  MaterialTapTargetSize.shrinkWrap,
                            ),
                          ),
                        ],
                      ),
                    ),
                    // Show days in advance options when expanded
                    if (_isDaysInAdvanceExpanded) ...[
                      const Divider(height: 1, thickness: 0.5),
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: shared.DaysInAdvanceSelector(
                          currentValue: daysValue,
                          onChanged: (value) {
                            controller.setDaysInAdvance(value);
                            // Keep the menu open after value changes
                            setState(() {
                              _isDaysInAdvanceExpanded = true;
                            });
                          },
                          hasTotalAverage: true, // Assume we have total average
                          displayMode: shared.SettingsDisplayMode.compact,
                          showHelperText: true,
                          showTitle: false,
                        ),
                      ),
                    ],
                  ],
                );
              },
            ),

            const Divider(height: 1, thickness: 0.5),

            // Notifications
            Padding(
              padding:
                  const EdgeInsets.symmetric(horizontal: 12.0, vertical: 0),
              child: Row(
                children: [
                  // Content area (title and subtitle)
                  const Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          'Notifications',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        Text(
                          'Notification settings',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey,
                          ),
                        ),
                      ],
                    ),
                  ),
                  // Toggle switch for submenu
                  Transform.scale(
                    scale: 0.8, // Scale down the switch by 20%
                    child: Switch(
                      value: _isNotificationsExpanded,
                      onChanged: (value) {
                        setState(() {
                          if (value) {
                            // When Notifications submenu is expanded, close all other submenus in Alerts & Notifications
                            _closeAllSubmenusExcept(
                                'AlertsNotifications', 'Notifications');
                          }
                          _isNotificationsExpanded = value;
                        });
                      },
                      activeColor: Theme.of(context).colorScheme.primary,
                      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    ),
                  ),
                ],
              ),
            ),
            // Show notification options when expanded
            if (_isNotificationsExpanded) ...[
              const Divider(height: 1, thickness: 0.5),
              const Padding(
                padding: EdgeInsets.all(8.0),
                child: NotificationSettings(),
              ),
            ],

            const Divider(height: 1, thickness: 0.5),

            // Enable Reading Reminders
            Padding(
              padding:
                  const EdgeInsets.symmetric(horizontal: 12.0, vertical: 0),
              child: Row(
                children: [
                  // Content area (title and subtitle)
                  const Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          'Reminders',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        Text(
                          'Meter reading reminder settings',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey,
                          ),
                        ),
                      ],
                    ),
                  ),
                  // Toggle switch for submenu
                  Transform.scale(
                    scale: 0.8, // Scale down the switch by 20%
                    child: Switch(
                      value: _isReadingRemindersExpanded,
                      onChanged: (value) {
                        setState(() {
                          if (value) {
                            // When Reading Reminders submenu is expanded, close all other submenus in Alerts & Notifications
                            _closeAllSubmenusExcept(
                                'AlertsNotifications', 'ReadingReminders');
                          }
                          _isReadingRemindersExpanded = value;
                        });
                      },
                      activeColor: Theme.of(context).colorScheme.primary,
                      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    ),
                  ),
                ],
              ),
            ),
            // Show meter reading reminder options when expanded
            if (_isReadingRemindersExpanded) ...[
              const Divider(height: 1, thickness: 0.5),
              const Padding(
                padding: EdgeInsets.all(8.0),
                child: MeterReadingReminderSettings(),
              ),
            ],
          ],
        );
      },
    );
  }

  void _showUpgradeDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              Icons.upgrade,
              color: Theme.of(context).colorScheme.primary,
              size: 20,
            ),
            const SizedBox(width: 8),
            const Text('Upgrade', style: TextStyle(fontSize: 18)),
            const Spacer(),
            IconButton(
              icon: Icon(
                Icons.close,
                color: Theme.of(context).brightness == Brightness.dark
                    ? Colors.white70
                    : AppColors.onSurface,
              ),
              onPressed: () => Navigator.of(context).pop(),
              tooltip: 'Close',
              constraints: const BoxConstraints(),
              padding: EdgeInsets.zero,
              iconSize: 20,
            ),
          ],
        ),
        content: const Text(
          'Not available yet',
          style: TextStyle(fontSize: 16),
          textAlign: TextAlign.center,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Got it'),
          ),
        ],
      ),
    );
  }

  void _showDonateDialog() {
    // Get the currency from the settings controller
    final controller = Provider.of<SettingsController>(context, listen: false);
    final currency = controller.meterUnitValue;

    // Map currency symbol to currency code for PayPal
    String currencyCode = 'USD';
    if (currency == '£') {
      currencyCode = 'GBP';
    } else if (currency == '€') {
      currencyCode = 'EUR';
    }

    String selectedAmount = PayPalService.donationAmounts[2]; // Default to 5.00
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final primaryColor = Theme.of(context).colorScheme.primary;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              Icons.favorite,
              color: isDarkMode ? AppColors.errorDark : AppColors.error,
              size: 20, // Reduced from 24
            ),
            const SizedBox(width: 8), // Reduced from 12
            const Text('Support Lekky',
                style: TextStyle(fontSize: 18)), // Reduced font size
          ],
        ),
        titlePadding:
            const EdgeInsets.fromLTRB(24, 20, 24, 8), // Reduced padding
        contentPadding:
            const EdgeInsets.fromLTRB(24, 8, 24, 16), // Reduced padding
        insetPadding: const EdgeInsets.symmetric(
            horizontal: 40, vertical: 24), // Reduced from default
        actionsPadding:
            const EdgeInsets.fromLTRB(16, 0, 16, 8), // Reduced padding
        content: StatefulBuilder(
          builder: (context, setState) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text(
                  'If you find this app useful, please consider making a donation to support its development.',
                  textAlign: TextAlign.center,
                  style: TextStyle(fontSize: 13), // Reduced from 14
                ),
                const SizedBox(height: 16), // Reduced from 24
                Container(
                  padding: const EdgeInsets.all(12), // Reduced from 16
                  decoration: BoxDecoration(
                    color: isDarkMode
                        ? AppColors.surfaceVariantDark
                        : Colors.grey[100],
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Select amount:',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 13,
                          color: isDarkMode ? Colors.white : Colors.black,
                        ),
                      ),
                      const SizedBox(height: 8),
                      // Create a grid with 2 columns of radio buttons
                      GridView.count(
                        crossAxisCount: 2,
                        shrinkWrap: true,
                        childAspectRatio: 3.5,
                        mainAxisSpacing: 8,
                        crossAxisSpacing: 8,
                        physics: const NeverScrollableScrollPhysics(),
                        children: PayPalService.donationAmounts.map((amount) {
                          return RadioListTile<String>(
                            title: Text(
                              '$currency$amount',
                              style: TextStyle(
                                color: isDarkMode ? Colors.white : Colors.black,
                                fontSize: 13,
                              ),
                            ),
                            value: amount,
                            groupValue: selectedAmount,
                            activeColor: primaryColor,
                            contentPadding: EdgeInsets.zero,
                            dense: true,
                            onChanged: (value) {
                              if (value != null) {
                                setState(() {
                                  selectedAmount = value;
                                });
                              }
                            },
                          );
                        }).toList(),
                      ),
                    ],
                  ),
                ),
              ],
            );
          },
        ),
        actions: [
          OutlinedButton(
            onPressed: () => Navigator.of(context).pop(),
            style: OutlinedButton.styleFrom(
              foregroundColor: primaryColor,
              side: BorderSide(color: primaryColor),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              padding: const EdgeInsets.symmetric(
                  horizontal: 12, vertical: 6), // Reduced padding
              visualDensity: VisualDensity.compact,
            ),
            child: const Text('Maybe Later'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              PayPalService.processDonation(
                  context, selectedAmount, currencyCode);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: primaryColor,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              padding: const EdgeInsets.symmetric(
                  horizontal: 12, vertical: 6), // Reduced padding
              visualDensity: VisualDensity.compact,
            ),
            child: const Text('Donate'),
          ),
        ],
      ),
    );
  }

  // These methods have been replaced by shared modules

  void _showClearDataDialog(SettingsController controller) async {
    final bool? confirmed = await ConfirmationDialog.show(
      context: context,
      title: 'Clear All Data',
      message:
          'Are you sure you want to delete all your data? This action cannot be undone.',
      confirmText: 'Delete',
      cancelText: 'Cancel',
      isDestructive: true,
      icon: Icons.delete_forever,
    );

    if (confirmed == true) {
      controller.clearAllData();
    }
  }

  void _showForceResetDialog(SettingsController controller) {
    bool hasBackup = false;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) {
          return AlertDialog(
            title: Row(
              children: [
                Icon(
                  Icons.warning_amber_rounded,
                  color: Theme.of(context).colorScheme.error,
                  size: 24,
                ),
                const SizedBox(width: 12),
                const Text('Force Reset App'),
              ],
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'This will reset the app to first-time state. Your current data can be backed up and restored after reset from the Welcome screen.',
                ),
                const SizedBox(height: 16),
                if (hasBackup)
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.green.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.green),
                    ),
                    child: const Row(
                      children: [
                        Icon(
                          Icons.check_circle,
                          color: Colors.green,
                          size: 20,
                        ),
                        SizedBox(width: 8),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Data Backed Up',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 14,
                                  color: Colors.green,
                                ),
                              ),
                              SizedBox(height: 4),
                              Text(
                                'Your data has been backed up and can be restored from the Welcome screen.',
                                style: TextStyle(fontSize: 12),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
              if (!hasBackup)
                ElevatedButton(
                  onPressed: () async {
                    try {
                      // Backup data
                      await controller.backupDataForTesting();
                      setState(() {
                        hasBackup = true;
                      });
                      if (context.mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text(
                                'Data backed up successfully. You can restore it after reset.'),
                            duration: Duration(seconds: 2),
                          ),
                        );
                      }
                    } catch (e) {
                      if (context.mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('Failed to backup data: $e'),
                            duration: const Duration(seconds: 3),
                          ),
                        );
                      }
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).colorScheme.primary,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('Backup Data'),
                ),
              if (hasBackup)
                TextButton(
                  onPressed: () async {
                    try {
                      Navigator.of(context).pop();

                      // Show loading indicator
                      showDialog(
                        context: context,
                        barrierDismissible: false,
                        builder: (context) => const AlertDialog(
                          content: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              CircularProgressIndicator(),
                              SizedBox(height: 16),
                              Text('Resetting app...'),
                            ],
                          ),
                        ),
                      );

                      // Use the controller's resetAppForTesting method
                      await controller.resetAppForTesting();

                      // Close loading dialog
                      if (context.mounted) {
                        Navigator.of(context).pop();

                        // Restart app
                        Navigator.of(context).pushNamedAndRemoveUntil(
                          '/',
                          (route) => false,
                        );
                      }
                    } catch (e) {
                      debugPrint('Error during force reset: $e');
                      if (context.mounted) {
                        Navigator.of(context)
                            .pop(); // Close loading dialog if open
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('Error resetting app: $e'),
                            backgroundColor: Colors.red,
                          ),
                        );
                      }
                    }
                  },
                  style: TextButton.styleFrom(
                    foregroundColor: AppColors.error,
                  ),
                  child: const Text('Force Reset'),
                ),
            ],
          );
        },
      ),
    );
  }

  void _showEmergencyResetDialog() async {
    final BuildContext currentContext = context;

    final bool? confirmed = await ConfirmationDialog.show(
      context: currentContext,
      title: 'Emergency Reset',
      message:
          'WARNING: This is a last resort option that will completely reset the app by clearing ALL data and restarting.\n\n'
          'Use this ONLY if the app is completely stuck and no other reset option works.\n\n'
          'ALL YOUR DATA WILL BE PERMANENTLY LOST. Continue?',
      confirmText: 'EMERGENCY RESET',
      cancelText: 'Cancel',
      isDestructive: true,
      icon: Icons.emergency,
      barrierDismissible: false,
    );

    if (confirmed == true) {
      try {
        // Show loading indicator
        if (!currentContext.mounted) return;

        await ProgressDialog.show(
          context: currentContext,
          title: 'Emergency Reset',
          message: 'Emergency Reset in progress...',
          barrierDismissible: false,
        );

        // Get SharedPreferences instance directly
        final prefs = await SharedPreferences.getInstance();

        // Clear all preferences
        await prefs.clear();

        // Explicitly set setup completed to false
        await prefs.setBool(AppConstants.keySetupCompleted, false);

        // Clear database
        try {
          // Reset the database
          final dbHelper = DBHelper();
          await dbHelper.resetDatabase();
          debugPrint('Database reset successfully');
        } catch (dbError) {
          debugPrint('Error resetting database: $dbError');
          // Continue with reset even if database reset fails
        }

        // Close loading dialog and show success message
        if (!currentContext.mounted) return;

        Navigator.of(currentContext).pop();

        // Show success message
        ScaffoldMessenger.of(currentContext).showSnackBar(
          const SnackBar(
            content: Text('Emergency Reset successful. Restarting app...'),
            duration: Duration(seconds: 2),
          ),
        );

        // Wait a moment for the user to see the message
        await Future.delayed(const Duration(seconds: 2));

        // Restart app
        if (!currentContext.mounted) return;

        Navigator.of(currentContext).pushNamedAndRemoveUntil(
          '/',
          (route) => false,
        );
      } catch (e) {
        debugPrint('Error during emergency reset: $e');

        if (!currentContext.mounted) return;

        Navigator.of(currentContext).pop(); // Close loading dialog if open

        ScaffoldMessenger.of(currentContext).showSnackBar(
          SnackBar(
            content: Text('Error during emergency reset: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showTestWelcomeSetupDialog(SettingsController controller) {
    bool hasBackup = false;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) {
          return AlertDialog(
            title: Row(
              children: [
                Icon(
                  Icons.science,
                  color: Theme.of(context).colorScheme.primary,
                  size: 24,
                ),
                const SizedBox(width: 12),
                const Text('Test Welcome & Setup'),
              ],
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'This will reset the app to show the Welcome and Setup screens. Your current data can be backed up and restored after testing.',
                  style: TextStyle(fontSize: 14),
                ),
                const SizedBox(height: 16),
                if (hasBackup)
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.green.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.green),
                    ),
                    child: const Row(
                      children: [
                        Icon(
                          Icons.check_circle,
                          color: Colors.green,
                          size: 20,
                        ),
                        SizedBox(width: 8),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Data Backed Up',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 14,
                                  color: Colors.green,
                                ),
                              ),
                              SizedBox(height: 4),
                              Text(
                                'Your data has been backed up and can be restored from the Welcome screen.',
                                style: TextStyle(fontSize: 12),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
              if (!hasBackup)
                ElevatedButton(
                  onPressed: () async {
                    try {
                      // Backup data
                      await controller.backupDataForTesting();
                      setState(() {
                        hasBackup = true;
                      });
                      if (context.mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('Data backed up successfully'),
                            duration: Duration(seconds: 2),
                          ),
                        );
                      }
                    } catch (e) {
                      if (context.mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('Failed to backup data: $e'),
                            duration: const Duration(seconds: 3),
                          ),
                        );
                      }
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).colorScheme.primary,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('Backup Data'),
                ),
              if (hasBackup)
                ElevatedButton(
                  onPressed: () async {
                    try {
                      // Show loading indicator
                      showDialog(
                        context: context,
                        barrierDismissible: false,
                        builder: (context) => const AlertDialog(
                          content: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              CircularProgressIndicator(),
                              SizedBox(height: 16),
                              Text('Resetting app...'),
                            ],
                          ),
                        ),
                      );

                      // Reset app
                      await controller.resetAppForTesting();

                      // Close all screens and navigate to welcome screen
                      if (context.mounted) {
                        // Close dialogs
                        Navigator.of(context).pop(); // Close loading dialog
                        Navigator.of(context).pop(); // Close reset dialog

                        // Restart app by pushing to the splash screen
                        Navigator.of(context).pushNamedAndRemoveUntil(
                          '/', // Root route goes to splash screen
                          (route) => false, // Remove all routes
                        );
                      }
                    } catch (e) {
                      // Handle errors
                      if (context.mounted) {
                        Navigator.of(context).pop(); // Close loading dialog
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('Error resetting app: $e'),
                            backgroundColor: Colors.red,
                          ),
                        );
                      }
                      debugPrint('Error resetting app: $e');
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).colorScheme.primary,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('Reset & Test'),
                ),
            ],
          );
        },
      ),
    );
  }

  void _showTipsDialog() {
    // Use the modular TipsTricksDialog component
    TipsTricksDialog.show(context: context);
  }
}
