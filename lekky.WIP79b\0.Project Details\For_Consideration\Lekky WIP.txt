Lekky WIP

Keys and their purposes:

is_first_launch (bool): Tracks if the app is launched for the first time to show the setup screen.
meter_unit (String): Stores the unit of measurement (e.g., 'kWh', '£', '$').
decimal_precision (String): Stores the decimal precision for readings (e.g., '1.11').
amount_left (double): Stores the initial amount left on the meter (set during setup).
alert_threshold (double): The threshold for sending low balance alerts.
days_in_advance (int): Number of days in advance to send alerts.
date_info (String): Whether to show 'Date only' or 'Date & Time' for timestamps.


SQLite Database (via sqflite):

Used to store meter readings and top-up history as MeterEntry objects.
The database is managed by the DBHelper class (defined in db_helper.dart).
Schema:
Table: meter_entries
Columns:
id (INTEGER PRIMARY KEY AUTOINCREMENT): Unique identifier for each entry.
reading (REAL): The meter reading value.
amountToppedUp (REAL): The amount topped up (0 if it’s a regular reading).
timestamp (TEXT): The timestamp of the entry, stored as an ISO 8601 string.


To support multiple densities, you can provide different image sizes in the res/drawable folders:

drawable-mdpi: 360x640 (1x)
drawable-hdpi: 540x960 (1.5x)
drawable-xhdpi: 720x1280 (2x)
drawable-xxhdpi: 1080x1920 (3x)
drawable-xxxhdpi: 1440x2560 (4x)


For the app icon:

For better results, provide multiple sizes (e.g., 192x192, 512x512, 1024x1024).




Code:

FFA500
B87E14









Proposed Module Structure
Here’s a detailed plan for splitting main.dart into modules, based on your app’s current structure (assumed from MyApp, SplashScreen, DebugPage, etc.) and requirements (notifications, averages, load/save). Each module is a Dart file with a specific purpose, and I’ll describe its functionality and relationships.

lib/main.dart
Purpose: Entry point, app initialization, and top-level widget (MyApp).
Functionality:
Initializes DBHelper and flutter_local_notifications.
Defines MyApp (sets initialRoute, handles navigation).
Imports all screen modules for navigation.
Contents:
main() function:
dart

Copy
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  final dbHelper = DBHelper();
  await dbHelper.init();
  final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();
  const AndroidInitializationSettings initializationSettingsAndroid =
      AndroidInitializationSettings('icon');
  const InitializationSettings initializationSettings =
      InitializationSettings(android: initializationSettingsAndroid);
  await flutterLocalNotificationsPlugin.initialize(initializationSettings);
  runApp(const MyApp(initialRoute: SplashScreen()));
}
MyApp class (minimal, routes to initialRoute).
Relationships:
Imports DBHelper from lib/db_helper.dart.
Imports all screens from lib/screens/ (e.g., SplashScreen, DebugPage).
Passes DBHelper or settings to screens if needed (e.g., via constructor or provider).
Size: ~50–100 lines, lean and focused.
lib/db_helper.dart (Existing)
Purpose: Manages persistence for MeterEntry objects and settings.
Functionality:
Stores meterEntries in SharedPreferences (JSON-encoded).
Handles settings (meter_unit, alert_threshold, etc.).
Provides CRUD methods (addMeterEntry, getMeterEntries, clearEntries).
Contents:
MeterEntry class.
DBHelper class with _initialize, _saveEntries, etc.
Relationships:
Used by all screens (MyHomePage, DebugPage, etc.) for data access.
Independent of UI, no direct navigation.
Size: ~150–200 lines (already modular, no changes needed).
Note: We’ll add _clearEntries for reset functionality during load/save step.
lib/screens/splash_screen.dart
Purpose: Displays splash screen on app start/reopen.
Functionality:
Shows logo (icon.png) or background (background.png).
Checks SharedPreferences for setup status (e.g., isSetupComplete).
Navigates to SetupScreen (if first run) or MyHomePage (if setup done).
Contents:
SplashScreen (StatelessWidget or StatefulWidget for timer).
Navigation logic (e.g., Future.delayed to wait 2 seconds).
Relationships:
Imports SharedPreferences for setup check.
Navigates to SetupScreen or MyHomePage.
Uses db_helper.dart indirectly via settings.
Size: ~50–100 lines.
Note: Fix reopen issue (not showing) in “review” step.
lib/screens/setup_screen.dart
Purpose: Configures initial settings on first run.
Functionality:
Form for amount_left, alert_threshold, days_in_advance, date_info, date_format.
Saves to SharedPreferences.
Future: Add Load Meter Data button.
Contents:
SetupScreen (StatefulWidget for form state).
Form fields, save logic, navigation to MyHomePage.
Relationships:
Uses SharedPreferences and DBHelper for settings/entries.
Navigates to MyHomePage on completion.
Size: ~100–150 lines.
Note: Add button in Step 4 (load/save).
lib/screens/my_home_page.dart
Purpose: Main dashboard with action buttons.
Functionality:
Displays current meter balance (from meterEntries.last.reading or amount_left).
Buttons for Input Meter Reading, Top Up, History, Settings, Debug.
Contents:
MyHomePage (StatefulWidget for dynamic balance).
UI layout (buttons, background image).
Relationships:
Uses DBHelper for balance.
Navigates to InputMeterReadingPage, TopUpPage, MeterHistoryPage, SettingsPage, DebugPage.
Size: ~100–150 lines.
lib/screens/input_meter_reading_page.dart
Purpose: Records new meter readings.
Functionality:
Form for reading amount, date (defaults to today).
Saves MeterEntry via DBHelper.
Triggers _scheduleLowBalanceAlert if reading < threshold.
Contents:
InputMeterReadingPage (StatefulWidget).
Form, date picker, notification logic.
Relationships:
Uses DBHelper to save entries.
Uses flutter_local_notifications for alerts.
Returns to MyHomePage.
Size: ~100–150 lines.
Note: Fix notifications in Step 2.
lib/screens/top_up_page.dart
Purpose: Records top-up amounts.
Functionality:
Form for top-up amount, date (defaults to today).
Saves MeterEntry with amountToppedUp.
Contents:
TopUpPage (StatefulWidget).
Form, date picker, save logic.
Relationships:
Uses DBHelper to save entries.
Returns to MyHomePage.
Size: ~80–120 lines.
Note: Verify top-up dates in Step 3 (averages).
lib/screens/meter_history_page.dart
Purpose: Displays and analyzes meter entries.
Functionality:
Shows table of meterEntries (ID, date, reading, top-up, usage, averages).
Calculates Short Average/Total Average.
Future: Edit entries, search, 15 rows, custom rows setting.
Contents:
MeterHistoryPage (StatefulWidget for table updates).
Table UI, calculation methods.
Relationships:
Uses DBHelper for entries.
Returns to MyHomePage.
Size: ~150–200 lines.
Note: Fix averages, add features in Steps 3 and 5.
lib/screens/settings_page.dart
Purpose: Updates app settings.
Functionality:
Form for meter_unit, alert_threshold, etc.
Saves to SharedPreferences.
Future: Load Meter Data button, rows-per-page, back button.
Contents:
SettingsPage (StatefulWidget).
Form fields, save logic.
Relationships:
Uses SharedPreferences and DBHelper.
Returns to MyHomePage.
Size: ~100–150 lines.
Note: Add features in Step 4/5.
lib/screens/debug_page.dart
Purpose: Displays diagnostic info for debugging.
Functionality:
Shows settings (amount_left, alert_threshold) and meterEntries.
Formats timestamps with _formatDateTime.
Future: Refresh button for _loadMeterData.
Contents:
DebugPage (StatelessWidget with FutureBuilder).
Text widgets for data display.
Relationships:
Uses DBHelper and SharedPreferences.
Returns to MyHomePage.
Size: ~100–150 lines.
Note: First to modularize, add refresh in Step 4.
lib/utils/data_loader.dart (Future)
Purpose: Handles load/save logic for real-world data.
Functionality:
Parses CSV/JSON using file_picker.
Converts to MeterEntry objects for DBHelper.
Contents:
_loadMeterData, _saveMeterData functions.
Relationships:
Used by SetupScreen, SettingsPage for import.
Interacts with DBHelper.
Size: ~50–100 lines.
Note: Create in Step 4.
Module Relationships
Data Flow:
DBHelper is the central data source, used by all screens for meterEntries and settings.
SharedPreferences stores settings, accessed directly or via DBHelper.
Navigation:
MyApp routes to SplashScreen initially.
SplashScreen → SetupScreen (first run) or MyHomePage (setup complete).
MyHomePage → all other screens via buttons.
Each screen returns to MyHomePage (except SetupScreen → MyHomePage one-way).
Dependencies:
All screens import db_helper.dart for DBHelper, MeterEntry.
InputMeterReadingPage uses flutter_local_notifications.
Future: SetupScreen, SettingsPage, data_loader.dart use file_picker.
UI:
Shared background.png (via DecorationImage in Scaffold).
Consistent date formatting (_formatDateTime moved to a utility or each screen).
How They Support Features
Notifications: input_meter_reading_page.dart isolates _scheduleLowBalanceAlert, easy to fix in Step 2.
Averages: meter_history_page.dart contains calculation logic, focused for Step 3.
Load/Save: data_loader.dart centralizes import, called by setup_screen.dart, settings_page.dart (Step 4).
History/UI Features: meter_history_page.dart, settings_page.dart handle rows, editing, search in Step 5.
Size (<50MB): Modular code doesn’t increase APK size; data_loader.dart uses lightweight file_picker.




Updated Work Plan
Incorporating your clarifications (history working, top-up dates for averages, deferred permissions/data), here’s the updated plan. I’ll include this in each response to track progress.

1. Modularize main.dart
Goal: Split into smaller files for maintainability.
Tasks:
Create lib/screens/ folder.
Move widgets to:
lib/screens/splash_screen.dart
lib/screens/setup_screen.dart
lib/screens/my_home_page.dart
lib/screens/input_meter_reading_page.dart
lib/screens/top_up_page.dart
lib/screens/meter_history_page.dart
lib/screens/settings_page.dart
lib/screens/debug_page.dart
Keep main.dart for main(), MyApp.
Update imports (DBHelper, MeterEntry from lib/db_helper.dart).
Update navigation (Navigator.push).
Test each page with flutter run.
Deliverable: Smaller main.dart, pages in lib/screens/, app unchanged.
Test: Add reading, restart, verify DebugPage/MeterHistoryPage.
Update: Log navigation/import issues, note top-up dates.
2. Fix Notifications
Goal: Trigger low balance alerts.
Tasks:
Verify AndroidManifest.xml permissions (POST_NOTIFICATIONS).
Check flutter_local_notifications initialization in main.dart.
Ensure input_meter_reading_page.dart calls _scheduleLowBalanceAlert.
Optionally add permission prompt if denied.
Test with reading = 8 (threshold = 12).
Deliverable: Alerts show on low balance.
Test: Enter low reading, confirm alert, verify persistence.
Update: Note permission bugs.
3. Fix Averages
Goal: Show Short Average/Total Average on MeterHistoryPage.
Tasks:
Review _calculateShortAverage/_calculateTotalAverage in meter_history_page.dart.
Fix same-day entry issues (e.g., use hours if <1 day).
Verify top-up dates display in DebugPage/MeterHistoryPage.
Test with multi-day entries.
Deliverable: Averages and top-up dates display correctly.
Test: Add dated entries, verify averages/dates.
Update: Log edge cases.
4. Implement Load/Save Data with Load Meter Data Buttons
Goal: Import real-world data (Excel-based), add buttons.
Tasks:
Add file_picker to pubspec.yaml.
Create lib/utils/data_loader.dart for _loadMeterData.
Parse CSV/JSON per your data.
Add ElevatedButton to SetupScreen/SettingsPage.
Add DebugPage refresh button.
Test importing, verify DebugPage/MeterHistoryPage.
Deliverable: Buttons load data, UI refreshes.
Test: Import sample, add reading, restart.
Update: Adjust parsing for your data.
5. Review and Make Further Improvements
Goal: Fix remaining issues, add features.
Tasks:
Splash Screen: Ensure it shows on reopen, consider redesign.
Meter History Page:
Date format to dd-mm-yy or yy-mm-dd.
Increase rows to 15.
Add edit button per row.
Add date/time frame search.
Add rows-per-page setting to SettingsPage.
Date Selection UI: Bold/larger “Today’s Date” with “or”.
SettingsPage:
Select number on edit.
Add “Back to HomePage” button.
Permission prompt if needed.
Sidebar: Add for long pages.
Deliverable: All features working.
Test: Full walkthrough.
Update: Prioritize new ideas.
6. Test
Goal: Ensure stability.
Tasks:
Run test sequence (e.g., 91, 12, 73, …).
Test edge cases.
Deliverable: Bug-free app.
Update: Log fixes.
7. Optimize App Size
Goal: <50MB.
Tasks:
Compress background.png (~1.1 MB → <200 KB), icon.png (~0.6 MB → <100 KB).
Remove unused dependencies.
Build flutter build apk --release --split-per-abi.
Deliverable: APK <50MB.
Test: Install, verify functionality.
Update: Note size tweaks.