// File: lib/features/home/<USER>/widgets/notifications_card.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../core/providers/notification_provider.dart';
import '../../../../core/shared_modules/settings_provider.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/widgets/app_card.dart';

/// A card that displays notification settings and meter reading reminders
class NotificationsCard extends StatelessWidget {
  const NotificationsCard({
    super.key,
  });

  // Format reminder text to limit to 15 chars per line
  String _formatReminderText(String frequency, String time) {
    // Insert a newline character after the frequency to force wrapping
    return '$frequency\n@ $time';
  }

  // Helper method to get reminder settings asynchronously
  Future<Map<String, dynamic>> _getReminderSettings(
      NotificationProvider provider) async {
    // Get reminder settings
    final bool remindersEnabled =
        await provider.areMeterReadingRemindersEnabled();

    // Get reminder frequency
    final int frequencyDays = await provider.getMeterReadingReminderFrequency();
    String reminderFrequency = 'Weekly'; // Default

    // Convert days to readable frequency
    if (frequencyDays == 1) {
      reminderFrequency = 'Daily';
    } else if (frequencyDays == 7) {
      reminderFrequency = 'Weekly';
    } else if (frequencyDays == 14) {
      reminderFrequency = 'Bi-weekly';
    } else if (frequencyDays == 30) {
      reminderFrequency = 'Monthly';
    }

    // Get reminder time
    final reminderTimeModel = await provider.getMeterReadingReminderTime();
    // Format time as 24-hour format (HH:MM)
    final hour = reminderTimeModel.timeOfDay.hour;
    final minute = reminderTimeModel.timeOfDay.minute;
    final reminderTime =
        '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}';

    return {
      'remindersEnabled': remindersEnabled,
      'reminderFrequency': reminderFrequency,
      'reminderTime': reminderTime,
    };
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final labelColor =
        isDarkMode ? AppColors.lastReadingLabelDark : AppColors.primary;
    final valueColor =
        isDarkMode ? AppColors.valueTextDark : AppColors.onBackground;

    return Consumer<SettingsProvider>(
      builder: (context, settingsProvider, _) {
        // Get notification settings
        final bool notificationsEnabled = settingsProvider.notificationsEnabled;
        final double alertThreshold = settingsProvider.alertThreshold;
        final int daysInAdvance = settingsProvider.daysInAdvance;

        // Get currency symbol
        final String currency = settingsProvider.currency;

        // Get notification provider
        final notificationProvider =
            Provider.of<NotificationProvider>(context, listen: true);

        // Use FutureBuilder for reminder settings
        return FutureBuilder<Map<String, dynamic>>(
          future: _getReminderSettings(notificationProvider),
          builder: (context, snapshot) {
            // Default values if data is still loading
            bool remindersEnabled = false;
            String reminderFrequency = 'Weekly';
            String reminderTime = '7:00pm';

            // Update with actual values if available
            if (snapshot.hasData) {
              remindersEnabled = snapshot.data!['remindersEnabled'];
              reminderFrequency = snapshot.data!['reminderFrequency'];
              reminderTime = snapshot.data!['reminderTime'];
            }

            return AppCard(
              padding: const EdgeInsets.all(16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Notification Details Column
                  Expanded(
                    child: Row(
                      children: [
                        const Icon(
                          Icons.show_chart,
                          size: 20,
                          color: AppColors.primary,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Notifications:',
                                style: AppTextStyles.labelLarge.copyWith(
                                  color: labelColor,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                notificationsEnabled
                                    ? '$currency${alertThreshold.toStringAsFixed(2)} notice by: ${daysInAdvance}days'
                                    : 'Not enabled',
                                style: AppTextStyles.bodyLarge.copyWith(
                                  fontWeight: FontWeight.w500,
                                  color: valueColor,
                                ),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  // Meter Reading Reminder Column
                  Expanded(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        const Icon(
                          Icons.notifications,
                          size: 20,
                          color: AppColors.primary,
                        ),
                        const SizedBox(width: 8),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Meter Reminder:',
                              style: AppTextStyles.labelLarge.copyWith(
                                color: labelColor,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              remindersEnabled
                                  ? _formatReminderText(
                                      reminderFrequency, reminderTime)
                                  : 'Not enabled',
                              style: AppTextStyles.bodyLarge.copyWith(
                                fontWeight: FontWeight.w500,
                                color: valueColor,
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }
}
