// File: lib/features/notifications/data/migration/migration_initializer.dart

import 'package:flutter/material.dart';
import '../../di/notification_service_locator.dart';
import 'auto_migration_manager.dart';
import 'storage_migration_service.dart';

/// Initializer for the storage migration system
class MigrationInitializer {
  /// Initialize the storage migration system
  ///
  /// This should be called during app startup, after the notification system is initialized.
  /// It will check if migration is needed and trigger it automatically if appropriate.
  static Future<void> initialize() async {
    try {
      // Ensure notification service locator is initialized
      await initNotificationServiceLocator();

      // Initialize auto migration manager
      await AutoMigrationManager.initialize();

      debugPrint('MigrationInitializer: Storage migration system initialized');
    } catch (e) {
      debugPrint(
          'MigrationInitializer: Error initializing storage migration system: $e');
    }
  }

  /// Check if migration is needed
  static Future<bool> isMigrationNeeded() async {
    try {
      final migrationService =
          notificationServiceLocator<StorageMigrationService>();
      return await migrationService.isMigrationNeeded();
    } catch (e) {
      debugPrint(
          'MigrationInitializer: Error checking if migration is needed: $e');
      return false;
    }
  }

  /// Get the current storage type
  static Future<String> getCurrentStorageType() async {
    try {
      final migrationService =
          notificationServiceLocator<StorageMigrationService>();
      return await migrationService.getCurrentStorageType();
    } catch (e) {
      debugPrint(
          'MigrationInitializer: Error getting current storage type: $e');
      return 'Unknown';
    }
  }

  /// Trigger manual migration with progress dialog
  ///
  /// This will show a progress dialog to the user and trigger the migration process.
  /// Returns true if migration was successful, false otherwise.
  static Future<bool> triggerManualMigration(BuildContext context) async {
    try {
      final migrationService =
          notificationServiceLocator<StorageMigrationService>();

      // Check if migration is needed
      final isMigrationNeeded = await migrationService.isMigrationNeeded();
      if (!isMigrationNeeded) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Storage migration is not needed')),
        );
        return true;
      }

      // Show a simple progress dialog
      bool migrationResult = false;

      // Show dialog
      await showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          // Start migration in background
          _executeMigration(context, migrationService).then((result) {
            migrationResult = result;
            // Close dialog when migration completes
            Navigator.of(context).pop();
          });

          // Return the dialog widget
          return const AlertDialog(
            title: Text('Storage Migration'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text('Migrating notification data to SQLite database...'),
                SizedBox(height: 20),
                CircularProgressIndicator(),
              ],
            ),
          );
        },
      );

      // Show result message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(migrationResult
              ? 'Storage migration completed successfully'
              : 'Storage migration failed'),
          backgroundColor: migrationResult ? Colors.green : Colors.red,
        ),
      );

      return migrationResult;
    } catch (e) {
      debugPrint('MigrationInitializer: Error triggering manual migration: $e');

      // Show error message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error: $e')),
      );

      return false;
    }
  }

  /// Execute migration in background
  static Future<bool> _executeMigration(
      BuildContext context, StorageMigrationService migrationService) async {
    try {
      // Execute migration
      return await migrationService.executeMigration(
        onStatusUpdate: (status) {
          debugPrint('Migration status: $status');
        },
      );
    } catch (e) {
      debugPrint('Error during migration: $e');
      return false;
    }
  }
}
