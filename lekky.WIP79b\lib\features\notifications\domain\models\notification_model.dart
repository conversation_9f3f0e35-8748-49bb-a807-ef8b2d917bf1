// File: lib/features/notifications/domain/models/notification_model.dart
import 'package:flutter/material.dart';

/// Enum representing notification priority levels
enum NotificationPriority {
  info,
  warning,
  alert,
}

/// Model class for storing notification data
class NotificationModel {
  final String id;
  final String title;
  final String message;
  final DateTime timestamp;
  final NotificationPriority priority;
  final bool isRead;
  final String?
      actionType; // For specific actions like 'low_balance', 'top_up', 'invalid_record'

  NotificationModel({
    required this.id,
    required this.title,
    required this.message,
    required this.timestamp,
    required this.priority,
    this.isRead = false,
    this.actionType,
  });

  /// Create a copy of this notification with updated fields
  NotificationModel copyWith({
    String? id,
    String? title,
    String? message,
    DateTime? timestamp,
    NotificationPriority? priority,
    bool? isRead,
    String? actionType,
  }) {
    return NotificationModel(
      id: id ?? this.id,
      title: title ?? this.title,
      message: message ?? this.message,
      timestamp: timestamp ?? this.timestamp,
      priority: priority ?? this.priority,
      isRead: isRead ?? this.isRead,
      actionType: actionType ?? this.actionType,
    );
  }

  /// Convert notification to a map for storage
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'message': message,
      'timestamp': timestamp.toIso8601String(),
      'priority': priority.index,
      'isRead': isRead,
      'actionType': actionType,
    };
  }

  /// Create a notification from a map
  factory NotificationModel.fromMap(Map<String, dynamic> map) {
    return NotificationModel(
      id: map['id'],
      title: map['title'],
      message: map['message'],
      timestamp: DateTime.parse(map['timestamp']),
      priority: NotificationPriority.values[map['priority']],
      isRead: map['isRead'] ?? false,
      actionType: map['actionType'],
    );
  }

  /// Get the color associated with this notification's priority
  Color getColor() {
    switch (priority) {
      case NotificationPriority.info:
        return Colors.blue.shade300; // Light blue for info
      case NotificationPriority.warning:
        return Colors.amber.shade300; // Amber for warnings
      case NotificationPriority.alert:
        return Colors.red.shade300; // Red for alerts
    }
  }

  /// Format the timestamp as a string
  String getFormattedTimestamp() {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    // If less than 24 hours ago, show relative time
    if (difference.inHours < 24) {
      if (difference.inMinutes < 1) {
        return 'Just now';
      } else if (difference.inHours < 1) {
        return '${difference.inMinutes} ${difference.inMinutes == 1 ? 'minute' : 'minutes'} ago';
      } else {
        return '${difference.inHours} ${difference.inHours == 1 ? 'hour' : 'hours'} ago';
      }
    }
    // If within the last week, show day of week and time
    else if (difference.inDays < 7) {
      final dayNames = [
        'Monday',
        'Tuesday',
        'Wednesday',
        'Thursday',
        'Friday',
        'Saturday',
        'Sunday'
      ];
      final day = dayNames[timestamp.weekday - 1];
      return '$day, ${_formatTime(timestamp)}';
    }
    // Otherwise show full date
    else {
      return '${timestamp.day}/${timestamp.month}/${timestamp.year}, ${_formatTime(timestamp)}';
    }
  }

  /// Format time as HH:MM
  String _formatTime(DateTime dateTime) {
    return '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
