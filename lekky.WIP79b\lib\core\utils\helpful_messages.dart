// File: lib/core/utils/helpful_messages.dart

/// Class that provides helpful messages to display to users
class HelpfulMessages {
  // Private constructor to prevent instantiation
  HelpfulMessages._();

  /// List of all helpful messages for the message bar
  static final List<String> allMessages = [
    // Original messages - shortened for better display in message bar
    'Log meter readings weekly for accurate data.',
    'Recent-avg shows usage between consecutive readings.',
    'Total-avg shows usage since your first reading.',
    'Date column shows when readings/top-ups happened.',
    'Review your usage history to find savings.',
    'Export history for spreadsheet analysis.',
    'Enable alerts to avoid usage surprises.',
    'Update thresholds when your plan changes.',
    'Test scenarios to see potential savings.',
    'Check the app daily for better control.',

    // Additional messages - shortened for better display
    'Compare monthly averages to spot patterns.',
    'Add top-ups promptly for accurate balance.',
    'Use filters to focus on specific entries.',
    'Check for invalid entries regularly.',
    'Track daily average for realistic goals.',
    'Identify high usage periods to save money.',
    'Validate readings for accurate calculations.',
    'Monitor trends to predict future costs.',
    'Total-avg provides long-term consumption insights.',
    'Use history to spot unusual patterns.',
    'Enter readings at consistent times daily.',
    'Review past top-ups for better budgeting.',
    'Check usage after adding new appliances.',
    'Use dark mode to save battery at night.',
    'Set reminders for regular meter readings.',
    'Compare usage before/after energy changes.',
    'Look for usage spikes to identify issues.',
    'Track credit balance to avoid cutoffs.',
    'Track your usage patterns for budget planning.',
    'Check history before contacting provider.',

    // Quick tips from settings screen (added after checking for duplicates)
    'Check your short-average after each top-up.',
    'Set custom date ranges to spot seasonal patterns.',
    'Review cost breakdowns to identify high-usage days.',
    'Make daily checks a habit for consistent savings.',
    'Export your history for deeper analysis in spreadsheets.',
    'Try what-if scenarios to plan future savings.',
    'Check the Home screen for your last reading date.',
    'Backup files are saved to your phone\'s Downloads folder.',
    'Lekky can store up to 600 entries before needing cleanup.',
  ];

  /// List of tips with detailed information for the Tips & Tricks dialog
  static final List<Map<String, dynamic>> detailedTips = [
    {
      'title': 'Log Readings Weekly',
      'description':
          'Regular readings help spot trends early and keep your data accurate.',
      'icon': 'calendar_today',
    },
    {
      'title': 'Check Short-Average After Top-Ups',
      'description':
          'See your daily usage rate right after topping up for the most accurate estimate.',
      'icon': 'show_chart',
    },
    {
      'title': 'Set Custom Date Ranges',
      'description':
          'Compare holiday weekends or yearly patterns to understand your usage better.',
      'icon': 'date_range',
    },
    {
      'title': 'Review Cost Breakdowns',
      'description':
          'Identify high-usage days and adjust habits like reducing phantom loads.',
      'icon': 'attach_money',
    },
    {
      'title': 'Enable Alerts',
      'description':
          'Get notified when you reach usage thresholds to avoid unexpected large bills.',
      'icon': 'notifications_active',
    },
    {
      'title': 'Make It a Habit',
      'description':
          'Check your usage daily, like after dinner, to build consistent monitoring.',
      'icon': 'repeat',
    },
  ];

  /// Get a random helpful message
  static String getRandomMessage() {
    allMessages.shuffle();
    return allMessages.first;
  }

  /// Get a specific message by index
  static String getMessageByIndex(int index) {
    if (index < 0 || index >= allMessages.length) {
      return getRandomMessage();
    }
    return allMessages[index];
  }

  /// Get a rotating message based on the current date
  /// This ensures users see different messages each day
  static String getDailyMessage() {
    final today = DateTime.now();
    final dayOfYear = today.difference(DateTime(today.year, 1, 1)).inDays;
    final messageIndex = dayOfYear % allMessages.length;
    return allMessages[messageIndex];
  }
}
