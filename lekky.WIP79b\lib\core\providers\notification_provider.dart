// File: lib/core/providers/notification_provider.dart
import 'package:flutter/material.dart';
import '../models/date_to_top_up_result.dart';
import '../models/notification_model.dart';
import '../models/reminder_time_model.dart';
import '../services/notification_service.dart';
import '../utils/logger.dart' show logger;
import '../utils/event_bus.dart';
import '../utils/notification_helper.dart';

/// Provider for managing notifications in the app
class NotificationProvider extends ChangeNotifier {
  final NotificationService _notificationService = NotificationService();
  List<NotificationModel> _notifications = [];
  bool _isInitialized = false;

  /// Get all notifications
  List<NotificationModel> get notifications =>
      List.unmodifiable(_notifications);

  /// Get unread notifications count
  int get unreadCount => _notifications.where((n) => !n.isRead).length;

  /// Check if there are any unread notifications
  bool get hasUnread => _notifications.any((n) => !n.isRead);

  /// Initialize the provider with enhanced reliability
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      logger.i('Starting NotificationProvider initialization');

      // Initialize the notification service
      await _notificationService.initialize();
      logger.i('NotificationService initialized');

      // Load existing notifications
      await _loadNotifications();
      logger.i('Loaded ${_notifications.length} notifications');

      // Check if time zone has changed since last app run
      final timeZoneChanged = await NotificationHelper().hasTimeZoneChanged();
      if (timeZoneChanged) {
        logger.i(
            'Time zone has changed since last app run, rescheduling all reminders');
        await rescheduleMeterReadingReminders(forceReschedule: true);
      }

      // Check notification permissions
      final permissionsGranted =
          await NotificationHelper().checkNotificationPermissions();
      logger.i('Notification permissions granted: $permissionsGranted');

      if (!permissionsGranted) {
        logger.w(
            'Notification permissions not granted, will prompt user when needed');
        // We'll request permissions when the user enables notifications in settings
        // rather than at startup, which is a better user experience

        // Check if notifications are enabled in app settings
        final notificationsEnabled = await areNotificationsEnabled();
        if (notificationsEnabled) {
          logger.w(
              'Notifications are enabled in app settings but permissions are not granted. '
              'This may cause notifications to not be delivered.');

          // Check if permission is permanently denied
          final isPermanentlyDenied = await NotificationHelper()
              .isNotificationPermissionPermanentlyDenied();
          if (isPermanentlyDenied) {
            logger.w(
                'Notification permission is permanently denied. User needs to enable it in app settings.');
          }
        }
      }

      // Always reschedule meter reading reminders on app startup
      // This ensures reminders persist across device restarts
      final remindersEnabled = await areMeterReadingRemindersEnabled();
      logger.i('Meter reading reminders enabled: $remindersEnabled');

      if (remindersEnabled) {
        await rescheduleMeterReadingReminders();
      } else {
        logger.i(
            'Skipping meter reading reminder scheduling as they are disabled');
      }

      _isInitialized = true;

      // Reschedule all pending notifications
      if (permissionsGranted) {
        final rescheduledCount =
            await NotificationHelper().rescheduleAllNotifications();
        logger.i('Rescheduled $rescheduledCount pending notifications');
      }

      logger.i('NotificationProvider initialized successfully');

      // Log the next scheduled reminder for debugging
      if (remindersEnabled && permissionsGranted) {
        final nextReminderDate = await getNextMeterReadingReminderDate();
        if (nextReminderDate != null) {
          logger.i(
              'Next meter reading reminder scheduled for: $nextReminderDate');
        } else {
          logger.w('No next meter reading reminder date found');
        }
      }
    } catch (e) {
      logger.e('Error initializing notification provider: $e',
          details: e.toString());
    }
  }

  /// Reschedule meter reading reminders with enhanced reliability
  /// This ensures reminders persist across app restarts and time zone changes
  Future<bool> rescheduleMeterReadingReminders(
      {bool forceReschedule = false}) async {
    try {
      // Check if reminders are enabled
      final remindersEnabled = await areMeterReadingRemindersEnabled();
      if (!remindersEnabled && !forceReschedule) {
        logger.i('Meter reading reminders are disabled, not rescheduling');
        return false;
      }

      // Check notification permissions
      final permissionsGranted =
          await NotificationHelper().checkNotificationPermissions();
      if (!permissionsGranted) {
        logger.w(
            'Cannot reschedule reminders: notification permissions not granted');

        // Check if notifications are enabled in app settings
        final notificationsEnabled = await areNotificationsEnabled();
        if (notificationsEnabled) {
          logger.w(
              'Notifications are enabled in app settings but permissions are not granted. '
              'This may cause reminders to not be delivered.');

          // Check if permission is permanently denied
          final isPermanentlyDenied = await NotificationHelper()
              .isNotificationPermissionPermanentlyDenied();
          if (isPermanentlyDenied) {
            logger.w(
                'Notification permission is permanently denied. User needs to enable it in app settings.');
          }
        }

        return false;
      }

      logger.i('Rescheduling meter reading reminders');

      // Get the reminder frequency
      final frequency = await getMeterReadingReminderFrequency();
      logger.i('Reminder frequency: $frequency days');

      // Get the reminder time
      final reminderTime = await getMeterReadingReminderTime();
      logger.i(
          'Reminder time: ${reminderTime.timeOfDay.hour}:${reminderTime.timeOfDay.minute}');

      // Cancel any existing reminders first to avoid duplicates
      await NotificationHelper()
          .cancelNotification(4); // 4 is the meterReadingReminderNotificationId
      logger.i('Cancelled existing meter reading reminders');

      // Schedule the reminder with enhanced reliability
      final success =
          await _notificationService.scheduleMeterReadingReminder(frequency);

      if (success) {
        logger.i('Successfully rescheduled meter reading reminders');

        // Get and log the next reminder date for debugging
        final nextReminderDate =
            await _notificationService.getNextMeterReadingReminderDate();
        if (nextReminderDate != null) {
          logger.i(
              'Next meter reading reminder scheduled for: $nextReminderDate');
        } else {
          logger.w('Next reminder date is null after scheduling');
        }

        // Emit event to update UI if needed
        EventBus().fire(EventType.reminderSettingsUpdated);
      } else {
        logger.e('Failed to reschedule meter reading reminders');

        // Try one more time with a delay
        await Future.delayed(const Duration(seconds: 1));
        logger.i('Retrying meter reading reminder scheduling after delay');

        final retrySuccess =
            await _notificationService.scheduleMeterReadingReminder(frequency);

        if (retrySuccess) {
          logger.i('Successfully rescheduled meter reading reminders on retry');
          return true;
        } else {
          logger.e(
              'Failed to reschedule meter reading reminders even after retry');
        }
      }

      return success;
    } catch (e) {
      logger.e('Error rescheduling meter reading reminders: $e',
          details: e.toString());
      return false;
    }
  }

  /// Load notifications from the service
  Future<void> _loadNotifications() async {
    try {
      _notifications = await _notificationService.getNotifications();
      notifyListeners();
    } catch (e) {
      logger.e('Error loading notifications: $e');
    }
  }

  /// Add a notification
  Future<void> addNotification({
    required String title,
    required String message,
    required NotificationPriority priority,
    String? actionType,
  }) async {
    try {
      await initialize();

      await _notificationService.addNotification(
        title: title,
        message: message,
        priority: priority,
        actionType: actionType,
      );

      await _loadNotifications();
    } catch (e) {
      logger.e('Error adding notification: $e');
    }
  }

  /// Mark a notification as read
  Future<void> markAsRead(String id) async {
    try {
      await initialize();
      await _notificationService.markAsRead(id);
      await _loadNotifications();
    } catch (e) {
      logger.e('Error marking notification as read: $e');
    }
  }

  /// Mark all notifications as read
  Future<void> markAllAsRead() async {
    try {
      await initialize();
      await _notificationService.markAllAsRead();
      await _loadNotifications();
    } catch (e) {
      logger.e('Error marking all notifications as read: $e');
    }
  }

  /// Remove a notification
  Future<void> removeNotification(String id) async {
    try {
      await initialize();
      await _notificationService.removeNotification(id);
      await _loadNotifications();
    } catch (e) {
      logger.e('Error removing notification: $e');
    }
  }

  /// Clear all notifications
  Future<void> clearAll() async {
    try {
      await initialize();
      await _notificationService.clearAll();
      await _loadNotifications();
    } catch (e) {
      logger.e('Error clearing all notifications: $e');
    }
  }

  /// Refresh notifications
  Future<void> refresh() async {
    try {
      await _loadNotifications();
    } catch (e) {
      logger.e('Error refreshing notifications: $e');
    }
  }

  /// Check if notifications are enabled
  Future<bool> areNotificationsEnabled() async {
    try {
      return await _notificationService.areNotificationsEnabled();
    } catch (e) {
      logger.e('Error checking if notifications are enabled: $e');
      return false;
    }
  }

  /// Enable or disable notifications
  /// If enabling notifications, this will also request permission if needed
  Future<void> setNotificationsEnabled(bool enabled,
      [BuildContext? context]) async {
    try {
      // If we're enabling notifications, check and request permissions if needed
      if (enabled) {
        // First check if we already have permission
        final permissionsGranted =
            await NotificationHelper().checkNotificationPermissions();

        // Store the context for later use
        final BuildContext? originalContext = context;

        // If permissions are not granted and we have a context, show rationale dialog
        if (!permissionsGranted && originalContext != null) {
          // Check if the context is still valid
          if (originalContext.mounted) {
            // Show a rationale dialog explaining why we need notification permissions
            final shouldRequest =
                await _showNotificationPermissionRationaleDialog(
                    originalContext);

            // If user agrees, request permissions
            if (shouldRequest) {
              final permissionResult =
                  await NotificationHelper().requestPermissions();

              // If permission request failed, log it but still enable the setting
              // (the actual notifications won't work, but the user's preference is saved)
              if (!permissionResult) {
                logger.w(
                    'User enabled notifications but permission request failed');
              }
            }
          }
        } else if (!permissionsGranted) {
          // No context provided, just request permissions directly
          await NotificationHelper().requestPermissions();
        }
      }

      // Update the setting regardless of permission status
      // (this stores the user's preference)
      await _notificationService.setNotificationsEnabled(enabled);
      notifyListeners();
    } catch (e) {
      logger.e('Error setting notifications enabled: $e');
    }
  }

  /// Show a dialog explaining why notification permissions are needed
  Future<bool> _showNotificationPermissionRationaleDialog(
      BuildContext context) async {
    // Store the context for later use
    final BuildContext originalContext = context;

    // Check if the context is still valid
    if (!originalContext.mounted) return false;

    bool result = false;

    await showDialog(
      context: originalContext,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Enable Notifications'),
          content: const SingleChildScrollView(
            child: ListBody(
              children: <Widget>[
                Text(
                  'Lekky needs permission to send notifications for:',
                  style: TextStyle(fontSize: 16),
                ),
                SizedBox(height: 12),
                Text(
                  '• Meter reading reminders',
                  style: TextStyle(fontSize: 16),
                ),
                Text(
                  '• Low balance alerts',
                  style: TextStyle(fontSize: 16),
                ),
                Text(
                  '• Time to top-up reminders',
                  style: TextStyle(fontSize: 16),
                ),
                SizedBox(height: 12),
                Text(
                  'Without this permission, these features won\'t work even if enabled in settings.',
                  style: TextStyle(fontSize: 16),
                ),
              ],
            ),
          ),
          actions: <Widget>[
            TextButton(
              child: const Text('Not Now'),
              onPressed: () {
                Navigator.of(context).pop();
                result = false;
              },
            ),
            TextButton(
              child: const Text('Allow'),
              onPressed: () {
                Navigator.of(context).pop();
                result = true;
              },
            ),
          ],
        );
      },
    );

    return result;
  }

  /// Check if low balance alerts are enabled
  Future<bool> areLowBalanceAlertsEnabled() async {
    try {
      return await _notificationService.areLowBalanceAlertsEnabled();
    } catch (e) {
      logger.e('Error checking if low balance alerts are enabled: $e');
      return false;
    }
  }

  /// Enable or disable low balance alerts
  Future<void> setLowBalanceAlertsEnabled(bool enabled) async {
    try {
      await _notificationService.setLowBalanceAlertsEnabled(enabled);
      notifyListeners();
    } catch (e) {
      logger.e('Error setting low balance alerts enabled: $e');
    }
  }

  /// Check if top-up alerts are enabled
  Future<bool> areTopUpAlertsEnabled() async {
    try {
      return await _notificationService.areTopUpAlertsEnabled();
    } catch (e) {
      logger.e('Error checking if top-up alerts are enabled: $e');
      return false;
    }
  }

  /// Enable or disable top-up alerts
  Future<void> setTopUpAlertsEnabled(bool enabled) async {
    try {
      await _notificationService.setTopUpAlertsEnabled(enabled);
      notifyListeners();
    } catch (e) {
      logger.e('Error setting top-up alerts enabled: $e');
    }
  }

  /// Check if invalid record alerts are enabled
  Future<bool> areInvalidRecordAlertsEnabled() async {
    try {
      return await _notificationService.areInvalidRecordAlertsEnabled();
    } catch (e) {
      logger.e('Error checking if invalid record alerts are enabled: $e');
      return false;
    }
  }

  /// Enable or disable invalid record alerts
  Future<void> setInvalidRecordAlertsEnabled(bool enabled) async {
    try {
      await _notificationService.setInvalidRecordAlertsEnabled(enabled);
      notifyListeners();
    } catch (e) {
      logger.e('Error setting invalid record alerts enabled: $e');
    }
  }

  /// Show a low balance notification
  Future<void> showLowBalanceNotification({
    required String meterUnit,
    required double balance,
    required double threshold,
  }) async {
    try {
      await initialize();
      await _notificationService.showLowBalanceNotification(
        meterUnit: meterUnit,
        balance: balance,
        threshold: threshold,
      );
      await _loadNotifications();
    } catch (e) {
      logger.e('Error showing low balance notification: $e');
    }
  }

  /// Show a time to top up notification with enhanced information
  Future<void> showTimeToTopUpNotification({
    required String meterUnit,
    required double balance,
    required int daysRemaining,
    ConfidenceLevel? confidenceLevel,
    double? daysSinceLastReading,
  }) async {
    try {
      await initialize();
      await _notificationService.showTimeToTopUpNotification(
        meterUnit: meterUnit,
        balance: balance,
        daysRemaining: daysRemaining,
        confidenceLevel: confidenceLevel,
        daysSinceLastReading: daysSinceLastReading,
      );
      await _loadNotifications();
    } catch (e) {
      logger.e('Error showing time to top up notification: $e');
    }
  }

  /// Queue an invalid record notification for grouping
  /// This stores the notification to be sent later as part of a group
  Future<void> queueInvalidRecordNotification({
    required String message,
    required String date,
  }) async {
    try {
      await initialize();

      await _notificationService.queueInvalidRecordNotification(
        message: message,
        date: date,
      );

      logger.i('Queued invalid record notification for date: $date');
    } catch (e) {
      logger.e('Error queuing invalid record notification: $e',
          details: e.toString());
    }
  }

  /// Show all grouped invalid record notifications
  Future<void> showGroupedInvalidRecordNotifications() async {
    try {
      await initialize();

      await _notificationService.showGroupedInvalidRecordNotifications();

      logger.i('Showed grouped invalid record notifications');
    } catch (e) {
      logger.e('Error showing grouped invalid record notifications: $e',
          details: e.toString());
    }
  }

  /// Show an invalid record notification
  Future<void> showInvalidRecordNotification({
    required String message,
  }) async {
    try {
      await initialize();
      await _notificationService.showInvalidRecordNotification(
        message: message,
      );
      await _loadNotifications();
    } catch (e) {
      logger.e('Error showing invalid record notification: $e');
    }
  }

  /// Check if meter reading reminders are enabled
  Future<bool> areMeterReadingRemindersEnabled() async {
    try {
      return await _notificationService.areMeterReadingRemindersEnabled();
    } catch (e) {
      logger.e('Error checking if meter reading reminders are enabled: $e');
      return false;
    }
  }

  /// Enable or disable meter reading reminders
  Future<void> setMeterReadingRemindersEnabled(bool enabled) async {
    try {
      await _notificationService.setMeterReadingRemindersEnabled(enabled);

      // Fire event after the actual setting change
      EventBus().fire(EventType.reminderSettingsUpdated);
      logger.i(
          'Fired reminderSettingsUpdated event after updating reminder enabled state');

      notifyListeners();
    } catch (e) {
      logger.e('Error setting meter reading reminders enabled: $e');
    }
  }

  /// Check if frequency options are enabled
  Future<bool> isFrequencyOptionsEnabled() async {
    try {
      return await _notificationService.isFrequencyOptionsEnabled();
    } catch (e) {
      logger.e('Error checking if frequency options are enabled: $e');
      return true; // Default to enabled
    }
  }

  /// Enable or disable frequency options
  Future<void> setFrequencyOptionsEnabled(bool enabled) async {
    try {
      await _notificationService.setFrequencyOptionsEnabled(enabled);
      notifyListeners();
    } catch (e) {
      logger.e('Error setting frequency options enabled: $e');
    }
  }

  /// Get the meter reading reminder frequency in days
  Future<int> getMeterReadingReminderFrequency() async {
    try {
      return await _notificationService.getMeterReadingReminderFrequency();
    } catch (e) {
      logger.e('Error getting meter reading reminder frequency: $e');
      return 7; // Default to weekly
    }
  }

  /// Set the meter reading reminder frequency in days
  Future<void> setMeterReadingReminderFrequency(int days) async {
    try {
      await _notificationService.setMeterReadingReminderFrequency(days);

      // Fire event after the actual setting change
      EventBus().fire(EventType.reminderSettingsUpdated);
      logger.i(
          'Fired reminderSettingsUpdated event after updating reminder frequency');

      notifyListeners();
    } catch (e) {
      logger.e('Error setting meter reading reminder frequency: $e');
    }
  }

  /// Get the last date a meter reading reminder was shown
  Future<DateTime?> getLastMeterReadingReminderDate() async {
    try {
      return await _notificationService.getLastMeterReadingReminderDate();
    } catch (e) {
      logger.e('Error getting last meter reading reminder date: $e');
      return null;
    }
  }

  /// Schedule a meter reading reminder based on frequency with enhanced reliability
  Future<bool> scheduleMeterReadingReminder() async {
    try {
      await initialize();

      // Check notification permissions
      final permissionsGranted =
          await NotificationHelper().checkNotificationPermissions();
      if (!permissionsGranted) {
        logger.w(
            'Cannot schedule meter reading reminder: notification permissions not granted');
        return false;
      }

      // Get the reminder frequency
      final frequency = await getMeterReadingReminderFrequency();

      // Schedule the reminder with enhanced reliability
      final success =
          await _notificationService.scheduleMeterReadingReminder(frequency);

      if (success) {
        logger.i('Successfully scheduled meter reading reminder');

        // Get and log the next reminder date for debugging
        final nextReminderDate =
            await _notificationService.getNextMeterReadingReminderDate();
        if (nextReminderDate != null) {
          logger.i(
              'Next meter reading reminder scheduled for: $nextReminderDate');
        }
      } else {
        logger.e('Failed to schedule meter reading reminder');
      }

      return success;
    } catch (e) {
      logger.e('Error scheduling meter reading reminder: $e');
      return false;
    }
  }

  /// Get the next scheduled meter reading reminder date
  Future<DateTime?> getNextMeterReadingReminderDate() async {
    try {
      return await _notificationService.getNextMeterReadingReminderDate();
    } catch (e) {
      logger.e('Error getting next meter reading reminder date: $e');
      return null;
    }
  }

  /// Show a meter reading reminder notification immediately
  Future<void> showMeterReadingReminderNotification() async {
    try {
      await initialize();
      await _notificationService.showMeterReadingReminderNotification();

      // Fire event after the actual notification is sent
      EventBus().fire(EventType.reminderSettingsUpdated);
      logger.i(
          'Fired reminderSettingsUpdated event after sending reminder notification');

      await _loadNotifications();
    } catch (e) {
      logger.e('Error showing meter reading reminder notification: $e');
    }
  }

  /// Get the time of day for meter reading reminders
  Future<ReminderTimeModel> getMeterReadingReminderTime() async {
    try {
      return await _notificationService.getMeterReadingReminderTime();
    } catch (e) {
      logger.e('Error getting meter reading reminder time: $e');
      // Default to 7:00 PM if there's an error
      return const ReminderTimeModel(
        timeOfDay: TimeOfDay(hour: 19, minute: 0),
      );
    }
  }

  /// Set the time of day for meter reading reminders
  Future<void> setMeterReadingReminderTime(ReminderTimeModel time) async {
    try {
      await _notificationService.setMeterReadingReminderTime(time);

      // Fire the event only once after the update
      EventBus().fire(EventType.reminderSettingsUpdated);
      logger.i(
          'Fired reminderSettingsUpdated event after updating reminder time');

      notifyListeners();
    } catch (e) {
      logger.e('Error setting meter reading reminder time: $e');
    }
  }

  /// Show welcome notification for first-time users
  Future<void> showWelcomeNotification() async {
    try {
      await initialize();
      await _notificationService.showWelcomeNotification();
      await _loadNotifications();
    } catch (e) {
      logger.e('Error showing welcome notification: $e');
    }
  }

  /// Show first meter reading reminder for new users
  Future<void> showFirstMeterReadingReminder() async {
    try {
      await initialize();
      await _notificationService.showFirstMeterReadingReminder();
      await _loadNotifications();
    } catch (e) {
      logger.e('Error showing first meter reading reminder: $e');
    }
  }

  /// Schedule first meter reading reminder for 24 hours after setup
  Future<void> scheduleFirstMeterReadingReminder() async {
    try {
      await initialize();
      await _notificationService.scheduleFirstMeterReadingReminder();
    } catch (e) {
      logger.e('Error scheduling first meter reading reminder: $e');
    }
  }

  /// Set flag indicating user has meter readings
  Future<void> setHasMeterReadings(bool hasReadings) async {
    try {
      await initialize();
      await _notificationService.setHasMeterReadings(hasReadings);
    } catch (e) {
      logger.e('Error setting has meter readings flag: $e');
    }
  }

  /// Check if user has any meter readings
  Future<bool> hasMeterReadings() async {
    try {
      await initialize();
      return await _notificationService.hasMeterReadings();
    } catch (e) {
      logger.e('Error checking if user has meter readings: $e');
      return false;
    }
  }

  /// Show notification system activation message
  Future<void> showNotificationSystemActivation() async {
    try {
      await initialize();
      await _notificationService.showNotificationSystemActivation();
      await _loadNotifications();
    } catch (e) {
      logger.e('Error showing notification system activation: $e');
    }
  }

  /// Show a "Check your meter now" notification when projected balance may be below threshold
  Future<void> showCheckMeterNowNotification({
    required String meterUnit,
    required double projectedBalance,
    required double actualBalance,
    required double threshold,
    required double daysSinceLastReading,
    required double averageUsage,
  }) async {
    try {
      await initialize();
      await _notificationService.showCheckMeterNowNotification(
        meterUnit: meterUnit,
        projectedBalance: projectedBalance,
        actualBalance: actualBalance,
        threshold: threshold,
        daysSinceLastReading: daysSinceLastReading,
        averageUsage: averageUsage,
      );
      await _loadNotifications();
    } catch (e) {
      logger.e('Error showing check meter now notification: $e');
    }
  }
}
