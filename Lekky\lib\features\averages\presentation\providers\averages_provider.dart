import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../../../core/providers/data_change_provider.dart';
import '../../../../core/providers/database_provider.dart';
import '../../../../core/utils/logger.dart';
import '../../domain/models/average_state.dart';
import '../../domain/services/average_service.dart';

part 'averages_provider.g.dart';

/// Reactive provider for averages that updates when data changes
@riverpod
class Averages extends _$Averages {
  @override
  Future<AverageState> build() async {
    Logger.info('AveragesProvider: Building averages state');

    // Watch for data changes to trigger recalculation
    final changeCount = ref.watch(dataChangeNotifierProvider);
    Logger.info('AveragesProvider: Data change count: $changeCount');

    try {
      // Calculate fresh averages when data changes
      return await _calculateAverages();
    } catch (e, stackTrace) {
      Logger.error(
          'AveragesProvider: Failed to calculate averages: $e', stackTrace);
      return AverageState.error('Failed to calculate averages: $e');
    }
  }

  /// Calculate averages using existing AverageService
  Future<AverageState> _calculateAverages() async {
    Logger.info('AveragesProvider: Calculating fresh averages');

    try {
      // Get repositories
      final averageRepo = ref.read(averageRepositoryProvider);
      final perReadingRepo = ref.read(perReadingAverageRepositoryProvider);
      final meterReadingRepo = ref.read(meterReadingRepositoryProvider);
      final topUpRepo = ref.read(topUpRepositoryProvider);

      // Use existing AverageService logic with all required repositories
      final averageService = AverageService(
        averageRepository: averageRepo,
        perReadingAverageRepository: perReadingRepo,
        meterReadingRepository: meterReadingRepo,
        topUpRepository: topUpRepo,
      );

      final averageResult = await averageService.getAverages();

      Logger.info(
          'AveragesProvider: Successfully calculated averages - Recent: ${averageResult.recentAverage}, Total: ${averageResult.totalAverage}');

      return AverageState.success(
        recentAverage: averageResult.recentAverage,
        totalAverage: averageResult.totalAverage,
        fromCache: averageResult.fromCache,
        cacheTimestamp: DateTime.now(),
      );
    } catch (e, stackTrace) {
      Logger.error(
          'AveragesProvider: Error calculating averages: $e', stackTrace);
      throw e;
    }
  }

  /// Force refresh averages (for manual refresh actions)
  Future<void> refresh() async {
    Logger.info('AveragesProvider: Manual refresh requested');
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() => _calculateAverages());
  }

  /// Invalidate cache and recalculate
  Future<void> invalidateAndRefresh() async {
    Logger.info('AveragesProvider: Invalidate and refresh requested');

    try {
      // Clear any existing cache in AverageService
      final averageRepo = ref.read(averageRepositoryProvider);
      final perReadingRepo = ref.read(perReadingAverageRepositoryProvider);
      final meterReadingRepo = ref.read(meterReadingRepositoryProvider);
      final topUpRepo = ref.read(topUpRepositoryProvider);

      final averageService = AverageService(
        averageRepository: averageRepo,
        perReadingAverageRepository: perReadingRepo,
        meterReadingRepository: meterReadingRepo,
        topUpRepository: topUpRepo,
      );

      // Force cache invalidation by updating averages
      await averageService.updateAverages();

      // Refresh state
      await refresh();
    } catch (e, stackTrace) {
      Logger.error(
          'AveragesProvider: Failed to invalidate and refresh: $e', stackTrace);
      state = AsyncValue.error(e, stackTrace);
    }
  }
}
