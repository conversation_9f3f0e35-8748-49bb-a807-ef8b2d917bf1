// File: lib/features/notifications/data/migration/auto_migration_manager.dart

import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../di/notification_service_locator.dart';
import 'storage_migration_service.dart';

/// Manager for handling automatic migration during app startup
class AutoMigrationManager {
  // Keys for storing migration status
  static const String _autoMigrationEnabledKey =
      'notification_auto_migration_enabled';
  static const String _autoMigrationLastVersionKey =
      'notification_auto_migration_last_version';
  static const String _autoMigrationLastAttemptKey =
      'notification_auto_migration_last_attempt';

  // Current app version
  static const String _currentVersion =
      '1.0.1'; // Should match the app version in pubspec.yaml

  // Singleton instance
  static final AutoMigrationManager _instance =
      AutoMigrationManager._internal();

  // Factory constructor
  factory AutoMigrationManager() => _instance;

  // Private constructor
  AutoMigrationManager._internal();

  /// Check if automatic migration is enabled
  Future<bool> isAutoMigrationEnabled() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_autoMigrationEnabledKey) ??
        true; // Enabled by default
  }

  /// Set automatic migration enabled/disabled
  Future<void> setAutoMigrationEnabled(bool enabled) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_autoMigrationEnabledKey, enabled);
  }

  /// Check if migration should be attempted at startup
  Future<bool> shouldAttemptMigrationAtStartup() async {
    try {
      // Check if auto migration is enabled
      final isEnabled = await isAutoMigrationEnabled();
      if (!isEnabled) {
        debugPrint('AutoMigrationManager: Auto migration is disabled');
        return false;
      }

      // Get migration service
      final migrationService =
          notificationServiceLocator<StorageMigrationService>();

      // Check if migration is needed
      final isMigrationNeeded = await migrationService.isMigrationNeeded();
      if (!isMigrationNeeded) {
        debugPrint('AutoMigrationManager: Migration is not needed');
        return false;
      }

      // Check if app version has changed since last migration attempt
      final prefs = await SharedPreferences.getInstance();
      final lastVersion = prefs.getString(_autoMigrationLastVersionKey) ?? '';

      // If version has changed, attempt migration
      if (lastVersion != _currentVersion) {
        debugPrint(
            'AutoMigrationManager: App version changed, migration should be attempted');
        return true;
      }

      // Check when the last attempt was made
      final lastAttemptTimestamp =
          prefs.getInt(_autoMigrationLastAttemptKey) ?? 0;
      final lastAttemptDate =
          DateTime.fromMillisecondsSinceEpoch(lastAttemptTimestamp);
      final now = DateTime.now();

      // If last attempt was more than 7 days ago, try again
      if (now.difference(lastAttemptDate).inDays > 7) {
        debugPrint(
            'AutoMigrationManager: Last attempt was more than 7 days ago, migration should be attempted');
        return true;
      }

      debugPrint('AutoMigrationManager: Migration not needed at this time');
      return false;
    } catch (e) {
      debugPrint('AutoMigrationManager: Error checking migration status: $e');
      return false;
    }
  }

  /// Execute migration in background
  Future<bool> executeBackgroundMigration() async {
    try {
      // Update last attempt timestamp
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(
          _autoMigrationLastAttemptKey, DateTime.now().millisecondsSinceEpoch);
      await prefs.setString(_autoMigrationLastVersionKey, _currentVersion);

      // Get migration service
      final migrationService =
          notificationServiceLocator<StorageMigrationService>();

      // Execute migration
      debugPrint('AutoMigrationManager: Starting background migration');
      final result = await migrationService.executeMigration(
        onStatusUpdate: (status) {
          debugPrint('AutoMigrationManager: Migration status: $status');
        },
      );

      debugPrint(
          'AutoMigrationManager: Background migration ${result ? 'completed successfully' : 'failed'}');
      return result;
    } catch (e) {
      debugPrint('AutoMigrationManager: Error during background migration: $e');
      return false;
    }
  }

  /// Initialize the migration manager
  /// Call this during app startup to check and perform migration if needed
  static Future<void> initialize() async {
    final manager = AutoMigrationManager();

    // Check if migration should be attempted
    final shouldAttempt = await manager.shouldAttemptMigrationAtStartup();
    if (shouldAttempt) {
      // Execute migration in background
      manager.executeBackgroundMigration();
    }
  }
}
