import 'package:freezed_annotation/freezed_annotation.dart';

part 'preference_state.freezed.dart';

/// Immutable state for user preferences
@freezed
class PreferenceState with _$PreferenceState {
  const factory PreferenceState({
    /// Currency symbol (£, €, $, etc.)
    @Default('£') String currencySymbol,

    /// Language code
    @Default('en') String languageCode,

    /// Whether app has been set up
    @Default(false) bool isSetupComplete,

    /// Whether welcome screen has been shown
    @Default(false) bool hasShownWelcome,

    /// Alert threshold for low balance
    @Default(5.0) double alertThreshold,

    /// Days in advance for notifications
    @Default(2) int daysInAdvance,

    /// Date format preference
    @Default('dd-MM-yyyy') String dateFormat,

    /// Whether to show time with date
    @Default(true) bool showTimeWithDate,

    /// Theme mode preference
    @Default('system') String themeMode,

    /// Whether notifications are enabled
    @Default(false) bool notificationsEnabled,

    /// Whether low balance alerts are enabled
    @Default(false) bool lowBalanceAlertsEnabled,

    /// Whether time to top up alerts are enabled
    @Default(false) bool timeToTopUpAlertsEnabled,

    /// Whether invalid record alerts are enabled
    @Default(false) bool invalidRecordAlertsEnabled,

    /// Initial meter reading value
    @Default(0.0) double initialMeterReading,

    /// Whether to use 24-hour time format
    @Default(true) bool use24HourFormat,

    /// Whether to show decimal places
    @Default(true) bool showDecimalPlaces,

    /// Number of decimal places to show
    @Default(2) int decimalPlaces,

    /// Whether this is the first app run
    @Default(true) bool isFirstRun,

    /// Whether first run flow is complete
    @Default(false) bool firstRunCompleted,

    /// Whether welcome notification was triggered
    @Default(false) bool welcomeNotificationTriggered,
  }) = _PreferenceState;

  /// Initial preference state with default values
  factory PreferenceState.initial() => const PreferenceState();
}

/// Extension methods for PreferenceState
extension PreferenceStateX on PreferenceState {
  /// Get formatted currency display
  String formatCurrency(double amount) {
    if (showDecimalPlaces) {
      return '$currencySymbol${amount.toStringAsFixed(decimalPlaces)}';
    } else {
      return '$currencySymbol${amount.toStringAsFixed(0)}';
    }
  }

  /// Check if setup is required
  bool get requiresSetup => !isSetupComplete;

  /// Check if welcome should be shown (legacy method)
  bool get shouldShowWelcome => !hasShownWelcome && isSetupComplete;

  /// Check if welcome notification should be triggered
  bool get shouldTriggerWelcomeNotification =>
      isSetupComplete && !welcomeNotificationTriggered;

  /// Check if this is first time after setup
  bool get isFirstTimeAfterSetup => isSetupComplete && !firstRunCompleted;

  /// Check if app is in first run flow
  bool get isInFirstRunFlow => isFirstRun && !firstRunCompleted;
}
