// File: lib/core/theme/app_button_styles.dart
import 'package:flutter/material.dart';
import 'app_colors.dart';
import 'app_text_styles.dart';
import '../extensions/context_extensions.dart';

/// Standardized button styles for the app
class AppButtonStyles {
  // Private constructor to prevent instantiation
  AppButtonStyles._();

  /// Primary button style
  static ButtonStyle primaryButton(BuildContext context) {
    final isDarkMode = context.isDarkMode;
    return ElevatedButton.styleFrom(
      backgroundColor: isDarkMode ? AppColors.primaryDark : AppColors.primary,
      foregroundColor: AppColors.onPrimary,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
    );
  }

  /// Cancel button style (outlined)
  static ButtonStyle cancelButton(BuildContext context) {
    final isDarkMode = context.isDarkMode;
    return OutlinedButton.styleFrom(
      foregroundColor: isDarkMode ? AppColors.primaryDark : AppColors.primary,
      side: BorderSide(
        color: isDarkMode ? AppColors.primaryDark : AppColors.primary,
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
    );
  }

  /// Delete button style (red)
  static ButtonStyle deleteButton(BuildContext context) {
    return ElevatedButton.styleFrom(
      backgroundColor: AppColors.error,
      foregroundColor: AppColors.onError,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
    );
  }

  /// Edit button style (orange)
  static ButtonStyle editButton(BuildContext context) {
    return ElevatedButton.styleFrom(
      backgroundColor: AppColors.costTab,
      foregroundColor: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
    );
  }
}
