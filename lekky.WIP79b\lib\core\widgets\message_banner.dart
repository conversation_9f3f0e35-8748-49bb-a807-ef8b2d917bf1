// File: lib/core/widgets/message_banner.dart
import 'package:flutter/material.dart';
import '../theme/app_colors.dart';
import '../theme/app_text_styles.dart';
import '../extensions/context_extensions.dart';

/// A reusable message banner widget that displays a message with proper theming
class MessageBanner extends StatelessWidget {
  /// The message to display
  final String message;
  
  /// The height of the banner
  final double height;

  /// Creates a message banner
  const MessageBanner({
    Key? key,
    required this.message,
    this.height = 32,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isDarkMode = context.isDarkMode;

    return Container(
      color: isDarkMode ? AppColors.messageBannerDark : AppColors.messageBannerLight,
      height: height,
      child: Center(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Text(
            message,
            style: AppTextStyles.bodyMedium.copyWith(
              color: isDarkMode ? AppColors.messageTextDark : AppColors.messageTextLight,
              height: 1.0,
              fontSize: 13.0,
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ),
    );
  }
}
