// File: lib/core/platform/timezone/timezone_adapter.dart

/// Interface for platform-specific timezone functionality
abstract class TimezoneAdapter {
  /// Initialize the timezone adapter
  Future<void> initialize();

  /// Get the current timezone
  String getCurrentTimezone();

  /// Get the last known timezone
  Future<String?> getLastKnownTimezone();

  /// Save the current timezone
  Future<void> saveCurrentTimezone(String timezone);

  /// Check if the timezone has changed since last check
  Future<bool> hasTimezoneChanged();

  /// Convert a local DateTime to UTC
  DateTime convertToUtc(DateTime localDateTime);

  /// Convert a UTC DateTime to local timezone
  DateTime convertToLocal(DateTime utcDateTime);

  /// Check if a DateTime is in DST (Daylight Saving Time)
  bool isInDaylightSavingTime(DateTime dateTime);

  /// Get the timezone offset in hours for a specific date
  double getTimezoneOffset(DateTime dateTime);

  /// Get the IANA timezone name
  String getIanaTimezoneName();

  /// Get the timezone abbreviation (e.g., "EST", "PST")
  String getTimezoneAbbreviation(DateTime dateTime);

  /// Register a callback for timezone changes
  void registerTimezoneChangeCallback(void Function() callback);

  /// Unregister a timezone change callback
  void unregisterTimezoneChangeCallback(void Function() callback);

  /// Get a DateTime adjusted for timezone changes
  DateTime getAdjustedDateTime(DateTime original);

  /// Validate if a scheduled time is still valid after timezone changes
  bool isScheduledTimeValid(DateTime scheduledTime);

  /// Reschedule a DateTime after timezone changes
  DateTime rescheduleAfterTimezoneChange(DateTime original);
}
