// File: lib/features/notifications/presentation/widgets/permission_education_dialog.dart

import 'package:flutter/material.dart';

/// A dialog that explains the benefits of enabling notifications
/// before requesting permission
class PermissionEducationDialog extends StatelessWidget {
  final VoidCallback onEnablePressed;
  final VoidCallback onNotNowPressed;

  const PermissionEducationDialog({
    Key? key,
    required this.onEnablePressed,
    required this.onNotNowPressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Header
            const Text(
              'Enable Notifications',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),

            // Notification example
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.shade200,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.notifications_active,
                    color: Colors.blue,
                    size: 40,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: const [
                        Text(
                          'Meter Reading Reminder',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        SizedBox(height: 4),
                        Text(
                          'Time to submit your meter reading',
                          style: TextStyle(
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 20),

            // Benefits
            const Text(
              'Why enable notifications?',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 8),
            _buildBenefitItem(
              icon: Icons.access_time,
              text: 'Get timely reminders for meter readings',
            ),
            const SizedBox(height: 8),
            _buildBenefitItem(
              icon: Icons.money_off,
              text: 'Avoid estimated bills with accurate readings',
            ),
            const SizedBox(height: 8),
            _buildBenefitItem(
              icon: Icons.notifications_active,
              text: 'Never miss a reading date again',
            ),
            const SizedBox(height: 8),
            _buildBenefitItem(
              icon: Icons.privacy_tip,
              text: 'We only send essential notifications',
            ),
            const SizedBox(height: 20),

            // Action buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                TextButton(
                  onPressed: onNotNowPressed,
                  child: const Text('Not Now'),
                ),
                ElevatedButton(
                  onPressed: onEnablePressed,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 12,
                    ),
                  ),
                  child: const Text('Enable Notifications'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBenefitItem({
    required IconData icon,
    required String text,
  }) {
    return Row(
      children: [
        Icon(
          icon,
          color: Colors.green,
          size: 20,
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            text,
            style: const TextStyle(fontSize: 14),
          ),
        ),
      ],
    );
  }
}

/// Show the permission education dialog
Future<bool> showPermissionEducationDialog(BuildContext context) async {
  return await showDialog<bool>(
        context: context,
        barrierDismissible: false,
        builder: (context) => PermissionEducationDialog(
          onEnablePressed: () {
            Navigator.of(context).pop(true);
          },
          onNotNowPressed: () {
            Navigator.of(context).pop(false);
          },
        ),
      ) ??
      false;
}
