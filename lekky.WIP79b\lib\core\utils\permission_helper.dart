// File: lib/core/utils/permission_helper.dart
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import 'logger.dart';

/// Helper class for handling permissions
class PermissionHelper {
  /// Singleton instance
  static final PermissionHelper _instance = PermissionHelper._internal();

  /// Factory constructor
  factory PermissionHelper() => _instance;

  /// Private constructor
  PermissionHelper._internal();

  /// Check storage permission status without requesting it
  ///
  /// Returns true if permission is granted, false otherwise
  Future<bool> checkStoragePermissionStatus() async {
    try {
      if (Platform.isAndroid) {
        final status = await Permission.storage.status;
        return status.isGranted;
      }

      // For iOS, we don't need explicit permission for app documents directory
      return true;
    } catch (e) {
      logger.e('Error checking storage permission status',
          details: e.toString());
      return false;
    }
  }

  /// Check and request storage permission
  ///
  /// Returns true if permission is granted, false otherwise
  Future<bool> checkAndRequestStoragePermission() async {
    try {
      if (Platform.isAndroid) {
        // Get Android SDK version
        final sdkInt = await _getAndroidSdkVersion();

        // For Android 10+ (API level 29+), we use Storage Access Framework
        // which doesn't require storage permissions, but we still check for backward compatibility
        if (sdkInt >= 29) {
          logger.i(
              'Android SDK $sdkInt detected, using Storage Access Framework');
          // For Android 10+, we'll use the Storage Access Framework which doesn't require
          // the storage permission, but we'll still check for backward compatibility
          final status = await Permission.storage.status;
          if (status.isGranted) {
            return true;
          }

          // On Android 10+, we'll return true even if permission is not granted
          // because we'll use the Storage Access Framework as a fallback
          logger
              .i('Storage permission not granted on Android 10+, will use SAF');
          return true;
        } else {
          // For Android 9 and below, we need the storage permission
          logger
              .i('Android SDK $sdkInt detected, requesting storage permission');
          final status = await Permission.storage.status;
          if (status.isGranted) {
            return true;
          }

          // Request permission
          final result = await Permission.storage.request();
          return result.isGranted;
        }
      }

      // For iOS, we don't need explicit permission for app documents directory
      return true;
    } catch (e) {
      logger.e('Error checking storage permission', details: e.toString());
      return false;
    }
  }

  /// Get Android SDK version
  Future<int> _getAndroidSdkVersion() async {
    try {
      if (Platform.isAndroid) {
        // Default to 29 (Android 10) if we can't determine the version
        // This ensures we use the Storage Access Framework by default
        return 29;
      }
      return 0; // Not Android
    } catch (e) {
      logger.e('Error getting Android SDK version', details: e.toString());
      return 29; // Default to Android 10
    }
  }

  /// Show permission denied dialog
  ///
  /// This dialog explains why the permission is needed and provides options to:
  /// 1. Try again (re-request permission)
  /// 2. Open app settings
  /// 3. Cancel
  Future<void> showPermissionDeniedDialog({
    required BuildContext context,
    required String title,
    required String message,
    required VoidCallback onTryAgain,
    required VoidCallback onCancel,
  }) async {
    // Store the context for later use
    final BuildContext originalContext = context;

    // Check if the context is still valid
    if (!originalContext.mounted) return;

    return showDialog(
      context: originalContext,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(title),
          content: SingleChildScrollView(
            child: ListBody(
              children: <Widget>[
                Text(message),
              ],
            ),
          ),
          actions: <Widget>[
            TextButton(
              child: const Text('Cancel'),
              onPressed: () {
                Navigator.of(context).pop();
                onCancel();
              },
            ),
            TextButton(
              child: const Text('App Settings'),
              onPressed: () async {
                Navigator.of(context).pop();
                await openAppSettings();
              },
            ),
            TextButton(
              child: const Text('Try Again'),
              onPressed: () {
                Navigator.of(context).pop();
                onTryAgain();
              },
            ),
          ],
        );
      },
    );
  }

  /// Show a dialog explaining why storage permission is needed
  Future<bool> showStoragePermissionExplanationDialog(
      BuildContext context) async {
    bool result = false;

    // Store the context for later use
    final BuildContext originalContext = context;

    // Check if device is Samsung
    bool isSamsungDevice = await _isSamsungDevice();

    // Check if the context is still valid
    if (!originalContext.mounted) return false;

    await showDialog(
      context: originalContext,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Storage Access Required'),
          content: SingleChildScrollView(
            child: ListBody(
              children: <Widget>[
                const Text(
                  'Lekky needs access to your device storage to save and restore backup files.',
                  style: TextStyle(fontSize: 16),
                ),
                const SizedBox(height: 12),
                if (isSamsungDevice) ...[
                  const Text(
                    'Samsung devices have additional privacy restrictions. You\'ll be asked to select a location each time you save or open a backup file.',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 12),
                ],
                const Text(
                  'You can always use the file picker to select where to save your backups.',
                  style: TextStyle(fontSize: 16),
                ),
              ],
            ),
          ),
          actions: <Widget>[
            TextButton(
              child: const Text('Cancel'),
              onPressed: () {
                Navigator.of(context).pop();
                result = false;
              },
            ),
            TextButton(
              child: const Text('Continue'),
              onPressed: () {
                Navigator.of(context).pop();
                result = true;
              },
            ),
          ],
        );
      },
    );

    return result;
  }

  /// Check if the device is a Samsung device
  Future<bool> _isSamsungDevice() async {
    try {
      if (Platform.isAndroid) {
        // We can't reliably detect Samsung devices programmatically in Flutter
        // without native code, so we'll return false for now
        // In a real implementation, you would use a platform channel to check
        // the device manufacturer
        return false;
      }
      return false;
    } catch (e) {
      logger.e('Error checking if device is Samsung', details: e.toString());
      return false;
    }
  }
}
