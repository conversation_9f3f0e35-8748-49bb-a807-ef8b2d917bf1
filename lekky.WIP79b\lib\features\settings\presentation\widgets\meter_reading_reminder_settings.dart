// File: lib/features/settings/presentation/widgets/meter_reading_reminder_settings.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../../../../core/models/reminder_time_model.dart';
import '../../../../core/providers/notification_provider.dart';
import '../../../../core/utils/event_bus.dart';
import '../../../../core/utils/logger.dart';
import '../../../../core/utils/notification_helper.dart';

/// A widget for configuring meter reading reminder settings
class MeterReadingReminderSettings extends StatefulWidget {
  final bool defaultOn;

  const MeterReadingReminderSettings({
    Key? key,
    this.defaultOn = false,
  }) : super(key: key);

  @override
  State<MeterReadingReminderSettings> createState() =>
      _MeterReadingReminderSettingsState();
}

class _MeterReadingReminderSettingsState
    extends State<MeterReadingReminderSettings> {
  final logger = Logger();
  bool _remindersEnabled = false;
  int _reminderFrequency = 7; // Default to weekly
  DateTime? _lastReminderDate;
  DateTime? _nextReminderDate;
  ReminderTimeModel _reminderTime = const ReminderTimeModel(
    timeOfDay: TimeOfDay(hour: 19, minute: 0), // Default to 7:00 PM
  );
  bool _isLoading = true;
  bool _notificationsPermissionGranted = false;

  @override
  void initState() {
    super.initState();
    _loadSettings();

    // If defaultOn is true, we'll set the reminders to enabled
    // after loading the settings
    if (widget.defaultOn) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _enableReminders();
      });
    }
  }

  // Helper method to enable reminders with default settings
  Future<void> _enableReminders() async {
    if (!_remindersEnabled) {
      final notificationProvider =
          Provider.of<NotificationProvider>(context, listen: false);

      await notificationProvider.setMeterReadingRemindersEnabled(true);
      await notificationProvider.setMeterReadingReminderFrequency(7); // Weekly

      // Set default time to 7:00 PM
      const defaultTime = ReminderTimeModel(
        timeOfDay: TimeOfDay(hour: 19, minute: 0),
      );
      await notificationProvider.setMeterReadingReminderTime(defaultTime);

      setState(() {
        _remindersEnabled = true;
        _reminderFrequency = 7;
        _reminderTime = defaultTime;
      });
    }
  }

  Future<void> _loadSettings() async {
    final notificationProvider =
        Provider.of<NotificationProvider>(context, listen: false);

    setState(() {
      _isLoading = true;
    });

    final remindersEnabled =
        await notificationProvider.areMeterReadingRemindersEnabled();
    final reminderFrequency =
        await notificationProvider.getMeterReadingReminderFrequency();
    final lastReminderDate =
        await notificationProvider.getLastMeterReadingReminderDate();
    final reminderTime =
        await notificationProvider.getMeterReadingReminderTime();

    // Check if notifications are enabled and permissions are granted
    final notificationsEnabled =
        await notificationProvider.areNotificationsEnabled();
    // Use NotificationHelper directly to check permissions
    final permissionsGranted =
        await NotificationHelper().checkNotificationPermissions();

    // Get the next scheduled reminder date if reminders are enabled
    DateTime? nextReminderDate;
    if (remindersEnabled && notificationsEnabled && permissionsGranted) {
      nextReminderDate =
          await notificationProvider.getNextMeterReadingReminderDate();
    }

    setState(() {
      _remindersEnabled = remindersEnabled;
      _reminderFrequency = reminderFrequency;
      _lastReminderDate = lastReminderDate;
      _reminderTime = reminderTime;
      _notificationsPermissionGranted = permissionsGranted;
      _nextReminderDate = nextReminderDate;
      _isLoading = false;
    });
  }

  // Methods for showing dialog and building frequency options removed
  // as we now show the options directly in the settings screen

  /// Request notification permission and show appropriate dialogs
  Future<void> _requestNotificationPermission() async {
    try {
      // Request notification permissions
      final granted = await NotificationHelper().requestPermissions();

      // Check if permission is permanently denied
      final isPermanentlyDenied = await NotificationHelper()
          .isNotificationPermissionPermanentlyDenied();

      if (mounted) {
        if (isPermanentlyDenied) {
          // Show dialog to open app settings
          _showPermissionPermanentlyDeniedDialog();
        } else if (!granted) {
          // Show dialog to explain why notifications are needed
          _showPermissionRequiredDialog();
        }

        // Reload settings to update UI
        await _loadSettings();
      }
    } catch (e) {
      logger.e('Error requesting notification permission',
          details: e.toString());
      if (mounted) {
        await _loadSettings();
      }
    }
  }

  /// Show dialog when permission is permanently denied
  void _showPermissionPermanentlyDeniedDialog() {
    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: const Text('Permission Required'),
        content: const Text(
          'Notifications permission is permanently denied. '
          'Please open app settings and enable notifications.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(dialogContext),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(dialogContext);
              NotificationHelper().openNotificationSettings();
            },
            child: const Text('Open Settings'),
          ),
        ],
      ),
    );
  }

  /// Show dialog when permission is required
  void _showPermissionRequiredDialog() {
    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: const Text('Permission Required'),
        content: const Text(
          'Notifications permission is required to send meter reading reminders. '
          'Please enable notifications to receive reminders.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(dialogContext),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final notificationProvider = Provider.of<NotificationProvider>(context);
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    if (_isLoading) {
      return Container(
        decoration: BoxDecoration(
          color: isDarkMode ? Colors.grey[850] : Colors.grey[100],
          borderRadius: BorderRadius.circular(8),
        ),
        padding: const EdgeInsets.all(16),
        child: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Main reminder toggle
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
          child: Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Enable Meter Reminders',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Text(
                      _remindersEnabled
                          ? 'You will receive reminders to record your meter readings'
                          : 'You will not receive any reminders',
                      style: TextStyle(
                        fontSize: 12,
                        color: Theme.of(context).brightness == Brightness.dark
                            ? Colors.grey
                            : Colors.black,
                      ),
                    ),
                  ],
                ),
              ),
              Transform.scale(
                scale: 0.8, // Scale down the switch by 20%
                child: Switch(
                  value: _remindersEnabled,
                  onChanged: (value) async {
                    await notificationProvider
                        .setMeterReadingRemindersEnabled(value);
                    setState(() {
                      _remindersEnabled = value;
                    });
                  },
                  activeColor: Theme.of(context).colorScheme.primary,
                  materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                ),
              ),
            ],
          ),
        ),

        // Show reminder options when reminders are enabled
        if (_remindersEnabled) ...[
          const Divider(height: 1, thickness: 0.5),
          Padding(
            padding: const EdgeInsets.all(12.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Select frequency:',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 8),
                // Organize frequency options in 2 columns
                Row(
                  children: [
                    // Left column
                    Expanded(
                      child: Column(
                        children: [
                          // Daily option
                          RadioListTile<int>(
                            title: const Text('Daily'),
                            value: 1,
                            groupValue: _reminderFrequency,
                            onChanged: (value) async {
                              if (value != null) {
                                await notificationProvider
                                    .setMeterReadingReminderFrequency(value);
                                setState(() {
                                  _reminderFrequency = value;
                                });
                                // Fire event after actual setting change
                                EventBus()
                                    .fire(EventType.reminderSettingsUpdated);
                              }
                            },
                            dense: true,
                            contentPadding: EdgeInsets.zero,
                          ),
                          // Bi-weekly option
                          RadioListTile<int>(
                            title: const Text('Bi-weekly'),
                            value: 14,
                            groupValue: _reminderFrequency,
                            onChanged: (value) async {
                              if (value != null) {
                                await notificationProvider
                                    .setMeterReadingReminderFrequency(value);
                                setState(() {
                                  _reminderFrequency = value;
                                });
                                // Fire event after actual setting change
                                EventBus()
                                    .fire(EventType.reminderSettingsUpdated);
                              }
                            },
                            dense: true,
                            contentPadding: EdgeInsets.zero,
                          ),
                        ],
                      ),
                    ),
                    // Right column
                    Expanded(
                      child: Column(
                        children: [
                          // Weekly option
                          RadioListTile<int>(
                            title: const Text('Weekly'),
                            value: 7,
                            groupValue: _reminderFrequency,
                            onChanged: (value) async {
                              if (value != null) {
                                await notificationProvider
                                    .setMeterReadingReminderFrequency(value);
                                setState(() {
                                  _reminderFrequency = value;
                                });
                                // Fire event after actual setting change
                                EventBus()
                                    .fire(EventType.reminderSettingsUpdated);
                              }
                            },
                            dense: true,
                            contentPadding: EdgeInsets.zero,
                          ),
                          // Monthly option
                          RadioListTile<int>(
                            title: const Text('Monthly'),
                            value: 30,
                            groupValue: _reminderFrequency,
                            onChanged: (value) async {
                              if (value != null) {
                                await notificationProvider
                                    .setMeterReadingReminderFrequency(value);
                                setState(() {
                                  _reminderFrequency = value;
                                });
                                // Fire event after actual setting change
                                EventBus()
                                    .fire(EventType.reminderSettingsUpdated);
                              }
                            },
                            dense: true,
                            contentPadding: EdgeInsets.zero,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 16),
                const Text(
                  'Select reminder date and time:',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 8),

                // Date and time selector
                ListTile(
                  title: const Text('Reminder time'),
                  subtitle: Text(_reminderTime.format(context)),
                  trailing: const Icon(Icons.calendar_today),
                  dense: true,
                  contentPadding: EdgeInsets.zero,
                  onTap: () async {
                    // Use local state management instead of global events
                    // for UI state during picker interactions

                    // Capture context before async gap
                    final currentContext = context;

                    // First, show date picker if frequency is not daily
                    DateTime? selectedDate;
                    if (_reminderFrequency > 1) {
                      // Show date picker for non-daily frequency
                      selectedDate = await showDatePicker(
                        context: currentContext,
                        initialDate:
                            _reminderTime.selectedDate ?? DateTime.now(),
                        firstDate: DateTime.now(),
                        lastDate: DateTime.now().add(const Duration(days: 365)),
                      );

                      // If user cancels date selection, abort the whole process
                      if (selectedDate == null || !mounted) {
                        return;
                      }
                    }

                    // Check if still mounted before continuing
                    if (!mounted) return;

                    // Then show time picker
                    final TimeOfDay? pickedTime = await showTimePicker(
                      context: currentContext,
                      initialTime: _reminderTime.timeOfDay,
                      builder: (BuildContext context, Widget? child) {
                        return MediaQuery(
                          data: MediaQuery.of(context).copyWith(
                            alwaysUse24HourFormat:
                                MediaQuery.of(context).alwaysUse24HourFormat,
                          ),
                          child: child!,
                        );
                      },
                    );

                    // If a new time was selected
                    if (pickedTime != null) {
                      // Create new reminder time model with selected date and time
                      final newReminderTime = ReminderTimeModel(
                        timeOfDay: pickedTime,
                        selectedDate:
                            selectedDate ?? _reminderTime.selectedDate,
                      );

                      // Update the reminder time
                      await notificationProvider
                          .setMeterReadingReminderTime(newReminderTime);

                      // Update the local state
                      if (mounted) {
                        setState(() {
                          _reminderTime = newReminderTime;
                        });

                        // Fire event only once after the actual setting change
                        EventBus().fire(EventType.reminderSettingsUpdated);
                        logger.i(
                            'Fired reminderSettingsUpdated event after updating time');
                      }
                    }
                  },
                ),
              ],
            ),
          ),
          // Show notification status
          const Divider(height: 1, thickness: 0.5),
          Padding(
            padding:
                const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Notification Status',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Row(
                        children: [
                          Icon(
                            _notificationsPermissionGranted
                                ? Icons.check_circle
                                : Icons.error,
                            size: 16,
                            color: _notificationsPermissionGranted
                                ? Colors.green
                                : Colors.red,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            _notificationsPermissionGranted
                                ? 'Notifications are enabled'
                                : 'Permission required',
                            style: TextStyle(
                              fontSize: 12,
                              color: _notificationsPermissionGranted
                                  ? Colors.green
                                  : Colors.red,
                            ),
                          ),
                        ],
                      ),
                      if (!_notificationsPermissionGranted)
                        Padding(
                          padding: const EdgeInsets.only(top: 4.0),
                          child: TextButton(
                            onPressed: () {
                              _requestNotificationPermission();
                            },
                            style: TextButton.styleFrom(
                              padding: EdgeInsets.zero,
                              minimumSize: const Size(0, 0),
                              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                            ),
                            child: const Text('Request Permission'),
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Show next reminder date if available
          if (_nextReminderDate != null) ...[
            const Divider(height: 1, thickness: 0.5),
            Padding(
              padding:
                  const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Next Reminder',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        Text(
                          '${_nextReminderDate!.day}/${_nextReminderDate!.month}/${_nextReminderDate!.year} at '
                          '${_nextReminderDate!.hour}:${_nextReminderDate!.minute.toString().padLeft(2, '0')}',
                          style: const TextStyle(
                            fontSize: 12,
                            color: Colors.grey,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],

          // Show last reminder date if available
          if (_lastReminderDate != null) ...[
            const Divider(height: 1, thickness: 0.5),
            Padding(
              padding:
                  const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Last Reminder',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        Text(
                          '${_lastReminderDate!.day}/${_lastReminderDate!.month}/${_lastReminderDate!.year}',
                          style: const TextStyle(
                            fontSize: 12,
                            color: Colors.grey,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
          const Divider(height: 1, thickness: 0.5),
          Padding(
            padding: const EdgeInsets.all(12.0),
            child: ElevatedButton(
              onPressed: () async {
                // Capture the context before the async gap
                final scaffoldMessenger = ScaffoldMessenger.of(context);

                await notificationProvider
                    .showMeterReadingReminderNotification();

                if (mounted) {
                  scaffoldMessenger.showSnackBar(
                    const SnackBar(
                      content: Text('Reminder sent'),
                    ),
                  );
                  await _loadSettings(); // Reload to update last reminder date
                }
              },
              child: const Text('Send Reminder Now'),
            ),
          ),
        ],
      ],
    );
  }
}
