// File: lib/core/widgets/dialogs/notification_confirmation_dialog.dart
import 'package:flutter/material.dart';
import '../../theme/app_colors.dart';
import '../../theme/app_text_styles.dart';
import '../../utils/dialog_button_styles.dart';

/// A specialized confirmation dialog for notification deletion
class NotificationConfirmationDialog extends StatelessWidget {
  final String title;
  final String message;
  final VoidCallback onConfirm;
  final VoidCallback onCancel;

  const NotificationConfirmationDialog({
    Key? key,
    required this.title,
    required this.message,
    required this.onConfirm,
    required this.onCancel,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final backgroundColor =
        isDarkMode ? AppColors.surfaceDark : AppColors.surface;
    final textColor =
        isDarkMode ? AppColors.onSurfaceDark : AppColors.onSurface;

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      backgroundColor: backgroundColor,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Title
            Text(
              title,
              style: AppTextStyles.titleMedium.copyWith(
                color: textColor,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),

            // Message
            Text(
              message,
              style: AppTextStyles.bodyMedium.copyWith(
                color: textColor.withOpacity(0.8),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),

            // Action buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                // Cancel button
                DialogButtonStyles.createCancelButton(
                  context: context,
                  onPressed: onCancel,
                ),

                // Delete button
                DialogButtonStyles.createDestructiveButton(
                  context: context,
                  onPressed: onConfirm,
                  text: 'Delete',
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Show a notification confirmation dialog
  static Future<bool?> show({
    required BuildContext context,
    required String title,
    required String message,
  }) {
    return showDialog<bool>(
      context: context,
      builder: (context) => NotificationConfirmationDialog(
        title: title,
        message: message,
        onConfirm: () => Navigator.of(context).pop(true),
        onCancel: () => Navigator.of(context).pop(false),
      ),
    );
  }
}
