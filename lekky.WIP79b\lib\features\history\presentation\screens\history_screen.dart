// File: lib/features/history/presentation/screens/history_screen.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../core/models/meter_entry.dart';
import '../../../../core/shared_modules/settings_provider.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/utils/helpful_messages.dart';
import '../../../../core/widgets/balanced_height_table_container.dart';
import '../../../../core/widgets/message_banner.dart';
import '../../domain/models/related_validation_result.dart';
import '../controllers/history_controller.dart';
import '../widgets/entry_detail_dialog.dart';
import '../../../../core/widgets/dialogs/entry_edit_dialog.dart';
import '../widgets/filter_dialog.dart';
import '../widgets/history_filter_bar.dart';
import '../widgets/history_info_dialog.dart';
import '../widgets/fixed_header_history_table.dart';

/// The history screen of the app
class HistoryScreen extends StatefulWidget {
  const HistoryScreen({Key? key}) : super(key: key);

  @override
  State<HistoryScreen> createState() => _HistoryScreenState();
}

class _HistoryScreenState extends State<HistoryScreen> {
  // Index for the current message to display
  int _currentMessageIndex = 0;
  // Scroll controller to track scrolling for parallax effect
  final ScrollController _scrollController = ScrollController();
  // Value notifier to track scroll position for the parallax effect
  final ValueNotifier<double> _scrollPosition = ValueNotifier<double>(0.0);
  // Value notifier to track max scroll extent
  final ValueNotifier<double> _maxScrollExtent = ValueNotifier<double>(0.0);
  // Scroll controller for the table content
  final ScrollController _tableScrollController = ScrollController();
  // Flag to track if we should filter invalid entries after loading
  bool _shouldFilterInvalidEntries = false;
  // Flag to prevent multiple filter applications
  bool _hasAppliedFilter = false;

  @override
  void initState() {
    super.initState();

    // Check if we should filter invalid entries from navigation arguments
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final args = ModalRoute.of(context)?.settings.arguments;
      if (args is Map && args['filterInvalidEntries'] == true) {
        // Set flag to apply filter after loading completes
        _shouldFilterInvalidEntries = true;
      }

      // Initialize the controller
      final controller = context.read<HistoryController>();

      // Add listener to controller to detect when loading is complete
      controller.addListener(_onControllerUpdate);

      // Start initialization
      controller.init();
    });

    // Add listener to update scroll position for parallax effect
    _scrollController.addListener(_updateScrollInfo);

    // Rotate messages every 10 seconds
    _startMessageRotation();
  }

  // Listen for controller updates
  void _onControllerUpdate() {
    final controller = context.read<HistoryController>();

    // Check if loading has completed and we need to filter invalid entries
    if (!controller.isLoading &&
        _shouldFilterInvalidEntries &&
        !_hasAppliedFilter) {
      // Apply the filter only once
      _hasAppliedFilter = true;
      _filterInvalidEntries(controller);
    }
  }

  // Update scroll information for parallax effect
  void _updateScrollInfo() {
    if (!mounted) return;

    _scrollPosition.value =
        _scrollController.hasClients ? _scrollController.offset : 0;
    _maxScrollExtent.value = _scrollController.hasClients
        ? _scrollController.position.maxScrollExtent
        : 0;
  }

  void _startMessageRotation() {
    // Rotate messages every 10 seconds
    Future.delayed(const Duration(seconds: 10), () {
      if (mounted) {
        setState(() {
          _currentMessageIndex =
              (_currentMessageIndex + 1) % HelpfulMessages.allMessages.length;
        });
        _startMessageRotation(); // Schedule the next rotation
      }
    });
  }

  @override
  void dispose() {
    // Reset edit mode when leaving the screen
    final controller = context.read<HistoryController>();
    if (controller.isEditMode) {
      controller.toggleEditMode();
    }

    // Remove controller listener
    controller.removeListener(_onControllerUpdate);

    // Remove listener and dispose scroll controllers
    _scrollController.removeListener(_updateScrollInfo);
    _scrollController.dispose();
    _tableScrollController.dispose();

    // Dispose value notifiers
    _scrollPosition.dispose();
    _maxScrollExtent.dispose();

    // Reset filter flags
    _shouldFilterInvalidEntries = false;
    _hasAppliedFilter = false;

    super.dispose();
  }

  List<Widget> _buildAppBarActions(HistoryController controller) {
    return [
      if (controller.hasValidationErrors)
        Transform.translate(
          offset:
              const Offset(0, 4), // Move down by 4px to align with other icons
          child: Container(
            margin: const EdgeInsets.only(right: 8),
            decoration: const BoxDecoration(
              color: AppColors.error,
              shape: BoxShape.circle,
            ),
            child: GestureDetector(
              onTap: () => _filterInvalidEntries(controller),
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Text(
                  '${controller.invalidEntryCount}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
              ),
            ),
          ),
        ),
      Transform.translate(
        offset: const Offset(0, 4), // Move down by 4px
        child: IconButton(
          icon: Icon(
            controller.isEditMode ? Icons.edit_off : Icons.edit,
            color: controller.isEditMode ? Colors.blue[800] : Colors.white,
            size: 24,
          ),
          onPressed: controller.toggleEditMode,
          tooltip: controller.isEditMode ? 'Exit Edit Mode' : 'Enter Edit Mode',
        ),
      ),
      Transform.translate(
        offset: const Offset(0, 4), // Move down by 4px
        child: IconButton(
          icon: const Icon(Icons.filter_list, color: Colors.white, size: 24),
          onPressed: () => _showFilterDialog(context, controller),
          tooltip: 'Filter Options',
        ),
      ),
      Transform.translate(
        offset: const Offset(0, 4), // Move down by 4px
        child: IconButton(
          icon: const Icon(Icons.info_outline, color: Colors.white, size: 24),
          onPressed: () => _showInfoDialog(context, controller),
          tooltip: 'History Information',
        ),
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    // Reset filter flags when navigating away
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted) {
        _shouldFilterInvalidEntries = false;
        _hasAppliedFilter = false;
      }
    });

    return Consumer<HistoryController>(
      builder: (context, controller, _) {
        return Scaffold(
          backgroundColor:
              Colors.transparent, // Match Homepage transparent background
          body: _buildMainContent(controller),
          bottomNavigationBar: _buildFixedControlsBar(controller),
        );
      },
    );
  }

  // Main content with scrolling
  Widget _buildMainContent(HistoryController controller) {
    return RefreshIndicator(
      onRefresh: controller.refresh,
      child: CustomScrollView(
        controller: _scrollController, // Attach scroll controller
        slivers: [
          _buildAppBar(controller),
          SliverPadding(
            padding: const EdgeInsets.fromLTRB(
                8, 16, 8, 8), // 8px bottom padding for consistent gap
            sliver: SliverToBoxAdapter(
              child: controller.isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : controller.hasFilteredEntries
                      ? Column(
                          children: [
                            // Table content with balanced spacing
                            Center(
                              child: BalancedHeightTableContainer(
                                // Calculate spacing values
                                topSpacing: 96.0 +
                                    32.0 +
                                    16.0, // AppBar + MessageBar + TopPadding
                                bottomSpacing:
                                    60.0 + 8.0, // BottomBar + 8px gap
                                // Coordinate scrolling with parent
                                coordinateScrolling: true,
                                parentScrollController: _scrollController,
                                // Use the dedicated table scroll controller
                                childScrollController: _tableScrollController,
                                child: FixedHeaderHistoryTable(
                                  entries: controller.filteredEntries,
                                  controller: controller,
                                  onEntryTap: (entry) =>
                                      _showEntryEditDialog(entry),
                                  onInvalidEntryTap: (entry) =>
                                      _showInvalidEntryExplanationDialog(entry),
                                  // Pass the same scroll controller to the table
                                  scrollController: _tableScrollController,
                                ),
                              ),
                            ),
                          ],
                        )
                      : const Center(child: Text('No entries found')),
            ),
          ),
          if (controller.error.isNotEmpty)
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Text(
                  controller.error,
                  style:
                      AppTextStyles.bodyMedium.copyWith(color: AppColors.error),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
        ],
      ),
    );
  }

  // Build the message banner with rotating helpful messages
  Widget _buildMessageBanner() {
    return MessageBanner(
      message: HelpfulMessages.allMessages[_currentMessageIndex],
    );
  }

  Widget _buildAppBar(HistoryController controller) {
    return SliverAppBar(
      toolbarHeight: 96, // Fixed height of 96px for the banner
      pinned: true,
      automaticallyImplyLeading: false, // Remove back arrow
      // Use a Stack for the banner content
      flexibleSpace: SizedBox(
        height: 96, // Fixed height of 96px
        child: Stack(
          children: [
            // Background gradient
            Positioned.fill(
              child: Container(
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: AppColors.historyGradient,
                  ),
                ),
              ),
            ),
            // Custom positioned title - aligned with Meter Total dialog box
            const Positioned(
              top: 20,
              left: 20, // Exactly 20px from left edge to match Homepage
              child: Text(
                'History',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 40, // Same font size as Homepage
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            // App bar actions
            Positioned(
              top: 10, // Changed from 6 to 10 (moved down by 4px)
              right: 12,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: _buildAppBarActions(controller),
              ),
            ),
            // Active filter chip - moved up by 3px
            if (_hasActiveFilter(controller))
              Positioned(
                top:
                    59, // Changed from 61 to 59 (moved up by an additional 2px)
                right: 42,
                child: _buildActiveFilterChip(controller),
              ),
          ],
        ),
      ),
      // Remove notification button
      // Position the message bar directly below the banner with no spacing
      bottom: PreferredSize(
        // Use zero height to eliminate any spacing
        preferredSize: const Size.fromHeight(0),
        child: _buildMessageBanner(),
      ),
    );
  }

  Widget _buildFixedControlsBar(HistoryController controller) {
    // Get the current page and total pages from the controller
    final currentPage = controller.currentPage;
    final totalPages = controller.totalPages;
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Container(
      height: 60, // Fixed height for the bottom bar
      decoration: BoxDecoration(
        color: isDarkMode ? AppColors.surfaceDark : Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            spreadRadius: 0,
            offset: const Offset(0, -1),
          ),
        ],
      ),
      padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 8.0),
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Pagination controls centered horizontally
          Center(
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Previous page button
                IconButton(
                  icon: const Icon(Icons.chevron_left, size: 20),
                  onPressed: currentPage > 0
                      ? () => controller.goToPage(currentPage - 1)
                      : null,
                  color: Theme.of(context).colorScheme.primary,
                  padding: EdgeInsets.zero,
                  constraints:
                      const BoxConstraints(minWidth: 28, minHeight: 28),
                ),

                // Page indicator - more compact
                Text(
                  '${currentPage + 1}/$totalPages',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color:
                        isDarkMode ? AppColors.onSurfaceDark : Colors.black87,
                  ),
                ),

                // Next page button
                IconButton(
                  icon: const Icon(Icons.chevron_right, size: 20),
                  onPressed: currentPage < totalPages - 1
                      ? () => controller.goToPage(currentPage + 1)
                      : null,
                  color: Theme.of(context).colorScheme.primary,
                  padding: EdgeInsets.zero,
                  constraints:
                      const BoxConstraints(minWidth: 28, minHeight: 28),
                ),
              ],
            ),
          ),

          // Add Entry button positioned 42px from the right edge
          Positioned(
            right: 42, // 42px from the right edge
            child: IconButton(
              onPressed: () => _showEntryEditDialog(null),
              icon: const Icon(
                Icons.add_circle,
                color: Color(
                    0xFF43E97B), // Match the green color from homepage add button
                size: 39, // 40% bigger than original size of 28
              ),
              tooltip: 'Add Entry',
            ),
          ),
        ],
      ),
    );
  }

  void _showEntryEditDialog(MeterEntry? entry) {
    final controller = context.read<HistoryController>();

    // If we're not in edit mode and entry is not null, show the detail dialog
    if (!controller.isEditMode && entry != null) {
      _showEntryDetailDialog(entry);
      return;
    }

    // Otherwise, show the edit dialog
    EntryEditDialog.show(
      context: context,
      entry: entry,
      controller: controller,
      onSave: (updatedEntry) => controller.addEntry(updatedEntry),
      onDelete: entry?.id != null ? (id) => controller.deleteEntry(id) : null,
    );
  }

  void _showEntryDetailDialog(MeterEntry entry) {
    final controller = context.read<HistoryController>();
    EntryDetailDialog.show(
      context: context,
      entry: entry,
      controller: controller,
    );
  }

  void _showInfoDialog(BuildContext context, HistoryController controller) {
    HistoryInfoDialog.show(context: context, controller: controller);
  }

  void _showFilterDialog(BuildContext context, HistoryController controller) {
    DateTime? earliestDate;
    DateTime? latestDate;

    if (controller.allEntries.isNotEmpty) {
      final sortedEntries = List<MeterEntry>.from(controller.allEntries)
        ..sort((a, b) => a.timestamp.compareTo(b.timestamp));
      earliestDate = sortedEntries.first.timestamp;
      latestDate = sortedEntries.last.timestamp;
    }

    FilterDialog.show(
      context: context,
      filter: controller.filter,
      controller: controller,
      onApply: (filter) {
        // Check if we need to apply special filtering for invalid entries
        // Use the isInvalidFilter flag to determine if this is an invalid entries filter
        if (filter.isInvalidFilter) {
          // Apply special filtering for invalid entries
          controller.filterInvalidEntries(filter);
        } else {
          // Apply normal filtering
          controller.updateFilter(filter);
        }
      },
      earliestDate: earliestDate,
      latestDate: latestDate,
      showDateRangeFilter: controller.hasEnoughReadingsForDateRange,
    );
  }

  void _filterInvalidEntries(HistoryController controller) {
    // Reset the filter flag if this is a manual click (not from navigation)
    if (!_shouldFilterInvalidEntries) {
      _hasAppliedFilter = false;
    }

    final filter = controller.filter.copyWith(
      startDate: null,
      endDate: null,
      showMeterReadings: true,
      showTopUps: true,
      showInvalidEntries: true,
    );

    controller.filterInvalidEntries(filter);

    // Show a snackbar notification
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content:
              Text('Showing ${controller.invalidEntryCount} invalid entries'),
          duration: const Duration(seconds: 2),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _showInvalidEntryExplanationDialog(MeterEntry entry) {
    final controller = context.read<HistoryController>();
    final validationResult = controller.getValidationResult(entry.id ?? -1);

    if (validationResult.isValid) {
      // This entry might be related to an invalid entry
      // Check if this entry is related to any invalid entry
      bool isRelatedToInvalidEntry = false;
      int? invalidEntryId;
      MeterEntry? relatedEntry;

      for (final result in controller.validationResults.entries) {
        if (!result.value.isValid &&
            result.value is RelatedValidationResult &&
            (result.value as RelatedValidationResult).relatedEntryId ==
                entry.id) {
          isRelatedToInvalidEntry = true;
          invalidEntryId = result.key; // This is the ID of the invalid entry

          // Find the related entry (the invalid entry)
          for (final e in controller.filteredEntries) {
            if (e.id == invalidEntryId) {
              relatedEntry = e;
              break;
            }
          }
          break;
        }
      }

      if (!isRelatedToInvalidEntry) return; // Not related to any invalid entry

      // Create a detailed message for the related entry
      String detailedMessage =
          'This entry is used to validate another entry that has an issue.';

      if (relatedEntry != null) {
        final formattedDate =
            '${relatedEntry.timestamp.day}/${relatedEntry.timestamp.month}/${relatedEntry.timestamp.year}';
        final formattedReading = relatedEntry.reading.toStringAsFixed(2);

        // If this entry is used to validate a later entry, it means this entry might be too low
        if (relatedEntry.timestamp.isAfter(entry.timestamp)) {
          detailedMessage =
              'This reading may be too low compared to the reading on $formattedDate (£$formattedReading)';
        } else {
          detailedMessage =
              'This reading may be too high compared to the reading on $formattedDate (£$formattedReading)';
        }
      }

      // Show dialog for related entry with the same design as the invalid entry dialog
      showDialog(
        context: context,
        builder: (context) => Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          insetPadding:
              const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
          child: Container(
            constraints: BoxConstraints(
              maxWidth: MediaQuery.of(context).size.width * 0.9,
              maxHeight: MediaQuery.of(context).size.height * 0.8,
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Header with icon to the left of title
                Padding(
                  padding: const EdgeInsets.fromLTRB(24, 16, 24, 0),
                  child: Row(
                    children: [
                      const Icon(
                        Icons.warning,
                        color: Colors.amber,
                        size: 24,
                      ),
                      const SizedBox(width: 10),
                      const Text(
                        'Table Entry Warning',
                        style: TextStyle(
                          color: AppColors.primary,
                          fontWeight: FontWeight.bold,
                          fontSize: 18,
                        ),
                      ),
                      const Spacer(),
                      IconButton(
                        icon:
                            const Icon(Icons.close, color: AppColors.onSurface),
                        onPressed: () => Navigator.of(context).pop(),
                        tooltip: 'Close',
                        constraints: const BoxConstraints(),
                        padding: EdgeInsets.zero,
                        iconSize: 20,
                      ),
                    ],
                  ),
                ),

                // Content
                Padding(
                  padding: const EdgeInsets.fromLTRB(24, 16, 24, 12),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Validation issue:',
                        style: TextStyle(fontSize: 16),
                      ),
                      const SizedBox(height: 12),
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 10),
                        decoration: BoxDecoration(
                          color: Colors.red[50],
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.red[200]!),
                        ),
                        child: Text(
                          detailedMessage,
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.red[800],
                            fontSize: 16,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // Actions
                Padding(
                  padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      SizedBox(
                        height:
                            40, // Taller button to match Meter Reading Details dialog
                        child: OutlinedButton(
                          onPressed: () => Navigator.of(context).pop(),
                          style: OutlinedButton.styleFrom(
                            foregroundColor: AppColors.primary,
                            side: const BorderSide(color: AppColors.primary),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            padding: const EdgeInsets.symmetric(horizontal: 16),
                          ),
                          child: const Text('Cancel'),
                        ),
                      ),
                      const SizedBox(width: 8),
                      SizedBox(
                        height:
                            40, // Taller button to match Meter Reading Details dialog
                        child: ElevatedButton(
                          onPressed: () {
                            Navigator.of(context).pop();
                            controller.toggleEditMode();
                            _showEntryEditDialog(entry);
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors
                                .costTab, // Dark orange from Cost screen
                            foregroundColor: Colors.white,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            padding: const EdgeInsets.symmetric(horizontal: 16),
                          ),
                          child: const Text('Edit Entry',
                              style: TextStyle(color: Colors.white)),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      );
      return;
    }

    // Show dialog for invalid entry
    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        insetPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
        child: Container(
          constraints: BoxConstraints(
            maxWidth: MediaQuery.of(context).size.width * 0.9,
            maxHeight: MediaQuery.of(context).size.height * 0.8,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Header with icon to the left of title
              Padding(
                padding: const EdgeInsets.fromLTRB(24, 16, 24, 0),
                child: Row(
                  children: [
                    const Icon(
                      Icons.warning,
                      color: Colors.amber,
                      size: 24,
                    ),
                    const SizedBox(width: 10),
                    const Text(
                      'Table Entry Warning',
                      style: TextStyle(
                        color: AppColors.primary,
                        fontWeight: FontWeight.bold,
                        fontSize: 18,
                      ),
                    ),
                    const Spacer(),
                    IconButton(
                      icon: const Icon(Icons.close, color: AppColors.onSurface),
                      onPressed: () => Navigator.of(context).pop(),
                      tooltip: 'Close',
                      constraints: const BoxConstraints(),
                      padding: EdgeInsets.zero,
                      iconSize: 20,
                    ),
                  ],
                ),
              ),

              // Content
              Padding(
                padding: const EdgeInsets.fromLTRB(24, 16, 24, 12),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Validation issue:',
                      style: TextStyle(fontSize: 16),
                    ),
                    const SizedBox(height: 12),
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 10),
                      decoration: BoxDecoration(
                        color: Colors.red[50],
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.red[200]!),
                      ),
                      child: Text(
                        validationResult is RelatedValidationResult
                            ? validationResult.getDetailedErrorMessage()
                            : (validationResult.errorMessage ??
                                'Unknown validation error'),
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.red[800],
                          fontSize: 16,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // Actions
              Padding(
                padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    SizedBox(
                      height:
                          40, // Taller button to match Meter Reading Details dialog
                      child: OutlinedButton(
                        onPressed: () => Navigator.of(context).pop(),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: AppColors.primary,
                          side: const BorderSide(color: AppColors.primary),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                        ),
                        child: const Text('Cancel'),
                      ),
                    ),
                    const SizedBox(width: 8),
                    SizedBox(
                      height:
                          40, // Taller button to match Meter Reading Details dialog
                      child: ElevatedButton(
                        onPressed: () {
                          Navigator.of(context).pop();
                          controller.toggleEditMode();
                          _showEntryEditDialog(entry);
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor:
                              AppColors.costTab, // Dark orange from Cost screen
                          foregroundColor: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                        ),
                        child: const Text('Edit Entry',
                            style: TextStyle(color: Colors.white)),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  bool _hasActiveFilter(HistoryController controller) {
    final filter = controller.filter;

    // Check if we're specifically filtering for invalid entries
    bool isInvalidFilter = filter.showInvalidEntries &&
        controller.filteredEntries.length == controller.invalidEntryCount &&
        controller.invalidEntryCount > 0;

    return filter.startDate != null ||
        filter.endDate != null ||
        !filter.showMeterReadings ||
        !filter.showTopUps ||
        isInvalidFilter;
  }

  Widget _buildActiveFilterChip(HistoryController controller) {
    final filter = controller.filter;
    String filterText = '';
    Color chipColor = AppColors.primary;
    IconData filterIcon = Icons.filter_list;

    if (!filter.showMeterReadings && filter.showTopUps) {
      filterText = 'Top Up';
      chipColor = Colors.orange;
      filterIcon = Icons.add_circle;
    } else if (filter.showMeterReadings && !filter.showTopUps) {
      filterText = 'Meter';
      chipColor = Colors.blue;
      filterIcon = Icons.electric_meter;
    } else if (filter.startDate != null || filter.endDate != null) {
      if (controller.hasEnoughReadingsForDateRange) {
        filterText = 'Date';
        chipColor = Colors.green;
        filterIcon = Icons.calendar_today;
      } else {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          controller
              .updateFilter(filter.copyWith(startDate: null, endDate: null));
        });
      }
    } else if (filter.showInvalidEntries &&
        controller.filteredEntries.length == controller.invalidEntryCount &&
        controller.invalidEntryCount > 0) {
      filterText = 'Invalid';
      chipColor = Colors.red;
      filterIcon = Icons.error_outline;
    }

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () => controller.resetFilter(),
        borderRadius: BorderRadius.circular(16),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: chipColor.withOpacity(0.9),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(filterIcon, color: Colors.white, size: 18),
              const SizedBox(width: 4),
              Text(
                filterText,
                style: const TextStyle(color: Colors.white),
              ),
              const SizedBox(width: 4),
              const Icon(Icons.close, color: Colors.white, size: 16),
            ],
          ),
        ),
      ),
    );
  }
}
