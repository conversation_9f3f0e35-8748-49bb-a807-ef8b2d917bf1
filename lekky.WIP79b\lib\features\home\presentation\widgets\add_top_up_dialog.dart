// File: lib/features/home/<USER>/widgets/add_top_up_dialog.dart
import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/utils/date_time_utils.dart';
import '../../../../core/utils/input_validator.dart';
import '../../../../core/widgets/app_text_field.dart';
import '../../../../core/widgets/dialogs/date_picker_dialog.dart'
    as custom_date_picker;
import '../../../../features/history/presentation/controllers/history_controller.dart';
import '../../../../features/history/data/history_repository.dart';
import '../../../../core/data/repositories/meter_entry_repository.dart';
import '../../../../core/data/repositories/settings_repository.dart';

/// A dialog for adding a top-up
class AddTopUpDialog extends StatefulWidget {
  final String meterUnit;

  // Add controller for date info setting
  final HistoryController controller;

  AddTopUpDialog({
    super.key,
    required this.meterUnit,
  }) : controller = HistoryController(HistoryRepository(
          meterEntryRepository: MeterEntryRepository(),
          settingsRepository: SettingsRepository(),
        ));

  /// Show the dialog
  static Future<Map<String, dynamic>?> show({
    required BuildContext context,
    required String meterUnit,
  }) async {
    return showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => AddTopUpDialog(
        meterUnit: meterUnit,
      ),
    );
  }

  @override
  State<AddTopUpDialog> createState() => _AddTopUpDialogState();
}

class _AddTopUpDialogState extends State<AddTopUpDialog> {
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  DateTime _selectedDate = DateTime.now();

  @override
  void dispose() {
    _amountController.dispose();
    super.dispose();
  }

  Future<void> _selectDate(BuildContext context) async {
    // Check the Date Info setting
    final dateInfo = await widget.controller.getDateInfo();
    final includeTime = dateInfo == 'Date and time';

    // Store the current context in a local variable
    final currentContext = context;

    DateTime? picked;

    try {
      if (includeTime) {
        // Use date time picker with proper time inclusion
        if (!mounted) return;

        // Use a synchronous function to avoid BuildContext issues
        picked = await Future<DateTime?>.sync(() async {
          if (!mounted) return null;
          return await custom_date_picker.DatePickerDialog.showDateTimePicker(
            context: currentContext,
            title: 'Select Date and Time',
            initialDate: _selectedDate,
            firstDate: DateTime(2000),
            lastDate: DateTime.now(),
            helpText: 'Select the date and time of your top-up',
            includeTime: true,
          );
        });
      } else {
        // Use date only picker
        if (!mounted) return;

        // Use a synchronous function to avoid BuildContext issues
        picked = await Future<DateTime?>.sync(() async {
          if (!mounted) return null;
          return await custom_date_picker.DatePickerDialog.showDatePicker(
            context: currentContext,
            title: 'Select Date',
            initialDate: _selectedDate,
            firstDate: DateTime(2000),
            lastDate: DateTime.now(),
            helpText: 'Select the date of your top-up',
          );
        });
      }

      if (picked != null && mounted) {
        setState(() {
          _selectedDate = picked;
        });
      }
    } catch (e) {
      // Handle any errors that might occur during date picking
      if (mounted) {
        // Store the current context in a local variable
        final errorContext = context;

        // Use Future.microtask to avoid BuildContext issues
        Future.microtask(() {
          if (mounted) {
            ScaffoldMessenger.of(errorContext).showSnackBar(
              SnackBar(
                content: Text('Error selecting date: $e'),
                backgroundColor: Colors.red,
              ),
            );
          }
        });
      }
    }
  }

  void _submit() {
    if (_formKey.currentState!.validate()) {
      Navigator.of(context).pop({
        'amount': double.parse(_amountController.text),
        'date': _selectedDate,
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Add Top-up'),
      content: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            AppTextField(
              controller: _amountController,
              labelText: 'Amount',
              hintText: 'e.g. 20',
              keyboardType:
                  const TextInputType.numberWithOptions(decimal: true),
              prefixText: widget.meterUnit,
              validator: (value) {
                final result = InputValidator.validateTopUpAmount(value ?? '');
                if (!result['isValid']) {
                  return result['errorMessage'];
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            InkWell(
              onTap: () => _selectDate(context),
              child: InputDecorator(
                decoration: InputDecoration(
                  labelText: 'Date',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(DateTimeUtils.formatDateDefault(_selectedDate)),
                    const Icon(Icons.calendar_today),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _submit,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.secondary,
            foregroundColor: Colors.white,
          ),
          child: const Text('Save'),
        ),
      ],
    );
  }
}
