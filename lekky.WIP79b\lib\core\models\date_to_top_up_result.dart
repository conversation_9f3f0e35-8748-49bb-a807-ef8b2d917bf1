// File: lib/core/models/date_to_top_up_result.dart

/// Enum representing confidence level for predictions
enum ConfidenceLevel {
  /// High confidence - based on recent data (less than 3 days old)
  high,

  /// Medium confidence - based on moderately recent data (3-7 days old)
  medium,

  /// Low confidence - based on older data (more than 7 days old)
  low,
}

/// Result model for date to top up calculations
class DateToTopUpResult {
  /// The calculated date when the balance will reach the alert threshold
  final DateTime? date;

  /// Confidence level based on the age of the most recent meter reading
  final ConfidenceLevel confidenceLevel;

  /// Whether the balance is already below the threshold
  final bool isAlreadyBelowThreshold;

  /// Constructor
  const DateToTopUpResult({
    required this.date,
    required this.confidenceLevel,
    required this.isAlreadyBelowThreshold,
  });

  /// Format the result as a string
  String format() {
    if (date == null) {
      return 'N/A';
    }

    // If the balance is already below threshold, return "Now"
    if (isAlreadyBelowThreshold) {
      return 'Now';
    }

    // Otherwise, format the date
    return '${date!.day}/${date!.month}/${date!.year}';
  }
}
