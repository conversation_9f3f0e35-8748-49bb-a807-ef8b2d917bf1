// File: lib/features/home/<USER>/usecases/calculate_average_usage.dart
import '../../../../core/data/repositories/meter_entry_repository.dart';
import '../../../../core/models/meter_entry.dart';
import '../../../../core/utils/average_calculator.dart';
import '../../../../core/utils/logger.dart';

/// Use case for calculating the average usage
///
/// This class provides methods for calculating various average usage values.
/// It uses the stored values from the database when available, with fallback
/// calculations when needed.
///
/// For the short-term average, it retrieves the stored `shortAverageAfterTopUp` value
/// from the most recent meter reading. This ensures consistency between the history
/// table and homepage.
class CalculateAverageUsage {
  final MeterEntryRepository _repository;

  CalculateAverageUsage(this._repository);

  /// Execute the use case to get the most recent total average
  Future<double> execute() async {
    final entries = await _repository.getAllEntriesWithCache();
    if (entries.isEmpty) {
      return 0.0;
    }

    return AverageCalculator.getMostRecentTotalAverage(entries);
  }

  /// Calculate the short-term average (between the last two meter readings)
  Future<double> calculateShortTermAverage() async {
    final entries = await _repository.getAllEntriesWithCache();
    if (entries.length < 2) {
      return 0.0;
    }

    // Find all meter readings (not top-ups)
    final meterReadings = entries.where((e) => e.amountToppedUp == 0).toList();
    if (meterReadings.length < 2) {
      return 0.0;
    }

    // Sort meter readings by timestamp to ensure we get the most recent one
    meterReadings.sort((a, b) => b.timestamp.compareTo(a.timestamp));

    // Get the most recent meter reading by timestamp
    final mostRecentMeterReading = meterReadings.first;

    // If the most recent meter reading has a stored shortAverageAfterTopUp value, use it
    if (mostRecentMeterReading.shortAverageAfterTopUp != null) {
      logger.d(
          "Using stored short average value: ${mostRecentMeterReading.shortAverageAfterTopUp}");
      return mostRecentMeterReading.shortAverageAfterTopUp!;
    }

    // If no stored value, find the index of the most recent meter reading in the original entries list
    final mostRecentMeterReadingIndex = entries.indexWhere((e) =>
        e.id == mostRecentMeterReading.id &&
        e.timestamp == mostRecentMeterReading.timestamp);

    if (mostRecentMeterReadingIndex < 0) {
      return 0.0;
    }

    // Calculate the short average for the most recent meter reading as a fallback
    logger.d("No stored short average value found, calculating on the fly");
    return AverageCalculator.calculateShortAverage(
        entries, mostRecentMeterReadingIndex);
  }

  /// Calculate the cost of electric for a specific time period
  Future<double> calculateCost(DateTime startDate, DateTime endDate) async {
    final entries = await _repository.getAllEntriesWithCache();
    if (entries.isEmpty) {
      return 0.0;
    }

    return AverageCalculator.calculateCost(entries, startDate, endDate);
  }
}
