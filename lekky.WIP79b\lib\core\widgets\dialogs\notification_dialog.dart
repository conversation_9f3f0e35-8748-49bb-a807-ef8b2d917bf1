// File: lib/core/widgets/dialogs/notification_dialog.dart
import 'package:flutter/material.dart';
import '../../theme/app_colors.dart';
import '../../theme/app_text_styles.dart';
import '../app_dialog.dart';

/// Enum representing the priority level of a notification.
enum NotificationPriority {
  /// Low priority notification (informational).
  low,
  
  /// Medium priority notification (warning).
  medium,
  
  /// High priority notification (error/critical).
  high,
}

/// A specialized dialog for alerting users about important events.
/// 
/// This dialog presents a clear title, concise message, appropriate icon and color
/// based on severity, and action/dismiss buttons.
class NotificationDialog {
  /// Shows a notification dialog with the specified title, message, and priority.
  /// 
  /// Parameters:
  /// - [context]: The build context.
  /// - [title]: The title of the dialog, should clearly indicate the type of notification.
  /// - [message]: A concise message explaining the notification.
  /// - [priority]: The priority level of the notification (default: medium).
  /// - [actionText]: Optional text for the action button.
  /// - [onAction]: Optional callback for when the action button is pressed.
  /// - [dismissText]: The text for the dismiss button (default: "Dismiss").
  /// - [barrierDismissible]: Whether the dialog can be dismissed by tapping outside (default: true).
  static Future<void> show({
    required BuildContext context,
    required String title,
    required String message,
    NotificationPriority priority = NotificationPriority.medium,
    String? actionText,
    VoidCallback? onAction,
    String dismissText = 'Dismiss',
    bool barrierDismissible = true,
  }) async {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    // Determine the icon and color based on the priority
    IconData icon;
    Color iconColor;
    
    switch (priority) {
      case NotificationPriority.low:
        icon = Icons.info;
        iconColor = isDarkMode ? AppColors.infoDark : AppColors.info;
        break;
      case NotificationPriority.medium:
        icon = Icons.warning;
        iconColor = isDarkMode ? AppColors.warningDark : AppColors.warning;
        break;
      case NotificationPriority.high:
        icon = Icons.error;
        iconColor = isDarkMode ? AppColors.errorDark : AppColors.error;
        break;
    }
    
    // Create the content widget with the icon
    final Widget content = Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          icon,
          size: 48,
          color: iconColor,
        ),
        const SizedBox(height: 16),
        Text(
          message,
          style: AppTextStyles.bodyMedium.copyWith(
            color: isDarkMode
                ? AppColors.onSurfaceDark.withOpacity(0.8)
                : AppColors.onSurface.withOpacity(0.8),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
    
    // Create the actions
    final List<Widget> actions = [];
    
    // Add the dismiss button
    actions.add(
      TextButton(
        onPressed: () => Navigator.of(context).pop(),
        child: Text(dismissText),
      ),
    );
    
    // Add the action button if provided
    if (actionText != null && onAction != null) {
      actions.add(
        TextButton(
          onPressed: () {
            Navigator.of(context).pop();
            onAction();
          },
          style: TextButton.styleFrom(
            foregroundColor: isDarkMode ? AppColors.primaryDark : AppColors.primary,
          ),
          child: Text(actionText),
        ),
      );
    }
    
    return AppDialog.show(
      context: context,
      title: title,
      content: content,
      actions: actions,
      barrierDismissible: barrierDismissible,
    );
  }
  
  /// Shows a low priority (informational) notification dialog.
  /// 
  /// This is a specialized version of the notification dialog for informational messages,
  /// with an info icon and info colors.
  static Future<void> showInfo({
    required BuildContext context,
    required String title,
    required String message,
    String? actionText,
    VoidCallback? onAction,
    String dismissText = 'Dismiss',
    bool barrierDismissible = true,
  }) async {
    return show(
      context: context,
      title: title,
      message: message,
      priority: NotificationPriority.low,
      actionText: actionText,
      onAction: onAction,
      dismissText: dismissText,
      barrierDismissible: barrierDismissible,
    );
  }
  
  /// Shows a medium priority (warning) notification dialog.
  /// 
  /// This is a specialized version of the notification dialog for warning messages,
  /// with a warning icon and warning colors.
  static Future<void> showWarning({
    required BuildContext context,
    required String title,
    required String message,
    String? actionText,
    VoidCallback? onAction,
    String dismissText = 'Dismiss',
    bool barrierDismissible = true,
  }) async {
    return show(
      context: context,
      title: title,
      message: message,
      priority: NotificationPriority.medium,
      actionText: actionText,
      onAction: onAction,
      dismissText: dismissText,
      barrierDismissible: barrierDismissible,
    );
  }
  
  /// Shows a high priority (error/critical) notification dialog.
  /// 
  /// This is a specialized version of the notification dialog for error messages,
  /// with an error icon and error colors.
  static Future<void> showError({
    required BuildContext context,
    required String title,
    required String message,
    String? actionText,
    VoidCallback? onAction,
    String dismissText = 'Dismiss',
    bool barrierDismissible = true,
  }) async {
    return show(
      context: context,
      title: title,
      message: message,
      priority: NotificationPriority.high,
      actionText: actionText,
      onAction: onAction,
      dismissText: dismissText,
      barrierDismissible: barrierDismissible,
    );
  }
}
