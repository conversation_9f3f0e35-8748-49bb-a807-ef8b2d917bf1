// File: lib/features/notifications/di/notification_service_locator.dart

import 'package:get_it/get_it.dart';
import '../../../core/data/database/notification_db_helper.dart';
import '../../../core/platform/notification/notification_adapter.dart';
import '../../../core/platform/notification/notification_adapter_factory.dart';
import '../../../core/platform/permissions/permission_adapter.dart';
import '../../../core/platform/permissions/permission_adapter_factory.dart';
import '../../../core/platform/timezone/timezone_adapter.dart';
import '../../../core/platform/timezone/timezone_adapter_factory.dart';
import '../data/repositories/notification_repository_impl.dart';
import '../data/migration/storage_migration_service.dart';
import '../data/sources/platform_notification_data_source.dart';
import '../data/sources/storage_factory.dart';
import '../domain/repositories/notification_repository.dart';
import '../domain/usecases/cancel_reminder.dart';
import '../domain/usecases/get_reminder_status.dart';
import '../domain/usecases/migrate_notification_storage.dart';
import '../domain/usecases/schedule_meter_reading_reminder.dart';
import '../presentation/providers/notification_provider.dart';

/// Service locator for notification feature
final GetIt notificationServiceLocator = GetIt.instance;

/// Initialize the notification service locator
Future<void> initNotificationServiceLocator() async {
  // Core components
  if (!notificationServiceLocator.isRegistered<NotificationDBHelper>()) {
    notificationServiceLocator.registerSingleton<NotificationDBHelper>(
      NotificationDBHelper(),
    );
  }

  // Platform adapters
  if (!notificationServiceLocator.isRegistered<NotificationAdapter>()) {
    notificationServiceLocator.registerSingleton<NotificationAdapter>(
      NotificationAdapterFactory.createAdapter(),
    );
  }

  if (!notificationServiceLocator.isRegistered<PermissionAdapter>()) {
    notificationServiceLocator.registerSingleton<PermissionAdapter>(
      PermissionAdapterFactory.createAdapter(),
    );
  }

  if (!notificationServiceLocator.isRegistered<TimezoneAdapter>()) {
    notificationServiceLocator.registerSingleton<TimezoneAdapter>(
      TimezoneAdapterFactory.createAdapter(),
    );
  }

  // Storage factory
  if (!notificationServiceLocator.isRegistered<StorageFactory>()) {
    notificationServiceLocator.registerSingleton<StorageFactory>(
      StorageFactory(),
    );
  }

  // Data sources
  if (!notificationServiceLocator
      .isRegistered<PlatformNotificationDataSource>()) {
    notificationServiceLocator
        .registerSingleton<PlatformNotificationDataSource>(
      PlatformNotificationDataSource(
        notificationAdapter: notificationServiceLocator<NotificationAdapter>(),
        permissionAdapter: notificationServiceLocator<PermissionAdapter>(),
        timezoneAdapter: notificationServiceLocator<TimezoneAdapter>(),
      ),
    );
  }

  // Repositories
  if (!notificationServiceLocator.isRegistered<NotificationRepository>()) {
    notificationServiceLocator.registerSingleton<NotificationRepository>(
      NotificationRepositoryImpl(
        storageFactory: notificationServiceLocator<StorageFactory>(),
        platformDataSource:
            notificationServiceLocator<PlatformNotificationDataSource>(),
        notificationAdapter: notificationServiceLocator<NotificationAdapter>(),
        permissionAdapter: notificationServiceLocator<PermissionAdapter>(),
        timezoneAdapter: notificationServiceLocator<TimezoneAdapter>(),
      ),
    );
  }

  // Use cases
  if (!notificationServiceLocator
      .isRegistered<ScheduleMeterReadingReminder>()) {
    notificationServiceLocator.registerFactory<ScheduleMeterReadingReminder>(
      () => ScheduleMeterReadingReminder(
        notificationServiceLocator<NotificationRepository>(),
      ),
    );
  }

  if (!notificationServiceLocator.isRegistered<CancelReminder>()) {
    notificationServiceLocator.registerFactory<CancelReminder>(
      () => CancelReminder(
        notificationServiceLocator<NotificationRepository>(),
      ),
    );
  }

  if (!notificationServiceLocator.isRegistered<GetReminderStatus>()) {
    notificationServiceLocator.registerFactory<GetReminderStatus>(
      () => GetReminderStatus(
        notificationServiceLocator<NotificationRepository>(),
      ),
    );
  }

  if (!notificationServiceLocator.isRegistered<MigrateNotificationStorage>()) {
    notificationServiceLocator.registerFactory<MigrateNotificationStorage>(
      () => MigrateNotificationStorage(
        repository: notificationServiceLocator<NotificationRepository>(),
      ),
    );
  }

  // Providers
  if (!notificationServiceLocator.isRegistered<NotificationProvider>()) {
    notificationServiceLocator.registerFactory<NotificationProvider>(
      () => NotificationProvider(
        repository: notificationServiceLocator<NotificationRepository>(),
      ),
    );
  }

  // Migration service
  if (!notificationServiceLocator.isRegistered<StorageMigrationService>()) {
    notificationServiceLocator.registerFactory<StorageMigrationService>(
      () => StorageMigrationService(
        migrateStorageUseCase:
            notificationServiceLocator<MigrateNotificationStorage>(),
        notificationProvider:
            notificationServiceLocator<NotificationProvider>(),
      ),
    );
  }

  // Initialize components
  await notificationServiceLocator<NotificationDBHelper>().database;
  await notificationServiceLocator<NotificationRepository>().initialize();
}
