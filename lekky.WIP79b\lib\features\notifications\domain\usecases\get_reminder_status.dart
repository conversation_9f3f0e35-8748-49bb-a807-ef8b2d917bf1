// File: lib/features/notifications/domain/usecases/get_reminder_status.dart

import '../../../../core/platform/permissions/permission_adapter.dart';
import '../../../../core/platform/timezone/timezone_adapter.dart';
import '../models/reminder_time_model.dart';
import '../models/timezone_aware_reminder_time.dart';
import '../repositories/notification_repository.dart';

/// Result class for reminder status
class ReminderStatus {
  final bool isEnabled;
  final int frequency;
  final ReminderTimeModel reminderTime;
  final DateTime? lastReminderDate;
  final DateTime? nextReminderDate;
  final bool areNotificationsEnabled;
  final bool arePermissionsGranted;

  // Timezone-related fields
  final String currentTimezone;
  final String? reminderTimezone;
  final bool isInDifferentTimezone;
  final bool isDaylightSavingTime;
  final TimezoneAwareReminderTime? timezoneAwareReminderTime;

  ReminderStatus({
    required this.isEnabled,
    required this.frequency,
    required this.reminderTime,
    this.lastReminderDate,
    this.nextReminderDate,
    required this.areNotificationsEnabled,
    required this.arePermissionsGranted,
    required this.currentTimezone,
    this.reminderTimezone,
    required this.isInDifferentTimezone,
    required this.isDaylightSavingTime,
    this.timezoneAwareReminderTime,
  });

  /// Check if reminders are fully functional
  bool get isFullyFunctional =>
      isEnabled && areNotificationsEnabled && arePermissionsGranted;

  /// Get status description
  String getStatusDescription() {
    if (!areNotificationsEnabled) {
      return 'Notifications are disabled in app settings';
    }

    if (!arePermissionsGranted) {
      return 'Notification permissions are not granted';
    }

    if (!isEnabled) {
      return 'Meter reading reminders are disabled';
    }

    String status = 'Meter reading reminders are enabled';

    // Add timezone information if in a different timezone
    if (isEnabled && isInDifferentTimezone) {
      status += ' (timezone adjusted)';
    }

    return status;
  }

  /// Get timezone status description
  String getTimezoneStatusDescription() {
    if (!isInDifferentTimezone) {
      return 'Current timezone: $currentTimezone';
    }

    return 'Current timezone: $currentTimezone (Reminder set in: $reminderTimezone)';
  }
}

/// Use case for getting reminder status
class GetReminderStatus {
  final NotificationRepository _repository;

  GetReminderStatus(this._repository);

  /// Execute the use case
  Future<ReminderStatus> execute() async {
    // Get reminder settings
    final isEnabled = await _repository.areMeterReadingRemindersEnabled();
    final frequency = await _repository.getMeterReadingReminderFrequency();
    final reminderTime = await _repository.getMeterReadingReminderTime();
    final lastReminderDate =
        await _repository.getLastMeterReadingReminderDate();
    final nextReminderDate =
        await _repository.getNextMeterReadingReminderDate();

    // Get notification settings
    final areNotificationsEnabled = await _repository.areNotificationsEnabled();

    // Get permission status from the repository
    final permissionStatus = await _repository.getPermissionStatus();
    final arePermissionsGranted = permissionStatus == PermissionStatus.granted;

    // Get timezone information
    final repo =
        _repository as dynamic; // Cast to access the additional methods
    final currentTimezone = repo.getCurrentTimezone();

    // Get timezone-aware reminder time
    TimezoneAwareReminderTime? timezoneAwareReminderTime;
    try {
      timezoneAwareReminderTime = await repo.getTimezoneAwareReminderTime();
    } catch (e) {
      print('Error getting timezone-aware reminder time: $e');
    }

    // Get reminder timezone from timezone-aware reminder time
    final reminderTimezone = timezoneAwareReminderTime?.creationTimezone;

    // Check if current timezone is different from reminder timezone
    final isInDifferentTimezone =
        reminderTimezone != null && reminderTimezone != currentTimezone;

    // Check if currently in DST
    bool isDaylightSavingTime = false;
    try {
      final timezoneInfo = repo.getTimezoneInfo(DateTime.now());
      isDaylightSavingTime = timezoneInfo['isDST'] ?? false;
    } catch (e) {
      print('Error getting DST status: $e');
    }

    return ReminderStatus(
      isEnabled: isEnabled,
      frequency: frequency,
      reminderTime: reminderTime,
      lastReminderDate: lastReminderDate,
      nextReminderDate: nextReminderDate,
      areNotificationsEnabled: areNotificationsEnabled,
      arePermissionsGranted: arePermissionsGranted,
      currentTimezone: currentTimezone,
      reminderTimezone: reminderTimezone,
      isInDifferentTimezone: isInDifferentTimezone,
      isDaylightSavingTime: isDaylightSavingTime,
      timezoneAwareReminderTime: timezoneAwareReminderTime,
    );
  }
}
