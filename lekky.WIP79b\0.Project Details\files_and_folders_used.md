# DO NOT DELETE ANY OF THE FOLLOWING FILES OR FOLDERS

This document provides a comprehensive list of all files and folders required for the full working of the Lekky project. The structure reflects the actual relative directory structure from the project root.

## Root Project Files

```
lekky/
├── .gitignore                  # Git ignore file
├── .metadata                   # Flutter metadata
├── analysis_options.yaml       # Dart analysis options
├── pubspec.yaml                # Flutter dependencies and configuration
├── pubspec.lock                # Lock file for dependencies
├── lekky.iml                   # IntelliJ module file
└── run_app.bat                 # Batch file to run the app
```

## Assets

```
lekky/assets/
├── background.png              # Main background image
├── dark_mode_background.png    # Background image for dark mode
├── icon.png                    # App icon
└── splash.png                  # Splash screen image
```

## Core Application Files

```
lekky/lib/
├── app_scaffold.dart           # Main app scaffold
├── main.dart                   # Entry point of the application
└── test_notification.dart      # Notification testing utility
```

### Core Module

```
lekky/lib/core/
├── index.dart                  # Core module exports
├── constants/                  # Application constants
│   ├── app_config.dart         # App configuration
│   ├── app_constants.dart      # General constants
│   ├── db_constants.dart       # Database constants
│   └── index.dart              # Constants exports
├── data/                       # Data layer
│   ├── database/               # Database implementation
│   │   ├── db_constants.dart   # Database constants
│   │   ├── db_helper.dart      # Database helper
│   │   ├── db_optimizer.dart   # Database optimization
│   │   └── index.dart          # Database exports
│   └── repositories/           # Data repositories
│       ├── index.dart          # Repository exports
│       ├── meter_entry_repository.dart  # Meter entry repository
│       └── settings_repository.dart     # Settings repository
├── di/                         # Dependency injection
│   └── service_locator.dart    # Service locator implementation
├── extensions/                 # Dart extensions
│   └── context_extensions.dart # BuildContext extensions
├── models/                     # Core models
│   ├── index.dart              # Models exports
│   ├── meter_entry.dart        # Meter entry model
│   ├── meter_entry_with_averages.dart  # Meter entry with averages
│   ├── notification_model.dart # Notification model
│   └── reminder_time_model.dart # Reminder time model
├── providers/                  # Provider implementations
│   ├── notification_provider.dart  # Notification provider
│   └── theme_provider.dart     # Theme provider
├── services/                   # Core services
│   └── notification_service.dart  # Notification service
├── settings/                   # Settings implementation
│   ├── index.dart              # Settings exports
│   ├── models/                 # Settings models
│   │   ├── app_settings.dart   # App settings model
│   │   ├── settings_repository.dart  # Settings repository model
│   │   └── settings_validator.dart   # Settings validator model
│   ├── validators/             # Settings validators
│   │   └── settings_validator.dart   # Settings validator
│   └── widgets/                # Settings widgets
│       ├── alert_threshold_dialog.dart  # Alert threshold dialog
│       ├── alert_threshold_input.dart   # Alert threshold input
│       ├── alert_threshold_selector_adapter.dart  # Alert threshold selector adapter
│       ├── currency_selector.dart       # Currency selector
│       ├── date_format_selector.dart    # Date format selector
│       ├── date_info_selector.dart      # Date info selector
│       ├── days_advance_input.dart      # Days advance input
│       ├── days_in_advance_dialog.dart  # Days in advance dialog
│       ├── days_in_advance_selector_adapter.dart  # Days in advance selector adapter
│       ├── notifications_toggle.dart    # Notifications toggle
│       ├── radio_alert_threshold_selector.dart  # Radio alert threshold selector
│       ├── radio_currency_selector.dart  # Radio currency selector
│       ├── radio_days_in_advance_selector.dart  # Radio days in advance selector
│       ├── radio_region_selector.dart   # Radio region selector
│       ├── reminder_frequency_selector.dart  # Reminder frequency selector
│       ├── settings_dialogs.dart        # Settings dialogs
│       ├── setting_card.dart            # Setting card
│       └── setting_dialog.dart          # Setting dialog
├── shared_modules/             # Shared modules
│   ├── alert_threshold_selector.dart    # Alert threshold selector
│   ├── appearance_selector.dart         # Appearance selector
│   ├── base_settings_widget.dart        # Base settings widget
│   ├── currency_selector.dart           # Currency selector
│   ├── currency_selector_adapter.dart   # Currency selector adapter
│   ├── data_import_service.dart         # Data import service
│   ├── data_import_widget.dart          # Data import widget
│   ├── date_format_selector.dart        # Date format selector
│   ├── date_info_selector.dart          # Date info selector
│   ├── days_in_advance_selector.dart    # Days in advance selector
│   ├── initial_meter_credit_selector.dart  # Initial meter credit selector
│   ├── language_selector.dart           # Language selector
│   ├── notification_settings_selector.dart  # Notification settings selector
│   ├── radio_alert_threshold_selector_adapter.dart  # Radio alert threshold selector adapter
│   ├── radio_days_in_advance_selector_adapter.dart  # Radio days in advance selector adapter
│   ├── radio_selectors_adapter.dart     # Radio selectors adapter
│   ├── settings_model.dart              # Settings model
│   ├── settings_provider.dart           # Settings provider
│   ├── settings_service.dart            # Settings service
│   └── shared_modules.dart              # Shared modules exports
├── theme/                      # Theme implementation
│   ├── app_button_styles.dart  # Button styles
│   ├── app_colors.dart         # App colors
│   ├── app_text_styles.dart    # Text styles
│   ├── app_theme.dart          # App theme
│   ├── index.dart              # Theme exports
│   └── theme_service.dart      # Theme service
├── utils/                      # Utility classes
│   ├── agreed_average_calculator.dart   # Agreed average calculator
│   ├── average_calculator.dart          # Average calculator
│   ├── average_manager.dart             # Average manager
│   ├── cost_calculator.dart             # Cost calculator
│   ├── cost_interval.dart               # Cost interval
│   ├── date_time_utils.dart             # Date time utilities
│   ├── dialog_button_styles.dart        # Dialog button styles
│   ├── error_handler.dart               # Error handler
│   ├── event_bus.dart                   # Event bus
│   ├── helpful_messages.dart            # Helpful messages
│   ├── index.dart                       # Utils exports
│   ├── input_validator.dart             # Input validator
│   ├── logger.dart                      # Logger
│   ├── new_average_calculator.dart      # New average calculator
│   ├── notification_helper.dart         # Notification helper
│   ├── responsive_layout.dart           # Responsive layout
│   ├── responsive_text.dart             # Responsive text
│   ├── result.dart                      # Result wrapper
│   └── snackbar_util.dart               # Snackbar utility
└── widgets/                    # Core widgets
    ├── app_bottom_nav_bar.dart          # App bottom navigation bar
    ├── app_card.dart                    # App card
    ├── app_dialog.dart                  # App dialog
    ├── app_text_field.dart              # App text field
    ├── balanced_height_table_container.dart  # Balanced height table container
    ├── bottom_nav_bar.dart              # Bottom navigation bar
    ├── gradient_button.dart             # Gradient button
    ├── home_notification_button.dart    # Home notification button
    ├── home_settings_button.dart        # Home settings button
    ├── index.dart                       # Widgets exports
    ├── info_dialog.dart                 # Info dialog
    ├── message_banner.dart              # Message banner
    ├── notification_button.dart         # Notification button
    ├── notification_dialog.dart         # Notification dialog
    ├── outlined_cancel_button.dart      # Outlined cancel button
    ├── setting_dialog.dart              # Setting dialog
    ├── solid_save_button.dart           # Solid save button
    ├── themed_choice_chip.dart          # Themed choice chip
    ├── theme_toggle.dart                # Theme toggle
    ├── ticker_tape.dart                 # Ticker tape
    └── dialogs/                         # Dialog widgets
        ├── confirmation_dialog.dart     # Confirmation dialog
        ├── date_picker_dialog.dart      # Date picker dialog
        ├── import_options_dialog.dart   # Import options dialog
        ├── index.dart                   # Dialog exports
        ├── information_dialog.dart      # Information dialog
        ├── input_dialog.dart            # Input dialog
        ├── notification_confirmation_dialog.dart  # Notification confirmation dialog
        ├── notification_dialog.dart     # Notification dialog
        ├── progress_dialog.dart         # Progress dialog
        ├── README.md                    # Dialogs documentation
        └── selection_dialog.dart        # Selection dialog
```

### Features Module

```
lekky/lib/features/
├── backup/                     # Backup feature
│   ├── backup_errors.dart      # Backup error handling
│   ├── backup_service.dart     # Backup service implementation
│   └── README.md               # Backup documentation
├── cost/                       # Cost feature
│   ├── data/                   # Cost data layer
│   │   ├── cost_repository.dart  # Cost repository
│   │   ├── cost_repository_fix.dart  # Cost repository fix
│   │   └── cost_repository_fixed.dart  # Fixed cost repository
│   ├── domain/                 # Cost domain layer
│   │   ├── models/             # Cost models
│   │   │   ├── cost_mode.dart  # Cost mode model
│   │   │   ├── cost_period.dart  # Cost period model
│   │   │   ├── cost_result.dart  # Cost result model
│   │   │   ├── date_range.dart  # Date range model
│   │   │   └── index.dart      # Cost models exports
│   │   ├── usecases/           # Cost use cases
│   │   │   ├── calculate_cost.dart  # Calculate cost use case
│   │   │   └── index.dart      # Cost use cases exports
│   │   └── utils/              # Cost utilities
│   │       └── cost_message_provider.dart  # Cost message provider
│   └── presentation/           # Cost presentation layer
│       ├── controllers/        # Cost controllers
│       │   └── cost_controller.dart  # Cost controller
│       ├── models/             # Cost presentation models
│       │   ├── chart_data.dart  # Chart data model
│       │   └── cost_period.dart  # Cost period model
│       ├── screens/            # Cost screens
│       │   └── cost_screen.dart  # Cost screen
│       └── widgets/            # Cost widgets
│           ├── cost_card.dart  # Cost card
│           ├── cost_dialog.dart  # Cost dialog
│           ├── cost_display.dart  # Cost display
│           ├── cost_mode_toggle.dart  # Cost mode toggle
│           ├── cost_period_selector.dart  # Cost period selector
│           ├── cost_summary_card.dart  # Cost summary card
│           ├── date_calendar_selector.dart  # Date calendar selector
│           ├── date_range_selector.dart  # Date range selector
│           ├── index.dart      # Cost widgets exports
│           ├── period_selector.dart  # Period selector
│           └── usage_chart.dart  # Usage chart
├── history/                    # History feature
│   ├── data/                   # History data layer
│   │   └── history_repository.dart  # History repository
│   ├── domain/                 # History domain layer
│   │   ├── models/             # History models
│   │   │   ├── history_filter.dart  # History filter model
│   │   │   ├── index.dart      # History models exports
│   │   │   ├── related_validation_result.dart  # Related validation result model
│   │   │   └── validation_result.dart  # Validation result model
│   │   └── usecases/           # History use cases
│   │       ├── calculate_averages.dart  # Calculate averages use case
│   │       ├── filter_entries.dart  # Filter entries use case
│   │       ├── index.dart      # History use cases exports
│   │       └── validate_entries.dart  # Validate entries use case
│   └── presentation/           # History presentation layer
│       ├── controllers/        # History controllers
│       │   └── history_controller.dart  # History controller
│       ├── screens/            # History screens
│       │   └── history_screen.dart  # History screen
│       └── widgets/            # History widgets
│           ├── dotted_line_painter.dart  # Dotted line painter
│           ├── entry_detail_dialog.dart  # Entry detail dialog
│           ├── entry_edit_dialog.dart  # Entry edit dialog
│           ├── filter_dialog.dart  # Filter dialog
│           ├── fixed_header_history_table.dart  # Fixed header history table
│           ├── history_filter_bar.dart  # History filter bar
│           ├── history_info_dialog.dart  # History info dialog
│           ├── history_table.dart  # History table
│           ├── index.dart      # History widgets exports
│           ├── invalid_entry_indicator.dart  # Invalid entry indicator
│           ├── paginated_history_table.dart  # Paginated history table
│           └── parallax_line_painter.dart  # Parallax line painter
├── home/                       # Home feature
│   ├── data/                   # Home data layer
│   │   └── home_repository.dart  # Home repository
│   ├── domain/                 # Home domain layer
│   │   ├── models/             # Home models
│   │   │   └── meter_entry.dart  # Meter entry model
│   │   └── usecases/           # Home use cases
│   │       ├── calculate_average_usage.dart  # Calculate average usage use case
│   │       ├── calculate_meter_total.dart  # Calculate meter total use case
│   │       ├── calculate_top_up_date.dart  # Calculate top up date use case
│   │       └── index.dart      # Home use cases exports
│   └── presentation/           # Home presentation layer
│       ├── controllers/        # Home controllers
│       │   └── home_controller.dart  # Home controller
│       ├── screens/            # Home screens
│       │   └── home_screen.dart  # Home screen
│       └── widgets/            # Home widgets
│           ├── add_reading_dialog.dart  # Add reading dialog
│           ├── add_top_up_dialog.dart  # Add top up dialog
│           ├── combined_info_card.dart  # Combined info card
│           ├── index.dart      # Home widgets exports
│           ├── meter_value_card.dart  # Meter value card
│           ├── notifications_card.dart  # Notifications card
│           └── recent_entries.dart  # Recent entries
├── settings/                   # Settings feature
│   ├── data/                   # Settings data layer
│   │   └── paypal_service.dart  # PayPal service
│   └── presentation/           # Settings presentation layer
│       ├── controllers/        # Settings controllers
│       │   └── settings_controller.dart  # Settings controller
│       ├── screens/            # Settings screens
│       │   └── settings_screen.dart  # Settings screen
│       └── widgets/            # Settings widgets
│           ├── expandable_settings_section.dart  # Expandable settings section
│           ├── meter_reading_reminder_settings.dart  # Meter reading reminder settings
│           ├── notification_settings.dart  # Notification settings
│           ├── settings_section.dart  # Settings section
│           ├── theme_selector.dart  # Theme selector
│           └── tips_tricks_dialog.dart  # Tips and tricks dialog
├── setup/                      # Setup feature
│   ├── data/                   # Setup data layer
│   │   └── setup_repository.dart  # Setup repository
│   ├── domain/                 # Setup domain layer
│   │   ├── models/             # Setup models
│   │   │   ├── index.dart      # Setup models exports
│   │   │   └── setup_config.dart  # Setup config model
│   │   └── usecases/           # Setup use cases
│   │       ├── index.dart      # Setup use cases exports
│   │       ├── load_setup.dart  # Load setup use case
│   │       └── save_setup.dart  # Save setup use case
│   └── presentation/           # Setup presentation layer
│       ├── controllers/        # Setup controllers
│       │   └── setup_controller.dart  # Setup controller
│       ├── screens/            # Setup screens
│       │   └── setup_screen.dart  # Setup screen
│       └── widgets/            # Setup widgets
│           ├── date_format_selector.dart  # Date format selector
│           ├── days_advance_input.dart  # Days advance input
│           ├── index.dart      # Setup widgets exports
│           ├── initial_meter_credit_input.dart  # Initial meter credit input
│           ├── meter_unit_selector.dart  # Meter unit selector
│           ├── notifications_toggle.dart  # Notifications toggle
│           ├── setup_actions.dart  # Setup actions
│           ├── setup_dialogs.dart  # Setup dialogs
│           └── threshold_input.dart  # Threshold input
├── splash/                     # Splash feature
│   └── presentation/           # Splash presentation layer
│       ├── screens/            # Splash screens
│       │   └── splash_screen.dart  # Splash screen
│       └── widgets/            # Splash widgets
│           └── splash_animation.dart  # Splash animation
└── welcome/                    # Welcome feature
    └── presentation/           # Welcome presentation layer
        ├── screens/            # Welcome screens
        │   └── welcome_screen.dart  # Welcome screen
        └── widgets/            # Welcome widgets
            ├── welcome_carousel.dart  # Welcome carousel
            └── welcome_feature_item.dart  # Welcome feature item
```

## Platform-Specific Files

```
lekky/
├── android/                    # Android platform files
│   ├── app/                    # Android app files
│   │   └── src/                # Android source files
│   ├── gradle/                 # Gradle files
│   │   └── wrapper/            # Gradle wrapper
│   ├── build.gradle            # Android build file
│   ├── gradle.properties       # Gradle properties
│   ├── gradlew                 # Gradle wrapper script
│   ├── gradlew.bat             # Gradle wrapper batch script
│   └── settings.gradle         # Gradle settings
├── ios/                        # iOS platform files
│   ├── Flutter/                # iOS Flutter files
│   ├── Runner/                 # iOS Runner files
│   │   ├── Assets.xcassets/    # iOS assets
│   │   └── Base.lproj/         # iOS base localization
│   ├── Runner.xcodeproj/       # Xcode project
│   │   ├── project.xcworkspace/  # Xcode workspace
│   │   └── xcshareddata/       # Xcode shared data
│   ├── Runner.xcworkspace/     # Xcode workspace
│   │   └── xcshareddata/       # Xcode shared data
│   └── RunnerTests/            # iOS tests
├── linux/                      # Linux platform files
│   └── flutter/                # Linux Flutter files
│       └── ephemeral/          # Linux ephemeral files
├── macos/                      # macOS platform files
│   ├── Flutter/                # macOS Flutter files
│   │   └── ephemeral/          # macOS ephemeral files
│   ├── Runner/                 # macOS Runner files
│   │   ├── Assets.xcassets/    # macOS assets
│   │   ├── Base.lproj/         # macOS base localization
│   │   └── Configs/            # macOS configs
│   ├── Runner.xcodeproj/       # Xcode project
│   │   ├── project.xcworkspace/  # Xcode workspace
│   │   └── xcshareddata/       # Xcode shared data
│   ├── Runner.xcworkspace/     # Xcode workspace
│   │   └── xcshareddata/       # Xcode shared data
│   └── RunnerTests/            # macOS tests
├── web/                        # Web platform files
│   └── icons/                  # Web icons
└── windows/                    # Windows platform files
    ├── flutter/                # Windows Flutter files
    │   └── ephemeral/          # Windows ephemeral files
    └── runner/                 # Windows runner files
        └── resources/          # Windows resources
```

## Test Files

```
lekky/test/
├── average_calculator_test.dart  # Average calculator test
├── language_settings_test.dart   # Language settings test
├── widget_test.dart              # Widget test
├── core/                         # Core tests
│   └── utils/                    # Utils tests
│       ├── cost_calculator_test.dart  # Cost calculator test
│       ├── error_handler_test.dart    # Error handler test
│       ├── logger_test.dart           # Logger test
│       └── result_test.dart           # Result test
├── features/                     # Features tests
│   └── backup/                   # Backup tests
│       └── backup_service_test.dart  # Backup service test
├── utils/                        # Utils tests
│   └── usage_estimator_test.dart  # Usage estimator test
└── widgets/                      # Widget tests
    └── app_button_styles_test.dart  # App button styles test
```

## Project Documentation

```
lekky/0.Project Details/
├── files_and_folders_used.md     # This file
└── secondary/                    # Secondary documentation
```

## NOTES & RULES

### Project Structure Conventions

1. **Feature-First Architecture**: The project follows a feature-first modular architecture with a layered structure (Presentation, Domain, Data).

2. **Folder Organization**:
   - Screens go in `lib/features/[feature]/presentation/screens/`
   - Widgets go in `lib/features/[feature]/presentation/widgets/`
   - Models go in `lib/features/[feature]/domain/models/`
   - Providers go in `lib/providers/` or `lib/core/providers/`
   - Services go in `lib/features/[feature]/data/` or `lib/core/services/`
   - Utilities go in `lib/core/utils/`
   - Themes go in `lib/core/theme/`

3. **Naming Conventions**:
   - All files should follow the snake_case naming convention
   - Classes should follow PascalCase
   - Files should be named after their primary class or function
   - Feature folders should be named in singular form

4. **Code Organization**:
   - Follow Single Responsibility Principle with files between 300-600 lines
   - Use dependency injection and state management (Provider/Riverpod) to separate UI from business logic
   - Implement proper separation of concerns between layers

5. **Data Storage**:
   - Persistent data is stored using sqflite, shared_preferences
   - Backup data is saved to Downloads folder as lekky_export_[version].csv with version in header
   - Use numeric codes for data types (0 = Meter Reading, 1 = Top Up) in exports and database

### Future Cleanup Tasks

1. Remove any files with `.dartBU` extension as they are backups and not needed for production
2. Consider consolidating duplicate implementations in the settings widgets
3. Review and clean up any temporary files in the `For_Consideration` directory
4. Ensure all test files have corresponding implementation files
