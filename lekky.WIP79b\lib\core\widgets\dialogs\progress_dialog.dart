// File: lib/core/widgets/dialogs/progress_dialog.dart
import 'package:flutter/material.dart';
import '../../theme/app_colors.dart';
import '../../theme/app_text_styles.dart';
import '../app_dialog.dart';

/// A specialized dialog for indicating ongoing operations.
/// 
/// This dialog presents a simple title, progress indicator,
/// and optionally a cancel button.
class ProgressDialog {
  /// Shows an indeterminate progress dialog.
  /// 
  /// Parameters:
  /// - [context]: The build context.
  /// - [title]: The title of the dialog, should indicate the operation.
  /// - [message]: Optional message to display below the progress indicator.
  /// - [cancelText]: Optional text for the cancel button. If null, no cancel button is shown.
  /// - [onCancel]: Optional callback for when the cancel button is pressed.
  /// - [barrierDismissible]: Whether the dialog can be dismissed by tapping outside (default: false).
  static Future<void> show({
    required BuildContext context,
    required String title,
    String? message,
    String? cancelText,
    VoidCallback? onCancel,
    bool barrierDismissible = false,
  }) async {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    // Create the content widget with the progress indicator
    final Widget content = Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        const SizedBox(height: 8),
        CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(
            isDarkMode ? AppColors.primaryDark : AppColors.primary,
          ),
        ),
        const SizedBox(height: 16),
        if (message != null)
          Text(
            message,
            style: AppTextStyles.bodyMedium.copyWith(
              color: isDarkMode
                  ? AppColors.onSurfaceDark.withOpacity(0.8)
                  : AppColors.onSurface.withOpacity(0.8),
            ),
            textAlign: TextAlign.center,
          ),
      ],
    );
    
    // Create the actions
    final List<Widget>? actions = cancelText != null && onCancel != null
        ? [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                onCancel();
              },
              child: Text(cancelText),
            ),
          ]
        : null;
    
    return AppDialog.show(
      context: context,
      title: title,
      content: content,
      actions: actions,
      barrierDismissible: barrierDismissible,
    );
  }
  
  /// Shows a determinate progress dialog with a progress bar.
  /// 
  /// Parameters:
  /// - [context]: The build context.
  /// - [title]: The title of the dialog, should indicate the operation.
  /// - [progress]: The current progress value between 0.0 and 1.0.
  /// - [message]: Optional message to display below the progress indicator.
  /// - [cancelText]: Optional text for the cancel button. If null, no cancel button is shown.
  /// - [onCancel]: Optional callback for when the cancel button is pressed.
  /// - [barrierDismissible]: Whether the dialog can be dismissed by tapping outside (default: false).
  static Future<void> showDeterminate({
    required BuildContext context,
    required String title,
    required double progress,
    String? message,
    String? cancelText,
    VoidCallback? onCancel,
    bool barrierDismissible = false,
  }) async {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    // Create the content widget with the progress indicator
    final Widget content = Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        const SizedBox(height: 8),
        LinearProgressIndicator(
          value: progress.clamp(0.0, 1.0),
          valueColor: AlwaysStoppedAnimation<Color>(
            isDarkMode ? AppColors.primaryDark : AppColors.primary,
          ),
          backgroundColor: isDarkMode
              ? AppColors.surfaceVariantDark
              : AppColors.surfaceVariant,
        ),
        const SizedBox(height: 8),
        Text(
          '${(progress * 100).toInt()}%',
          style: AppTextStyles.bodyMedium.copyWith(
            fontWeight: FontWeight.bold,
            color: isDarkMode ? AppColors.primaryDark : AppColors.primary,
          ),
        ),
        const SizedBox(height: 8),
        if (message != null)
          Text(
            message,
            style: AppTextStyles.bodyMedium.copyWith(
              color: isDarkMode
                  ? AppColors.onSurfaceDark.withOpacity(0.8)
                  : AppColors.onSurface.withOpacity(0.8),
            ),
            textAlign: TextAlign.center,
          ),
      ],
    );
    
    // Create the actions
    final List<Widget>? actions = cancelText != null && onCancel != null
        ? [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                onCancel();
              },
              child: Text(cancelText),
            ),
          ]
        : null;
    
    return AppDialog.show(
      context: context,
      title: title,
      content: content,
      actions: actions,
      barrierDismissible: barrierDismissible,
    );
  }
  
  /// Shows a progress dialog that automatically updates its progress.
  /// 
  /// This method creates a dialog that shows progress updates from a stream.
  /// It's useful for operations that can report progress incrementally.
  /// 
  /// Parameters:
  /// - [context]: The build context.
  /// - [title]: The title of the dialog, should indicate the operation.
  /// - [progressStream]: A stream of progress values between 0.0 and 1.0.
  /// - [messageStream]: Optional stream of messages to display below the progress indicator.
  /// - [cancelText]: Optional text for the cancel button. If null, no cancel button is shown.
  /// - [onCancel]: Optional callback for when the cancel button is pressed.
  /// - [barrierDismissible]: Whether the dialog can be dismissed by tapping outside (default: false).
  /// - [onComplete]: Optional callback for when the progress reaches 1.0.
  static Future<void> showWithStream({
    required BuildContext context,
    required String title,
    required Stream<double> progressStream,
    Stream<String>? messageStream,
    String? cancelText,
    VoidCallback? onCancel,
    bool barrierDismissible = false,
    VoidCallback? onComplete,
  }) async {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return showDialog(
      context: context,
      barrierDismissible: barrierDismissible,
      builder: (BuildContext context) {
        return StreamBuilder<double>(
          stream: progressStream,
          builder: (context, progressSnapshot) {
            final double progress = progressSnapshot.data?.clamp(0.0, 1.0) ?? 0.0;
            
            // Auto-dismiss when progress reaches 1.0
            if (progress >= 1.0 && onComplete != null) {
              WidgetsBinding.instance.addPostFrameCallback((_) {
                Navigator.of(context).pop();
                onComplete();
              });
            }
            
            return StreamBuilder<String>(
              stream: messageStream,
              builder: (context, messageSnapshot) {
                final String? message = messageSnapshot.data;
                
                return AppDialog(
                  title: title,
                  content: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const SizedBox(height: 8),
                      LinearProgressIndicator(
                        value: progress,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          isDarkMode ? AppColors.primaryDark : AppColors.primary,
                        ),
                        backgroundColor: isDarkMode
                            ? AppColors.surfaceVariantDark
                            : AppColors.surfaceVariant,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        '${(progress * 100).toInt()}%',
                        style: AppTextStyles.bodyMedium.copyWith(
                          fontWeight: FontWeight.bold,
                          color: isDarkMode ? AppColors.primaryDark : AppColors.primary,
                        ),
                      ),
                      const SizedBox(height: 8),
                      if (message != null)
                        Text(
                          message,
                          style: AppTextStyles.bodyMedium.copyWith(
                            color: isDarkMode
                                ? AppColors.onSurfaceDark.withOpacity(0.8)
                                : AppColors.onSurface.withOpacity(0.8),
                          ),
                          textAlign: TextAlign.center,
                        ),
                    ],
                  ),
                  actions: cancelText != null && onCancel != null
                      ? [
                          TextButton(
                            onPressed: () {
                              Navigator.of(context).pop();
                              onCancel();
                            },
                            child: Text(cancelText),
                          ),
                        ]
                      : null,
                );
              },
            );
          },
        );
      },
    );
  }
}
