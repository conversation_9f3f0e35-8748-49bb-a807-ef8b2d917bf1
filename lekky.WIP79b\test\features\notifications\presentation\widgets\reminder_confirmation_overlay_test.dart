// File: test/features/notifications/presentation/widgets/reminder_confirmation_overlay_test.dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:lekky/features/notifications/presentation/widgets/reminder_confirmation_overlay.dart';

void main() {
  testWidgets('ReminderConfirmationOverlay shows and animates correctly',
      (WidgetTester tester) async {
    bool dismissCalled = false;

    // Build our widget under test
    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: Center(
            child: ReminderConfirmationOverlay(
              message: 'Your reminder has been scheduled',
              duration: const Duration(seconds: 2),
              onDismiss: () {
                dismissCalled = true;
              },
            ),
          ),
        ),
      ),
    );

    // The overlay should be visible with the message
    expect(find.text('Your reminder has been scheduled'), findsOneWidget);

    // The title should be visible
    expect(find.text('Reminder Scheduled'), findsOneWidget);

    // The check icon should be visible
    expect(find.byIcon(Icons.check_circle), findsOneWidget);

    // Pump half the animation duration to see it partially animated
    await tester.pump(const Duration(milliseconds: 250));

    // Pump to complete the fade-in animation
    await tester.pump(const Duration(milliseconds: 250));

    // The overlay should be fully visible now

    // Wait for the display duration
    await tester.pump(const Duration(seconds: 2));

    // Pump to start the fade-out animation
    await tester.pump();

    // The overlay should still be visible during fade-out
    expect(find.text('Your reminder has been scheduled'), findsOneWidget);

    // Pump to complete the fade-out animation
    await tester.pump(const Duration(milliseconds: 500));

    // Verify the onDismiss callback was called
    expect(dismissCalled, true);
  });

  testWidgets(
      'ReminderConfirmationOverlay uses default duration when not specified',
      (WidgetTester tester) async {
    // Build our widget under test with default duration
    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: Center(
            child: ReminderConfirmationOverlay(
              message: 'Using default duration',
              onDismiss: () {},
            ),
          ),
        ),
      ),
    );

    // The overlay should be visible with the message
    expect(find.text('Using default duration'), findsOneWidget);

    // Complete the animation
    await tester.pump(const Duration(milliseconds: 500));

    // Wait for part of the default duration (3 seconds)
    await tester.pump(const Duration(seconds: 1));

    // The overlay should still be visible
    expect(find.text('Using default duration'), findsOneWidget);

    // Wait for the rest of the default duration
    await tester.pump(const Duration(seconds: 2));

    // Pump to start the fade-out animation
    await tester.pump();

    // The overlay should start fading out
    expect(find.text('Using default duration'), findsOneWidget);
  });

  testWidgets('showReminderConfirmation function adds overlay to screen',
      (WidgetTester tester) async {
    // Build a basic app with overlay support
    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: Builder(
            builder: (context) {
              return Center(
                child: ElevatedButton(
                  onPressed: () {
                    showReminderConfirmation(
                      context,
                      message: 'Confirmation shown via function',
                      duration: const Duration(seconds: 1),
                    );
                  },
                  child: const Text('Show Confirmation'),
                ),
              );
            },
          ),
        ),
      ),
    );

    // Initially, no confirmation is shown
    expect(find.text('Confirmation shown via function'), findsNothing);

    // Tap the button to show the confirmation
    await tester.tap(find.text('Show Confirmation'));
    await tester.pump();

    // The confirmation should be visible in the overlay
    expect(find.text('Confirmation shown via function'), findsOneWidget);

    // Complete the animation
    await tester.pump(const Duration(milliseconds: 500));

    // Wait for the display duration
    await tester.pump(const Duration(seconds: 1));

    // Pump to start the fade-out animation
    await tester.pump();

    // Pump to complete the fade-out animation
    await tester.pump(const Duration(milliseconds: 500));

    // The overlay should be removed after animation completes
    // Note: In widget tests, removed overlay entries might still be found
    // because the overlay system works differently in tests
  });
}
