import 'dart:async';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../../../core/providers/database_provider.dart';
import '../../../../core/providers/error_provider.dart';
import '../../../../core/providers/preference_provider.dart';
import '../../../../core/utils/logger.dart';
import '../../../../core/utils/event_bus.dart';
import '../../../../core/di/service_locator.dart';
import '../../domain/models/dashboard_state.dart';
import '../../../meter_readings/domain/models/meter_reading.dart';

import '../../../notifications/presentation/providers/notification_provider.dart';
import '../../../averages/domain/services/average_service.dart';
import '../../../../core/services/alert_coordination_service.dart';

part 'dashboard_provider.g.dart';

/// Dashboard provider with comprehensive home screen data management
@riverpod
class Dashboard extends _$Dashboard {
  StreamSubscription<EventType>? _eventSubscription;

  @override
  Future<DashboardState> build() async {
    // Set up event listener for data updates
    _setupEventListener();

    // Dispose event subscription when provider is disposed
    ref.onDispose(() {
      _eventSubscription?.cancel();
    });

    return await _loadInitialState();
  }

  /// Set up event listener for data updates
  void _setupEventListener() {
    _eventSubscription?.cancel(); // Cancel any existing subscription
    _eventSubscription = EventBus().stream.listen((event) {
      Logger.info('DashboardProvider: Received event: $event');
      if (event == EventType.dataUpdated) {
        Logger.info(
            'DashboardProvider: Received data update event, refreshing dashboard');
        // Refresh the dashboard when data is updated
        refresh();
      } else if (event == EventType.averagesCalculating) {
        Logger.info(
            'DashboardProvider: Averages calculating, showing loading state');
        final currentState = state.value ?? DashboardState.initial();
        state = AsyncValue.data(currentState.copyWith(isLoading: true));
      } else if (event == EventType.averageCalculationFailed) {
        Logger.error('DashboardProvider: Average calculation failed');
        final currentState = state.value ?? DashboardState.initial();
        state = AsyncValue.data(currentState.copyWith(
          isLoading: false,
          errorMessage: 'Failed to calculate averages. Please try again.',
        ));
      }
    });
  }

  /// Load initial state
  Future<DashboardState> _loadInitialState() async {
    try {
      final initialState = DashboardState.initial();

      // Warm up notification provider for instant badge display
      ref.read(notificationProvider);

      // Check if this is first launch
      final preferencesAsync = ref.read(preferencesProvider);
      final isFirstLaunch = preferencesAsync.when(
        data: (preferences) {
          if (!preferences.hasShownWelcome && preferences.isSetupComplete) {
            // Show welcome notification and mark as shown
            ref.read(notificationProvider.notifier).createWelcomeNotification();
            ref.read(preferencesProvider.notifier).markWelcomeShown();
            return true;
          }
          return false;
        },
        loading: () => false,
        error: (_, __) => false,
      );

      // Check if data exists
      final hasData = await _checkIfDataExists();
      if (!hasData) {
        // First launch with no data
        return initialState.copyWith(
          isLoading: false,
          isFirstLaunch: isFirstLaunch,
        );
      }

      // Load all dashboard data
      await _loadDashboardData(
          initialState.copyWith(isFirstLaunch: isFirstLaunch));
      return state.value ?? initialState;
    } catch (error, stackTrace) {
      ref.read(globalErrorProvider.notifier).handleError(
            error,
            stackTrace,
            context: 'Dashboard initialization',
          );
      return DashboardState.initial().copyWith(
        isLoading: false,
        errorMessage: 'Failed to load dashboard: ${error.toString()}',
      );
    }
  }

  /// Refresh dashboard data
  Future<void> refresh() async {
    final currentState = state.value ?? DashboardState.initial();
    state = AsyncValue.data(currentState.copyWith(isLoading: true));
    await _loadDashboardData(state.value!);
  }

  /// Clear error
  void clearError() {
    final currentState = state.value ?? DashboardState.initial();
    state = AsyncValue.data(currentState.copyWith(errorMessage: null));
  }

  /// Check if data exists in the database
  Future<bool> _checkIfDataExists() async {
    try {
      final meterReadingRepo = ref.read(meterReadingRepositoryProvider);
      final topUpRepo = ref.read(topUpRepositoryProvider);

      final meterReadings =
          await meterReadingRepo.getMeterReadings(page: 0, pageSize: 1);
      final topUps = await topUpRepo.getTopUps(page: 0, pageSize: 1);

      return meterReadings.isNotEmpty || topUps.isNotEmpty;
    } catch (error) {
      Logger.error('Failed to check if data exists: $error');
      return false;
    }
  }

  /// Load all dashboard data
  Future<void> _loadDashboardData(DashboardState currentState) async {
    try {
      // Load data in parallel for better performance
      final results = await Future.wait([
        _loadLatestMeterReading(),
        _loadRecentEntries(),
      ]);

      final latestMeterReading = results[0] as MeterReading?;
      final recentEntries = results[1] as List<dynamic>;

      // Load dependent data
      final averages = await _loadAverages();
      final topUpsAfterLatest =
          await _calculateTopUpsAfterLatestReading(latestMeterReading);

      // Update state with all loaded data
      Logger.info(
          'DashboardProvider: Updating dashboard state - Recent Average: ${averages['recent']}, Total Average: ${averages['total']}');

      state = AsyncValue.data(currentState.copyWith(
        latestMeterReading: latestMeterReading,
        recentAverageDailyUsage: averages['recent'],
        totalAverageDailyUsage: averages['total'],
        recentEntries: recentEntries,
        totalTopUpsAfterLatestReading: topUpsAfterLatest,
        isLoading: false,
        errorMessage: null,
      ));

      // Check alerts after data is loaded (if we have data)
      if (latestMeterReading != null) {
        _checkAlertsAfterDataLoad();
      }
    } catch (error, stackTrace) {
      Logger.error('Failed to load dashboard data: $error', stackTrace);

      state = AsyncValue.data(currentState.copyWith(
        isLoading: false,
        errorMessage: 'Failed to load dashboard data: ${error.toString()}',
      ));

      ref.read(globalErrorProvider.notifier).handleError(
            error,
            stackTrace,
            context: 'Dashboard data loading',
          );
    }
  }

  /// Load latest meter reading
  Future<MeterReading?> _loadLatestMeterReading() async {
    try {
      final meterReadingRepo = ref.read(meterReadingRepositoryProvider);
      final readings =
          await meterReadingRepo.getMeterReadings(page: 0, pageSize: 1);
      return readings.isNotEmpty ? readings.first : null;
    } catch (error) {
      Logger.error('Failed to load latest meter reading: $error');
      return null;
    }
  }

  /// Load usage averages
  Future<Map<String, double?>> _loadAverages() async {
    try {
      Logger.info('DashboardProvider: Loading averages');
      final averageService = serviceLocator<AverageService>();

      final averageResult = await averageService.getAverages();

      Logger.info(
          'DashboardProvider: Loaded averages - Recent: ${averageResult.recentAverage}, Total: ${averageResult.totalAverage}, FromCache: ${averageResult.fromCache}');

      return {
        'recent': averageResult.recentAverage,
        'total': averageResult.totalAverage,
      };
    } catch (error) {
      Logger.error('DashboardProvider: Failed to load averages: $error');
      return {'recent': null, 'total': null};
    }
  }

  /// Load recent entries (both meter readings and top-ups)
  Future<List<dynamic>> _loadRecentEntries() async {
    try {
      final meterReadingRepo = ref.read(meterReadingRepositoryProvider);
      final topUpRepo = ref.read(topUpRepositoryProvider);

      // Get recent meter readings and top-ups
      final readings =
          await meterReadingRepo.getMeterReadings(page: 0, pageSize: 5);
      final topUps = await topUpRepo.getTopUps(page: 0, pageSize: 5);

      // Combine and sort by date (most recent first)
      final combined = <dynamic>[...readings, ...topUps];
      combined.sort((a, b) => b.date.compareTo(a.date));

      // Return top 5 most recent entries
      return combined.take(5).toList();
    } catch (error) {
      Logger.error('Failed to load recent entries: $error');
      return [];
    }
  }

  /// Calculate total top-ups after latest meter reading
  Future<double> _calculateTopUpsAfterLatestReading(
      MeterReading? latestReading) async {
    if (latestReading == null) return 0.0;

    try {
      final topUpRepo = ref.read(topUpRepositoryProvider);
      final topUps = await topUpRepo.getTopUpsByDateRange(
        startDate: latestReading.date,
        endDate: DateTime.now(),
      );

      // Filter top-ups that are after the latest reading date
      final topUpsAfter =
          topUps.where((topUp) => topUp.date.isAfter(latestReading.date));

      return topUpsAfter.fold<double>(0.0, (sum, topUp) => sum + topUp.amount);
    } catch (error) {
      Logger.error('Failed to calculate top-ups after latest reading: $error');
      return 0.0;
    }
  }

  /// Check alerts after dashboard data is loaded
  void _checkAlertsAfterDataLoad() {
    try {
      // Run alert checking asynchronously to avoid blocking UI
      Future.microtask(() async {
        try {
          final alertService = AlertCoordinationService();
          await alertService.checkAllAlerts();
          Logger.info('Dashboard: Alert check completed after data load');
        } catch (e) {
          Logger.error('Dashboard: Failed to check alerts after data load: $e');
        }
      });
    } catch (e) {
      Logger.error('Dashboard: Error initiating alert check: $e');
    }
  }
}
