// File: lib/features/history/domain/usecases/validate_entries.dart
import '../../../../core/data/repositories/meter_entry_repository.dart';
import '../../../../core/models/meter_entry.dart';
import '../models/validation_result.dart';
import '../models/related_validation_result.dart';

/// Use case for validating meter entries
class ValidateEntries {
  final MeterEntryRepository _repository;

  ValidateEntries(this._repository);

  /// Execute the use case to validate a single entry
  Future<ValidationResult> execute(MeterEntry entry) async {
    if (entry.amountToppedUp > 0) {
      // Top-up entries are always valid
      return ValidationResult.valid();
    }

    // Validate meter reading
    final result = await _repository.validateMeterReading(
      entry.reading,
      entry.timestamp,
      entryId: entry.id,
    );

    // Check if this is a RelatedValidationResult
    if (result.containsKey('relatedEntryId') &&
        result['relatedEntryId'] != null) {
      // Create a ValidationResult with the appropriate message
      final isValid = result['isValid'] as bool? ?? false;
      final severity = result['severity'] as String? ?? 'none';

      // Get the related entry information
      final relatedEntryDate = result['relatedEntryDate'] != null
          ? DateTime.parse(result['relatedEntryDate'] as String)
          : null;
      final relatedEntryReading = result['relatedEntryReading'] as double?;
      final isEarlierEntry = result['isEarlierEntry'] as bool? ?? false;

      // Create a detailed error message
      String errorMessage =
          result['errorMessage'] as String? ?? 'Invalid entry';
      if (relatedEntryDate != null && relatedEntryReading != null) {
        final formattedDate =
            '${relatedEntryDate.day}/${relatedEntryDate.month}/${relatedEntryDate.year}';
        final formattedReading = relatedEntryReading.toStringAsFixed(2);

        if (isEarlierEntry) {
          errorMessage =
              'This reading may be too low compared to the reading on $formattedDate (£$formattedReading)';
        } else {
          errorMessage =
              'This reading may be too high compared to the reading on $formattedDate (£$formattedReading)';
        }
      }

      return ValidationResult(
        isValid: isValid,
        errorMessage: errorMessage,
        severity: severity,
      );
    }

    return ValidationResult.fromMap(result);
  }

  /// Execute the use case to validate multiple entries
  Future<Map<int, ValidationResult>> executeMultiple(
      List<MeterEntry> entries) async {
    final Map<int, ValidationResult> results = {};

    // Get validation results from the repository
    final validationResults = await _repository.validateAllEntries();

    // Convert the repository results to ValidationResult objects
    for (final entry in entries) {
      if (entry.id != null) {
        if (entry.amountToppedUp > 0) {
          // Top-up entries are always valid
          results[entry.id!] = ValidationResult.valid();
        } else if (validationResults.containsKey(entry.id)) {
          // Convert the map to a ValidationResult
          final result = validationResults[entry.id!];
          if (result != null) {
            // Check if this is a RelatedValidationResult
            if (result.containsKey('relatedEntryId') &&
                result['relatedEntryId'] != null) {
              // Create a ValidationResult with the appropriate message
              final isValid = result['isValid'] as bool? ?? false;
              final severity = result['severity'] as String? ?? 'none';

              // Get the related entry information
              final relatedEntryId = result['relatedEntryId'] as int?;
              final relatedEntryDate = result['relatedEntryDate'] != null
                  ? DateTime.parse(result['relatedEntryDate'] as String)
                  : null;
              final relatedEntryReading =
                  result['relatedEntryReading'] as double?;
              final isEarlierEntry = result['isEarlierEntry'] as bool? ?? false;

              // Create a detailed error message
              String errorMessage =
                  result['errorMessage'] as String? ?? 'Invalid entry';
              if (relatedEntryDate != null && relatedEntryReading != null) {
                final formattedDate =
                    '${relatedEntryDate.day}/${relatedEntryDate.month}/${relatedEntryDate.year}';
                final formattedReading = relatedEntryReading.toStringAsFixed(2);

                if (isEarlierEntry) {
                  errorMessage =
                      'This reading may be too low compared to the reading on $formattedDate (£$formattedReading)';
                } else {
                  errorMessage =
                      'This reading may be too high compared to the reading on $formattedDate (£$formattedReading)';
                }
              }

              // Create a RelatedValidationResult for the current entry
              results[entry.id!] = RelatedValidationResult(
                isValid: isValid,
                errorMessage: errorMessage,
                severity: severity,
                relatedEntryId: relatedEntryId,
                relatedEntryDate: relatedEntryDate,
                relatedEntryReading: relatedEntryReading,
                isEarlierEntry: isEarlierEntry,
              );

              // Always mark the related entry as invalid if it exists
              if (relatedEntryId != null) {
                // Create the opposite message (if this is too high, the other is too low)
                String relatedErrorMessage = 'Invalid entry';
                if (relatedEntryDate != null) {
                  final formattedDate =
                      '${entry.timestamp.day}/${entry.timestamp.month}/${entry.timestamp.year}';
                  final formattedReading = entry.reading.toStringAsFixed(2);

                  if (isEarlierEntry) {
                    relatedErrorMessage =
                        'This reading may be too high compared to the reading on $formattedDate (£$formattedReading)';
                  } else {
                    relatedErrorMessage =
                        'This reading may be too low compared to the reading on $formattedDate (£$formattedReading)';
                  }
                }

                // Create a RelatedValidationResult for the related entry
                results[relatedEntryId] = RelatedValidationResult(
                  isValid: false,
                  errorMessage: relatedErrorMessage,
                  severity: severity,
                  relatedEntryId: entry.id,
                  relatedEntryDate: entry.timestamp,
                  relatedEntryReading: entry.reading,
                  isEarlierEntry:
                      !isEarlierEntry, // Opposite of the current entry
                );
              }
            } else {
              // Regular ValidationResult
              results[entry.id!] = ValidationResult.fromMap(result);
            }
          } else {
            // No result found, mark as valid
            results[entry.id!] = ValidationResult.valid();
          }
        } else {
          // No result found, mark as valid
          results[entry.id!] = ValidationResult.valid();
        }
      }
    }

    return results;
  }
}
