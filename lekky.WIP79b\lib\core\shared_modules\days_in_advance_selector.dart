// File: lib/core/shared_modules/days_in_advance_selector.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../theme/app_colors.dart';
import '../theme/app_text_styles.dart';
import '../widgets/app_text_field.dart';
import 'settings_model.dart';

class DaysInAdvanceSelector extends StatefulWidget {
  final int currentValue;
  final ValueChanged<int> onChanged;
  final String? errorText;
  final bool hasTotalAverage;
  final SettingsDisplayMode displayMode;
  final bool showHelperText;
  final bool showTitle;

  // Common day options
  static const List<int> dayOptions = [1, 2, 3, 5, 7, 14];

  const DaysInAdvanceSelector({
    Key? key,
    required this.currentValue,
    required this.onChanged,
    this.errorText,
    this.hasTotalAverage = false,
    this.displayMode = SettingsDisplayMode.compact,
    this.showHelperText = false,
    this.showTitle = true,
  }) : super(key: key);

  @override
  State<DaysInAdvanceSelector> createState() => _DaysInAdvanceSelectorState();
}

class _DaysInAdvanceSelectorState extends State<DaysInAdvanceSelector> {
  late TextEditingController _controller;
  late FocusNode _focusNode;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(
      text: widget.currentValue.toString(),
    );
    _focusNode = FocusNode()
      ..addListener(() {
        if (_focusNode.hasFocus) {
          _controller.selection = TextSelection(
            baseOffset: 0,
            extentOffset: _controller.text.length,
          );
        }
      });
  }

  @override
  void didUpdateWidget(covariant DaysInAdvanceSelector oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Only update the controller text if the external value changed and we're not focused
    if (!_focusNode.hasFocus && widget.currentValue != oldWidget.currentValue) {
      _controller.text = widget.currentValue.toString();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return widget.displayMode == SettingsDisplayMode.expanded
        ? _buildExpandedView(context)
        : _buildCompactView(context);
  }

  Widget _buildExpandedView(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.showTitle) _buildSectionTitle(context, 'Days in Advance'),

        if (widget.showHelperText)
          _buildHelperText(context,
              'How many days in advance should we notify you about low balance?'),

        if (!widget.hasTotalAverage) ...[
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppColors.warning.withOpacity(0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                const Icon(Icons.info_outline,
                    color: AppColors.warning, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Days in advance alerts will be active after you enter at least two meter readings to calculate your average usage.',
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: Theme.of(context).brightness == Brightness.dark
                          ? AppColors.warning
                          : AppColors.onBackground,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],

        const SizedBox(height: 16),

        // Custom input field
        AppTextField(
          controller: _controller,
          focusNode: _focusNode,
          keyboardType: TextInputType.number,
          inputFormatters: [
            FilteringTextInputFormatter.allow(RegExp(r'^[1-9][0-9]?$')),
          ],
          labelText: 'Days in Advance (1-99)',
          helperText: 'Enter a value between 1 and 99 days',
          selectAllOnFocus: false, // We handle selection in the focus listener
          onChanged: (value) {
            final parsedValue = int.tryParse(value);
            if (parsedValue != null && parsedValue >= 1 && parsedValue <= 99) {
              widget.onChanged(parsedValue);
            }
          },
        ),

        const SizedBox(height: 8),
        const Text(
          'Tip: Consider your usage patterns when setting this value. If you use electricity quickly, choose fewer days.',
          style: AppTextStyles.helperText,
        ),

        if (widget.errorText != null) ...[
          const SizedBox(height: 8),
          Text(
            widget.errorText!,
            style: AppTextStyles.errorText,
          ),
        ],
      ],
    );
  }

  Widget _buildCompactView(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.showTitle) _buildLabel(context, 'Days in Advance'),

        if (widget.showTitle)
          Text(
            'Current: ${widget.currentValue == 1 ? "1 day" : "${widget.currentValue} days"}',
            style: AppTextStyles.helperText,
          ),

        if (!widget.hasTotalAverage) ...[
          const SizedBox(height: 4),
          Row(
            children: [
              const Icon(Icons.info_outline,
                  color: AppColors.warning, size: 14),
              const SizedBox(width: 4),
              Expanded(
                child: Text(
                  'Requires two meter readings',
                  style: AppTextStyles.helperText.copyWith(
                    color:
                        isDarkMode ? AppColors.warning : AppColors.onBackground,
                  ),
                ),
              ),
            ],
          ),
        ],

        if (widget.showHelperText)
          _buildHelperText(
              context, 'Set how many days before you run out to be notified'),

        const SizedBox(height: 8),

        // Custom input field - more compact
        AppTextField(
          controller: _controller,
          focusNode: _focusNode,
          keyboardType: TextInputType.number,
          inputFormatters: [
            FilteringTextInputFormatter.allow(RegExp(r'^[1-9][0-9]?$')),
          ],
          labelText: 'Days (1-99)',
          helperText: 'Enter days in advance (1-99)',
          isDense: true,
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
          selectAllOnFocus: false, // We handle selection in the focus listener
          onChanged: (value) {
            final parsedValue = int.tryParse(value);
            if (parsedValue != null && parsedValue >= 1 && parsedValue <= 99) {
              widget.onChanged(parsedValue);
            }
          },
        ),

        if (widget.errorText != null) ...[
          const SizedBox(height: 8),
          Text(
            widget.errorText!,
            style: AppTextStyles.errorText,
          ),
        ],
      ],
    );
  }

  Widget _buildSectionTitle(BuildContext context, String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Text(
        title,
        style: AppTextStyles.titleMedium.copyWith(
          color: Theme.of(context).brightness == Brightness.dark
              ? Colors.white
              : AppColors.onBackground,
        ),
      ),
    );
  }

  Widget _buildLabel(BuildContext context, String label) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4.0),
      child: Text(
        label,
        style: AppTextStyles.labelLarge.copyWith(
          color: Theme.of(context).brightness == Brightness.dark
              ? Colors.white
              : AppColors.onBackground,
        ),
      ),
    );
  }

  Widget _buildHelperText(BuildContext context, String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Text(
        text,
        style: AppTextStyles.helperText.copyWith(
          color: Theme.of(context).brightness == Brightness.dark
              ? AppColors.helperTextDark
              : AppColors.helperTextLight,
        ),
      ),
    );
  }
}
