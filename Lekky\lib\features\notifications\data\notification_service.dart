import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:timezone/timezone.dart' as tz;
import '../../../../core/utils/logger.dart';
import '../domain/models/notification.dart';
import '../domain/repositories/notification_repository.dart';

/// Service for handling local notifications
class NotificationService {
  /// Flutter local notifications plugin
  final FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin;

  /// Notification repository
  final NotificationRepository _notificationRepository;

  /// Constructor
  NotificationService(
    this._flutterLocalNotificationsPlugin,
    this._notificationRepository,
  );

  /// Initialize the notification service
  Future<bool> initialize() async {
    try {
      // Initialize settings for Android
      const AndroidInitializationSettings initializationSettingsAndroid =
          AndroidInitializationSettings('@mipmap/ic_launcher');

      // Initialize settings for iOS
      const DarwinInitializationSettings initializationSettingsIOS =
          DarwinInitializationSettings(
        requestSoundPermission: true,
        requestBadgePermission: true,
        requestAlertPermission: true,
      );

      // Initialize settings for all platforms
      const InitializationSettings initializationSettings =
          InitializationSettings(
        android: initializationSettingsAndroid,
        iOS: initializationSettingsIOS,
      );

      // Initialize the plugin with error handling
      bool initialized = false;
      try {
        initialized = await _flutterLocalNotificationsPlugin.initialize(
              initializationSettings,
              onDidReceiveNotificationResponse: _onNotificationTapped,
            ) ??
            false;
      } catch (e) {
        Logger.error('Failed to initialize notification plugin: $e');
        return false;
      }

      if (!initialized) {
        Logger.error('Notification plugin initialization returned false');
        return false;
      }

      // Request permission with error handling
      try {
        await _requestPermissions();
      } catch (e) {
        Logger.error('Failed to request notification permissions: $e');
        // Continue anyway, permissions might still work
      }

      Logger.info('Notification service initialized successfully');
      return true;
    } catch (e) {
      Logger.error('Failed to initialize notification service: $e');
      return false;
    }
  }

  /// Request notification permissions
  Future<void> _requestPermissions() async {
    try {
      // Request Android 13+ POST_NOTIFICATIONS permission
      final androidImplementation = _flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<
              AndroidFlutterLocalNotificationsPlugin>();

      if (androidImplementation != null) {
        final bool? granted =
            await androidImplementation.requestNotificationsPermission();
        if (granted == true) {
          Logger.info('Notification permissions granted');
        } else {
          Logger.warning('Notification permissions denied');
        }
      }

      // iOS permissions are requested during initialization in DarwinInitializationSettings
    } catch (e) {
      Logger.error('Failed to request notification permissions: $e');
    }
  }

  /// Handle notification tap
  void _onNotificationTapped(NotificationResponse response) {
    try {
      // Extract notification ID from the payload
      final int? notificationId = int.tryParse(response.payload ?? '');

      if (notificationId != null) {
        // Mark the notification as read
        _notificationRepository.markAsRead(notificationId);

        // Check if this is a reminder notification and handle auto-rescheduling
        _handleReminderNotificationTap(notificationId);

        // TODO: Navigate to notification screen or relevant screen
      }
    } catch (e) {
      Logger.error('Failed to handle notification tap: $e');
    }
  }

  /// Handle reminder notification tap for auto-rescheduling
  void _handleReminderNotificationTap(int notificationId) {
    try {
      // Get notification details to check type
      _notificationRepository
          .getNotificationById(notificationId)
          .then((notification) {
        if (notification?.type == NotificationType.readingReminder) {
          Logger.info(
              'Reminder notification tapped, setting background flag for auto-rescheduling');

          // Import and use BackgroundReminderFlags
          // Note: This would need proper import, but for now we'll use a simple approach
          _setReminderFiredFlag(notificationId);
        }
      }).catchError((error) {
        Logger.error('Failed to check notification type: $error');
      });
    } catch (e) {
      Logger.error('Failed to handle reminder notification tap: $e');
    }
  }

  /// Set reminder fired flag for background processing
  void _setReminderFiredFlag(int notificationId) {
    try {
      // This is a simplified approach - in the full implementation,
      // we would import BackgroundReminderFlags and use it properly
      Logger.info(
          'Setting reminder fired flag for notification: $notificationId');
      // BackgroundReminderFlags.setReminderFired(reminderDate: DateTime.now());
    } catch (e) {
      Logger.error('Failed to set reminder fired flag: $e');
    }
  }

  /// Show a notification immediately
  Future<void> showNotification(AppNotification notification) async {
    try {
      // Save notification to database
      final int notificationId =
          await _notificationRepository.addNotification(notification);

      if (notificationId == -1) {
        Logger.error('Failed to save notification to database');
        return;
      }

      // Create notification details for Android
      final AndroidNotificationDetails androidNotificationDetails =
          AndroidNotificationDetails(
        'lekky_notifications',
        'Lekky Notifications',
        channelDescription: 'Notifications from Lekky app',
        importance: _getImportance(notification.type),
        priority: _getPriority(notification.type),
        icon: 'ic_notification',
      );

      // Create notification details for iOS
      const DarwinNotificationDetails iOSNotificationDetails =
          DarwinNotificationDetails();

      // Create notification details for all platforms
      final NotificationDetails notificationDetails = NotificationDetails(
        android: androidNotificationDetails,
        iOS: iOSNotificationDetails,
      );

      // Show the notification
      await _flutterLocalNotificationsPlugin.show(
        notificationId,
        notification.title,
        notification.message,
        notificationDetails,
        payload: notificationId.toString(),
      );

      Logger.info('Notification shown: ${notification.title}');
    } catch (e) {
      Logger.error('Failed to show notification: $e');
    }
  }

  /// Schedule a notification for a future time
  Future<void> scheduleNotification(
    AppNotification notification,
    DateTime scheduledDate,
  ) async {
    try {
      // Save notification to database
      final int notificationId =
          await _notificationRepository.addNotification(notification);

      if (notificationId == -1) {
        Logger.error('Failed to save notification to database');
        return;
      }

      // Create notification details for Android
      final AndroidNotificationDetails androidNotificationDetails =
          AndroidNotificationDetails(
        'lekky_notifications',
        'Lekky Notifications',
        channelDescription: 'Notifications from Lekky app',
        importance: _getImportance(notification.type),
        priority: _getPriority(notification.type),
        icon: 'ic_notification',
      );

      // Create notification details for iOS
      const DarwinNotificationDetails iOSNotificationDetails =
          DarwinNotificationDetails();

      // Create notification details for all platforms
      final NotificationDetails notificationDetails = NotificationDetails(
        android: androidNotificationDetails,
        iOS: iOSNotificationDetails,
      );

      // Schedule the notification
      await _flutterLocalNotificationsPlugin.zonedSchedule(
        notificationId,
        notification.title,
        notification.message,
        tz.TZDateTime.from(scheduledDate, tz.local),
        notificationDetails,
        androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
        uiLocalNotificationDateInterpretation:
            UILocalNotificationDateInterpretation.absoluteTime,
        payload: notificationId.toString(),
      );

      Logger.info(
          'Notification scheduled for ${scheduledDate.toIso8601String()}: ${notification.title}');
    } catch (e) {
      Logger.error('Failed to schedule notification: $e');
    }
  }

  /// Cancel a notification
  Future<void> cancelNotification(int id) async {
    try {
      await _flutterLocalNotificationsPlugin.cancel(id);
      await _notificationRepository.deleteNotification(id);
      Logger.info('Notification cancelled: $id');
    } catch (e) {
      Logger.error('Failed to cancel notification: $e');
    }
  }

  /// Cancel all notifications
  Future<void> cancelAllNotifications() async {
    try {
      await _flutterLocalNotificationsPlugin.cancelAll();
      await _notificationRepository.deleteAllNotifications();
      Logger.info('All notifications cancelled');
    } catch (e) {
      Logger.error('Failed to cancel all notifications: $e');
    }
  }

  /// Get the importance level for Android notifications
  Importance _getImportance(NotificationType type) {
    switch (type) {
      case NotificationType.lowBalance:
        return Importance.high;
      case NotificationType.timeToTopUp:
      case NotificationType.invalidRecord:
        return Importance.defaultImportance;
      case NotificationType.readingReminder:
      case NotificationType.welcome:
        return Importance.low;
    }
  }

  /// Get the priority level for Android notifications
  Priority _getPriority(NotificationType type) {
    switch (type) {
      case NotificationType.lowBalance:
        return Priority.high;
      case NotificationType.timeToTopUp:
      case NotificationType.invalidRecord:
        return Priority.defaultPriority;
      case NotificationType.readingReminder:
      case NotificationType.welcome:
        return Priority.low;
    }
  }
}
