// File: lib/core/settings/widgets/reminder_frequency_selector.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/notification_provider.dart';
import '../../theme/app_text_styles.dart';
import '../../widgets/app_card.dart';
import '../../widgets/setting_dialog.dart';

/// A shared widget for selecting reminder frequency
/// Can be used in both Setup and Settings screens
class ReminderFrequencySelector extends StatefulWidget {
  final int currentValue;
  final Function(int) onChanged;
  final bool useDialog;
  final bool showCard;

  const ReminderFrequencySelector({
    Key? key,
    required this.currentValue,
    required this.onChanged,
    this.useDialog = false,
    this.showCard = true,
  }) : super(key: key);

  @override
  State<ReminderFrequencySelector> createState() => _ReminderFrequencySelectorState();
}

class _ReminderFrequencySelectorState extends State<ReminderFrequencySelector> {
  @override
  Widget build(BuildContext context) {
    if (widget.useDialog) {
      return ListTile(
        title: const Text('Reminder Frequency'),
        subtitle: Text(_getFrequencyText(widget.currentValue)),
        leading: const Icon(Icons.repeat),
        trailing: const Icon(Icons.chevron_right),
        onTap: () => _showFrequencyDialog(context),
      );
    }

    final content = Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Reminder Frequency',
          style: AppTextStyles.titleMedium,
        ),
        const SizedBox(height: 8),
        Text(
          'How often would you like to be reminded to record your meter readings?',
          style: AppTextStyles.bodyMedium,
        ),
        const SizedBox(height: 16),
        _buildFrequencyOptions(context),
      ],
    );

    if (widget.showCard) {
      return AppCard(
        padding: const EdgeInsets.all(16),
        child: content,
      );
    }

    return content;
  }

  Widget _buildFrequencyOptions(BuildContext context) {
    return Column(
      children: [
        _buildFrequencyOption(1, 'Daily', context),
        _buildFrequencyOption(7, 'Weekly', context),
        _buildFrequencyOption(14, 'Bi-weekly', context),
        _buildFrequencyOption(30, 'Monthly', context),
      ],
    );
  }

  Widget _buildFrequencyOption(int days, String label, BuildContext context) {
    final isSelected = widget.currentValue == days;
    final theme = Theme.of(context);

    return RadioListTile<int>(
      title: Text(label),
      value: days,
      groupValue: widget.currentValue,
      onChanged: (value) {
        if (value != null) {
          widget.onChanged(value);
          if (widget.useDialog) {
            Navigator.of(context).pop();
          }
        }
      },
      activeColor: theme.colorScheme.primary,
      selected: isSelected,
      dense: true,
    );
  }

  String _getFrequencyText(int days) {
    switch (days) {
      case 1:
        return 'Daily';
      case 7:
        return 'Weekly';
      case 14:
        return 'Bi-weekly';
      case 30:
        return 'Monthly';
      default:
        return 'Every $days days';
    }
  }

  /// Show a dialog for selecting the reminder frequency
  void _showFrequencyDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => SettingDialog(
        title: 'Reminder Frequency',
        subtitle: 'How often would you like to be reminded to record your meter readings?',
        content: ReminderFrequencySelector(
          currentValue: widget.currentValue,
          onChanged: (value) {
            widget.onChanged(value);
            Navigator.of(context).pop();
          },
          useDialog: false,
          showCard: false,
        ),
      ),
    );
  }

  /// Static method to show a reminder frequency dialog
  static Future<void> showReminderFrequencyDialog(
    BuildContext context,
    int currentValue,
    Function(int) onChanged,
  ) async {
    return showDialog(
      context: context,
      builder: (context) => SettingDialog(
        title: 'Reminder Frequency',
        subtitle: 'How often would you like to be reminded to record your meter readings?',
        content: ReminderFrequencySelector(
          currentValue: currentValue,
          onChanged: (value) {
            onChanged(value);
            Navigator.of(context).pop();
          },
          useDialog: false,
          showCard: false,
        ),
      ),
    );
  }
}
