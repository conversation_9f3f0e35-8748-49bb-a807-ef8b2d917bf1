# Lekky App Restructuring Plan

## 1. Overall Architecture Approach

We'll implement a feature-first architecture with clean separation of concerns:

```
lib/
├── core/                  # Core functionality used across the app
│   ├── constants/         # App-wide constants
│   ├── theme/             # Theme definitions
│   ├── utils/             # Utility functions
│   └── widgets/           # Shared widgets
├── features/              # Feature modules
│   ├── feature_name/      # Each feature in its own directory
│   │   ├── data/          # Data sources, repositories
│   │   ├── domain/        # Business logic, models
│   │   └── presentation/  # UI components
│   │       ├── screens/   # Full screens
│   │       ├── widgets/   # Feature-specific widgets
│   │       └── controllers/ # Screen controllers
└── main.dart              # Entry point
```

## 2. Detailed Restructuring Plan

### 2.1. Home Screen Restructuring

The current `my_home_page.dart` (24,777 lines) will be broken down into:

```
features/home/
├── data/
│   └── home_repository.dart       # Data access for home screen
├── domain/
│   ├── models/
│   │   └── home_data.dart         # Home screen data models
│   └── usecases/
│       ├── calculate_meter_total.dart
│       ├── calculate_average_usage.dart
│       └── calculate_top_up_date.dart
└── presentation/
    ├── controllers/
    │   └── home_controller.dart   # Business logic for home screen
    ├── screens/
    │   └── home_screen.dart       # Main screen UI (<300 lines)
    └── widgets/
        ├── meter_value_card.dart  # Meter total and average usage display
        ├── meter_info_card.dart   # Last reading date and date to top up
        └── cost_button.dart       # Cost of electric button
```

### 2.2. Meter History Page Restructuring

The current `meter_history_page.dart` (63,437 lines) will be broken down into:

```
features/history/
├── data/
│   └── history_repository.dart    # Data access for history
├── domain/
│   ├── models/
│   │   ├── history_filter.dart    # Filter options model
│   │   └── validation_result.dart # Validation result model
│   └── usecases/
│       ├── filter_entries.dart    # Filter entries logic
│       ├── validate_entries.dart  # Validation logic
│       └── calculate_averages.dart # Average calculation logic
└── presentation/
    ├── controllers/
    │   └── history_controller.dart # Business logic for history screen
    ├── screens/
    │   └── history_screen.dart    # Main screen UI (<300 lines)
    └── widgets/
        ├── history_table.dart     # Table display for history entries
        ├── history_filter_bar.dart # Filter options UI
        ├── history_pagination.dart # Pagination controls
        ├── entry_edit_dialog.dart # Edit dialog for entries
        └── invalid_entry_indicator.dart # Visual indicator for invalid entries
```

### 2.3. Setup Screen Restructuring

The current `setup_screen.dart` (38,296 lines) will be broken down into:

```
features/setup/
├── data/
│   └── setup_repository.dart      # Data access for setup
├── domain/
│   ├── models/
│   │   └── setup_config.dart      # Setup configuration model
│   └── usecases/
│       ├── save_setup.dart        # Save setup logic
│       └── load_setup.dart        # Load setup logic
└── presentation/
    ├── controllers/
    │   └── setup_controller.dart  # Business logic for setup screen
    ├── screens/
    │   └── setup_screen.dart      # Main screen UI (<300 lines)
    └── widgets/
        ├── meter_unit_selector.dart # Meter unit selection UI
        ├── date_format_selector.dart # Date format selection UI
        ├── threshold_input.dart   # Alert threshold input UI
        ├── days_advance_input.dart # Days in advance input UI
        └── setup_actions.dart     # Save and load buttons
```

### 2.4. Core Components Restructuring

#### 2.4.1. Database Access

```
core/data/
├── database/
│   ├── db_helper.dart           # Database helper (refactored to be smaller)
│   ├── db_constants.dart        # Database constants
│   ├── db_migrations.dart       # Database migrations
│   └── db_optimizer.dart        # Database optimization
└── repositories/
    ├── meter_entry_repository.dart # Repository for meter entries
    └── settings_repository.dart    # Repository for app settings
```

#### 2.4.2. Shared Widgets

```
core/widgets/
├── app_background.dart          # App background widget
├── app_card.dart                # Card widget used throughout the app
├── app_dialog.dart              # Dialog widget base
├── app_text_field.dart          # Text field widget
├── bottom_nav_bar.dart          # Bottom navigation bar
├── custom_button.dart           # Button base widget
├── gradient_button.dart         # Gradient button widget
├── notification_button.dart     # Notification button widget
├── page_banner.dart             # Page banner widget
└── semantic_text.dart           # Accessible text widget
```

#### 2.4.3. Utilities

```
core/utils/
├── date_time_utils.dart         # Date and time formatting utilities
├── input_validator.dart         # Input validation utilities
├── snackbar_util.dart           # Snackbar utilities
├── responsive_layout.dart       # Responsive layout utilities
└── responsive_text.dart         # Responsive text utilities
```

#### 2.4.4. Services

```
core/services/
├── notification_service.dart    # Notification service
└── paypal_service.dart          # PayPal integration service
```

#### 2.4.5. Providers

```
core/providers/
├── theme_provider.dart          # Theme provider
└── notification_provider.dart   # Notification provider
```

### 2.5. Feature-Specific Components

#### 2.5.1. Meter Reading Feature

```
features/meter_reading/
├── data/
│   └── meter_reading_repository.dart
├── domain/
│   ├── models/
│   │   └── reading_data.dart
│   └── usecases/
│       └── save_meter_reading.dart
└── presentation/
    ├── controllers/
    │   └── meter_reading_controller.dart
    ├── screens/
    │   └── meter_reading_screen.dart
    └── widgets/
        ├── reading_input_form.dart
        └── reading_confirmation.dart
```

#### 2.5.2. Top Up Feature

```
features/top_up/
├── data/
│   └── top_up_repository.dart
├── domain/
│   ├── models/
│   │   └── top_up_data.dart
│   └── usecases/
│       └── save_top_up.dart
└── presentation/
    ├── controllers/
    │   └── top_up_controller.dart
    ├── screens/
    │   └── top_up_screen.dart
    └── widgets/
        ├── top_up_input_form.dart
        └── top_up_confirmation.dart
```

#### 2.5.3. Settings Feature

```
features/settings/
├── data/
│   └── settings_repository.dart
├── domain/
│   ├── models/
│   │   └── app_settings.dart
│   └── usecases/
│       ├── save_settings.dart
│       └── reset_app.dart
└── presentation/
    ├── controllers/
    │   └── settings_controller.dart
    ├── screens/
    │   └── settings_screen.dart
    └── widgets/
        ├── settings_section.dart
        ├── theme_selector.dart
        └── donation_button.dart
```

## 3. Implementation Strategy

1. **Create the new directory structure** first without moving any files
2. **Implement core components** that will be used across features
3. **Refactor one feature at a time**, starting with the Home feature:
   - Create the new files with proper separation of concerns
   - Move and adapt code from the old files
   - Update imports in dependent files
   - Test the feature thoroughly before moving to the next
4. **Update the main.dart file** to use the new structure
5. **Remove old files** once all functionality has been migrated

## 4. Benefits of the New Structure

- **Improved maintainability**: Each file has a clear purpose and is under 300 lines
- **Better testability**: Separation of UI, business logic, and data access
- **Enhanced reusability**: Components can be reused across features
- **Easier navigation**: Logical organization makes it easier to find code
- **Simplified collaboration**: Team members can work on different features without conflicts
- **Future-proof**: Structure supports adding new features without major refactoring
