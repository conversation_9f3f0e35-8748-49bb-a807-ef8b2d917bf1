// File: lib/features/notifications/domain/usecases/cancel_reminder.dart

import '../repositories/notification_repository.dart';

/// Use case for canceling meter reading reminders
class CancelReminder {
  final NotificationRepository _repository;

  CancelReminder(this._repository);

  /// Execute the use case
  ///
  /// This cancels any scheduled meter reading reminders and disables the feature.
  /// Returns true if the operation was successful, false otherwise.
  Future<bool> execute() async {
    try {
      // First disable the reminders
      await _repository.setMeterReadingRemindersEnabled(false);

      // Then update the reminder status
      return true;
    } catch (e) {
      // Handle error
      return false;
    }
  }
}
