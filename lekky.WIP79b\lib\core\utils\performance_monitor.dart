// File: lib/core/utils/performance_monitor.dart

import 'dart:async';
import 'dart:collection';
import 'package:flutter/foundation.dart';
import 'package:lekky/core/utils/enhanced_logger.dart';

/// Types of performance metrics
enum MetricType {
  latency,
  memory,
  cpu,
  database,
  network,
  rendering,
  startup,
  custom,
}

/// Performance metric data point
class PerformanceMetric {
  final String name;
  final MetricType type;
  final double value;
  final String unit;
  final DateTime timestamp;
  final Map<String, dynamic>? tags;

  PerformanceMetric({
    required this.name,
    required this.type,
    required this.value,
    required this.unit,
    Map<String, dynamic>? tags,
  })  : this.timestamp = DateTime.now(),
        this.tags = tags;

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'type': type.toString().split('.').last,
      'value': value,
      'unit': unit,
      'timestamp': timestamp.toIso8601String(),
      if (tags != null) 'tags': tags,
    };
  }

  @override
  String toString() {
    return '$name: $value $unit [${type.toString().split('.').last}]';
  }
}

/// Statistics for a metric
class MetricStatistics {
  final String name;
  final MetricType type;
  final String unit;
  final double min;
  final double max;
  final double average;
  final double median;
  final double p95;
  final double p99;
  final int count;

  MetricStatistics({
    required this.name,
    required this.type,
    required this.unit,
    required this.min,
    required this.max,
    required this.average,
    required this.median,
    required this.p95,
    required this.p99,
    required this.count,
  });

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'type': type.toString().split('.').last,
      'unit': unit,
      'min': min,
      'max': max,
      'average': average,
      'median': median,
      'p95': p95,
      'p99': p99,
      'count': count,
    };
  }
}

/// Performance monitoring service
class PerformanceMonitor {
  static const int _maxMetricsPerType = 1000;
  static const int _maxTrackedOperations = 100;

  static bool _isInitialized = false;
  static bool _enabled = true;
  static final Map<MetricType, List<PerformanceMetric>> _metrics = {};
  static final Map<String, Stopwatch> _activeOperations = {};
  static final Queue<String> _operationKeys = Queue<String>();
  static final StreamController<PerformanceMetric> _metricStreamController =
      StreamController<PerformanceMetric>.broadcast();

  /// Initialize the performance monitor
  static void initialize({bool enabled = true}) {
    if (_isInitialized) return;

    _enabled = enabled;

    // Initialize metrics storage
    for (final type in MetricType.values) {
      _metrics[type] = [];
    }

    _isInitialized = true;

    EnhancedLogger.info(
      'Performance monitor initialized',
      details: {'enabled': enabled},
      component: 'performance',
    );
  }

  /// Enable or disable performance monitoring
  static void setEnabled(bool enabled) {
    _enabled = enabled;
  }

  /// Track a metric
  static void trackMetric({
    required String name,
    required MetricType type,
    required double value,
    required String unit,
    Map<String, dynamic>? tags,
  }) {
    if (!_isInitialized) {
      initialize();
    }

    if (!_enabled) return;

    final metric = PerformanceMetric(
      name: name,
      type: type,
      value: value,
      unit: unit,
      tags: tags,
    );

    // Add to metrics storage
    _metrics[type]!.add(metric);

    // Trim if exceeding max capacity
    if (_metrics[type]!.length > _maxMetricsPerType) {
      _metrics[type]!.removeAt(0);
    }

    // Add to stream
    _metricStreamController.add(metric);

    // Log if it's an unusual value (for now, just log all metrics in debug)
    if (kDebugMode) {
      EnhancedLogger.debug(
        'Performance metric: $metric',
        component: 'performance',
      );
    }
  }

  /// Start tracking an operation
  static void startOperation(String name, {Map<String, dynamic>? tags}) {
    if (!_isInitialized) {
      initialize();
    }

    if (!_enabled) return;

    final key = _generateOperationKey(name, tags);

    // Check if we're already tracking too many operations
    if (_activeOperations.length >= _maxTrackedOperations) {
      // Remove the oldest operation
      final oldestKey = _operationKeys.removeFirst();
      _activeOperations.remove(oldestKey);
    }

    // Start tracking this operation
    final stopwatch = Stopwatch()..start();
    _activeOperations[key] = stopwatch;
    _operationKeys.add(key);

    if (kDebugMode) {
      EnhancedLogger.debug(
        'Started operation: $name',
        details: tags,
        component: 'performance',
      );
    }
  }

  /// End tracking an operation and record the elapsed time
  static void endOperation(String name, {Map<String, dynamic>? tags}) {
    if (!_isInitialized || !_enabled) return;

    final key = _generateOperationKey(name, tags);
    final stopwatch = _activeOperations[key];

    if (stopwatch == null) {
      EnhancedLogger.warning(
        'Attempted to end operation that was not started: $name',
        details: tags,
        component: 'performance',
      );
      return;
    }

    // Stop the stopwatch and calculate elapsed time
    stopwatch.stop();
    final elapsedMilliseconds = stopwatch.elapsedMilliseconds;

    // Remove from active operations
    _activeOperations.remove(key);
    _operationKeys.remove(key);

    // Track as a latency metric
    trackMetric(
      name: name,
      type: MetricType.latency,
      value: elapsedMilliseconds.toDouble(),
      unit: 'ms',
      tags: tags,
    );
  }

  /// Generate a unique key for an operation
  static String _generateOperationKey(String name, Map<String, dynamic>? tags) {
    if (tags == null || tags.isEmpty) {
      return name;
    }

    // Sort tags by key for consistent ordering
    final sortedTags = Map.fromEntries(
        tags.entries.toList()..sort((a, b) => a.key.compareTo(b.key)));

    // Generate a string representation of the tags
    final tagsString =
        sortedTags.entries.map((e) => '${e.key}=${e.value}').join(',');

    return '$name[$tagsString]';
  }

  /// Track a function execution time
  static Future<T> trackFunction<T>(
    String name,
    Future<T> Function() function, {
    Map<String, dynamic>? tags,
  }) async {
    if (!_isInitialized || !_enabled) {
      return await function();
    }

    startOperation(name, tags: tags);

    try {
      final result = await function();
      endOperation(name, tags: tags);
      return result;
    } catch (e) {
      endOperation(name, tags: tags);
      rethrow;
    }
  }

  /// Track a synchronous function execution time
  static T trackSyncFunction<T>(
    String name,
    T Function() function, {
    Map<String, dynamic>? tags,
  }) {
    if (!_isInitialized || !_enabled) {
      return function();
    }

    startOperation(name, tags: tags);

    try {
      final result = function();
      endOperation(name, tags: tags);
      return result;
    } catch (e) {
      endOperation(name, tags: tags);
      rethrow;
    }
  }

  /// Get all metrics
  static List<PerformanceMetric> getAllMetrics() {
    if (!_isInitialized) {
      initialize();
    }

    final allMetrics = <PerformanceMetric>[];
    for (final metrics in _metrics.values) {
      allMetrics.addAll(metrics);
    }

    // Sort by timestamp, newest first
    allMetrics.sort((a, b) => b.timestamp.compareTo(a.timestamp));

    return allMetrics;
  }

  /// Get metrics by type
  static List<PerformanceMetric> getMetricsByType(MetricType type) {
    if (!_isInitialized) {
      initialize();
    }

    final metrics = _metrics[type] ?? [];

    // Sort by timestamp, newest first
    metrics.sort((a, b) => b.timestamp.compareTo(a.timestamp));

    return metrics;
  }

  /// Get metrics by name
  static List<PerformanceMetric> getMetricsByName(String name) {
    if (!_isInitialized) {
      initialize();
    }

    final allMetrics = <PerformanceMetric>[];
    for (final metrics in _metrics.values) {
      allMetrics.addAll(metrics.where((m) => m.name == name));
    }

    // Sort by timestamp, newest first
    allMetrics.sort((a, b) => b.timestamp.compareTo(a.timestamp));

    return allMetrics;
  }

  /// Get statistics for a metric
  static MetricStatistics? getMetricStatistics(String name) {
    final metrics = getMetricsByName(name);

    if (metrics.isEmpty) {
      return null;
    }

    // Extract values
    final values = metrics.map((m) => m.value).toList();
    values.sort();

    // Calculate statistics
    final min = values.first;
    final max = values.last;
    final average = values.reduce((a, b) => a + b) / values.length;

    // Calculate median
    final median = values.length.isOdd
        ? values[values.length ~/ 2]
        : (values[values.length ~/ 2 - 1] + values[values.length ~/ 2]) / 2;

    // Calculate percentiles
    final p95Index = (values.length * 0.95).ceil() - 1;
    final p99Index = (values.length * 0.99).ceil() - 1;

    final p95 = values[p95Index >= 0 ? p95Index : 0];
    final p99 = values[p99Index >= 0 ? p99Index : 0];

    return MetricStatistics(
      name: name,
      type: metrics.first.type,
      unit: metrics.first.unit,
      min: min,
      max: max,
      average: average,
      median: median,
      p95: p95,
      p99: p99,
      count: metrics.length,
    );
  }

  /// Get a stream of metrics as they are tracked
  static Stream<PerformanceMetric> get metricStream =>
      _metricStreamController.stream;

  /// Clear all metrics
  static void clearMetrics() {
    if (!_isInitialized) {
      initialize();
    }

    for (final type in MetricType.values) {
      _metrics[type]!.clear();
    }
  }

  /// Clear metrics of a specific type
  static void clearMetricsOfType(MetricType type) {
    if (!_isInitialized) {
      initialize();
    }

    _metrics[type]!.clear();
  }

  /// Get active operations
  static Map<String, Duration> getActiveOperations() {
    if (!_isInitialized) {
      initialize();
    }

    final result = <String, Duration>{};
    for (final entry in _activeOperations.entries) {
      result[entry.key] =
          Duration(milliseconds: entry.value.elapsedMilliseconds);
    }

    return result;
  }

  /// Get a report of all metrics
  static Map<String, dynamic> getReport() {
    if (!_isInitialized) {
      initialize();
    }

    final report = <String, dynamic>{
      'timestamp': DateTime.now().toIso8601String(),
      'metrics_count': getAllMetrics().length,
      'active_operations': getActiveOperations().length,
    };

    // Add metrics by type
    final metricsByType = <String, List<Map<String, dynamic>>>{};
    for (final type in MetricType.values) {
      final metrics = getMetricsByType(type);
      if (metrics.isNotEmpty) {
        metricsByType[type.toString().split('.').last] =
            metrics.map((m) => m.toJson()).toList();
      }
    }
    report['metrics_by_type'] = metricsByType;

    // Add statistics for each unique metric name
    final allMetrics = getAllMetrics();
    final uniqueNames = allMetrics.map((m) => m.name).toSet();
    final statistics = <String, Map<String, dynamic>>{};

    for (final name in uniqueNames) {
      final stats = getMetricStatistics(name);
      if (stats != null) {
        statistics[name] = stats.toJson();
      }
    }
    report['statistics'] = statistics;

    return report;
  }

  /// Dispose the performance monitor
  static void dispose() {
    _metricStreamController.close();
  }
}

/// Extension for tracking performance of Future extensions
extension PerformanceFutureExtension<T> on Future<T> {
  /// Track the performance of this Future
  Future<T> trackPerformance(String name, {Map<String, dynamic>? tags}) {
    return PerformanceMonitor.trackFunction(name, () => this, tags: tags);
  }
}
