// File: lib/core/settings/widgets/radio_currency_selector.dart
import 'package:flutter/material.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';

/// A shared widget for selecting currency/meter unit using radio buttons in a two-column layout
/// Can be used in both Setup and Settings screens
class RadioCurrencySelector extends StatefulWidget {
  final String currentValue;
  final Function(String) onChanged;
  final bool useDialog;
  final bool showCard;
  final FocusNode? focusNode;

  const RadioCurrencySelector({
    Key? key,
    required this.currentValue,
    required this.onChanged,
    this.useDialog = false,
    this.showCard = true,
    this.focusNode,
  }) : super(key: key);

  @override
  State<RadioCurrencySelector> createState() => _RadioCurrencySelectorState();
}

class _RadioCurrencySelectorState extends State<RadioCurrencySelector> {
  // Define currency options - updated to 12 options
  final List<Map<String, String>> currencies = [
    {'code': 'USD', 'symbol': '\$', 'name': 'United States Dollar'},
    {'code': 'EUR', 'symbol': '€', 'name': 'Euro'},
    {'code': 'CNY', 'symbol': 'CN¥', 'name': 'Chinese Yuan'},
    {'code': 'INR', 'symbol': '₹', 'name': 'Indian Rupee'},
    {'code': 'JPY', 'symbol': '¥', 'name': 'Japanese Yen'},
    {'code': 'GBP', 'symbol': '£', 'name': 'British Pound'},
    {'code': 'BRL', 'symbol': 'R\$', 'name': 'Brazilian Real'},
    {'code': 'RUB', 'symbol': '₽', 'name': 'Russian Ruble'},
    {'code': 'IDR', 'symbol': 'Rp', 'name': 'Indonesian Rupiah'},
    {'code': 'CAD', 'symbol': 'C\$', 'name': 'Canadian Dollar'},
    {'code': 'AUD', 'symbol': 'A\$', 'name': 'Australian Dollar'},
    {'code': 'MXN', 'symbol': 'Mex\$', 'name': 'Mexican Peso'},
  ];

  late String _selectedCurrency;

  @override
  void initState() {
    super.initState();
    _initializeSelectedCurrency();
  }

  void _initializeSelectedCurrency() {
    // Check if the current value is in our predefined list
    final currencyMatch =
        currencies.where((c) => c['symbol'] == widget.currentValue).toList();

    if (currencyMatch.isNotEmpty) {
      _selectedCurrency = widget.currentValue;
    } else {
      // If not in our list, default to first currency
      _selectedCurrency = currencies[0]['symbol']!;
    }
  }

  @override
  void didUpdateWidget(RadioCurrencySelector oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.currentValue != widget.currentValue) {
      _initializeSelectedCurrency();
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.useDialog) {
      return ListTile(
        title: const Text('Currency'),
        subtitle: Text(widget.currentValue),
        leading: const Icon(Icons.attach_money),
        trailing: const Icon(Icons.chevron_right),
        onTap: () => _showCurrencyDialog(context),
      );
    }

    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    final content = Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Icon(Icons.attach_money, size: 20),
            const SizedBox(width: 8),
            Text(
              'Currency',
              style: AppTextStyles.titleMedium.copyWith(
                color:
                    isDarkMode ? AppColors.primaryTextDark : AppColors.primary,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Text(
          'Select the currency for your meter readings',
          style: TextStyle(
            fontSize: 14,
            color: isDarkMode ? Colors.grey[300] : Colors.grey[700],
          ),
        ),
        const SizedBox(height: 16),
        _buildCurrencyRadioGrid(context),
      ],
    );

    // We're no longer using the card to match the language selector styling
    return content;
  }

  Widget _buildCurrencyRadioGrid(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final primaryColor = isDarkMode ? Colors.blue[300] : Colors.blue[700];

    // Split currencies into two columns
    final int halfLength = (currencies.length / 2).ceil();
    final firstColumn = currencies.sublist(0, halfLength);
    final secondColumn = currencies.sublist(halfLength);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Two-column layout for currency radio buttons
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // First column
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: firstColumn.map((currency) {
                  return _buildRadioOption(
                    context,
                    currency['symbol']!,
                    '${currency['symbol']} (${currency['code']})',
                    primaryColor,
                    isDarkMode,
                  );
                }).toList(),
              ),
            ),
            const SizedBox(width: 8), // Space between columns
            // Second column
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: secondColumn.map((currency) {
                  return _buildRadioOption(
                    context,
                    currency['symbol']!,
                    '${currency['symbol']} (${currency['code']})',
                    primaryColor,
                    isDarkMode,
                  );
                }).toList(),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildRadioOption(
    BuildContext context,
    String value,
    String label,
    Color? primaryColor,
    bool isDarkMode,
  ) {
    return RadioListTile<String>(
      title: Text(label),
      value: value,
      groupValue: _selectedCurrency,
      onChanged: (newValue) {
        if (newValue != null) {
          setState(() {
            _selectedCurrency = newValue;
          });
          widget.onChanged(newValue);
        }
      },
      activeColor: primaryColor,
      dense: true,
      contentPadding: const EdgeInsets.symmetric(horizontal: 8),
    );
  }

  void _showCurrencyDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) {
          return AlertDialog(
            title: const Text('Select Currency'),
            content: SizedBox(
              width: double.maxFinite,
              child: RadioCurrencySelector(
                currentValue: _selectedCurrency,
                onChanged: (value) {
                  setState(() {
                    _selectedCurrency = value;
                  });
                },
                useDialog: false,
                showCard: false,
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () {
                  widget.onChanged(_selectedCurrency);
                  Navigator.of(context).pop();
                },
                child: const Text('Save'),
              ),
            ],
          );
        },
      ),
    );
  }

  /// Static method to show a currency selection dialog
  static Future<void> showCurrencyDialog(
    BuildContext context,
    String currentValue,
    Function(String) onChanged,
  ) async {
    String selectedValue = currentValue;

    return showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Currency'),
        content: SizedBox(
          width: double.maxFinite,
          child: RadioCurrencySelector(
            currentValue: selectedValue,
            onChanged: (value) {
              selectedValue = value;
            },
            useDialog: false,
            showCard: false,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              onChanged(selectedValue);
              Navigator.of(context).pop();
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }
}
