// File: lib/main.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';

// Feature imports
import 'features/home/<USER>/controllers/home_controller.dart';
import 'features/history/presentation/controllers/history_controller.dart';
import 'features/cost/presentation/controllers/cost_controller.dart';
import 'features/settings/presentation/controllers/settings_controller.dart';
import 'features/setup/presentation/controllers/setup_controller.dart';

// Core imports
import 'core/constants/app_constants.dart';
import 'core/data/repositories/settings_repository.dart';
import 'core/di/service_locator.dart';
import 'core/providers/localization_provider.dart';
import 'core/providers/notification_provider.dart';
import 'core/providers/theme_provider.dart';
import 'core/shared_modules/settings_provider.dart';
import 'core/theme/app_theme.dart';
import 'core/utils/logger.dart';
import 'core/utils/notification_helper.dart';

// Feature imports
import 'features/setup/presentation/screens/setup_screen.dart';
import 'features/welcome/presentation/screens/welcome_screen.dart';
import 'features/splash/presentation/screens/splash_screen.dart';
import 'features/settings/presentation/screens/settings_screen.dart';

// App scaffold
import 'app_scaffold.dart';

// Global navigator key to access navigator from anywhere
final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

// Global RouteObserver for tracking route changes
final RouteObserver<ModalRoute<void>> routeObserver =
    RouteObserver<ModalRoute<void>>();

void main() async {
  // Variables that need to be accessible outside the try block
  bool isSetupCompleted = false;
  late ThemeProvider themeProvider;

  try {
    debugPrint('BOOT: Starting app initialization');
    WidgetsFlutterBinding.ensureInitialized();
    debugPrint('BOOT: WidgetsFlutterBinding initialized');

    // Set preferred orientations
    await SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
    debugPrint('BOOT: Orientation set');

    // Initialize logger
    await logger.init(logToConsole: true, logToFile: true);
    logger.i('Application starting');
    debugPrint('BOOT: Logger initialized');

    // Initialize notification helper with enhanced error handling
    try {
      await NotificationHelper.initialize();
      debugPrint('BOOT: Notification helper initialized successfully');

      // Check notification permissions
      final permissionsGranted =
          await NotificationHelper().checkNotificationPermissions();
      debugPrint('BOOT: Notification permissions granted: $permissionsGranted');

      // Reschedule all notifications to ensure they persist
      final rescheduledCount =
          await NotificationHelper().rescheduleAllNotifications();
      debugPrint(
          'BOOT: Rescheduled $rescheduledCount notifications during startup');
    } catch (e) {
      debugPrint('BOOT ERROR: Failed to initialize notification helper: $e');
      logger.e('Failed to initialize notification helper',
          details: e.toString());
    }

    // Initialize service locator
    await initServiceLocator();
    logger.i('Service locator initialized');
    debugPrint('BOOT: Service locator initialized');

    // Check if setup is completed
    debugPrint('BOOT: Checking setup status');
    final settingsRepository = serviceLocator<SettingsRepository>();
    isSetupCompleted = await settingsRepository.isSetupCompleted();
    logger.i('Setup completed: $isSetupCompleted');
    debugPrint('BOOT: Setup completed: $isSetupCompleted');

    // Initialize theme provider
    debugPrint('BOOT: Initializing theme provider');
    themeProvider = ThemeProvider();
    await themeProvider.initialize();
    debugPrint('BOOT: Theme provider initialized');
  } catch (e, stackTrace) {
    debugPrint('BOOT ERROR: Error during app initialization: $e');
    debugPrint('BOOT ERROR: Stack trace: $stackTrace');

    // Initialize theme provider if it wasn't initialized in the try block
    if (!themeProvider.initialized) {
      debugPrint('BOOT: Initializing theme provider after error');
      themeProvider = ThemeProvider();
      // Don't await here to avoid potential hanging
      themeProvider.initialize();
    }
  }

  debugPrint('BOOT: Starting app with isSetupCompleted=$isSetupCompleted');

  // Run the app with providers
  runApp(
    MultiProvider(
      providers: [
        // Home controller
        ChangeNotifierProvider(
          create: (_) => serviceLocator<HomeController>(),
        ),
        // History controller
        ChangeNotifierProvider(
          create: (_) => serviceLocator<HistoryController>(),
        ),
        // Cost controller
        ChangeNotifierProvider(
          create: (_) => serviceLocator<CostController>(),
        ),
        // Settings controller
        ChangeNotifierProvider(
          create: (_) => serviceLocator<SettingsController>(),
        ),
        // Setup controller
        ChangeNotifierProvider(
          create: (_) => serviceLocator<SetupController>(),
        ),
        // Notification provider with enhanced initialization
        ChangeNotifierProvider(
          create: (_) {
            final provider = NotificationProvider();
            // Initialize the provider immediately with error handling
            provider.initialize().catchError((error) {
              debugPrint('Error initializing NotificationProvider: $error');
              logger.e('Error initializing NotificationProvider',
                  details: error.toString());
              // Don't return anything, just log the error
            });
            return provider;
          },
        ),
        // Settings provider - central source of truth for all app settings
        ChangeNotifierProvider(
          create: (_) {
            final provider = serviceLocator<SettingsProvider>();
            // Settings provider already loads settings in constructor
            return provider;
          },
        ),
        // Theme provider
        ChangeNotifierProvider.value(
          value: themeProvider,
        ),
        // Localization provider
        ChangeNotifierProvider.value(
          value: serviceLocator<LocalizationProvider>(),
        ),
      ],
      child: LekkyApp(isSetupCompleted: isSetupCompleted),
    ),
  );
}

class LekkyApp extends StatefulWidget {
  final bool isSetupCompleted;

  const LekkyApp({
    super.key,
    required this.isSetupCompleted,
  });

  @override
  State<LekkyApp> createState() => _LekkyAppState();
}

class _LekkyAppState extends State<LekkyApp> {
  @override
  Widget build(BuildContext context) {
    // Get the providers
    final themeProvider = Provider.of<ThemeProvider>(context);
    final localizationProvider = Provider.of<LocalizationProvider>(context);

    return MaterialApp(
      navigatorKey: navigatorKey, // Use global navigator key
      title: 'Lekky Meter',
      debugShowCheckedModeBanner: false,
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: themeProvider.themeMode,
      navigatorObservers: [routeObserver], // Add RouteObserver

      // Localization settings
      locale: localizationProvider.locale,
      localizationsDelegates: LocalizationProvider.localizationsDelegates,
      supportedLocales: LocalizationProvider.supportedLocales,

      home: const SplashScreen(),
      routes: {
        AppConstants.routeHome: (context) => const AppScaffold(initialIndex: 0),
        AppConstants.routeCost: (context) => const AppScaffold(initialIndex: 1),
        AppConstants.routeHistory: (context) =>
            const AppScaffold(initialIndex: 2),
        // Settings is now a standalone screen, not part of the bottom navigation
        AppConstants.routeSettings: (context) => const SettingsScreen(),
        AppConstants.routeWelcome: (context) => WelcomeScreen(),
        AppConstants.routeSetup: (context) =>
            const SetupScreen(isInitialSetup: true),
      },
      builder: (context, child) {
        // Apply any global UI modifications here, including text direction
        return Directionality(
          textDirection: localizationProvider.textDirection,
          child: child!,
        );
      },
    );
  }
}
