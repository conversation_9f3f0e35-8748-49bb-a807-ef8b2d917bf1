// File: lib/core/di/service_locator.dart
import 'package:get_it/get_it.dart';

// Core imports
import '../data/database/db_helper.dart';
import '../data/repositories/meter_entry_repository.dart';
import '../data/repositories/settings_repository.dart';
import '../providers/localization_provider.dart';
import '../utils/notification_helper.dart';
import '../shared_modules/settings_provider.dart';

// Feature repositories
import '../../features/home/<USER>/home_repository.dart';
import '../../features/history/data/history_repository.dart';
import '../../features/cost/data/cost_repository.dart';
import '../../features/setup/data/setup_repository.dart';

// Feature controllers
import '../../features/home/<USER>/controllers/home_controller.dart';
import '../../features/history/presentation/controllers/history_controller.dart';
import '../../features/cost/presentation/controllers/cost_controller.dart';
import '../../features/settings/presentation/controllers/settings_controller.dart';
import '../../features/setup/presentation/controllers/setup_controller.dart';

/// Service locator for dependency injection
final GetIt serviceLocator = GetIt.instance;

/// Initialize the service locator
Future<void> initServiceLocator() async {
  // Database
  serviceLocator.registerSingleton<DBHelper>(DBHelper());

  // Repositories
  serviceLocator.registerSingleton<SettingsRepository>(SettingsRepository());
  serviceLocator.registerSingleton<MeterEntryRepository>(
    MeterEntryRepository(dbHelper: serviceLocator<DBHelper>()),
  );

  // Utils
  serviceLocator.registerSingleton<NotificationHelper>(NotificationHelper());

  // Feature repositories
  serviceLocator.registerFactory<HomeRepository>(() => HomeRepository(
        meterEntryRepository: serviceLocator<MeterEntryRepository>(),
        settingsRepository: serviceLocator<SettingsRepository>(),
      ));

  serviceLocator.registerFactory<HistoryRepository>(() => HistoryRepository(
        meterEntryRepository: serviceLocator<MeterEntryRepository>(),
        settingsRepository: serviceLocator<SettingsRepository>(),
      ));

  serviceLocator.registerFactory<CostRepository>(() => CostRepository(
        meterEntryRepository: serviceLocator<MeterEntryRepository>(),
        settingsRepository: serviceLocator<SettingsRepository>(),
      ));

  serviceLocator.registerFactory<SetupRepository>(() => SetupRepository(
        settingsRepository: serviceLocator<SettingsRepository>(),
      ));

  // Feature controllers
  serviceLocator.registerFactory<HomeController>(() => HomeController(
        serviceLocator<HomeRepository>(),
      ));

  // Old home controller registration removed

  serviceLocator.registerFactory<HistoryController>(() => HistoryController(
        serviceLocator<HistoryRepository>(),
      ));

  serviceLocator.registerFactory<CostController>(() => CostController(
        serviceLocator<CostRepository>(),
      ));

  serviceLocator.registerFactory<SettingsController>(() => SettingsController(
        settingsRepository: serviceLocator<SettingsRepository>(),
        meterEntryRepository: serviceLocator<MeterEntryRepository>(),
        settingsProvider: serviceLocator<SettingsProvider>(),
      ));

  serviceLocator.registerFactory<SetupController>(() => SetupController(
        serviceLocator<SetupRepository>(),
      ));

  // Register SettingsProvider as a singleton
  serviceLocator.registerSingleton<SettingsProvider>(SettingsProvider());

  // Register LocalizationProvider as a singleton
  serviceLocator.registerSingleton<LocalizationProvider>(
      LocalizationProvider(serviceLocator<SettingsRepository>()));

  // Initialize services
  await serviceLocator<DBHelper>().init();
  await NotificationHelper.initialize();
}
