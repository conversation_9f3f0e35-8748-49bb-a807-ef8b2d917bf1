// File: lib/core/settings/widgets/alert_threshold_dialog.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../core/widgets/app_text_field.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../core/theme/app_colors.dart';
import '../validators/settings_validator.dart';

/// A dialog for setting the alert threshold
class AlertThresholdDialog extends StatefulWidget {
  final double currentValue;
  final String currencySymbol;
  final Function(double) onChanged;

  const AlertThresholdDialog({
    Key? key,
    required this.currentValue,
    required this.currencySymbol,
    required this.onChanged,
  }) : super(key: key);

  /// Static method to show the dialog
  static Future<void> show(
    BuildContext context,
    double currentValue,
    String currencySymbol,
    Function(double) onChanged,
  ) async {
    return showDialog(
      context: context,
      builder: (context) => AlertThresholdDialog(
        currentValue: currentValue,
        currencySymbol: currencySymbol,
        onChanged: onChanged,
      ),
    );
  }

  @override
  State<AlertThresholdDialog> createState() => _AlertThresholdDialogState();
}

class _AlertThresholdDialogState extends State<AlertThresholdDialog> {
  late TextEditingController _controller;
  String? _errorText;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(
      text: widget.currentValue.toStringAsFixed(2),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _validateAndSave() {
    final validation = SettingsValidator.validateThreshold(_controller.text);
    if (validation['isValid']) {
      final value = double.tryParse(_controller.text);
      if (value != null && value >= 1.0 && value <= 999.0) {
        // Round to 2 decimal places
        final roundedValue = double.parse(value.toStringAsFixed(2));
        widget.onChanged(roundedValue);
        Navigator.of(context).pop();
      } else {
        setState(() {
          _errorText = 'Please enter a value between 1 and 999';
        });
      }
    } else {
      setState(() {
        _errorText = validation['errorMessage'];
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return AlertDialog(
      title: Row(
        children: [
          Icon(
            Icons.warning_amber_rounded,
            color: isDarkMode ? AppColors.primaryDark : AppColors.primary,
          ),
          const SizedBox(width: 8),
          const Text('Alert Threshold'),
        ],
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'You will be notified when your meter balance falls below this amount',
            style: AppTextStyles.bodyMedium,
          ),
          const SizedBox(height: 16),
          AppTextField(
            controller: _controller,
            labelText: 'Alert Threshold',
            hintText: 'Enter value (1-999)',
            errorText: _errorText,
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
            inputFormatters: [
              FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}$')),
            ],
            prefixText: widget.currencySymbol,
            autofocus: true,
            selectAllOnFocus: true,
            onChanged: (value) {
              // Clear error when user types
              if (_errorText != null) {
                setState(() {
                  _errorText = null;
                });
              }
            },
          ),
          const SizedBox(height: 8),
          Text(
            'Recommended: ${widget.currencySymbol}5.00',
            style: AppTextStyles.bodySmall.copyWith(
              fontStyle: FontStyle.italic,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _validateAndSave,
          style: ElevatedButton.styleFrom(
            backgroundColor: isDarkMode ? AppColors.primaryDark : AppColors.primary,
            foregroundColor: Colors.white,
          ),
          child: const Text('Save'),
        ),
      ],
    );
  }
}
