// File: lib/core/platform/timezone/timezone_change_service.dart

import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'timezone_adapter_factory.dart';
import 'timezone_adapter.dart';

/// Service for monitoring timezone changes when the app is running
class TimezoneChangeService {
  static const String _timezoneChangeDetectedKey = 'timezone_change_detected';
  static const String _lastCheckTimeKey = 'timezone_last_check_time';

  static Timer? _periodicCheckTimer;
  static bool _isInitialized = false;
  static final List<Function()> _timezoneChangeListeners = [];

  /// Initialize the timezone change service
  static Future<void> initialize() async {
    if (_isInitialized) return;

    // Check for timezone changes on initialization
    await checkForTimezoneChanges();

    // Set up periodic check every 15 minutes while the app is running
    _periodicCheckTimer =
        Timer.periodic(const Duration(minutes: 15), (_) async {
      await checkForTimezoneChanges();
    });

    _isInitialized = true;
  }

  /// Start monitoring for timezone changes
  static Future<void> startMonitoring() async {
    if (!_isInitialized) {
      await initialize();
    }
  }

  /// Stop monitoring for timezone changes
  static void stopMonitoring() {
    _periodicCheckTimer?.cancel();
    _periodicCheckTimer = null;
  }

  /// Register a listener for timezone changes
  static void addTimezoneChangeListener(Function() listener) {
    if (!_timezoneChangeListeners.contains(listener)) {
      _timezoneChangeListeners.add(listener);
    }
  }

  /// Unregister a timezone change listener
  static void removeTimezoneChangeListener(Function() listener) {
    _timezoneChangeListeners.remove(listener);
  }

  /// Mark that a timezone change has been detected
  static Future<void> markTimezoneChangeDetected(bool detected) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_timezoneChangeDetectedKey, detected);
  }

  /// Check if a timezone change has been detected
  static Future<bool> hasTimezoneChangeBeenDetected() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_timezoneChangeDetectedKey) ?? false;
  }

  /// Clear the timezone change detection flag
  static Future<void> clearTimezoneChangeDetection() async {
    await markTimezoneChangeDetected(false);
  }

  /// Update the last check time
  static Future<void> _updateLastCheckTime() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(
        _lastCheckTimeKey, DateTime.now().millisecondsSinceEpoch);
  }

  /// Get the last check time
  static Future<DateTime?> _getLastCheckTime() async {
    final prefs = await SharedPreferences.getInstance();
    final lastCheckTime = prefs.getInt(_lastCheckTimeKey);
    if (lastCheckTime == null) return null;
    return DateTime.fromMillisecondsSinceEpoch(lastCheckTime);
  }

  /// Check for timezone changes
  static Future<bool> checkForTimezoneChanges() async {
    try {
      // Create a timezone adapter
      final timezoneAdapter = TimezoneAdapterFactory.create();
      await timezoneAdapter.initialize();

      // Check if the timezone has changed
      final hasChanged = await timezoneAdapter.hasTimezoneChanged();

      if (hasChanged) {
        debugPrint('Timezone change detected');

        // Save the new timezone
        final currentTimezone = timezoneAdapter.getCurrentTimezone();
        await timezoneAdapter.saveCurrentTimezone(currentTimezone);

        // Mark that a timezone change has been detected
        await markTimezoneChangeDetected(true);

        // Notify listeners
        for (final listener in _timezoneChangeListeners) {
          listener();
        }

        return true;
      }

      // Update the last check time
      await _updateLastCheckTime();

      return false;
    } catch (e) {
      debugPrint('Error checking for timezone changes: $e');
      return false;
    }
  }

  /// Check if a timezone change has occurred since the app was last opened
  static Future<bool> checkForTimezoneChangesSinceLastOpen() async {
    try {
      final lastCheckTime = await _getLastCheckTime();
      if (lastCheckTime == null) {
        // First run, no previous check
        await _updateLastCheckTime();
        return false;
      }

      // Check how long it's been since the last check
      final now = DateTime.now();
      final hoursSinceLastCheck = now.difference(lastCheckTime).inHours;

      // If it's been more than 3 hours, perform a check
      if (hoursSinceLastCheck >= 3) {
        return await checkForTimezoneChanges();
      }

      return await hasTimezoneChangeBeenDetected();
    } catch (e) {
      debugPrint('Error checking for timezone changes since last open: $e');
      return false;
    }
  }

  /// Clean up resources
  static void dispose() {
    stopMonitoring();
    _timezoneChangeListeners.clear();
  }
}
