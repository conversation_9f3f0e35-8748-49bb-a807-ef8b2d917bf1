// File: lib/core/shared_modules/alert_threshold_selector.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../theme/app_colors.dart';
import '../theme/app_text_styles.dart';
import '../widgets/app_text_field.dart';
import 'settings_model.dart';

class AlertThresholdSelector extends StatefulWidget {
  final double currentValue;
  final ValueChanged<double> onChanged;
  final String currencySymbol;
  final String? errorText;
  final bool hasMeterReadings;
  final SettingsDisplayMode displayMode;
  final bool showHelperText;
  final bool showTitle;

  const AlertThresholdSelector({
    super.key,
    required this.currentValue,
    required this.onChanged,
    required this.currencySymbol,
    this.errorText,
    this.hasMeterReadings = false,
    this.displayMode = SettingsDisplayMode.compact,
    this.showHelperText = false,
    this.showTitle = true,
  });

  @override
  State<AlertThresholdSelector> createState() => _AlertThresholdSelectorState();
}

class _AlertThresholdSelectorState extends State<AlertThresholdSelector> {
  late TextEditingController _controller;
  late FocusNode _focusNode;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(
      text: widget.currentValue.toStringAsFixed(2),
    );
    _focusNode = FocusNode()
      ..addListener(() {
        if (_focusNode.hasFocus) {
          _controller.selection = TextSelection(
            baseOffset: 0,
            extentOffset: _controller.text.length,
          );
        }
      });
  }

  @override
  void didUpdateWidget(covariant AlertThresholdSelector oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Only update the controller text if the external value changed and we're not focused
    if (!_focusNode.hasFocus && widget.currentValue != oldWidget.currentValue) {
      _controller.text = widget.currentValue.toStringAsFixed(2);
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return widget.displayMode == SettingsDisplayMode.expanded
        ? _buildExpandedView(context)
        : _buildCompactView(context);
  }

  Widget _buildExpandedView(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.showTitle) _buildSectionTitle(context, 'Alert Threshold'),

        if (widget.showHelperText)
          _buildHelperText(context,
              'You will be notified when your balance falls below this amount.'),

        if (!widget.hasMeterReadings) ...[
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppColors.warning.withOpacity(0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                const Icon(Icons.info_outline,
                    color: AppColors.warning, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Low balance alerts will be active after you enter your first meter reading.',
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: Theme.of(context).brightness == Brightness.dark
                          ? AppColors.warning
                          : AppColors.onBackground,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],

        const SizedBox(height: 16),

        // Precise input field
        AppTextField(
          controller: _controller,
          focusNode: _focusNode,
          keyboardType: const TextInputType.numberWithOptions(decimal: true),
          inputFormatters: [
            FilteringTextInputFormatter.allow(
                RegExp(r'^\d{1,3}(\.?\d{0,2})?$')),
          ],
          labelText: 'Alert Threshold Value (1-999)',
          prefixText: widget.currencySymbol,
          helperText:
              'Enter a value between ${widget.currencySymbol}1.00 and ${widget.currencySymbol}999.00',
          selectAllOnFocus: false, // We handle selection in the focus listener
          onChanged: (value) {
            final parsedValue = double.tryParse(value);
            if (parsedValue != null &&
                parsedValue >= 1.0 &&
                parsedValue <= 999.0) {
              widget.onChanged(parsedValue);
            }
          },
        ),

        const SizedBox(height: 8),
        const Text(
          'Tip: Set this to the amount you typically top up with to get reminders at the right time.',
          style: AppTextStyles.helperText,
        ),

        if (widget.errorText != null) ...[
          const SizedBox(height: 8),
          Text(
            widget.errorText!,
            style: AppTextStyles.errorText,
          ),
        ],
      ],
    );
  }

  Widget _buildCompactView(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.showTitle) _buildLabel(context, 'Alert Threshold'),

        if (widget.showTitle)
          Text(
            'Current: ${widget.currencySymbol}${widget.currentValue.toStringAsFixed(2)}',
            style: AppTextStyles.helperText,
          ),

        if (!widget.hasMeterReadings) ...[
          const SizedBox(height: 4),
          Row(
            children: [
              const Icon(Icons.info_outline,
                  color: AppColors.warning, size: 14),
              const SizedBox(width: 4),
              Expanded(
                child: Text(
                  'Requires meter reading',
                  style: AppTextStyles.helperText.copyWith(
                    color:
                        isDarkMode ? AppColors.warning : AppColors.onBackground,
                  ),
                ),
              ),
            ],
          ),
        ],

        if (widget.showHelperText)
          _buildHelperText(context, 'Set when to receive low balance alerts'),

        const SizedBox(height: 8),

        // Precise input field - more compact
        AppTextField(
          controller: _controller,
          focusNode: _focusNode,
          keyboardType: const TextInputType.numberWithOptions(decimal: true),
          inputFormatters: [
            FilteringTextInputFormatter.allow(
                RegExp(r'^\d{1,3}(\.?\d{0,2})?$')),
          ],
          labelText: 'Alert Value (1-999)',
          prefixText: widget.currencySymbol,
          helperText:
              'Enter a value (${widget.currencySymbol}1.00-${widget.currencySymbol}999.00)',
          isDense: true,
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
          selectAllOnFocus: false, // We handle selection in the focus listener
          onChanged: (value) {
            final parsedValue = double.tryParse(value);
            if (parsedValue != null &&
                parsedValue >= 1.0 &&
                parsedValue <= 999.0) {
              widget.onChanged(parsedValue);
            }
          },
        ),

        if (widget.errorText != null) ...[
          const SizedBox(height: 8),
          Text(
            widget.errorText!,
            style: AppTextStyles.errorText,
          ),
        ],
      ],
    );
  }

  Widget _buildSectionTitle(BuildContext context, String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Text(
        title,
        style: AppTextStyles.titleMedium.copyWith(
          color: Theme.of(context).brightness == Brightness.dark
              ? Colors.white
              : AppColors.onBackground,
        ),
      ),
    );
  }

  Widget _buildLabel(BuildContext context, String label) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4.0),
      child: Text(
        label,
        style: AppTextStyles.labelLarge.copyWith(
          color: Theme.of(context).brightness == Brightness.dark
              ? Colors.white
              : AppColors.onBackground,
        ),
      ),
    );
  }

  Widget _buildHelperText(BuildContext context, String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Text(
        text,
        style: AppTextStyles.helperText.copyWith(
          color: Theme.of(context).brightness == Brightness.dark
              ? AppColors.helperTextDark
              : AppColors.helperTextLight,
        ),
      ),
    );
  }
}
