// File: lib/features/history/presentation/widgets/history_info_dialog.dart
import 'package:flutter/material.dart';
import '../../../../core/models/meter_entry.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../controllers/history_controller.dart';

/// A dialog that displays information about the history screen
class HistoryInfoDialog extends StatelessWidget {
  final HistoryController controller;

  const HistoryInfoDialog({
    Key? key,
    required this.controller,
  }) : super(key: key);

  static Future<void> show({
    required BuildContext context,
    required HistoryController controller,
  }) async {
    await showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        insetPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
        child: Container(
          constraints: BoxConstraints(
            maxWidth: MediaQuery.of(context).size.width * 0.9,
            maxHeight: MediaQuery.of(context).size.height * 0.8,
          ),
          child: HistoryInfoDialog(
            controller: controller,
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // Header
        Padding(
          padding: const EdgeInsets.fromLTRB(24, 16, 24, 0),
          child: Row(
            children: [
              Icon(
                Icons.info_outline,
                color: Theme.of(context).brightness == Brightness.dark
                    ? Colors.white
                    : AppColors.primary,
              ),
              const SizedBox(width: 10),
              Text(
                'History Information',
                style: AppTextStyles.titleMedium.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).brightness == Brightness.dark
                      ? Colors.white
                      : AppColors.primary,
                ),
              ),
              const Spacer(),
              IconButton(
                icon: Icon(
                  Icons.close,
                  color: Theme.of(context).brightness == Brightness.dark
                      ? Colors.white70
                      : AppColors.onSurface,
                ),
                onPressed: () => Navigator.of(context).pop(),
                tooltip: 'Close',
                constraints: const BoxConstraints(),
                padding: EdgeInsets.zero,
                iconSize: 20,
              ),
            ],
          ),
        ),

        // Content
        Flexible(
          child: Padding(
            padding: const EdgeInsets.fromLTRB(24, 8, 24, 0),
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildStatisticsSection(),
                  const SizedBox(height: 16),
                  _buildInfoSection(
                    'Entry Information',
                    'Tap on any entry in the history table to view detailed information:',
                    [
                      _buildBulletPoint(
                          'Meter readings show your remaining balance at a specific date and time'),
                      _buildBulletPoint(
                          'Top-ups record amounts added to your meter'),
                      _buildBulletPoint(
                          'Tap entries to see full timestamp and additional details'),
                      _buildBulletPoint(
                          'Edit entries by tapping "Edit Now" in the detail view'),
                    ],
                    icon: Icons.info_outline,
                  ),
                  const SizedBox(height: 16),
                  _buildInfoSection(
                    'Color Coding',
                    'Entries are color-coded for easy identification:',
                    [
                      _buildColorInfo(AppColors.primary, 'Meter Readings'),
                      _buildColorInfo(AppColors.costTab, 'Top-ups'),
                      _buildColorInfo(
                          AppColors.error.withOpacity(0.7), 'Invalid Entries'),
                    ],
                    icon: Icons.palette,
                  ),
                  const SizedBox(height: 16),
                  _buildInfoSection(
                    'Edit Mode',
                    'Use the edit button to:',
                    [
                      _buildBulletPoint('Edit existing entries'),
                      _buildBulletPoint('Delete entries'),
                      _buildBulletPoint('Fix validation errors'),
                    ],
                    icon: Icons.edit,
                  ),
                  const SizedBox(height: 16),
                  _buildInfoSection(
                    'Validation',
                    'The app validates your entries to ensure accuracy:',
                    [
                      _buildBulletPoint(
                          'Meter readings should decrease over time'),
                      _buildBulletPoint(
                          'Dates should be in chronological order'),
                      _buildBulletPoint('Invalid entries are highlighted'),
                    ],
                    icon: Icons.check_circle,
                  ),
                  const SizedBox(height: 16),
                  _buildInfoSection(
                    'Table Columns',
                    'The table shows:',
                    [
                      _buildBulletPoint(
                          'Date: When the action happened (meter reading or top-up)'),
                      _buildBulletPoint(
                          '${controller.meterUnit}: Amount left or topped up'),
                      _buildBulletPoint(
                          '${controller.meterUnit}/day: Recent average usage'),
                      _buildBulletPoint(
                          '${controller.meterUnit}/day: Total average usage'),
                    ],
                    icon: Icons.table_chart,
                  ),
                  const SizedBox(height: 16),
                  _buildInfoSection(
                    'Average Calculations',
                    'Understanding how averages are calculated:',
                    [
                      _buildBulletPoint(
                          'Recent average: Calculated between consecutive meter readings, accounting for any top-ups in between. Formula: ((LastMeterReadingValue + TotalTopUpCount) - NewMeterReadingValue) / DaysBetweenReadings'),
                      _buildBulletPoint(
                          'Total average: Calculated from the first meter reading to the current one, accounting for all top-ups. Formula: ((first meter reading + all top ups before the current row) - current row meter reading) / days between first meter reading and current row date'),
                      _buildBulletPoint(
                          'Time intervals are calculated with minute-level precision for more accurate averages, especially for readings taken on the same day'),
                      _buildBulletPoint(
                          'Recent averages reflect recent usage patterns, while total averages provide a long-term view of consumption'),
                      _buildBulletPoint(
                          'Averages are automatically updated when entries are added, edited, or deleted'),
                    ],
                    icon: Icons.calculate,
                  ),
                  const SizedBox(height: 24),
                ],
              ),
            ),
          ),
        ),

        // Footer with "Got it" button
        Padding(
          padding: const EdgeInsets.all(16),
          child: TextButton(
            onPressed: () => Navigator.of(context).pop(),
            style: TextButton.styleFrom(
              foregroundColor: AppColors.primary,
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
            child: const Text('Got it'),
          ),
        ),
      ],
    );
  }

  Widget _buildStatisticsSection() {
    final totalEntries = controller.allEntries.length;
    final meterReadings =
        controller.allEntries.where((e) => e.amountToppedUp == 0).length;
    final topUps =
        controller.allEntries.where((e) => e.amountToppedUp > 0).length;

    // Calculate total top-up amount
    final totalTopUpAmount = controller.allEntries
        .where((e) => e.amountToppedUp > 0)
        .fold(0.0, (sum, e) => sum + e.amountToppedUp);

    // Get the latest total average if available
    double? latestTotalAverage;
    if (controller.allEntries.isNotEmpty) {
      final sortedEntries = List<MeterEntry>.from(controller.allEntries)
        ..sort((a, b) => b.timestamp.compareTo(a.timestamp));

      for (final entry in sortedEntries) {
        if (entry.totalAverageUpToThisPoint != null &&
            entry.amountToppedUp == 0) {
          latestTotalAverage = entry.totalAverageUpToThisPoint;
          break;
        }
      }
    }

    return Card(
      elevation: 0,
      margin: EdgeInsets.zero,
      color: AppColors.surfaceVariant.withOpacity(0.2),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: AppColors.primary.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.analytics,
                  color: Colors.black, // Changed to black
                  size: 18,
                ),
                const SizedBox(width: 8),
                Text(
                  'Statistics',
                  style: AppTextStyles.titleSmall.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.black, // Changed to black
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 16,
              runSpacing: 8,
              children: [
                _buildCompactStatItem(
                  'Entries',
                  '$totalEntries',
                  Icons.list,
                ),
                _buildCompactStatItem(
                  'Readings',
                  '$meterReadings',
                  Icons.remove_circle,
                  color: AppColors.primary,
                ),
                _buildCompactStatItem(
                  'Top-ups',
                  '$topUps',
                  Icons.add_circle,
                  color: AppColors.costTab,
                ),
              ],
            ),
            const Divider(height: 16),
            _buildStatItem(
              'Total Top-up',
              '${controller.meterUnit}${totalTopUpAmount.toStringAsFixed(2)}',
              Icons.account_balance_wallet,
              color: AppColors
                  .costTab, // Changed from tertiary to costTab (dark orange)
            ),
            if (latestTotalAverage != null) ...[
              const Divider(height: 16),
              _buildStatItem(
                'Average Daily Usage',
                '${controller.meterUnit}${latestTotalAverage.toStringAsFixed(2)}/day',
                Icons.trending_down,
                color: AppColors.primary,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon,
      {Color? color}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          children: [
            Icon(
              icon,
              size: 16,
              color: color ?? AppColors.onSurface,
            ),
            const SizedBox(width: 8),
            Text(
              label,
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        Text(
          value,
          style: AppTextStyles.bodyMedium.copyWith(
            fontWeight: FontWeight.bold,
            color: color ?? AppColors.onSurface,
          ),
        ),
      ],
    );
  }

  Widget _buildCompactStatItem(String label, String value, IconData icon,
      {Color? color}) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: (color ?? AppColors.primary).withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                size: 14,
                color: color ?? AppColors.primary,
              ),
              const SizedBox(width: 4),
              Text(
                label,
                style: AppTextStyles.bodySmall.copyWith(
                  fontWeight: FontWeight.w500,
                  color: color ?? AppColors.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 2),
          Text(
            value,
            style: AppTextStyles.bodyMedium.copyWith(
              fontWeight: FontWeight.bold,
              color: color ?? AppColors.primary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoSection(String title, String description, List<Widget> items,
      {IconData? icon}) {
    return Builder(
      builder: (context) => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              if (icon != null)
                Padding(
                  padding: const EdgeInsets.only(right: 8.0),
                  child: Icon(
                    icon,
                    color: Theme.of(context).brightness == Brightness.dark
                        ? Colors.white
                        : Colors.black,
                    size: 18,
                  ),
                ),
              Text(
                title,
                style: AppTextStyles.titleSmall.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).brightness == Brightness.dark
                      ? Colors.white
                      : Colors.black,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            description,
            style: AppTextStyles.bodyMedium.copyWith(
              color: Theme.of(context).brightness == Brightness.dark
                  ? Colors
                      .white70 // Lighter color for better readability in dark mode
                  : AppColors.onSurface,
            ),
          ),
          const SizedBox(height: 8),
          ...items,
        ],
      ),
    );
  }

  Widget _buildColorInfo(Color color, String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        children: [
          Container(
            width: 16,
            height: 16,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 8),
          Builder(
            builder: (context) => Text(
              text,
              style: AppTextStyles.bodyMedium.copyWith(
                color: Theme.of(context).brightness == Brightness.dark
                    ? Colors
                        .white70 // Lighter color for better readability in dark mode
                    : AppColors.onSurface,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBulletPoint(String text) {
    return Builder(
      builder: (context) => Padding(
        padding: const EdgeInsets.only(bottom: 4, left: 8),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '\u2022',
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).brightness == Brightness.dark
                    ? Colors.white70
                    : Colors.black,
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Builder(
                builder: (context) => Text(
                  text,
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: Theme.of(context).brightness == Brightness.dark
                        ? Colors
                            .white70 // Lighter color for better readability in dark mode
                        : AppColors.onSurface,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
