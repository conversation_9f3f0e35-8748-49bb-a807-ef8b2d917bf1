// File: lib/features/settings/presentation/controllers/settings_controller.dart
import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../core/data/repositories/meter_entry_repository.dart';
import '../../../../core/data/repositories/settings_repository.dart';
import '../../../../core/l10n/supported_locales.dart';
import '../../../../core/models/meter_entry.dart';
import '../../../../core/providers/localization_provider.dart';
import '../../../../core/shared_modules/progress_dialog.dart';
import '../../../../core/shared_modules/settings_provider.dart';
import '../../../../core/utils/error_handler.dart';
import '../../../../core/utils/event_bus.dart';
import '../../../../core/utils/logger.dart';
import '../../../../core/utils/result.dart';
import '../../../../core/widgets/dialogs/import_options_dialog.dart';
import '../../../../core/providers/notification_provider.dart';
import '../../../../features/backup/backup_service.dart';
import '../../../history/presentation/controllers/history_controller.dart';

/// Controller for the settings screen
class SettingsController extends ChangeNotifier {
  final SettingsRepository _settingsRepository;
  final MeterEntryRepository _meterEntryRepository;
  final BackupService _backupService = BackupService();
  final SettingsProvider _settingsProvider;
  final LocalizationProvider? _localizationProvider;

  // Event subscription
  StreamSubscription<EventType>? _eventSubscription;

  bool _isLoading = true;
  String _error = '';
  String _meterUnit = '£';
  double _alertThreshold = 5.0;
  int _daysInAdvance = 2;
  String _dateFormat = 'DD-MM-YYYY';
  String _dateInfo = 'Date only';
  String _themeMode = 'system';
  bool _notificationsEnabled = true;
  double? _initialCredit;
  String _language = 'en';

  // Use broadcast streams to allow multiple listeners
  late final StreamController<String> _meterUnitController;
  late final StreamController<double> _alertThresholdController;
  late final StreamController<int> _daysInAdvanceController;
  late final StreamController<String> _dateFormatController;
  late final StreamController<String> _dateInfoController;
  late final StreamController<String> _themeModeController;
  late final StreamController<bool> _notificationsEnabledController;
  late final StreamController<double?> _initialCreditController;
  late final StreamController<String> _languageController;

  SettingsController({
    SettingsRepository? settingsRepository,
    MeterEntryRepository? meterEntryRepository,
    SettingsProvider? settingsProvider,
    LocalizationProvider? localizationProvider,
  })  : _settingsRepository = settingsRepository ?? SettingsRepository(),
        _meterEntryRepository = meterEntryRepository ?? MeterEntryRepository(),
        _settingsProvider = settingsProvider ?? SettingsProvider(),
        _localizationProvider = localizationProvider {
    // Initialize with values from SettingsProvider
    _meterUnit = _settingsProvider.currency;
    _alertThreshold = _settingsProvider.alertThreshold;
    _daysInAdvance = _settingsProvider.daysInAdvance;
    _dateFormat = _settingsProvider.dateFormat;
    _dateInfo = _settingsProvider.dateInfo;
    _themeMode = _settingsProvider.themeMode == ThemeMode.dark
        ? 'dark'
        : _settingsProvider.themeMode == ThemeMode.light
            ? 'light'
            : 'system';
    _notificationsEnabled = _settingsProvider.notificationsEnabled;

    // Initialize stream controllers with current values from SettingsProvider
    _meterUnitController = StreamController<String>.broadcast()
      ..add(_meterUnit);
    _alertThresholdController = StreamController<double>.broadcast()
      ..add(_alertThreshold);
    _daysInAdvanceController = StreamController<int>.broadcast()
      ..add(_daysInAdvance);
    _dateFormatController = StreamController<String>.broadcast()
      ..add(_dateFormat);
    _dateInfoController = StreamController<String>.broadcast()..add(_dateInfo);
    _themeModeController = StreamController<String>.broadcast()
      ..add(_themeMode);
    _notificationsEnabledController = StreamController<bool>.broadcast()
      ..add(_notificationsEnabled);
    _initialCreditController = StreamController<double?>.broadcast()
      ..add(_initialCredit);
    _languageController = StreamController<String>.broadcast()..add(_language);

    // Subscribe to settings updated events
    _eventSubscription = EventBus().stream.listen((event) {
      if (event == EventType.settingsUpdated) {
        logger.i(
            'SettingsController: Received settingsUpdated event, reloading settings');
        reloadSettings();
      }
    });

    // Set default values immediately to prevent infinite loading
    _isLoading = false;
    notifyListeners();

    // Then load actual settings
    reloadSettings();
  }

  /// Get the loading state
  bool get isLoading => _isLoading;

  /// Get the error message
  String get error => _error;

  /// Get the meter unit
  Stream<String> get meterUnit => _meterUnitController.stream;

  /// Get the current meter unit value
  String get meterUnitValue => _meterUnit;

  /// Get the alert threshold stream
  Stream<double> get alertThreshold => _alertThresholdController.stream;

  /// Get the current alert threshold value
  double get alertThresholdValue => _alertThreshold;

  /// Get the days in advance stream
  Stream<int> get daysInAdvance => _daysInAdvanceController.stream;

  /// Get the current days in advance value
  int get daysInAdvanceValue => _daysInAdvance;

  /// Get the date format stream
  Stream<String> get dateFormat => _dateFormatController.stream;

  /// Get the current date format value
  String get dateFormatValue => _dateFormat;

  /// Get the date info stream
  Stream<String> get dateInfo => _dateInfoController.stream;

  /// Get the current date info value
  String get dateInfoValue => _dateInfo;

  /// Get the theme mode
  Stream<String> get themeMode => _themeModeController.stream;

  /// Get the notifications enabled state
  Stream<bool> get notificationsEnabled =>
      _notificationsEnabledController.stream;

  /// Get the initial credit
  Stream<double?> get initialCredit => _initialCreditController.stream;

  /// Get the initial credit value
  double? get initialCreditValue => _initialCredit;

  /// Get the language stream
  Stream<String> get language => _languageController.stream;

  /// Get the current language value
  String get languageValue => _language;

  /// Get the meter entry repository
  MeterEntryRepository get meterEntryRepository => _meterEntryRepository;

  /// Set the meter unit
  Future<void> setMeterUnit(String value) async {
    try {
      // Log the value being set
      logger.i('Setting meter unit to $value');

      // Save to repository
      await _settingsRepository.setMeterUnit(value);

      // Update local value
      _meterUnit = value;

      // Update stream with new value
      _meterUnitController.sink.add(value);

      // Also update the SettingsProvider
      await _settingsProvider.setCurrency(value);

      // Notify other parts of the app that settings have changed
      EventBus().fire(EventType.settingsUpdated);
      logger.i('Meter unit updated to $value');

      // Notify listeners to update UI
      notifyListeners();
    } catch (e) {
      _error = 'Failed to save meter unit: $e';
      logger.e('Error setting meter unit', details: e.toString());
      notifyListeners();
    }
  }

  /// Set the alert threshold
  Future<void> setAlertThreshold(double value) async {
    try {
      // Log the value being set
      logger.i('Setting alert threshold to $value');

      // Save to repository
      await _settingsRepository.setAlertThreshold(value);

      // Update local value
      _alertThreshold = value;

      // Update stream with new value
      _alertThresholdController.sink.add(value);

      // Also update the SettingsProvider
      await _settingsProvider.setAlertThreshold(value);

      // Notify other parts of the app that alert settings have changed
      EventBus().fire(EventType.alertSettingsUpdated);
      logger.i('Alert threshold updated to $value');

      // Notify listeners to update UI
      notifyListeners();
    } catch (e) {
      _error = 'Failed to save alert threshold: $e';
      logger.e('Error setting alert threshold', details: e.toString());
      notifyListeners();
    }
  }

  /// Set the days in advance
  Future<void> setDaysInAdvance(int value) async {
    try {
      // Log the value being set
      logger.i('Setting days in advance to $value');

      // Save to repository
      await _settingsRepository.setDaysInAdvance(value);

      // Update local value
      _daysInAdvance = value;

      // Update stream with new value
      _daysInAdvanceController.sink.add(value);

      // Also update the SettingsProvider
      await _settingsProvider.setDaysInAdvance(value);

      // Notify other parts of the app that alert settings have changed
      EventBus().fire(EventType.alertSettingsUpdated);
      logger.i('Days in advance updated to $value');

      // Notify listeners to update UI
      notifyListeners();
    } catch (e) {
      _error = 'Failed to save days in advance: $e';
      logger.e('Error setting days in advance', details: e.toString());
      notifyListeners();
    }
  }

  /// Set the date format
  Future<void> setDateFormat(String value) async {
    try {
      // Log the value being set
      logger.i('Setting date format to $value');

      // Save to repository
      await _settingsRepository.setDateFormat(value);

      // Update local value
      _dateFormat = value;

      // Update stream with new value
      _dateFormatController.sink.add(value);

      // Also update the SettingsProvider
      await _settingsProvider.setDateFormat(value);

      // Fire the dateSettingsUpdated event to notify date-specific components
      EventBus().fire(EventType.dateSettingsUpdated);
      logger.i('Date format updated to $value');

      // Fire the generic settings updated event to ensure all components are notified
      EventBus().fire(EventType.settingsUpdated);
      logger.i('Fired generic settingsUpdated event for date format change');

      // Notify listeners to update UI
      notifyListeners();
    } catch (e) {
      _error = 'Failed to save date format: $e';
      logger.e('Error setting date format', details: e.toString());
      notifyListeners();
    }
  }

  /// Set the date info
  Future<void> setDateInfo(String value) async {
    try {
      // Log the value being set with more details
      logger.i('Setting date info to "$value" (exact case and value)');

      // Check if the value is one of the expected values
      if (value != 'Date only' &&
          value != 'Date and time' &&
          value != 'Relative date') {
        logger.w('Warning: Setting date info to unexpected value: "$value"');
      }

      // Save to repository
      await _settingsRepository.setDateInfo(value);

      // Update local value
      _dateInfo = value;

      // Update stream with new value
      _dateInfoController.sink.add(value);

      // Also update the SettingsProvider
      await _settingsProvider.setDateInfo(value);

      // Fire the dateSettingsUpdated event to notify date-specific components
      EventBus().fire(EventType.dateSettingsUpdated);
      logger.i('Date info updated to $value');

      // Fire the generic settings updated event to ensure all components are notified
      EventBus().fire(EventType.settingsUpdated);
      logger.i('Fired generic settingsUpdated event for date info change');

      // Notify listeners to update UI
      notifyListeners();
    } catch (e) {
      _error = 'Failed to save date info: $e';
      logger.e('Error setting date info', details: e.toString());
      notifyListeners();
    }
  }

  /// Set the theme mode
  Future<void> setThemeMode(String value) async {
    try {
      // Log the value being set
      logger.i('Setting theme mode to $value');

      // Save to repository
      await _settingsRepository.setThemeMode(value);

      // Update local value
      _themeMode = value;

      // Update stream with new value
      _themeModeController.sink.add(value);

      // Also update the SettingsProvider
      ThemeMode themeMode;
      switch (value) {
        case 'dark':
          themeMode = ThemeMode.dark;
          break;
        case 'light':
          themeMode = ThemeMode.light;
          break;
        default:
          themeMode = ThemeMode.system;
      }
      await _settingsProvider.setThemeMode(themeMode);

      // Notify other parts of the app that settings have changed
      EventBus().fire(EventType.settingsUpdated);
      logger.i('Theme mode updated to $value');

      // Notify listeners to update UI
      notifyListeners();
    } catch (e) {
      _error = 'Failed to save theme mode: $e';
      logger.e('Error setting theme mode', details: e.toString());
      notifyListeners();
    }
  }

  /// Set the notifications enabled state
  Future<void> setNotificationsEnabled(bool value,
      [BuildContext? context]) async {
    try {
      await _settingsRepository.setNotificationsEnabled(value);
      _notificationsEnabled = value;
      _notificationsEnabledController.sink.add(value);

      // Also update the SettingsProvider
      await _settingsProvider.setNotificationsEnabled(value);

      // If we're enabling notifications and have a context, request permissions if needed
      if (value && context != null) {
        // Store the context for later use
        final BuildContext originalContext = context;

        // Get the NotificationProvider
        final notificationProvider = NotificationProvider();

        // Check if the context is still valid
        if (originalContext.mounted) {
          // Pass the context to handle permission requests
          await notificationProvider.setNotificationsEnabled(
              value, originalContext);
        } else {
          // If context is no longer valid, just enable without showing the permission dialog
          await notificationProvider.setNotificationsEnabled(value);
        }
      }

      // Notify other parts of the app that alert settings have changed
      EventBus().fire(EventType.alertSettingsUpdated);
      logger.i('Notifications enabled set to $value');

      notifyListeners();
    } catch (e) {
      _error = 'Failed to save notifications setting: $e';
      logger.e('Error setting notifications enabled', details: e.toString());
      notifyListeners();
    }
  }

  /// Set the language
  Future<void> setLanguage(String value) async {
    try {
      // Log the value being set
      logger.i('Setting language to $value');

      // Save to repository
      await _settingsRepository.setLanguage(value);

      // Update local value
      _language = value;

      // Update stream with new value
      _languageController.sink.add(value);

      // Update the LocalizationProvider if available
      if (_localizationProvider != null) {
        await _localizationProvider.setLocale(Locale(value));
        logger.i('Updated LocalizationProvider with locale: $value');
      } else {
        logger.i('LocalizationProvider not available, skipping locale update');
      }

      // Notify other parts of the app that settings have changed
      EventBus().fire(EventType.settingsUpdated);
      logger.i('Language updated to $value');

      // Notify listeners to update UI
      notifyListeners();
    } catch (e) {
      _error = 'Failed to save language setting: $e';
      logger.e('Error setting language', details: e.toString());
      notifyListeners();
    }
  }

  /// Set the initial credit
  Future<void> setInitialCredit(double? value) async {
    try {
      if (value == null) {
        await clearInitialCredit();
      } else {
        await _settingsRepository.setInitialCredit(value);
        _initialCredit = value;
        _initialCreditController.sink.add(value);
        notifyListeners();
      }
    } catch (e) {
      _error = 'Failed to save initial credit: $e';
      notifyListeners();
    }
  }

  /// Clear the initial credit
  Future<void> clearInitialCredit() async {
    try {
      await _settingsRepository.clearInitialCredit();
      _initialCredit = null;
      _initialCreditController.sink.add(null);
      notifyListeners();
    } catch (e) {
      _error = 'Failed to clear initial credit: $e';
      notifyListeners();
    }
  }

  @override
  void dispose() {
    // Cancel event subscription
    _eventSubscription?.cancel();

    // Close all stream controllers
    _meterUnitController.close();
    _alertThresholdController.close();
    _daysInAdvanceController.close();
    _dateFormatController.close();
    _dateInfoController.close();
    _themeModeController.close();
    _notificationsEnabledController.close();
    _initialCreditController.close();
    _languageController.close();
    super.dispose();
  }

  /// Reload settings from the SettingsProvider and repository
  Future<void> reloadSettings() async {
    try {
      _isLoading = true;
      notifyListeners();

      // Store old values for logging
      final oldAlertThreshold = _alertThreshold;
      final oldDaysInAdvance = _daysInAdvance;
      final oldMeterUnit = _meterUnit;
      final oldDateFormat = _dateFormat;
      final oldDateInfo = _dateInfo;

      // Log current values before reloading
      logger.i('Current date info before reload: $_dateInfo');

      // First try to load from SettingsProvider (the single source of truth)
      // This ensures we get the most up-to-date values that might have been
      // changed elsewhere in the app
      _meterUnit = _settingsProvider.currency;
      _alertThreshold = _settingsProvider.alertThreshold;
      _daysInAdvance = _settingsProvider.daysInAdvance;
      _dateFormat = _settingsProvider.dateFormat;
      _dateInfo = _settingsProvider.dateInfo;

      // Log values after reloading from SettingsProvider
      logger.i('Date info after reload from SettingsProvider: $_dateInfo');

      _themeMode = _settingsProvider.themeMode == ThemeMode.dark
          ? 'dark'
          : _settingsProvider.themeMode == ThemeMode.light
              ? 'light'
              : 'system';
      _notificationsEnabled = _settingsProvider.notificationsEnabled;

      // For values not in SettingsProvider, load from repository
      // Load initial credit
      _initialCredit = await _settingsRepository.getInitialCredit();

      // Load language
      try {
        _language = await _settingsRepository.getLanguage();
      } catch (e) {
        // If the method doesn't exist yet, use default
        _language = 'en';
      }

      // Always update all streams to ensure UI is refreshed
      _meterUnitController.sink.add(_meterUnit);
      _alertThresholdController.sink.add(_alertThreshold);
      _daysInAdvanceController.sink.add(_daysInAdvance);
      _dateFormatController.sink.add(_dateFormat);
      _dateInfoController.sink.add(_dateInfo);
      _themeModeController.sink.add(_themeMode);
      _notificationsEnabledController.sink.add(_notificationsEnabled);
      _initialCreditController.sink.add(_initialCredit);
      _languageController.sink.add(_language);

      // Log changes for debugging
      if (oldMeterUnit != _meterUnit) {
        logger.i('Meter unit updated from $oldMeterUnit to $_meterUnit');
      }

      if (oldAlertThreshold != _alertThreshold) {
        logger.i(
            'Alert threshold updated from $oldAlertThreshold to $_alertThreshold');
      }

      if (oldDaysInAdvance != _daysInAdvance) {
        logger.i(
            'Days in advance updated from $oldDaysInAdvance to $_daysInAdvance');
      }

      if (oldDateFormat != _dateFormat) {
        logger.i('Date format updated from $oldDateFormat to $_dateFormat');
      }

      if (oldDateInfo != _dateInfo) {
        logger.i('Date info updated from $oldDateInfo to $_dateInfo');
      }

      // Set loading to false after successfully loading settings
      _isLoading = false;
    } catch (e) {
      _error = 'Failed to load settings: $e';
      _isLoading = false; // Set loading to false even if there's an error
      logger.e('Error loading settings', details: e.toString());
    }
    // Notify listeners that loading state has changed
    notifyListeners();
  }

  /// Export data to a CSV file
  Future<void> exportData(BuildContext context) async {
    try {
      _isLoading = true;
      _error = '';
      notifyListeners();

      // Store the context for later use
      final BuildContext originalContext = context;

      // Get all entries
      final entries = await _meterEntryRepository.getAllEntries();

      // Export entries using the backup service with a callback to ensure we return to settings
      final result = await _backupService.exportMeterEntries(
        entries: entries,
        onComplete: () {
          // This will be called when the export is complete, regardless of success or failure
          // Fire an event to reopen the Data Backup menu
          if (originalContext.mounted) {
            logger.i('Export completed, reopening Data Backup menu');
            EventBus().fire(EventType.dataBackupMenuRequested);
          }
        },
      );

      // Check if the context is still valid
      if (!originalContext.mounted) {
        _isLoading = false;
        notifyListeners();
        return;
      }

      result.fold(
        onSuccess: (file) {
          // Success - check if the file path contains "Download" to determine which method was used
          final message = file.path.contains('Download')
              ? 'Backup saved to Downloads/lekky_export_101.csv'
              : 'Backup saved successfully';

          ScaffoldMessenger.of(originalContext).showSnackBar(
            SnackBar(
              content: Text(message),
              duration: const Duration(seconds: 3),
            ),
          );
        },
        onFailure: (error) {
          // Check if this is a special case where we have a temp file path
          if (error.details is Map &&
              (error.details as Map).containsKey('tempFilePath')) {
            // We have a temp file that we can share, but we'll just show instructions
            // for now rather than implementing a share feature

            // Show a different message with instructions
            ScaffoldMessenger.of(originalContext).showSnackBar(
              SnackBar(
                content: const Text(
                  'Could not save to Downloads folder. Please use a file picker to select where to save your backup.',
                ),
                duration: const Duration(seconds: 5),
                action: SnackBarAction(
                  label: 'Try Again',
                  onPressed: () {
                    // Try again with file picker directly
                    exportData(originalContext);
                  },
                ),
              ),
            );
          } else {
            // Regular error
            ScaffoldMessenger.of(originalContext).showSnackBar(
              SnackBar(
                content: Text(error.message),
                duration: const Duration(seconds: 3),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
      );
    } catch (e) {
      _error = 'Failed to export data: $e';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Import data from a CSV file
  Future<void> importData(BuildContext context) async {
    try {
      _isLoading = true;
      _error = '';
      notifyListeners();

      // Store the context for later use
      final BuildContext originalContext = context;

      // Show the import options dialog first
      final importOption = await ImportOptionsDialog.show(
        context: originalContext,
        title: 'Import Data',
        message: 'Choose how to import data from your backup file:',
        replaceText: 'Replace',
        cancelText: 'Cancel',
        appendText: 'Append',
        icon: Icons.file_download,
      );

      // If user cancels, return early
      if (importOption == null) {
        _isLoading = false;
        notifyListeners();
        return;
      }

      // Check if the context is still valid
      if (!originalContext.mounted) {
        _isLoading = false;
        notifyListeners();
        return;
      }

      // Show a loading dialog to indicate the process is starting
      ScaffoldMessenger.of(originalContext).showSnackBar(
        const SnackBar(
          content: Text('Selecting file...'),
          duration: Duration(seconds: 1),
        ),
      );

      // Use the pickBackupFile method which uses FilePicker
      final pickResult = await _backupService.pickBackupFile();

      if (pickResult.isFailure) {
        _isLoading = false;
        _error = 'No file selected';
        notifyListeners();
        return;
      }

      final csvFile = pickResult.value;
      logger.i('User selected file: ${csvFile.path}');

      // Check if the context is still valid
      if (!originalContext.mounted) {
        _isLoading = false;
        notifyListeners();
        return;
      }

      // Import entries using the backup service
      final importResult = await _backupService.importMeterEntries(csvFile);

      if (context.mounted) {
        importResult.fold(
          onSuccess: (entries) async {
            // Show progress dialog for large imports
            final bool isLargeImport = entries.length > 50;
            ProgressDialog? progressDialog;

            if (isLargeImport && context.mounted) {
              progressDialog = ProgressDialog(
                context: context,
                title: 'Importing Data',
                message: 'Processing ${entries.length} entries...',
              );
              progressDialog.show();
            }

            // Use the optimized bulk add method
            final historyController =
                Provider.of<HistoryController>(context, listen: false);
            await historyController.bulkAddEntries(
              entries,
              replace: importOption == 'replace',
              onProgress: isLargeImport
                  ? (progress) {
                      if (progressDialog != null && context.mounted) {
                        progressDialog.updateProgress(progress);
                      }
                    }
                  : null,
            );

            // Dismiss progress dialog
            if (isLargeImport && progressDialog != null && context.mounted) {
              progressDialog.dismiss();
            }

            logger.i(
                'Completed import of ${entries.length} entries using bulk add');

            if (context.mounted) {
              // Show appropriate success message based on import option
              final actionText =
                  importOption == 'replace' ? 'replaced' : 'appended';

              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                      'Backup loaded successfully: ${entries.length} entries $actionText'),
                  duration: const Duration(seconds: 3),
                  backgroundColor: Colors.green,
                ),
              );
            }
          },
          onFailure: (error) {
            // Error
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(error.message),
                duration: const Duration(seconds: 3),
                backgroundColor: Colors.red,
              ),
            );
          },
        );
      }
    } catch (e) {
      _error = 'Failed to import data: $e';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Backup all settings and data for testing
  Future<Result<File>> backupDataForTesting() async {
    try {
      _isLoading = true;
      _error = '';
      notifyListeners();

      // Get all entries
      final entries = await _meterEntryRepository.getAllEntries();

      // Export entries using the backup service with callback
      final result = await _backupService.exportMeterEntries(
        entries: entries,
        onComplete: () {
          // Fire event to reopen Data Backup menu
          EventBus().fire(EventType.dataBackupMenuRequested);
        },
      );

      return result;
    } catch (e) {
      _error = 'Failed to backup data: $e';
      logger.e('Failed to backup data', details: e.toString());
      rethrow;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Restore data from backup
  Future<Result<bool>> restoreDataFromBackup() async {
    try {
      _isLoading = true;
      _error = '';
      notifyListeners();

      File? backupFile;

      // First try to find the backup file in the Downloads folder
      if (await _backupService.backupFileExists()) {
        // Get the backup file path
        final path = await _backupService.getBackupFilePath();
        if (path != null) {
          backupFile = File(path);
          logger.i('Found backup file at $path');
        }
      }

      // If we couldn't find the file in Downloads, ask the user to pick it
      if (backupFile == null) {
        logger.i('No backup file found in Downloads, asking user to pick one');

        // Use the pickBackupFile method which uses FilePicker
        final pickResult = await _backupService.pickBackupFile();

        if (pickResult.isFailure) {
          _error = 'No backup file selected';
          logger.e('User did not select a backup file');
          return Result.failure(AppError(
            message: 'No backup file selected',
            type: ErrorType.permission,
            severity: ErrorSeverity.low,
          ));
        }

        backupFile = pickResult.value;
        logger.i('User selected backup file: ${backupFile.path}');
      }

      // Import entries using the backup service
      final importResult = await _backupService.importMeterEntries(backupFile);

      return importResult.fold(
        onSuccess: (entries) async {
          // Clear existing data first
          await clearAllData(silent: true);

          // Use the optimized bulk add method
          await _meterEntryRepository.bulkAddEntries(entries);
          logger.i('Used bulk add method to restore ${entries.length} entries');

          // Set setup completed to true
          await _settingsRepository.setSetupCompleted(true);

          // Notify other parts of the app that data has changed
          EventBus().fire(EventType.dataUpdated);
          logger.i(
              'Data restored from backup: ${entries.length} entries, fired dataUpdated event');

          // Also fire settings and alert settings events to ensure notification status is properly updated
          EventBus().fire(EventType.settingsUpdated);
          EventBus().fire(EventType.alertSettingsUpdated);
          logger.i(
              'Fired settings and alert settings events to update notification status');

          // Ensure notification provider is properly initialized with current settings
          await _initializeNotificationSettings();

          return Result.success(true);
        },
        onFailure: (error) {
          _error = error.message;
          logger.e('Failed to restore data', details: error.message);
          return Result.failure(error);
        },
      );
    } catch (e) {
      _error = 'Failed to restore data: $e';
      logger.e('Failed to restore data', details: e.toString());
      return Result.failure(AppError(
        message: 'Failed to restore data: $e',
        type: ErrorType.unknown,
        severity: ErrorSeverity.medium,
        details: e,
      ));
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Reset app for testing
  Future<void> resetAppForTesting() async {
    try {
      _isLoading = true;
      _error = '';
      notifyListeners();

      // Clear all settings
      await _settingsRepository.clearAllSettings();

      // Explicitly set setup completed to false
      await _settingsRepository.setSetupCompleted(false);

      // Clear all entries at once
      await _meterEntryRepository.deleteAllEntries();
      logger.i('Cleared all entries for app reset');

      // Reset all settings to defaults
      await setMeterUnit('£');
      await setAlertThreshold(5.0);
      await setDaysInAdvance(2);
      await setDateFormat('DD-MM-YYYY');
      await setDateInfo('Date only');
      await setThemeMode('system');
      await setNotificationsEnabled(false);
      await setInitialCredit(null);

      logger.i('App reset for testing');
    } catch (e) {
      _error = 'Failed to reset app: $e';
      logger.e('Failed to reset app', details: e.toString());
      rethrow; // Rethrow to allow proper error handling
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Clear all data
  Future<void> clearAllData({bool silent = false}) async {
    try {
      _isLoading = true;
      _error = '';
      notifyListeners();

      // Delete all entries at once
      await _meterEntryRepository.deleteAllEntries();
      logger.i('Cleared all data');

      // Show success message
      _error = '';
    } catch (e) {
      _error = 'Failed to clear data: $e';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Add a meter entry directly to the repository
  /// This is used by the welcome screen when restoring data
  Future<void> addMeterEntry(MeterEntry entry) async {
    try {
      await _meterEntryRepository.addEntry(entry);
    } catch (e) {
      _error = 'Failed to add meter entry: $e';
      logger.e('Error adding meter entry', details: e.toString());
      rethrow;
    }
  }

  /// Initialize notification settings after data restoration
  /// This ensures that the notification provider is properly initialized with current settings
  Future<void> _initializeNotificationSettings() async {
    try {
      // Get a reference to the notification provider
      final notificationProvider = NotificationProvider();

      // Initialize the notification provider
      await notificationProvider.initialize();

      // Get current notification settings from settings provider (the single source of truth)
      final notificationsEnabled = _settingsProvider.notificationsEnabled;
      final lowBalanceAlerts = _settingsProvider.lowBalanceAlerts;
      final daysInAdvanceAlerts = _settingsProvider.daysInAdvanceAlerts;

      // Update notification provider with current settings
      await notificationProvider.setNotificationsEnabled(notificationsEnabled);

      // Log the initialization
      logger.i('Notification settings initialized after data restoration: '
          'notificationsEnabled=$notificationsEnabled, '
          'lowBalanceAlerts=$lowBalanceAlerts, '
          'daysInAdvanceAlerts=$daysInAdvanceAlerts');
    } catch (e) {
      logger.e('Error initializing notification settings',
          details: e.toString());
    }
  }

  /// Complete the setup process
  /// This is used by the welcome screen when restoring data
  Future<void> completeSetup() async {
    try {
      await _settingsRepository.setSetupCompleted(true);

      // Notify other parts of the app that data has changed
      EventBus().fire(EventType.dataUpdated);
      logger.i('Setup completed');
    } catch (e) {
      _error = 'Failed to complete setup: $e';
      logger.e('Error completing setup', details: e.toString());
      rethrow;
    }
  }
}
