import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../../core/di/service_locator.dart';
import '../../../../core/providers/error_provider.dart';
import '../../../../core/utils/logger.dart';
import '../../../../core/utils/retry_helper.dart';
import '../../../../core/constants/preference_keys.dart';
import '../../../../core/services/notification_count_cache.dart';
import '../../domain/models/notification_state.dart';
import '../../domain/models/notification.dart';
import '../../domain/repositories/notification_repository.dart';
import '../../data/notification_service.dart';

part 'notification_provider.g.dart';

/// Notification repository provider
@riverpod
NotificationRepository notificationRepository(NotificationRepositoryRef ref) {
  return serviceLocator<NotificationRepository>();
}

/// Notification service provider
@riverpod
NotificationService notificationService(NotificationServiceRef ref) {
  return serviceLocator<NotificationService>();
}

/// Notification provider with comprehensive notification management
@riverpod
class Notification extends _$Notification {
  @override
  Future<NotificationState> build() async {
    return await _loadInitialState();
  }

  /// Load initial state with optimized caching
  Future<NotificationState> _loadInitialState() async {
    try {
      // Start with cached count for instant UI feedback
      final cachedCount = await NotificationCountCache.getCachedUnreadCount();
      final initialState = NotificationState.initial().copyWith(
        unreadCount: cachedCount,
        isLoading: true,
      );

      // Update state immediately with cached count
      state = AsyncValue.data(initialState);

      // Load full data in background
      await _loadNotificationsOptimized(initialState);
      return state.value ?? initialState;
    } catch (error, stackTrace) {
      ref.read(globalErrorProvider.notifier).handleError(
            error,
            stackTrace,
            context: 'Notification initialization',
          );
      return NotificationState.initial().copyWith(
        errorMessage: 'Failed to load notifications: ${error.toString()}',
      );
    }
  }

  /// Create a low balance notification
  Future<void> createLowBalanceNotification(
      double balance, double threshold) async {
    final currentState = state.value ?? NotificationState.initial();

    if (!currentState.isNotificationTypeEnabled(NotificationType.lowBalance)) {
      return;
    }

    // Check deduplication - only send once per day
    if (!await _shouldSendNotification(NotificationType.lowBalance)) {
      return;
    }

    try {
      final notification = AppNotification(
        title: 'Low Balance Alert',
        message:
            'Your meter balance (£$balance) is below your alert threshold (£$threshold).',
        timestamp: DateTime.now(),
        type: NotificationType.lowBalance,
      );

      final notificationService = ref.read(notificationServiceProvider);
      await notificationService.showNotification(notification);

      // Track notification date for deduplication
      await _setLastNotificationDate(
          NotificationType.lowBalance, DateTime.now());

      await _refreshNotifications();
    } catch (error, stackTrace) {
      Logger.error(
          'Failed to create low balance notification: $error', stackTrace);
      ref.read(globalErrorProvider.notifier).handleError(
            error,
            stackTrace,
            context: 'Low balance notification creation',
          );
    }
  }

  /// Create a time to top-up notification
  Future<void> createTimeToTopUpNotification(
      double daysRemaining, int daysInAdvance) async {
    final currentState = state.value ?? NotificationState.initial();

    if (!currentState.isNotificationTypeEnabled(NotificationType.timeToTopUp)) {
      return;
    }

    // Check deduplication - only send once per day
    if (!await _shouldSendNotification(NotificationType.timeToTopUp)) {
      return;
    }

    try {
      final notification = AppNotification(
        title: 'Time to Top-Up',
        message:
            'You have approximately ${daysRemaining.toStringAsFixed(1)} days of electricity remaining.',
        timestamp: DateTime.now(),
        type: NotificationType.timeToTopUp,
      );

      final notificationService = ref.read(notificationServiceProvider);
      await notificationService.showNotification(notification);

      // Track notification date for deduplication
      await _setLastNotificationDate(
          NotificationType.timeToTopUp, DateTime.now());

      await _refreshNotifications();
    } catch (error, stackTrace) {
      Logger.error(
          'Failed to create time to top-up notification: $error', stackTrace);
      ref.read(globalErrorProvider.notifier).handleError(
            error,
            stackTrace,
            context: 'Time to top-up notification creation',
          );
    }
  }

  /// Create an invalid record notification
  Future<void> createInvalidRecordNotification(String details) async {
    final currentState = state.value ?? NotificationState.initial();

    if (!currentState
        .isNotificationTypeEnabled(NotificationType.invalidRecord)) {
      return;
    }

    try {
      final notification = AppNotification(
        title: 'Invalid Record Detected',
        message: 'An invalid record was detected: $details',
        timestamp: DateTime.now(),
        type: NotificationType.invalidRecord,
      );

      final notificationService = ref.read(notificationServiceProvider);
      await notificationService.showNotification(notification);
      await _refreshNotifications();
    } catch (error, stackTrace) {
      Logger.error(
          'Failed to create invalid record notification: $error', stackTrace);
      ref.read(globalErrorProvider.notifier).handleError(
            error,
            stackTrace,
            context: 'Invalid record notification creation',
          );
    }
  }

  /// Create a welcome notification for first-time users with optimistic updates
  Future<void> createWelcomeNotification() async {
    try {
      // Always create in-app notification regardless of settings
      // Update UI immediately with optimistic count
      final currentState = state.value ?? NotificationState.initial();
      final optimisticCount = currentState.effectiveUnreadCount + 1;

      state = AsyncValue.data(currentState.copyWith(
        optimisticCount: optimisticCount,
      ));

      // Update cache immediately
      await NotificationCountCache.incrementCachedCount();

      final notification = AppNotification(
        title: 'Welcome to Lekky!',
        message:
            'Track your prepaid electricity meter readings and get alerts when it\'s time to top up. Tap the notification icon to see all notifications.',
        timestamp: DateTime.now(),
        type: NotificationType.welcome,
      );

      // Perform database operations in background with retry logic
      final notificationService = ref.read(notificationServiceProvider);
      await RetryHelper.executeNotificationOperationWithRetry(
        () => notificationService.showNotification(notification),
        operationName: 'Welcome notification creation',
      );

      // Sync with actual database count with retry
      final notificationRepo = ref.read(notificationRepositoryProvider);
      final actualCount = await RetryHelper.executeDbOperationWithRetry(
        () => notificationRepo.getUnreadCount(),
        operationName: 'Unread count sync',
      );
      await NotificationCountCache.updateCachedUnreadCount(actualCount);

      // Clear optimistic count and update with actual count
      state = AsyncValue.data(currentState.copyWith(
        unreadCount: actualCount,
        clearOptimisticCount: true,
      ));
    } catch (error, stackTrace) {
      Logger.error('Failed to create welcome notification: $error', stackTrace);

      // Rollback optimistic update on error
      final currentState = state.value ?? NotificationState.initial();
      await NotificationCountCache.decrementCachedCount();
      state =
          AsyncValue.data(currentState.copyWith(clearOptimisticCount: true));

      // Don't show error to user for welcome notification
    }
  }

  /// Schedule a reading reminder notification
  Future<void> scheduleReadingReminder(DateTime scheduledDate) async {
    try {
      final notification = AppNotification(
        title: 'Meter Reading Reminder',
        message: 'It\'s time to take a new meter reading.',
        timestamp: scheduledDate,
        type: NotificationType.readingReminder,
      );

      final notificationService = ref.read(notificationServiceProvider);
      await notificationService.scheduleNotification(
          notification, scheduledDate);
      await _refreshNotifications();
    } catch (error, stackTrace) {
      Logger.error('Failed to schedule reading reminder: $error', stackTrace);
      ref.read(globalErrorProvider.notifier).handleError(
            error,
            stackTrace,
            context: 'Reading reminder scheduling',
          );
    }
  }

  /// Mark a notification as read
  Future<void> markAsRead(int id) async {
    try {
      final notificationRepo = ref.read(notificationRepositoryProvider);
      await notificationRepo.markAsRead(id);

      final currentState = state.value ?? NotificationState.initial();
      final updatedNotifications =
          currentState.notifications.map((notification) {
        return notification.id == id
            ? notification.copyWith(isRead: true)
            : notification;
      }).toList();

      final newUnreadCount =
          updatedNotifications.where((n) => !n.isRead).length;

      state = AsyncValue.data(currentState.copyWith(
        notifications: updatedNotifications,
        unreadCount: newUnreadCount,
      ));
    } catch (error, stackTrace) {
      Logger.error('Failed to mark notification as read: $error', stackTrace);
      ref.read(globalErrorProvider.notifier).handleError(
            error,
            stackTrace,
            context: 'Mark notification as read',
          );
    }
  }

  /// Mark all notifications as read
  Future<void> markAllAsRead() async {
    try {
      final notificationRepo = ref.read(notificationRepositoryProvider);
      await notificationRepo.markAllAsRead();

      final currentState = state.value ?? NotificationState.initial();
      final updatedNotifications = currentState.notifications
          .map((notification) => notification.copyWith(isRead: true))
          .toList();

      state = AsyncValue.data(currentState.copyWith(
        notifications: updatedNotifications,
        unreadCount: 0,
      ));
    } catch (error, stackTrace) {
      Logger.error(
          'Failed to mark all notifications as read: $error', stackTrace);
      ref.read(globalErrorProvider.notifier).handleError(
            error,
            stackTrace,
            context: 'Mark all notifications as read',
          );
    }
  }

  /// Delete a notification
  Future<void> deleteNotification(int id) async {
    try {
      final notificationRepo = ref.read(notificationRepositoryProvider);
      final notificationService = ref.read(notificationServiceProvider);
      await notificationRepo.deleteNotification(id);
      await notificationService.cancelNotification(id);

      final currentState = state.value ?? NotificationState.initial();
      final updatedNotifications = currentState.notifications
          .where((notification) => notification.id != id)
          .toList();

      final newUnreadCount =
          updatedNotifications.where((n) => !n.isRead).length;

      state = AsyncValue.data(currentState.copyWith(
        notifications: updatedNotifications,
        unreadCount: newUnreadCount,
      ));
    } catch (error, stackTrace) {
      Logger.error('Failed to delete notification: $error', stackTrace);
      ref.read(globalErrorProvider.notifier).handleError(
            error,
            stackTrace,
            context: 'Delete notification',
          );
    }
  }

  /// Delete all notifications - Fixed: Immediate UI update
  Future<void> deleteAllNotifications() async {
    try {
      final notificationRepo = ref.read(notificationRepositoryProvider);
      final notificationService = ref.read(notificationServiceProvider);
      await notificationRepo.deleteAllNotifications();
      await notificationService.cancelAllNotifications();

      // Immediate UI update
      final currentState = state.value ?? NotificationState.initial();
      state = AsyncValue.data(currentState.copyWith(
        notifications: [],
        unreadCount: 0,
      ));
    } catch (error, stackTrace) {
      Logger.error('Failed to delete all notifications: $error', stackTrace);
      ref.read(globalErrorProvider.notifier).handleError(
            error,
            stackTrace,
            context: 'Delete all notifications',
          );
    }
  }

  /// Refresh notifications
  Future<void> refresh() async {
    final currentState = state.value ?? NotificationState.initial();
    state = AsyncValue.data(currentState.copyWith(isLoading: true));
    await _loadNotifications(state.value!);
  }

  /// Clear error
  void clearError() {
    final currentState = state.value ?? NotificationState.initial();
    state = AsyncValue.data(currentState.copyWith(errorMessage: null));
  }

  /// Get last notification date for a specific type
  Future<DateTime?> _getLastNotificationDate(NotificationType type) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final key = _getNotificationDateKey(type);
      final timestamp = prefs.getInt(key);
      return timestamp != null
          ? DateTime.fromMillisecondsSinceEpoch(timestamp)
          : null;
    } catch (e) {
      Logger.error('Failed to get last notification date for $type: $e');
      return null;
    }
  }

  /// Set last notification date for a specific type
  Future<void> _setLastNotificationDate(
      NotificationType type, DateTime date) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final key = _getNotificationDateKey(type);
      await prefs.setInt(key, date.millisecondsSinceEpoch);
    } catch (e) {
      Logger.error('Failed to set last notification date for $type: $e');
    }
  }

  /// Check if notification should be sent (once per day limit)
  Future<bool> _shouldSendNotification(NotificationType type) async {
    final lastDate = await _getLastNotificationDate(type);
    if (lastDate == null) return true;

    final now = DateTime.now();

    // Check if it's a different calendar day (not just 24 hours)
    final isSameDay = lastDate.year == now.year &&
        lastDate.month == now.month &&
        lastDate.day == now.day;

    return !isSameDay;
  }

  /// Get preference key for notification date tracking
  String _getNotificationDateKey(NotificationType type) {
    switch (type) {
      case NotificationType.lowBalance:
        return PreferenceKeys.lastLowBalanceNotificationDate;
      case NotificationType.timeToTopUp:
        return PreferenceKeys.lastTimeToTopUpNotificationDate;
      default:
        throw ArgumentError(
            'Unsupported notification type for deduplication: $type');
    }
  }

  /// Helper method to refresh notifications after operations
  Future<void> _refreshNotifications() async {
    final currentState = state.value ?? NotificationState.initial();
    await _loadNotifications(currentState);
  }

  /// Load notifications with optimized caching and background cleanup
  Future<void> _loadNotificationsOptimized(
      NotificationState currentState) async {
    try {
      final notificationRepo = ref.read(notificationRepositoryProvider);

      // Get actual unread count and sync cache
      final actualUnreadCount = await notificationRepo.getUnreadCount();
      await NotificationCountCache.updateCachedUnreadCount(actualUnreadCount);

      // Load notifications lazily (only when needed)
      final notifications = <AppNotification>[];

      state = AsyncValue.data(currentState.copyWith(
        notifications: notifications,
        unreadCount: actualUnreadCount,
        isLoading: false,
        errorMessage: null,
        clearOptimisticCount: true,
      ));

      // Schedule background cleanup
      _scheduleBackgroundCleanup();
    } catch (error, stackTrace) {
      Logger.error('Failed to load notifications: $error', stackTrace);

      state = AsyncValue.data(currentState.copyWith(
        isLoading: false,
        errorMessage: 'Failed to load notifications: ${error.toString()}',
        clearOptimisticCount: true,
      ));

      ref.read(globalErrorProvider.notifier).handleError(
            error,
            stackTrace,
            context: 'Notification loading',
          );
    }
  }

  /// Load notifications from repository (legacy method for compatibility)
  Future<void> _loadNotifications(NotificationState currentState) async {
    await _loadNotificationsOptimized(currentState);
  }

  /// Load full notification list (called when dialog is opened)
  Future<void> loadFullNotificationList() async {
    final currentState = state.value ?? NotificationState.initial();

    try {
      final notificationRepo = ref.read(notificationRepositoryProvider);
      final notifications = await RetryHelper.executeDbOperationWithRetry(
        () => notificationRepo.getAllNotifications(),
        operationName: 'Load full notification list',
      );

      state = AsyncValue.data(currentState.copyWith(
        notifications: notifications,
        isLoading: false,
        errorMessage: null,
      ));
    } catch (error, stackTrace) {
      Logger.error('Failed to load full notification list: $error', stackTrace);

      state = AsyncValue.data(currentState.copyWith(
        isLoading: false,
        errorMessage: 'Failed to load notifications: ${error.toString()}',
      ));
    }
  }

  /// Schedule background cleanup (non-blocking)
  void _scheduleBackgroundCleanup() {
    // Run cleanup in background without blocking UI
    Future.delayed(const Duration(seconds: 1), () async {
      await _cleanupOldNotifications();
    });
  }

  /// Clean up notifications older than 30 days (best practice retention)
  Future<void> _cleanupOldNotifications() async {
    try {
      final notificationRepo = ref.read(notificationRepositoryProvider);
      final cutoffDate = DateTime.now().subtract(const Duration(days: 30));

      final allNotifications = await notificationRepo.getAllNotifications();
      final oldNotifications = allNotifications
          .where((notification) => notification.timestamp.isBefore(cutoffDate))
          .toList();

      if (oldNotifications.isNotEmpty) {
        for (final notification in oldNotifications) {
          if (notification.id != null) {
            await notificationRepo.deleteNotification(notification.id!);
          }
        }
        Logger.info('Cleaned up ${oldNotifications.length} old notifications');
      }
    } catch (error) {
      Logger.error('Failed to cleanup old notifications: $error');
      // Don't throw error as this is a background cleanup operation
    }
  }
}
