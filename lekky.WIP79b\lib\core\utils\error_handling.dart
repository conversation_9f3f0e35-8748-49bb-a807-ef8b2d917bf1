// File: lib/core/utils/error_handling.dart

import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:lekky/core/utils/enhanced_logger.dart';

/// Error types for categorizing different errors
enum ErrorType {
  network,
  database,
  permission,
  validation,
  authentication,
  timeout,
  notFound,
  server,
  unknown,
}

/// Severity levels for errors
enum ErrorSeverity {
  low,
  medium,
  high,
  critical,
}

/// Application error class with rich error information
class AppError implements Exception {
  final String message;
  final ErrorType type;
  final ErrorSeverity severity;
  final Map<String, dynamic>? details;
  final Object? originalException;
  final StackTrace stackTrace;
  final DateTime timestamp;
  final String? component;
  final String? operationId;

  AppError({
    required this.message,
    this.type = ErrorType.unknown,
    this.severity = ErrorSeverity.medium,
    this.details,
    this.originalException,
    StackTrace? stackTrace,
    this.component,
    this.operationId,
  })  : this.stackTrace = stackTrace ?? StackTrace.current,
        this.timestamp = DateTime.now();

  /// Create a network error
  factory AppError.network(
    String message, {
    ErrorSeverity severity = ErrorSeverity.medium,
    Map<String, dynamic>? details,
    Object? originalException,
    StackTrace? stackTrace,
    String? component,
    String? operationId,
  }) {
    return AppError(
      message: message,
      type: ErrorType.network,
      severity: severity,
      details: details,
      originalException: originalException,
      stackTrace: stackTrace,
      component: component,
      operationId: operationId,
    );
  }

  /// Create a database error
  factory AppError.database(
    String message, {
    ErrorSeverity severity = ErrorSeverity.high,
    Map<String, dynamic>? details,
    Object? originalException,
    StackTrace? stackTrace,
    String? component,
    String? operationId,
  }) {
    return AppError(
      message: message,
      type: ErrorType.database,
      severity: severity,
      details: details,
      originalException: originalException,
      stackTrace: stackTrace,
      component: component,
      operationId: operationId,
    );
  }

  /// Create a permission error
  factory AppError.permission(
    String message, {
    ErrorSeverity severity = ErrorSeverity.high,
    Map<String, dynamic>? details,
    Object? originalException,
    StackTrace? stackTrace,
    String? component,
    String? operationId,
  }) {
    return AppError(
      message: message,
      type: ErrorType.permission,
      severity: severity,
      details: details,
      originalException: originalException,
      stackTrace: stackTrace,
      component: component,
      operationId: operationId,
    );
  }

  /// Create a validation error
  factory AppError.validation(
    String message, {
    ErrorSeverity severity = ErrorSeverity.low,
    Map<String, dynamic>? details,
    Object? originalException,
    StackTrace? stackTrace,
    String? component,
    String? operationId,
  }) {
    return AppError(
      message: message,
      type: ErrorType.validation,
      severity: severity,
      details: details,
      originalException: originalException,
      stackTrace: stackTrace,
      component: component,
      operationId: operationId,
    );
  }

  /// Create a timeout error
  factory AppError.timeout(
    String message, {
    ErrorSeverity severity = ErrorSeverity.medium,
    Map<String, dynamic>? details,
    Object? originalException,
    StackTrace? stackTrace,
    String? component,
    String? operationId,
    Duration? duration,
  }) {
    final errorDetails = details ?? {};
    if (duration != null) {
      errorDetails['timeoutDuration'] = duration.inMilliseconds;
    }

    return AppError(
      message: message,
      type: ErrorType.timeout,
      severity: severity,
      details: errorDetails,
      originalException: originalException,
      stackTrace: stackTrace,
      component: component,
      operationId: operationId,
    );
  }

  /// Create an error from an exception
  factory AppError.fromException(
    Object exception, {
    String? message,
    ErrorType? type,
    ErrorSeverity? severity,
    Map<String, dynamic>? details,
    StackTrace? stackTrace,
    String? component,
    String? operationId,
  }) {
    // Try to determine error type from exception
    final errorType = type ?? _determineErrorType(exception);
    final errorSeverity = severity ?? _determineErrorSeverity(errorType);
    final errorMessage = message ?? exception.toString();

    return AppError(
      message: errorMessage,
      type: errorType,
      severity: errorSeverity,
      details: details,
      originalException: exception,
      stackTrace: stackTrace,
      component: component,
      operationId: operationId,
    );
  }

  /// Determine error type from exception
  static ErrorType _determineErrorType(Object exception) {
    final exceptionString = exception.toString().toLowerCase();

    if (exceptionString.contains('network') ||
        exceptionString.contains('socket') ||
        exceptionString.contains('connection')) {
      return ErrorType.network;
    } else if (exceptionString.contains('database') ||
        exceptionString.contains('sql') ||
        exceptionString.contains('db')) {
      return ErrorType.database;
    } else if (exceptionString.contains('permission') ||
        exceptionString.contains('denied')) {
      return ErrorType.permission;
    } else if (exceptionString.contains('validation') ||
        exceptionString.contains('invalid')) {
      return ErrorType.validation;
    } else if (exceptionString.contains('timeout') ||
        exceptionString.contains('timed out')) {
      return ErrorType.timeout;
    } else if (exceptionString.contains('not found') ||
        exceptionString.contains('404')) {
      return ErrorType.notFound;
    } else if (exceptionString.contains('server') ||
        exceptionString.contains('500')) {
      return ErrorType.server;
    } else if (exceptionString.contains('auth') ||
        exceptionString.contains('unauthorized') ||
        exceptionString.contains('401')) {
      return ErrorType.authentication;
    }

    return ErrorType.unknown;
  }

  /// Determine error severity from error type
  static ErrorSeverity _determineErrorSeverity(ErrorType type) {
    switch (type) {
      case ErrorType.network:
      case ErrorType.timeout:
        return ErrorSeverity.medium;
      case ErrorType.database:
      case ErrorType.permission:
      case ErrorType.authentication:
      case ErrorType.server:
        return ErrorSeverity.high;
      case ErrorType.validation:
      case ErrorType.notFound:
        return ErrorSeverity.low;
      case ErrorType.unknown:
      default:
        return ErrorSeverity.medium;
    }
  }

  /// Log the error
  void log() {
    final errorDetails = details ?? {};
    if (originalException != null) {
      errorDetails['exception'] = originalException.toString();
    }

    switch (severity) {
      case ErrorSeverity.low:
        EnhancedLogger.warning(
          message,
          details: errorDetails,
          component: component,
        );
        break;
      case ErrorSeverity.medium:
        EnhancedLogger.error(
          message,
          details: errorDetails,
          component: component,
          exception: originalException,
          stackTrace: stackTrace,
        );
        break;
      case ErrorSeverity.high:
      case ErrorSeverity.critical:
        EnhancedLogger.critical(
          message,
          details: errorDetails,
          component: component,
          exception: originalException,
          stackTrace: stackTrace,
        );
        break;
    }
  }

  @override
  String toString() {
    return 'AppError: $message (${type.toString().split('.').last}, ${severity.toString().split('.').last})';
  }
}

/// Result class for operations that can succeed or fail
class Result<T> {
  final T? _value;
  final AppError? _error;

  Result.success(T value)
      : _value = value,
        _error = null;
  Result.failure(AppError error)
      : _value = null,
        _error = error;

  bool get isSuccess => _error == null;
  bool get isFailure => _error != null;

  T get value {
    if (_error != null) {
      throw _error!;
    }
    return _value as T;
  }

  AppError get error {
    if (_error == null) {
      throw Exception('Cannot get error from successful result');
    }
    return _error!;
  }

  /// Map the result to a new value if successful
  Result<R> map<R>(R Function(T value) mapper) {
    if (isSuccess) {
      try {
        return Result.success(mapper(value));
      } catch (e, stackTrace) {
        return Result.failure(
          AppError.fromException(
            e,
            stackTrace: stackTrace,
            message: 'Error mapping result',
          ),
        );
      }
    } else {
      return Result.failure(error);
    }
  }

  /// Flat map the result to a new result if successful
  Result<R> flatMap<R>(Result<R> Function(T value) mapper) {
    if (isSuccess) {
      try {
        return mapper(value);
      } catch (e, stackTrace) {
        return Result.failure(
          AppError.fromException(
            e,
            stackTrace: stackTrace,
            message: 'Error flat mapping result',
          ),
        );
      }
    } else {
      return Result.failure(error);
    }
  }

  /// Handle both success and failure cases
  R fold<R>(
    R Function(T value) onSuccess,
    R Function(AppError error) onFailure,
  ) {
    if (isSuccess) {
      return onSuccess(value);
    } else {
      return onFailure(error);
    }
  }

  /// Execute a function if the result is successful
  Result<T> onSuccess(void Function(T value) action) {
    if (isSuccess) {
      action(value);
    }
    return this;
  }

  /// Execute a function if the result is a failure
  Result<T> onFailure(void Function(AppError error) action) {
    if (isFailure) {
      action(error);
    }
    return this;
  }

  /// Recover from a failure with a new value
  Result<T> recover(T Function(AppError error) recovery) {
    if (isFailure) {
      try {
        return Result.success(recovery(error));
      } catch (e, stackTrace) {
        return Result.failure(
          AppError.fromException(
            e,
            stackTrace: stackTrace,
            message: 'Error recovering from failure',
          ),
        );
      }
    } else {
      return this;
    }
  }

  /// Recover from a failure with a new result
  Result<T> recoverWith(Result<T> Function(AppError error) recovery) {
    if (isFailure) {
      try {
        return recovery(error);
      } catch (e, stackTrace) {
        return Result.failure(
          AppError.fromException(
            e,
            stackTrace: stackTrace,
            message: 'Error recovering from failure',
          ),
        );
      }
    } else {
      return this;
    }
  }
}

/// Configuration for retry operations
class RetryConfig {
  final int maxAttempts;
  final Duration initialDelay;
  final double backoffFactor;
  final Duration maxDelay;
  final List<ErrorType> retryableErrors;
  final bool retryOnTimeout;

  const RetryConfig({
    this.maxAttempts = 3,
    this.initialDelay = const Duration(milliseconds: 500),
    this.backoffFactor = 2.0,
    this.maxDelay = const Duration(seconds: 30),
    this.retryableErrors = const [
      ErrorType.network,
      ErrorType.timeout,
      ErrorType.server,
    ],
    this.retryOnTimeout = true,
  });

  /// Create a config for quick retries
  factory RetryConfig.quick() {
    return RetryConfig(
      maxAttempts: 3,
      initialDelay: const Duration(milliseconds: 200),
      backoffFactor: 1.5,
      maxDelay: const Duration(seconds: 2),
    );
  }

  /// Create a config for aggressive retries
  factory RetryConfig.aggressive() {
    return RetryConfig(
      maxAttempts: 5,
      initialDelay: const Duration(milliseconds: 200),
      backoffFactor: 2.0,
      maxDelay: const Duration(minutes: 1),
      retryableErrors: [
        ErrorType.network,
        ErrorType.timeout,
        ErrorType.server,
        ErrorType.database,
        ErrorType.unknown,
      ],
    );
  }

  /// Create a config for network operations
  factory RetryConfig.network() {
    return RetryConfig(
      maxAttempts: 3,
      initialDelay: const Duration(seconds: 1),
      backoffFactor: 2.0,
      maxDelay: const Duration(seconds: 10),
      retryableErrors: [
        ErrorType.network,
        ErrorType.timeout,
        ErrorType.server,
      ],
    );
  }

  /// Create a config for database operations
  factory RetryConfig.database() {
    return RetryConfig(
      maxAttempts: 3,
      initialDelay: const Duration(milliseconds: 500),
      backoffFactor: 1.5,
      maxDelay: const Duration(seconds: 5),
      retryableErrors: [
        ErrorType.database,
        ErrorType.timeout,
      ],
    );
  }
}

/// Retry operation with exponential backoff
Future<Result<T>> retry<T>(
  Future<T> Function() operation, {
  RetryConfig config = const RetryConfig(),
  String? operationName,
  String? component,
}) async {
  int attempt = 0;
  Duration delay = config.initialDelay;

  while (true) {
    attempt++;

    try {
      final result = await operation();
      return Result.success(result);
    } catch (e, stackTrace) {
      // Create an AppError from the exception
      final error = (e is AppError)
          ? e
          : AppError.fromException(
              e,
              stackTrace: stackTrace,
              component: component,
              operationId: operationName,
            );

      // Log the error
      if (attempt == 1 || attempt == config.maxAttempts) {
        // Log first and last attempts
        error.log();
      }

      final isRetryableError = config.retryableErrors.contains(error.type) ||
          (config.retryOnTimeout && error.type == ErrorType.timeout);

      // Check if we should retry
      if (!isRetryableError || attempt >= config.maxAttempts) {
        return Result.failure(error);
      }

      // Wait before retrying
      await Future.delayed(delay);

      // Calculate next delay with exponential backoff
      delay = Duration(
        milliseconds: (delay.inMilliseconds * config.backoffFactor).round(),
      );

      // Cap the delay
      if (delay > config.maxDelay) {
        delay = config.maxDelay;
      }

      // Log retry attempt
      EnhancedLogger.info(
        'Retrying operation${operationName != null ? ' ($operationName)' : ''}',
        details: {
          'attempt': attempt,
          'maxAttempts': config.maxAttempts,
          'delay': delay.inMilliseconds,
          'errorType': error.type.toString(),
        },
        component: component,
      );
    }
  }
}

/// Execute an operation with timeout
Future<Result<T>> withTimeout<T>(
  Future<T> Function() operation,
  Duration timeout, {
  String? operationName,
  String? component,
}) async {
  try {
    final result = await operation().timeout(timeout);
    return Result.success(result);
  } on TimeoutException catch (e, stackTrace) {
    final error = AppError.timeout(
      'Operation${operationName != null ? ' ($operationName)' : ''} timed out after ${timeout.inMilliseconds}ms',
      stackTrace: stackTrace,
      component: component,
      operationId: operationName,
      duration: timeout,
    );
    error.log();
    return Result.failure(error);
  } catch (e, stackTrace) {
    final error = (e is AppError)
        ? e
        : AppError.fromException(
            e,
            stackTrace: stackTrace,
            component: component,
            operationId: operationName,
          );
    error.log();
    return Result.failure(error);
  }
}

/// Error handler for global error handling
class ErrorHandler {
  static final List<AppError> _errors = [];
  static final List<void Function(AppError)> _errorHandlers = [];

  /// Add an error to the error log
  static void addError(AppError error) {
    _errors.add(error);

    // Log the error
    error.log();

    // Notify error handlers
    for (final handler in _errorHandlers) {
      handler(error);
    }
  }

  /// Get all errors
  static List<AppError> get errors => List.unmodifiable(_errors);

  /// Clear all errors
  static void clearErrors() {
    _errors.clear();
  }

  /// Get errors by type
  static List<AppError> getErrorsByType(ErrorType type) {
    return _errors.where((error) => error.type == type).toList();
  }

  /// Get errors by severity
  static List<AppError> getErrorsBySeverity(ErrorSeverity severity) {
    return _errors.where((error) => error.severity == severity).toList();
  }

  /// Register an error handler
  static void registerErrorHandler(void Function(AppError) handler) {
    _errorHandlers.add(handler);
  }

  /// Unregister an error handler
  static void unregisterErrorHandler(void Function(AppError) handler) {
    _errorHandlers.remove(handler);
  }

  /// Handle an exception by converting it to an AppError and adding it
  static AppError handleException(
    Object exception, {
    StackTrace? stackTrace,
    String? component,
    String? operationId,
  }) {
    final error = AppError.fromException(
      exception,
      stackTrace: stackTrace ?? StackTrace.current,
      component: component,
      operationId: operationId,
    );

    addError(error);
    return error;
  }
}
