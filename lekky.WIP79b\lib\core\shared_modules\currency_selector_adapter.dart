// File: lib/core/shared_modules/currency_selector_adapter.dart
import 'package:flutter/material.dart';
import '../settings/widgets/radio_currency_selector.dart';
import 'settings_model.dart';

/// A shared widget for selecting currency/meter unit
/// This adapter maintains compatibility with the existing code while using the new radio-based selector
class RadioCurrencySelectorAdapter extends StatelessWidget {
  final String currentValue;
  final Function(String) onChanged;
  final SettingsDisplayMode displayMode;
  final bool showHelperText;
  final bool showTitle;
  final String? errorText;
  final bool useDialog;
  final bool showCard;
  final FocusNode? focusNode;

  const RadioCurrencySelectorAdapter({
    Key? key,
    required this.currentValue,
    required this.onChanged,
    this.displayMode = SettingsDisplayMode.expanded,
    this.showHelperText = true,
    this.showTitle = true,
    this.errorText,
    this.useDialog = false,
    this.showCard = true,
    this.focusNode,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Use the new radio-based currency selector
    return RadioCurrencySelector(
      currentValue: currentValue,
      onChanged: onChanged,
      useDialog: useDialog,
      showCard: showCard,
      focusNode: focusNode,
    );
  }

  /// Static method to show a currency selection dialog
  static Future<void> showCurrencyDialog(
    BuildContext context,
    String currentValue,
    Function(String) onChanged,
  ) {
    return showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) {
          String selectedValue = currentValue;

          return AlertDialog(
            title: const Text('Select Currency'),
            content: SizedBox(
              width: double.maxFinite,
              child: RadioCurrencySelector(
                currentValue: selectedValue,
                onChanged: (value) {
                  setState(() {
                    selectedValue = value;
                  });
                },
                useDialog: false,
                showCard: false,
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () {
                  onChanged(selectedValue);
                  Navigator.of(context).pop();
                },
                child: const Text('Save'),
              ),
            ],
          );
        },
      ),
    );
  }
}
