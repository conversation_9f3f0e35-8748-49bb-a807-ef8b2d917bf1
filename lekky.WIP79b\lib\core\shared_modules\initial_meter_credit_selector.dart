// File: lib/core/shared_modules/initial_meter_credit_selector.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../widgets/app_text_field.dart';
import '../settings/validators/settings_validator.dart';
import 'base_settings_widget.dart';
import 'settings_model.dart';

class InitialMeterCreditSelector extends BaseSettingsWidget {
  final double? currentValue;
  final Function(double?) onChanged;
  final String currencySymbol;
  final String? errorText;

  const InitialMeterCreditSelector({
    Key? key,
    required this.currentValue,
    required this.onChanged,
    required this.currencySymbol,
    this.errorText,
    SettingsDisplayMode displayMode = SettingsDisplayMode.compact,
    bool showHelperText = false,
    bool showTitle = true,
  }) : super(
            key: key,
            displayMode: displayMode,
            showHelperText: showHelperText,
            showTitle: showTitle);

  @override
  Widget buildExpandedView(BuildContext context) {
    final primaryColor = Theme.of(context).colorScheme.primary;
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (showTitle) buildSectionTitle(context, 'First Meter Reading'),

        if (showHelperText)
          buildHelperText(context,
              'Enter your initial meter credit (leave empty if not applicable)'),

        const SizedBox(height: 16),

        // Input field with controller
        _InitialMeterCreditInput(
          initialValue: currentValue,
          currencySymbol: currencySymbol,
          onChanged: onChanged,
        ),

        const SizedBox(height: 8),
        Text(
          'Leave blank if you don\'t want to track your initial credit',
          style: TextStyle(
            fontSize: 12,
            fontStyle: FontStyle.italic,
            color: Colors.grey[600],
          ),
        ),

        buildErrorText(context, errorText),
      ],
    );
  }

  @override
  Widget buildCompactView(BuildContext context) {
    final primaryColor = Theme.of(context).colorScheme.primary;
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (showTitle) buildSectionTitle(context, 'First Meter Reading'),

        if (showHelperText)
          buildHelperText(context,
              'Enter your initial meter credit (leave empty if not applicable)'),

        const SizedBox(height: 8),

        // Input field with controller - more compact
        _InitialMeterCreditInput(
          initialValue: currentValue,
          currencySymbol: currencySymbol,
          onChanged: onChanged,
          isCompact: true,
        ),

        buildErrorText(context, errorText),
      ],
    );
  }
}

class _InitialMeterCreditInput extends StatefulWidget {
  final double? initialValue;
  final String currencySymbol;
  final Function(double?) onChanged;
  final bool isCompact;

  const _InitialMeterCreditInput({
    Key? key,
    required this.initialValue,
    required this.currencySymbol,
    required this.onChanged,
    this.isCompact = false,
  }) : super(key: key);

  @override
  State<_InitialMeterCreditInput> createState() => _InitialMeterCreditInputState();
}

class _InitialMeterCreditInputState extends State<_InitialMeterCreditInput> {
  late TextEditingController _controller;
  late FocusNode _focusNode;
  String? _errorText;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(
      text: widget.initialValue?.toString() ?? '',
    );
    _focusNode = FocusNode();
    _focusNode.addListener(_onFocusChange);
  }

  void _onFocusChange() {
    if (_focusNode.hasFocus) {
      _controller.selection = TextSelection(
        baseOffset: 0,
        extentOffset: _controller.text.length,
      );
    }
  }

  @override
  void didUpdateWidget(_InitialMeterCreditInput oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Only update the controller text if the external value changed and we're not focused
    if (!_focusNode.hasFocus && widget.initialValue != oldWidget.initialValue) {
      _controller.text = widget.initialValue?.toString() ?? '';
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AppTextField(
      controller: _controller,
      focusNode: _focusNode,
      keyboardType: const TextInputType.numberWithOptions(decimal: true),
      inputFormatters: [
        FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*$')),
      ],
      prefixText: widget.currencySymbol,
      errorText: _errorText,
      labelText: widget.isCompact ? 'Initial Credit' : 'First Meter Reading',
      helperText: widget.isCompact ? null : 'Optional',
      isDense: widget.isCompact,
      contentPadding: widget.isCompact
          ? const EdgeInsets.symmetric(horizontal: 8, vertical: 8)
          : null,
      onChanged: _validateAndUpdate,
      selectAllOnFocus: false, // We handle selection in the focus listener
    );
  }

  void _validateAndUpdate(String value) {
    // Handle empty input (optional)
    if (value.isEmpty) {
      setState(() {
        _errorText = null;
      });
      widget.onChanged(null);
      return;
    }

    // Validate using SettingsValidator
    final validation = SettingsValidator.validateInitialCredit(value);

    setState(() {
      _errorText = validation['isValid'] ? null : validation['errorMessage'];
    });

    if (!validation['isValid']) {
      return;
    }

    // Parse the value
    final initialCredit = double.tryParse(value);
    
    // Additional validation for positive values
    if (initialCredit != null && initialCredit <= 0) {
      setState(() {
        _errorText = 'First meter reading must be greater than zero';
      });
      return;
    }

    // All validations passed
    setState(() {
      _errorText = null;
    });

    // Round to 2 decimal places when passing to parent
    if (initialCredit != null) {
      final roundedValue = double.parse(initialCredit.toStringAsFixed(2));
      widget.onChanged(roundedValue);
    }
  }
}
