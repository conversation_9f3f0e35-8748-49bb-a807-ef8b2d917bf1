# Lekky App - New Modular Architecture Plan

## 1. Project Overview

Lekky is a Flutter application designed to help users track and manage their electricity meter readings. The app allows users to record meter readings, track top-ups, calculate usage averages, estimate costs, and receive notifications for low balances. Key features include:

- Meter reading and top-up tracking
- Usage analytics and cost calculations
- Historical data visualization
- Customizable alerts and notifications
- Data backup and restore functionality
- Dark mode support and accessibility features

**Vision**: Lekky aims to be a scalable, modular, offline-first utility app that provides users with clear insights into their electricity usage while maintaining a high standard of code quality, performance, and user experience.

## 2. Summary of Improvements from Previous Plan

### User Experience Improvements
- **Enhanced Onboarding**: Guided tour and contextual help to improve first-time user experience
- **Improved Visualization**: Better data presentation in History and Cost screens for clearer insights
- **Accessibility Enhancements**: Screen reader support, text scaling, and high contrast mode
- **Responsive Design**: Optimized layouts for different screen sizes and orientations

**Why it matters**: These improvements will significantly enhance user satisfaction, reduce learning curve, and make the app accessible to a wider audience.

### Technical Architecture Improvements
- **Feature-Based Modularization**: Self-contained feature modules with clear boundaries
- **Services Layer**: Dedicated services for cross-cutting concerns
- **Dependency Injection**: Improved service locator implementation
- **State Management Optimization**: Feature-scoped providers with reduced rebuilds

**Why it matters**: This architecture will improve maintainability, testability, and make future feature additions more straightforward.

### Performance Optimizations
- **Efficient Widget Rebuilds**: Optimized rendering with const constructors and RepaintBoundary
- **Background Processing**: Moving heavy calculations off the main thread
- **Enhanced Caching**: Better data caching strategy for improved responsiveness
- **List Virtualization**: Optimized rendering for large datasets

**Why it matters**: These optimizations will result in a more responsive app with better battery efficiency and smoother user experience.

### Quality & Testing Improvements
- **Comprehensive Testing**: Unit, widget, and integration tests for critical functionality
- **Error Handling**: Robust error handling system with proper user feedback
- **Performance Monitoring**: Tools to track and improve app performance
- **Analytics Integration**: Usage tracking to guide future improvements

**Why it matters**: These improvements will reduce bugs, crashes, and provide data-driven insights for future development.

## 3. New Modular Project Structure

```
lib/
├── app.dart                 # App root (MaterialApp configuration)
├── main.dart                # Entry point (initialization, DI setup)
├── app_scaffold.dart        # Main app scaffold with navigation
│
├── core/                    # App-wide core functionality
│   ├── constants/           # App-wide constants and configuration
│   ├── di/                  # Dependency injection setup
│   ├── errors/              # Error handling and reporting
│   ├── logging/             # Logging infrastructure
│   ├── navigation/          # Navigation service and routes
│   ├── storage/             # Local storage abstractions
│   ├── theme/               # Theme definitions and services
│   │   ├── tokens/          # Design tokens (colors, spacing, etc.)
│   │   └── styles/          # Text and component styles
│   └── utils/               # Utility functions and helpers
│
├── data/                    # Data layer
│   ├── models/              # Core data models
│   ├── repositories/        # Data access repositories
│   └── sources/             # Data sources (local DB, preferences)
│
├── services/                # Cross-cutting services
│   ├── analytics/           # Usage analytics
│   ├── backup/              # Backup and restore
│   ├── notifications/       # Local notifications
│   ├── permissions/         # Permission handling
│   └── preferences/         # User preferences
│
├── shared/                  # Shared UI components
│   ├── widgets/             # Reusable widgets
│   │   ├── buttons/         # Button components
│   │   ├── cards/           # Card components
│   │   ├── dialogs/         # Dialog components
│   │   ├── forms/           # Form components
│   │   └── feedback/        # Loading, error states
│   ├── animations/          # Shared animations
│   └── extensions/          # Widget and context extensions
│
└── features/                # Feature modules
    ├── splash/              # Splash screen
    │   └── presentation/    # UI components
    │
    ├── welcome/             # Welcome and onboarding
    │   └── presentation/    # UI components
    │
    ├── setup/               # Initial app setup
    │   ├── data/            # Setup data handling
    │   ├── domain/          # Setup business logic
    │   └── presentation/    # UI components
    │
    ├── home/                # Home screen feature
    │   ├── data/            # Home data handling
    │   ├── domain/          # Home business logic
    │   └── presentation/    # UI components
    │       ├── controllers/ # State management
    │       ├── screens/     # Full screens
    │       └── widgets/     # Feature-specific widgets
    │
    ├── meter/               # Meter reading management
    │   ├── data/            # Meter data handling
    │   ├── domain/          # Meter business logic
    │   └── presentation/    # UI components
    │
    ├── history/             # History feature
    │   ├── data/            # History data handling
    │   ├── domain/          # History business logic
    │   └── presentation/    # UI components
    │
    ├── cost/                # Cost calculation feature
    │   ├── data/            # Cost data handling
    │   ├── domain/          # Cost business logic
    │   └── presentation/    # UI components
    │
    ├── settings/            # Settings feature
    │   ├── data/            # Settings data handling
    │   ├── domain/          # Settings business logic
    │   └── presentation/    # UI components
    │
    ├── backup/              # Backup and restore feature
    │   ├── data/            # Backup data handling
    │   ├── domain/          # Backup business logic
    │   └── presentation/    # UI components
    │
    ├── notifications/       # Notifications feature
    │   ├── data/            # Notification data handling
    │   ├── domain/          # Notification business logic
    │   └── presentation/    # UI components
    │
    └── help/                # Help and support feature
        ├── data/            # Help data handling
        ├── domain/          # Help business logic
        └── presentation/    # UI components
```

### Core Responsibilities

- **app.dart**: Configures the MaterialApp with themes, routes, and global providers.
- **main.dart**: Entry point that initializes services, sets up dependency injection, and launches the app.
- **core/**: Contains app-wide functionality that's used across multiple features.
- **data/**: Houses core data models and repositories that are used by multiple features.
- **services/**: Implements cross-cutting services that provide functionality to multiple features.
- **shared/**: Contains reusable UI components and utilities that are used across the app.
- **features/**: Contains feature-specific modules, each with its own data, domain, and presentation layers.

## 4. File Size Guidelines

To maintain code readability and organization, the following guidelines should be followed:

- **Maximum file size**: 600 lines per file
- **Target file size**: 300-400 lines where possible
- **Splitting rules**:
  - Screens, widgets, models, and services must live in separate files
  - Large widgets should be broken down into smaller, composable components
  - Business logic should be extracted into separate domain classes
  - Complex UI logic should be moved to controllers/providers

When a file approaches 500 lines, consider refactoring by:
1. Extracting reusable components to separate files
2. Moving business logic to domain layer classes
3. Splitting large widgets into smaller, focused components
4. Creating helper/utility classes for repeated functionality

## 5. State Management & Dependency Injection

### State Management Approach

The recommended approach is to use **Provider** with a potential migration path to **Riverpod** for improved testability:

- **Feature-level state**: Each feature should have its own controllers/providers scoped to that feature
- **App-level state**: Global state should be managed through app-level providers
- **UI state**: Local UI state should be managed within StatefulWidgets where appropriate

### Dependency Injection Structure

- Use **get_it** for service location and dependency injection
- Centralize registration in `core/di/service_locator.dart`
- Organize registrations by feature and layer:

```dart
// Example structure for service_locator.dart
void setupServiceLocator() {
  // Core services
  _registerCoreServices();
  
  // Feature-specific services
  _registerHomeFeature();
  _registerHistoryFeature();
  _registerCostFeature();
  // etc.
}
```

- Lazy-load feature-specific dependencies when possible to improve startup time
- Use factories for controllers and singletons for services

## 6. Theming, Utilities, and Cross-Cutting Concerns

### Theming

- Store theme definitions in `core/theme/`
- Use a design token system in `core/theme/tokens/` for colors, spacing, typography, etc.
- Implement a theme service in `core/theme/theme_service.dart` to manage theme changes
- Support both light and dark modes with proper contrast ratios

### Utilities and Helpers

- Place utility functions in `core/utils/` organized by purpose
- Create extension methods in `shared/extensions/` for common widget and context operations
- Implement reusable animations in `shared/animations/`

### Cross-Cutting Concerns

- **Logging**: Implement a centralized logging system in `core/logging/`
- **Error Handling**: Create a robust error handling system in `core/errors/`
- **Analytics**: Implement usage tracking in `services/analytics/`
- **Permissions**: Handle permission requests in `services/permissions/`
- **Navigation**: Centralize navigation logic in `core/navigation/`

## 7. Future Feature Expansion Plan

The modular architecture is designed to easily accommodate new features:

### Adding OCR Meter Reading

1. Create a new feature module at `features/ocr/`
2. Implement camera access in `features/ocr/data/`
3. Create OCR processing logic in `features/ocr/domain/`
4. Build UI components in `features/ocr/presentation/`
5. Register OCR services in the service locator
6. Integrate with existing meter reading flow

### Implementing Cloud Sync

1. Create a new feature module at `features/cloud_sync/`
2. Implement cloud storage adapters in `features/cloud_sync/data/`
3. Create synchronization logic in `features/cloud_sync/domain/`
4. Build UI components in `features/cloud_sync/presentation/`
5. Register cloud services in the service locator
6. Integrate with existing backup/restore functionality

### Adding Predictive Analytics

1. Create a new feature module at `features/insights/`
2. Implement data analysis algorithms in `features/insights/domain/`
3. Create visualization components in `features/insights/presentation/`
4. Register analytics services in the service locator
5. Integrate with existing history and cost features

This architecture allows new features to be developed independently while leveraging existing core functionality, ensuring that the app remains maintainable and scalable as it grows.
