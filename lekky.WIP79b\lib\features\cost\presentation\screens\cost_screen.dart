// File: lib/features/cost/presentation/screens/cost_screen.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/utils/helpful_messages.dart';
import '../../../../core/widgets/app_card.dart';
import '../../../../core/widgets/message_banner.dart';
import '../../../../core/shared_modules/settings_provider.dart';
import '../../../../core/widgets/dialogs/date_picker_dialog.dart'
    as custom_date_picker;

import '../../domain/models/cost_period.dart';
import '../../domain/utils/cost_message_provider.dart';
import '../controllers/cost_controller.dart';

/// The cost screen of the app
class CostScreen extends StatefulWidget {
  const CostScreen({super.key});

  @override
  State<CostScreen> createState() => _CostScreenState();
}

class _CostScreenState extends State<CostScreen> {
  // Focus node for initial unfocus
  final FocusNode _dummyFocusNode = FocusNode();

  // Index for the current message to display
  int _currentMessageIndex = 0;

  @override
  void initState() {
    super.initState();
    // Initialize the controller
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<CostController>().init();
      // Ensure no buttons are focused on screen load
      FocusScope.of(context).requestFocus(_dummyFocusNode);
    });

    // Rotate messages every 10 seconds
    _startMessageRotation();
  }

  void _startMessageRotation() {
    // Rotate messages every 10 seconds
    Future.delayed(const Duration(seconds: 10), () {
      if (mounted) {
        setState(() {
          _currentMessageIndex =
              (_currentMessageIndex + 1) % HelpfulMessages.allMessages.length;
        });
        _startMessageRotation(); // Schedule the next rotation
      }
    });
  }

  @override
  void dispose() {
    _dummyFocusNode.dispose();
    super.dispose();
  }

  // Add method to navigate to History screen with invalid entries filtered
  void _navigateToInvalidEntries(BuildContext context) {
    // Navigate to History screen
    Navigator.pushNamed(
      context,
      '/history',
      arguments: {'filterInvalidEntries': true},
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    return Consumer<CostController>(
      builder: (context, controller, _) {
        return RefreshIndicator(
          onRefresh: () async {
            await controller.init();
          },
          child: CustomScrollView(
            slivers: [
              _buildAppBar(),
              SliverPadding(
                padding: const EdgeInsets.symmetric(
                    horizontal: 8), // Only horizontal padding
                sliver: SliverList(
                  delegate: SliverChildListDelegate([
                    if (controller.isLoading)
                      const Center(
                        child: Padding(
                          padding: EdgeInsets.symmetric(vertical: 32),
                          child: CircularProgressIndicator(),
                        ),
                      )
                    else ...[
                      const SizedBox(height: 8), // Add 8px top spacing
                      _buildCostCard(controller),
                      const SizedBox(height: 8), // Add 8px bottom spacing
                    ],
                    if (controller.error.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(
                            top: 8), // Adjusted from 16px to 8px
                        child: Text(
                          controller.error,
                          style: AppTextStyles.bodyMedium.copyWith(
                            color:
                                Theme.of(context).brightness == Brightness.dark
                                    ? AppColors.errorDark
                                    : AppColors.error,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                  ]),
                ),
              ),
              // Add a dummy focus node to request focus on load
              SliverToBoxAdapter(
                child: SizedBox(
                  width: 0,
                  height: 0,
                  child: Focus(
                    focusNode: _dummyFocusNode,
                    child: Container(),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // Build the message banner with rotating helpful messages
  Widget _buildMessageBanner() {
    return MessageBanner(
      message: HelpfulMessages.allMessages[_currentMessageIndex],
    );
  }

  Widget _buildAppBar() {
    return SliverAppBar(
      toolbarHeight: 96, // Fixed height of 96px to match Homepage
      pinned: true,
      automaticallyImplyLeading: false, // Remove back arrow
      // Use fixed height Stack instead of FlexibleSpaceBar
      flexibleSpace: SizedBox(
        height: 96, // Fixed height of 96px
        child: Stack(
          children: [
            // Background gradient
            Positioned.fill(
              child: Container(
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: AppColors.costGradient,
                  ),
                ),
              ),
            ),
            // Custom positioned title - matching Home screen style and position
            const Positioned(
              top: 20,
              left: 20, // Exactly 20px from left edge to match Home screen
              child: Text(
                'Cost', // Banner text
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 40, // Matching Home screen font size
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            // App bar actions positioned exactly like History screen
            Positioned(
              top: 10,
              right: 12 +
                  MediaQuery.of(context).padding.right, // Adjust for safe area
              child: Consumer<CostController>(
                builder: (context, controller, _) {
                  return Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Invalid entry indicator - positioned to match History screen exactly
                      if (controller.hasInvalidHistoryRecords)
                        Transform.translate(
                          offset: const Offset(0,
                              4), // Move down by 4px to align with other icons
                          child: Container(
                            margin: const EdgeInsets.only(right: 8),
                            decoration: const BoxDecoration(
                              color: AppColors.error,
                              shape: BoxShape.circle,
                            ),
                            child: GestureDetector(
                              onTap: () => _navigateToInvalidEntries(context),
                              child: Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: Text(
                                  '${controller.invalidEntryCount}',
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 14,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      // Placeholder for future icons (e.g., settings, info) to match HistoryScreen layout
                    ],
                  );
                },
              ),
            ),
          ],
        ),
      ),
      // Remove notification button
      // Position the message bar directly below the banner with no spacing
      bottom: PreferredSize(
        // Use zero height to eliminate any spacing
        preferredSize: const Size.fromHeight(0),
        child: _buildMessageBanner(),
      ),
    );
  }

  Widget _buildCostCard(CostController controller) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final labelColor =
        isDarkMode ? AppColors.primaryTextDark : AppColors.primary;

    // Get screen dimensions to make the card extend to the bottom
    // Match the position of the Add Entry button on the Homepage
    final screenHeight = MediaQuery.of(context).size.height;
    const appBarHeight = 96 + 32; // Banner (96px) + Message bar (32px)
    const paddingHeight = 32; // Top and bottom padding (16px each)
    const bottomSafeArea = 16; // Additional padding for bottom safe area
    const bottomNavHeight =
        80; // Match the height of the Add Entry button (80px)
    // Increase available height by 10px (16px - 4px - 2px) to make the dialog box taller
    final availableHeight = screenHeight -
        appBarHeight -
        paddingHeight -
        bottomSafeArea -
        bottomNavHeight +
        10; // Add 10px to increase dialog height (16px - 4px - 2px)

    return AppCard(
      padding: const EdgeInsets.all(16),
      elevation: 2,
      borderRadius: BorderRadius.circular(16),
      backgroundColor: null, // Use default from AppCard
      hasShadow: true,
      child: Container(
        // Use constraints to ensure the card doesn't extend beyond the screen
        constraints: BoxConstraints(
          maxHeight: availableHeight,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with title and icon
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Consumer<SettingsProvider>(
                  builder: (context, settingsProvider, _) {
                    return Text(
                      'Cost of Electric ${settingsProvider.currency}',
                      style: AppTextStyles.titleMedium.copyWith(
                        color: labelColor,
                      ),
                    );
                  },
                ),
                const CircleAvatar(
                  backgroundColor: AppColors.costTab,
                  radius: 14,
                  child: Icon(
                    Icons.electric_meter,
                    color: Colors.white,
                    size: 16,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),

            // Fixed height container for cost display to prevent layout shifts
            SizedBox(
              height: 96, // Reduced by 24px from original 120px height
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Cost Display - styled like MeterTotal on homepage
                  Text(
                    controller.formattedCostPerPeriod,
                    style: AppTextStyles.valueText.copyWith(
                      fontSize: 40, // Adjusted to match Meter Total value size
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).brightness == Brightness.dark
                          ? AppColors.primaryDark
                          : AppColors.primary,
                    ),
                  ),

                  // Combined period and calculation method text
                  SizedBox(
                    height: 40, // Fixed height for 2 lines of text
                    child: Text(
                      _getCombinedPeriodText(controller),
                      style: AppTextStyles.labelLarge.copyWith(
                        color: isDarkMode ? Colors.white70 : labelColor,
                      ),
                      textAlign: TextAlign.left,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),

            // Divider between Cost Display and Period Selector
            Divider(
              color: isDarkMode ? Colors.white24 : Colors.black12,
              height: 16, // Reduced height
            ),

            // Period buttons (Day, Week, Month, Year) - using future periods
            Row(
              children: CostPeriod.futurePeriods.map((period) {
                return Expanded(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 2.0),
                    child: _buildPeriodButton(period, controller),
                  ),
                );
              }).toList(),
            ),

            const SizedBox(height: 12),

            // FROM date picker - full width
            _buildDatePickerField(
              'From',
              controller.dateRange.startDate,
              (date) {
                controller.fromDate = date;
                if (controller.selectedPeriod != CostPeriod.custom) {
                  controller.updatePeriod(CostPeriod.custom);
                }
              },
              true,
              controller,
              isDarkMode,
            ),

            const SizedBox(height: 12),

            // TO date picker - full width
            _buildDatePickerField(
              'To',
              controller.dateRange.endDate,
              (date) {
                controller.toDate = date;
                if (controller.selectedPeriod != CostPeriod.custom) {
                  controller.updatePeriod(CostPeriod.custom);
                }
              },
              false,
              controller,
              isDarkMode,
            ),

            const SizedBox(height: 12),

            // Custom button
            SizedBox(
              width: double.infinity,
              child: _buildCustomButton(controller, isDarkMode),
            ),

            // Error message if applicable
            if (controller.dateRangeError != null &&
                controller.dateRangeError!.isNotEmpty)
              Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: Text(
                  controller.dateRangeError!,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: isDarkMode ? AppColors.errorDark : AppColors.error,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
          ],
        ),
      ),
    );
  }

  // Build a period button (Day, Week, Month, Year)
  Widget _buildPeriodButton(CostPeriod period, CostController controller) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final isSelected = controller.selectedPeriod == period;

    return SizedBox(
      height: 40,
      child: ElevatedButton(
        onPressed: () => controller.updatePeriod(period),
        style: ElevatedButton.styleFrom(
          backgroundColor: isSelected
              ? (isDarkMode ? AppColors.primaryDark : AppColors.primary)
              : Colors.transparent,
          foregroundColor: isSelected
              ? Colors.white
              : (isDarkMode ? AppColors.primaryDark : AppColors.primary),
          elevation: isSelected ? 2 : 0,
          padding: EdgeInsets.zero,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(4),
            side: BorderSide(
              color: isDarkMode ? AppColors.primaryDark : AppColors.primary,
              width: 1,
            ),
          ),
        ),
        child: Text(
          period.name,
          style: TextStyle(
            fontSize: 14,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          ),
        ),
      ),
    );
  }

  // Build a date picker field
  Widget _buildDatePickerField(
    String label,
    DateTime? date,
    ValueChanged<DateTime?> onDateChanged,
    bool isStartDate,
    CostController controller,
    bool isDarkMode,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.bold,
            color: isDarkMode ? Colors.white70 : Colors.black87,
          ),
        ),
        const SizedBox(height: 4),

        // Date field
        InkWell(
          onTap: () => _selectDate(
            context,
            date,
            onDateChanged,
            isStartDate,
            controller,
          ),
          child: Container(
            height: 40,
            padding: const EdgeInsets.symmetric(horizontal: 12),
            decoration: BoxDecoration(
              border: Border.all(
                color: isDarkMode ? Colors.white30 : Colors.black26,
                width: 1,
              ),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // Date text
                FutureBuilder<String>(
                  future: controller.getDateInfo(),
                  builder: (context, snapshot) {
                    final includeTime = snapshot.data == 'Date and time';
                    return Text(
                      date != null
                          ? _formatDate(date, includeTime: includeTime)
                          : 'Today', // Both from and to dates default to today
                      style: TextStyle(
                        fontSize: 14,
                        color: isDarkMode ? Colors.white : Colors.black87,
                      ),
                    );
                  },
                ),

                // Calendar icon
                Icon(
                  Icons.calendar_today,
                  size: 16,
                  color: isDarkMode ? Colors.white54 : Colors.black54,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  // Build the custom button
  Widget _buildCustomButton(CostController controller, bool isDarkMode) {
    final isSelected = controller.selectedPeriod == CostPeriod.custom;
    final hasInvalidRecords = controller.hasInvalidHistoryRecords;

    return ElevatedButton(
      onPressed: hasInvalidRecords
          ? () {
              // Show error message if there are invalid history records
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text(
                    "Please fix any invalid history entries.",
                    style: TextStyle(color: Colors.white),
                  ),
                  backgroundColor: Colors.red,
                ),
              );
            }
          : () => controller.updatePeriod(CostPeriod.custom),
      style: ElevatedButton.styleFrom(
        backgroundColor: isSelected
            ? (isDarkMode ? AppColors.primaryDark : AppColors.primary)
            : Colors.transparent,
        foregroundColor: isSelected
            ? Colors.white
            : (isDarkMode ? AppColors.primaryDark : AppColors.primary),
        elevation: isSelected ? 2 : 0,
        padding: const EdgeInsets.symmetric(vertical: 10),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(4),
          side: BorderSide(
            color: isDarkMode ? AppColors.primaryDark : AppColors.primary,
            width: 1,
          ),
        ),
      ),
      child: Text(
        'Custom',
        style: TextStyle(
          fontSize: 16,
          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
        ),
      ),
    );
  }

  // Format a date as a string
  String _formatDate(DateTime date, {bool includeTime = false}) {
    if (includeTime) {
      return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }

  // Helper method to show error snackbar without BuildContext issues
  void _showErrorSnackBar(String message) {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  // Show date picker dialog
  Future<void> _selectDate(
    BuildContext context,
    DateTime? currentDate,
    ValueChanged<DateTime?> onDateChanged,
    bool isStartDate,
    CostController controller,
  ) async {
    // Check if total average is valid (not 0 or null)
    final totalAverage = await controller.getTotalAverageUsage();
    final isTotalAverageValid = totalAverage > 0;

    // Check the Date Info setting
    final dateInfo = await controller.getDateInfo();
    final includeTime = dateInfo == 'Date and time';

    // Check if the widget is still mounted before proceeding
    if (!mounted) return;

    // Determine the valid date range for this field
    final today = DateTime.now();
    final fiveYearsFromNow = today.add(const Duration(days: 365 * 5));

    // Default values
    DateTime? firstDateNullable;
    DateTime? lastDateNullable;

    if (isStartDate) {
      // "From" date constraints
      // Minimum date is the earliest meter reading date
      firstDateNullable = controller.earliestDate;

      // Maximum date is the currently selected "To" date (can be the same day)
      lastDateNullable = controller.dateRange.endDate;

      // If no valid dates are available or total average is not valid, show error message
      if (firstDateNullable == null || !isTotalAverageValid) {
        // No meter readings exist or total average is 0
        _showErrorSnackBar(firstDateNullable == null
            ? "Insufficient Meter Readings."
            : "Cannot calculate cost with zero average usage.");
        return; // Exit the method
      }

      // If no "To" date is set, use today or latest reading date
      lastDateNullable ??= controller.latestDate ?? today;
    } else {
      // "To" date constraints
      // Minimum date is the currently selected "From" date (can be the same day)
      firstDateNullable = controller.dateRange.startDate;

      // Maximum date is five years from now
      lastDateNullable = fiveYearsFromNow;

      // If no 'from date' is available or total average is not valid, then 'date to' also has no date available
      if (firstDateNullable == null || !isTotalAverageValid) {
        _showErrorSnackBar(firstDateNullable == null
            ? "Please select a 'From' date first."
            : "Cannot calculate cost with zero average usage.");
        return; // Exit the method
      }
    }

    // At this point, we've validated that both dates are non-null
    // We know these are non-null due to our checks above
    final DateTime firstDate =
        firstDateNullable; // We've already checked it's non-null
    final DateTime lastDate =
        lastDateNullable; // We've already checked it's non-null

    // Set a valid initial date
    DateTime initialDate;
    if (currentDate != null) {
      initialDate = currentDate;
    } else {
      // Default to today or the earliest/latest date if today is out of range
      initialDate = today;
      if (today.isBefore(firstDate)) {
        initialDate = firstDate;
      } else if (today.isAfter(lastDate)) {
        initialDate = lastDate;
      }
    }

    // Check if the widget is still mounted before proceeding
    if (!mounted) return;

    DateTime? pickedDateTime;

    try {
      if (includeTime) {
        // Show date and time picker if Date Info is set to "Date and time"
        // Use a safer approach to avoid BuildContext issues
        if (!mounted) return;

        // Use a synchronous function to avoid BuildContext issues
        pickedDateTime = await Future<DateTime?>.sync(() async {
          if (!mounted) return null;
          return await custom_date_picker.DatePickerDialog.showDateTimePicker(
            context: context,
            title: isStartDate
                ? 'Select Start Date and Time'
                : 'Select End Date and Time',
            initialDate: initialDate,
            firstDate: firstDate,
            lastDate: lastDate,
            currentTime: TimeOfDay.fromDateTime(initialDate),
            includeTime: true,
            addCurrentSecond: true, // Add current second to avoid duplicates
            confirmText: 'Select',
            cancelText: 'Cancel',
            helpText: isStartDate
                ? 'Choose a start date and time'
                : 'Choose an end date and time',
          );
        });
      } else {
        // Show only date picker if Date Info is set to "Date only"
        // Use a safer approach to avoid BuildContext issues
        if (!mounted) return;

        // Use a synchronous function to avoid BuildContext issues
        final pickedDate = await Future<DateTime?>.sync(() async {
          if (!mounted) return null;
          return await custom_date_picker.DatePickerDialog.showDatePicker(
            context: context,
            title: isStartDate ? 'Select Start Date' : 'Select End Date',
            initialDate: initialDate,
            firstDate: firstDate,
            lastDate: lastDate,
            helpText:
                isStartDate ? 'Choose a start date' : 'Choose an end date',
          );
        });

        if (pickedDate != null) {
          if (!isStartDate) {
            // For "To" date, use 23:59:59 as the default time
            pickedDateTime = DateTime(
              pickedDate.year,
              pickedDate.month,
              pickedDate.day,
              23,
              59,
              59,
            );
          } else {
            // For "From" date, continue using current time
            final now = DateTime.now();
            pickedDateTime = DateTime(
              pickedDate.year,
              pickedDate.month,
              pickedDate.day,
              now.hour,
              now.minute,
              now.second,
            );
          }
        }
      }

      // Update the date if a new one was picked
      if (pickedDateTime != null && mounted) {
        onDateChanged(pickedDateTime);
      }
    } catch (e) {
      // Handle any errors that might occur during date picking
      if (mounted) {
        // Use a safer approach to show error
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            _showErrorSnackBar('Error selecting date: $e');
          }
        });
      }
    }
  }

  /// Returns a combined message with period and calculation method
  String _getCombinedPeriodText(CostController controller) {
    // Get the time frame text
    String timeFrameText;
    if (controller.selectedPeriod == CostPeriod.custom) {
      // Use singular form "day" when the period is exactly 1 day
      timeFrameText = controller.dateRange.days == 1
          ? '1 day'
          : '${controller.dateRange.days} days';

      // For custom periods, use "for" prefix
      String prefix = 'for';

      // Get the calculation method message for custom periods
      String calculationMethod = CostMessageProvider.getAverageUsageMessage(
        selectedPeriod: controller.selectedPeriod,
        fromDate: controller.fromDate,
        toDate: controller.toDate,
        earliestDate: controller.earliestDate,
        latestDate: controller.latestDate,
      );

      // Extract just the calculation basis part
      String calculationBasis;
      if (calculationMethod.contains('total average')) {
        calculationBasis = 'your total average use.';
      } else if (calculationMethod.contains('records and averages')) {
        calculationBasis = 'your records and total averages.';
      } else {
        calculationBasis = 'your records.';
      }

      // Combine the messages for custom periods
      return '$prefix $timeFrameText based on $calculationBasis';
    } else {
      // For standard periods (day, week, month, year), always use total average
      final period = controller.selectedPeriod.name.toLowerCase();
      timeFrameText = 'per $period'; // Use "per" instead of "for a"

      // Standard periods always use total average
      String calculationBasis = 'your total average use.';

      // Combine the messages for standard periods
      return '$timeFrameText based on $calculationBasis';
    }
  }
}
