// File: lib/app_scaffold.dart
import 'package:flutter/material.dart';
import 'core/constants/app_constants.dart';
import 'core/widgets/app_bottom_nav_bar.dart';
import 'features/home/<USER>/screens/home_screen.dart';
import 'features/history/presentation/screens/history_screen.dart';
import 'features/cost/presentation/screens/cost_screen.dart';

/// The main app scaffold with bottom navigation
class AppScaffold extends StatefulWidget {
  final int initialIndex;

  const AppScaffold({
    super.key,
    this.initialIndex = 0,
  });

  @override
  State<AppScaffold> createState() => _AppScaffoldState();
}

class _AppScaffoldState extends State<AppScaffold> {
  late int _currentIndex;

  // ValueNotifier to track the current tab index and notify children
  final ValueNotifier<int> _tabIndexNotifier = ValueNotifier<int>(0);

  // Create screens with proper initialization
  late List<Widget> _screens;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _tabIndexNotifier.value = widget.initialIndex;

    // Initialize screens - Settings screen removed from navigation
    _screens = [
      const HomeScreen(),
      const CostScreen(),
      const HistoryScreen(),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: IndexedStack(
        index: _currentIndex,
        children: _screens,
      ),
      bottomNavigationBar: AppBottomNavBar(
        currentIndex: _currentIndex,
        onTap: _onTabTapped,
      ),
    );
  }

  void _onTabTapped(int index) {
    // Only update if the index has changed
    if (_currentIndex != index) {
      setState(() {
        _currentIndex = index;
        // Update the ValueNotifier to notify children of the tab change
        _tabIndexNotifier.value = index;
      });
    }
  }

  @override
  void dispose() {
    // Clean up the ValueNotifier
    _tabIndexNotifier.dispose();
    super.dispose();
  }
}
