// File: lib/features/notifications/data/sources/sql_notification_data_source.dart

import 'dart:convert';
import 'package:flutter/material.dart';
import '../../../../core/data/database/notification_db_helper.dart';
import '../../../../core/utils/logger.dart';
import '../../domain/models/notification_model.dart';
import '../../domain/models/reminder_time_model.dart';
import '../../domain/models/timezone_aware_reminder_time.dart';
import 'local_storage_data_source.dart';

/// Data source for locally stored notification data using SQLite
class SQLNotificationDataSource implements LocalStorageDataSource {
  final NotificationDBHelper _dbHelper;
  bool _isInitialized = false;

  // Key constants for settings
  static const String _notificationsEnabledKey = 'notifications_enabled';
  static const String _meterReadingReminderEnabledKey =
      'meter_reading_reminder_enabled';
  static const String _meterReadingReminderFrequencyKey =
      'meter_reading_reminder_frequency';
  static const String _meterReadingReminderLastDateKey =
      'meter_reading_reminder_last_date';
  static const String _meterReadingReminderTimeKey =
      'meter_reading_reminder_time';
  static const String _meterReadingReminderTimezoneAwareKey =
      'meter_reading_reminder_timezone_aware';
  static const String _nextMeterReminderDateKey = 'next_meter_reminder_date';
  static const String _nextMeterReminderTimezoneInfoKey =
      'next_meter_reminder_timezone_info';

  // Cache for frequently accessed settings
  final Map<String, dynamic> _settingsCache = {};
  final Map<String, DateTime> _cacheTimestamps = {};

  // Cache TTL in milliseconds (5 minutes)
  static const int _cacheTTL = 5 * 60 * 1000;

  SQLNotificationDataSource({
    required NotificationDBHelper dbHelper,
  }) : _dbHelper = dbHelper;

  /// Initialize the data source
  Future<void> initialize() async {
    if (_isInitialized) return;

    // Ensure database is initialized
    await _dbHelper.database;

    // Initialize cache with default values
    _settingsCache[_notificationsEnabledKey] = true;
    _settingsCache[_meterReadingReminderEnabledKey] = false;
    _settingsCache[_meterReadingReminderFrequencyKey] = 7;

    _isInitialized = true;
    logger.d('SQLNotificationDataSource: Initialized');
  }

  /// Get a cached setting value or retrieve from database
  Future<T?> _getCachedSetting<T>(
      String key, String table, Future<T?> Function() dbFetch) async {
    // Check if the setting is in cache and not expired
    final now = DateTime.now();
    if (_settingsCache.containsKey(key) &&
        _cacheTimestamps.containsKey(key) &&
        now.difference(_cacheTimestamps[key]!).inMilliseconds < _cacheTTL) {
      return _settingsCache[key] as T?;
    }

    // Fetch from database
    final value = await dbFetch();

    // Update cache
    _settingsCache[key] = value;
    _cacheTimestamps[key] = now;

    return value;
  }

  /// Clear cache for a specific key
  void _clearCache(String key) {
    _settingsCache.remove(key);
    _cacheTimestamps.remove(key);
  }

  /// Get all notifications
  Future<List<NotificationModel>> getNotifications() async {
    await initialize();
    try {
      return await _dbHelper.getNotifications();
    } catch (e) {
      logger.e('SQLNotificationDataSource: Error getting notifications',
          details: e.toString());
      return [];
    }
  }

  /// Add a notification
  Future<void> addNotification(NotificationModel notification) async {
    await initialize();
    try {
      await _dbHelper.insertNotification(notification);
    } catch (e) {
      logger.e('SQLNotificationDataSource: Error adding notification',
          details: e.toString());
      rethrow;
    }
  }

  /// Mark a notification as read
  Future<void> markAsRead(String id) async {
    await initialize();
    try {
      await _dbHelper.markNotificationAsRead(id);
    } catch (e) {
      logger.e('SQLNotificationDataSource: Error marking notification as read',
          details: e.toString());
      rethrow;
    }
  }

  /// Mark all notifications as read
  Future<void> markAllAsRead() async {
    await initialize();
    try {
      await _dbHelper.markAllNotificationsAsRead();
    } catch (e) {
      logger.e(
          'SQLNotificationDataSource: Error marking all notifications as read',
          details: e.toString());
      rethrow;
    }
  }

  /// Remove a notification
  Future<void> removeNotification(String id) async {
    await initialize();
    try {
      await _dbHelper.deleteNotification(id);
    } catch (e) {
      logger.e('SQLNotificationDataSource: Error removing notification',
          details: e.toString());
      rethrow;
    }
  }

  /// Clear all notifications
  Future<void> clearAll() async {
    await initialize();
    try {
      await _dbHelper.deleteAllNotifications();
    } catch (e) {
      logger.e('SQLNotificationDataSource: Error clearing all notifications',
          details: e.toString());
      rethrow;
    }
  }

  /// Check if notifications are enabled
  Future<bool> areNotificationsEnabled() async {
    await initialize();
    return await _getCachedSetting<bool>(_notificationsEnabledKey,
            NotificationDBHelper.notificationSettingsTable, () async {
          final value = await _dbHelper.getSetting(_notificationsEnabledKey,
              NotificationDBHelper.notificationSettingsTable);
          return value == null ? true : value == 'true';
        }) ??
        true;
  }

  /// Enable or disable notifications
  Future<void> setNotificationsEnabled(bool enabled) async {
    await initialize();
    try {
      await _dbHelper.saveSetting(
        _notificationsEnabledKey,
        enabled.toString(),
        NotificationDBHelper.notificationSettingsTable,
      );
      _settingsCache[_notificationsEnabledKey] = enabled;
      _cacheTimestamps[_notificationsEnabledKey] = DateTime.now();
    } catch (e) {
      logger.e('SQLNotificationDataSource: Error setting notifications enabled',
          details: e.toString());
      rethrow;
    }
  }

  /// Check if meter reading reminders are enabled
  Future<bool> areMeterReadingRemindersEnabled() async {
    await initialize();
    return await _getCachedSetting<bool>(_meterReadingReminderEnabledKey,
            NotificationDBHelper.reminderSettingsTable, () async {
          final value = await _dbHelper.getSetting(
              _meterReadingReminderEnabledKey,
              NotificationDBHelper.reminderSettingsTable);
          return value == null ? false : value == 'true';
        }) ??
        false;
  }

  /// Enable or disable meter reading reminders
  Future<void> setMeterReadingRemindersEnabled(bool enabled) async {
    await initialize();
    try {
      await _dbHelper.saveSetting(
        _meterReadingReminderEnabledKey,
        enabled.toString(),
        NotificationDBHelper.reminderSettingsTable,
      );
      _settingsCache[_meterReadingReminderEnabledKey] = enabled;
      _cacheTimestamps[_meterReadingReminderEnabledKey] = DateTime.now();
    } catch (e) {
      logger.e(
          'SQLNotificationDataSource: Error setting meter reading reminders enabled',
          details: e.toString());
      rethrow;
    }
  }

  /// Get the meter reading reminder frequency in days
  Future<int> getMeterReadingReminderFrequency() async {
    await initialize();
    return await _getCachedSetting<int>(_meterReadingReminderFrequencyKey,
            NotificationDBHelper.reminderSettingsTable, () async {
          final value = await _dbHelper.getSetting(
              _meterReadingReminderFrequencyKey,
              NotificationDBHelper.reminderSettingsTable);
          return value == null ? 7 : int.tryParse(value) ?? 7;
        }) ??
        7;
  }

  /// Set the meter reading reminder frequency in days
  Future<void> setMeterReadingReminderFrequency(int days) async {
    await initialize();
    try {
      await _dbHelper.saveSetting(
        _meterReadingReminderFrequencyKey,
        days.toString(),
        NotificationDBHelper.reminderSettingsTable,
      );
      _settingsCache[_meterReadingReminderFrequencyKey] = days;
      _cacheTimestamps[_meterReadingReminderFrequencyKey] = DateTime.now();
    } catch (e) {
      logger.e(
          'SQLNotificationDataSource: Error setting meter reading reminder frequency',
          details: e.toString());
      rethrow;
    }
  }

  /// Get the last date a meter reading reminder was shown
  Future<DateTime?> getLastMeterReadingReminderDate() async {
    await initialize();
    try {
      final value = await _dbHelper.getSetting(
        _meterReadingReminderLastDateKey,
        NotificationDBHelper.reminderSettingsTable,
      );
      return value != null ? DateTime.parse(value) : null;
    } catch (e) {
      logger.e(
          'SQLNotificationDataSource: Error getting last meter reading reminder date',
          details: e.toString());
      return null;
    }
  }

  /// Set the last date a meter reading reminder was shown
  Future<void> setLastMeterReadingReminderDate(DateTime date) async {
    await initialize();
    try {
      await _dbHelper.saveSetting(
        _meterReadingReminderLastDateKey,
        date.toIso8601String(),
        NotificationDBHelper.reminderSettingsTable,
      );
    } catch (e) {
      logger.e(
          'SQLNotificationDataSource: Error setting last meter reading reminder date',
          details: e.toString());
      rethrow;
    }
  }

  /// Get the time of day for meter reading reminders
  Future<ReminderTimeModel> getMeterReadingReminderTime() async {
    await initialize();
    try {
      final value = await _dbHelper.getSetting(
        _meterReadingReminderTimeKey,
        NotificationDBHelper.reminderSettingsTable,
      );

      if (value != null) {
        return ReminderTimeModel.fromString(value);
      }

      // Default to 7:00 PM if not set
      return const ReminderTimeModel(
        timeOfDay: TimeOfDay(hour: 19, minute: 0),
      );
    } catch (e) {
      logger.e(
          'SQLNotificationDataSource: Error getting meter reading reminder time',
          details: e.toString());
      // Return default time if there's an error
      return const ReminderTimeModel(
        timeOfDay: TimeOfDay(hour: 19, minute: 0),
      );
    }
  }

  /// Set the time of day for meter reading reminders
  Future<void> setMeterReadingReminderTime(ReminderTimeModel time) async {
    await initialize();
    try {
      await _dbHelper.saveSetting(
        _meterReadingReminderTimeKey,
        time.toTimeString(),
        NotificationDBHelper.reminderSettingsTable,
      );
    } catch (e) {
      logger.e(
          'SQLNotificationDataSource: Error setting meter reading reminder time',
          details: e.toString());
      rethrow;
    }
  }

  /// Get the timezone-aware reminder time for meter readings
  Future<TimezoneAwareReminderTime?> getTimezoneAwareReminderTime() async {
    await initialize();
    try {
      final jsonString = await _dbHelper.getSetting(
        _meterReadingReminderTimezoneAwareKey,
        NotificationDBHelper.reminderSettingsTable,
      );

      if (jsonString != null) {
        try {
          final Map<String, dynamic> json = jsonDecode(jsonString);
          return TimezoneAwareReminderTime.fromJson(json);
        } catch (e) {
          logger.e(
              'SQLNotificationDataSource: Error parsing timezone-aware reminder time',
              details: e.toString());
        }
      }

      // If no timezone-aware reminder time is stored, try to convert from the legacy format
      final legacyTime = await getMeterReadingReminderTime();

      // Default to UTC if this is the first time
      const String defaultTimezone = 'UTC';

      return TimezoneAwareReminderTime.fromReminderTimeModel(
        model: legacyTime,
        currentTimezone: defaultTimezone,
      );
    } catch (e) {
      logger.e(
          'SQLNotificationDataSource: Error getting timezone-aware reminder time',
          details: e.toString());
      return null;
    }
  }

  /// Set the timezone-aware reminder time for meter readings
  Future<void> setTimezoneAwareReminderTime(
      TimezoneAwareReminderTime time) async {
    await initialize();
    try {
      // Also update the legacy format for backward compatibility
      await setMeterReadingReminderTime(time.toReminderTimeModel());

      // Save the timezone-aware format
      final json = time.toJson();
      await _dbHelper.saveSetting(
        _meterReadingReminderTimezoneAwareKey,
        jsonEncode(json),
        NotificationDBHelper.reminderSettingsTable,
      );
    } catch (e) {
      logger.e(
          'SQLNotificationDataSource: Error setting timezone-aware reminder time',
          details: e.toString());
      rethrow;
    }
  }

  /// Save the next scheduled meter reading reminder date
  Future<void> saveNextMeterReadingReminderDate(DateTime date) async {
    await initialize();
    try {
      await _dbHelper.saveSetting(
        _nextMeterReminderDateKey,
        date.millisecondsSinceEpoch.toString(),
        NotificationDBHelper.reminderSettingsTable,
      );
    } catch (e) {
      logger.e(
          'SQLNotificationDataSource: Error saving next meter reading reminder date',
          details: e.toString());
      rethrow;
    }
  }

  /// Save the next scheduled meter reading reminder date with timezone info
  Future<void> saveNextMeterReadingReminderDateWithTimezone(
      DateTime date, String timezone, bool isDST) async {
    await initialize();
    try {
      // Save the basic timestamp for backward compatibility
      await _dbHelper.saveSetting(
        _nextMeterReminderDateKey,
        date.millisecondsSinceEpoch.toString(),
        NotificationDBHelper.reminderSettingsTable,
      );

      // Save additional timezone information
      final timezoneInfo = {
        'timestamp': date.millisecondsSinceEpoch,
        'timezone': timezone,
        'isDST': isDST,
        'utcTimestamp': date.toUtc().millisecondsSinceEpoch,
      };

      await _dbHelper.saveSetting(
        _nextMeterReminderTimezoneInfoKey,
        jsonEncode(timezoneInfo),
        NotificationDBHelper.reminderSettingsTable,
      );

      // Also save this in the reminder history table for tracking
      await _dbHelper.saveReminderHistory(
        scheduledDate: date,
        status: 'scheduled',
        timezone: timezone,
        timezoneOffset: date.timeZoneOffset.inMinutes,
        isDST: isDST,
      );
    } catch (e) {
      logger.e(
          'SQLNotificationDataSource: Error saving next meter reading reminder date with timezone',
          details: e.toString());
      rethrow;
    }
  }

  /// Get the next scheduled meter reading reminder date
  Future<DateTime?> getNextMeterReadingReminderDate() async {
    await initialize();
    try {
      final value = await _dbHelper.getSetting(
        _nextMeterReminderDateKey,
        NotificationDBHelper.reminderSettingsTable,
      );

      if (value != null) {
        final timestamp = int.tryParse(value);
        if (timestamp != null) {
          return DateTime.fromMillisecondsSinceEpoch(timestamp);
        }
      }

      return null;
    } catch (e) {
      logger.e(
          'SQLNotificationDataSource: Error getting next meter reading reminder date',
          details: e.toString());
      return null;
    }
  }

  /// Get the timezone information for the next scheduled reminder
  Future<Map<String, dynamic>?>
      getNextMeterReadingReminderTimezoneInfo() async {
    await initialize();
    try {
      final jsonString = await _dbHelper.getSetting(
        _nextMeterReminderTimezoneInfoKey,
        NotificationDBHelper.reminderSettingsTable,
      );

      if (jsonString != null) {
        return jsonDecode(jsonString);
      }

      return null;
    } catch (e) {
      logger.e(
          'SQLNotificationDataSource: Error getting next reminder timezone info',
          details: e.toString());
      return null;
    }
  }

  /// Get reminder history
  Future<List<Map<String, dynamic>>> getReminderHistory() async {
    await initialize();
    try {
      return await _dbHelper.getReminderHistory();
    } catch (e) {
      logger.e('SQLNotificationDataSource: Error getting reminder history',
          details: e.toString());
      return [];
    }
  }

  /// Record a reminder as delivered
  Future<void> recordReminderDelivered(int id) async {
    await initialize();
    try {
      await _dbHelper.updateReminderHistory(
        id: id,
        actualDate: DateTime.now(),
        status: 'delivered',
      );
    } catch (e) {
      logger.e(
          'SQLNotificationDataSource: Error recording reminder as delivered',
          details: e.toString());
      rethrow;
    }
  }

  /// Clean up old reminder history (older than 3 months)
  Future<int> cleanupOldReminderHistory() async {
    await initialize();
    try {
      return await _dbHelper.deleteOldReminderHistory();
    } catch (e) {
      logger.e(
          'SQLNotificationDataSource: Error cleaning up old reminder history',
          details: e.toString());
      return 0;
    }
  }

  /// Perform a batch operation
  Future<void> performBatchOperation(Function(dynamic batch) operations) async {
    await initialize();
    try {
      await _dbHelper.executeBatch(operations);
    } catch (e) {
      logger.e('SQLNotificationDataSource: Error performing batch operation',
          details: e.toString());
      rethrow;
    }
  }
}
