// File: test/features/notifications/domain/usecases/migrate_notification_storage_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:lekky/features/notifications/domain/repositories/notification_repository.dart';
import 'package:lekky/features/notifications/domain/usecases/migrate_notification_storage.dart';

// Manual mock implementation
class MockNotificationRepository implements NotificationRepository {
  bool migrateToSQLiteResult = true;
  bool isMigratedToSQLiteResult = false;
  String storageTypeResult = "sqlite";
  int migrateToSQLiteCallCount = 0;
  int isMigratedToSQLiteCallCount = 0;
  int getStorageTypeCallCount = 0;
  double? lastReportedProgress;

  @override
  Future<bool> migrateToSQLite({Function(double progress)? onProgress}) async {
    migrateToSQLiteCallCount++;

    // Simulate progress if callback provided
    if (onProgress != null) {
      onProgress(0.5);
      lastReportedProgress = 0.5;
    }

    return migrateToSQLiteResult;
  }

  @override
  Future<bool> isMigratedToSQLite() async {
    isMigratedToSQLiteCallCount++;
    return isMigratedToSQLiteResult;
  }

  @override
  Future<String> getStorageType() async {
    getStorageTypeCallCount++;
    return storageTypeResult;
  }

  // Stub implementation of other required methods
  @override
  noSuchMethod(Invocation invocation) => super.noSuchMethod(invocation);
}

void main() {
  late MigrateNotificationStorage useCase;
  late MockNotificationRepository mockRepository;

  setUp(() {
    mockRepository = MockNotificationRepository();
    useCase = MigrateNotificationStorage(repository: mockRepository);
  });

  group('MigrateNotificationStorage', () {
    test('should execute migration', () async {
      // Arrange
      mockRepository.migrateToSQLiteResult = true;
      mockRepository.migrateToSQLiteCallCount = 0;

      // Act
      final result = await useCase.execute();

      // Assert
      expect(result, true);
      expect(mockRepository.migrateToSQLiteCallCount, 1);
    });

    test('should handle migration failure', () async {
      // Arrange
      mockRepository.migrateToSQLiteResult = false;
      mockRepository.migrateToSQLiteCallCount = 0;

      // Act
      final result = await useCase.execute();

      // Assert
      expect(result, false);
      expect(mockRepository.migrateToSQLiteCallCount, 1);
    });

    test('should report migration progress', () async {
      // Arrange
      mockRepository.migrateToSQLiteResult = true;
      mockRepository.migrateToSQLiteCallCount = 0;
      double? reportedProgress;

      // Act
      final result = await useCase.execute(
        onProgress: (progress) {
          reportedProgress = progress;
        },
      );

      // Assert
      expect(result, true);
      expect(mockRepository.migrateToSQLiteCallCount, 1);
      expect(reportedProgress, 0.5);
      expect(mockRepository.lastReportedProgress, 0.5);
    });

    test('should check if migration is completed', () async {
      // Arrange
      mockRepository.isMigratedToSQLiteResult = true;
      mockRepository.isMigratedToSQLiteCallCount = 0;

      // Act
      final result = await useCase.isMigrationCompleted();

      // Assert
      expect(result, true);
      expect(mockRepository.isMigratedToSQLiteCallCount, 1);
    });

    test('should get current storage type', () async {
      // Arrange
      mockRepository.storageTypeResult = "sqlite";
      mockRepository.getStorageTypeCallCount = 0;

      // Act
      final result = await useCase.getCurrentStorageType();

      // Assert
      expect(result, "sqlite");
      expect(mockRepository.getStorageTypeCallCount, 1);
    });
  });
}
