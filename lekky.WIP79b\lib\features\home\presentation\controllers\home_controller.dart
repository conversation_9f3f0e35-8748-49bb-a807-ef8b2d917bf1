// File: lib/features/home/<USER>/controllers/home_controller.dart
import 'dart:async';
import 'package:flutter/material.dart';
import '../../../../core/data/database/db_optimizer.dart';
import '../../../../core/models/date_to_top_up_result.dart';
import '../../../../core/models/meter_entry.dart';
import '../../../../core/providers/notification_provider.dart';
import '../../../../core/utils/date_time_utils.dart';
import '../../../../core/utils/event_bus.dart';
import '../../../../core/utils/logger.dart';
import '../../data/home_repository.dart';

/// Controller for the home screen
class HomeController extends ChangeNotifier {
  final HomeRepository _repository;

  // State variables
  double _meterTotal = 0.0;
  double _averageUsage = 0.0;
  double _shortTermAverageUsage = 0.0;
  DateTime? _dateToTopUp;
  DateToTopUpResult? _dateToTopUpResult;
  ConfidenceLevel _topUpDateConfidenceLevel = ConfidenceLevel.medium;
  String _meterUnit = '£';
  MeterEntry? _mostRecentMeterEntry;
  MeterEntry? _mostRecentTopUpEntry;
  double _topUpsSinceLastReading = 0.0;
  int _meterReadingCount = 0; // Track the number of meter readings
  double _daysSinceLastReading =
      0.0; // Days since last meter reading with minute precision
  double _projectedBalance = 0.0; // Projected current balance
  bool _isLoading = true;
  String _error = '';

  // Event subscription
  StreamSubscription<EventType>? _eventSubscription;

  // Getters
  double get meterTotal => _meterTotal;
  double get averageUsage => _averageUsage;
  double get shortTermAverageUsage => _shortTermAverageUsage;
  DateTime? get dateToTopUp => _dateToTopUp;
  DateToTopUpResult? get dateToTopUpResult => _dateToTopUpResult;
  ConfidenceLevel get topUpDateConfidenceLevel => _topUpDateConfidenceLevel;
  String get meterUnit => _meterUnit;
  MeterEntry? get mostRecentMeterEntry => _mostRecentMeterEntry;
  MeterEntry? get mostRecentTopUpEntry => _mostRecentTopUpEntry;
  double get topUpsSinceLastReading => _topUpsSinceLastReading;
  double get daysSinceLastReading => _daysSinceLastReading;
  double get projectedBalance => _projectedBalance;
  bool get isLoading => _isLoading;
  String get error => _error;

  /// Check if there are any invalid entries
  bool get hasInvalidEntries {
    // Access the validation results from the repository
    return _repository.hasInvalidEntries();
  }

  /// Get the count of invalid entries
  int get invalidEntryCount {
    // Get the count from the repository
    return _repository.getInvalidEntryCount();
  }

  // Formatted getters
  String get formattedMeterTotal {
    // If no meter readings yet, but we have top-ups
    if (_mostRecentMeterEntry == null && _mostRecentTopUpEntry != null) {
      // Use the most recent top-up entry
      double totalTopUps = _mostRecentTopUpEntry!.amountToppedUp;
      final formattedTopUps = totalTopUps.toStringAsFixed(2);
      return '$_meterUnit 0.00 + $_meterUnit $formattedTopUps';
    }

    // If no meter readings and no top-ups
    if (_mostRecentMeterEntry == null) {
      return '$_meterUnit ${_meterTotal.toStringAsFixed(2)}';
    }

    // Format as "£XX.XX + £YY.YY"
    final lastReading = _mostRecentMeterEntry!.reading;
    if (_topUpsSinceLastReading > 0) {
      final formattedReading = lastReading.toStringAsFixed(2);
      final formattedTopUps = _topUpsSinceLastReading.toStringAsFixed(2);
      return '$_meterUnit $formattedReading + $_meterUnit $formattedTopUps';
    } else {
      return '$_meterUnit ${lastReading.toStringAsFixed(2)}';
    }
  }

  String get formattedAverageUsage {
    // Show "No averages yet :(" when there's only one meter reading or no readings
    if (_averageUsage <= 0 || _meterReadingCount <= 1) {
      return 'No averages yet :(';
    }
    return '$_meterUnit${_averageUsage.toStringAsFixed(2)}/day';
  }

  String get formattedShortTermAverageUsage {
    // Show "No averages yet :(" when there's only one meter reading or no readings
    if (_shortTermAverageUsage <= 0 || _meterReadingCount <= 1) {
      return 'No averages yet :(';
    }
    return '$_meterUnit${_shortTermAverageUsage.toStringAsFixed(2)}/day';
  }

  String get formattedDateToTopUp {
    if (_dateToTopUpResult != null) {
      final date = _dateToTopUpResult!.date;
      if (date != null) {
        final formattedDate = DateTimeUtils.formatDateRelative(date);

        // Add confidence level indicator
        String confidenceIndicator = '';
        switch (_dateToTopUpResult!.confidenceLevel) {
          case ConfidenceLevel.high:
            confidenceIndicator =
                ''; // High confidence doesn't need an indicator
            break;
          case ConfidenceLevel.medium:
            confidenceIndicator = ' (est.)';
            break;
          case ConfidenceLevel.low:
            confidenceIndicator = ' (approx.)';
            break;
        }

        // Add "Now" for dates that are already below threshold
        if (_dateToTopUpResult!.isAlreadyBelowThreshold) {
          return 'Now' + confidenceIndicator;
        }

        return formattedDate + confidenceIndicator;
      }
    }

    // Fallback to legacy formatting
    return _dateToTopUp != null
        ? DateTimeUtils.formatDateRelative(_dateToTopUp!)
        : 'N/A';
  }

  String get formattedLastReadingDate => _mostRecentMeterEntry != null
      ? DateTimeUtils.formatDateRelative(_mostRecentMeterEntry!.timestamp)
      : 'N/A';
  String get formattedLastTopUpDate => _mostRecentTopUpEntry != null
      ? DateTimeUtils.formatDateRelative(_mostRecentTopUpEntry!.timestamp)
      : 'N/A';
  String get formattedLastTopUpAmount => _mostRecentTopUpEntry != null
      ? '$_meterUnit${_mostRecentTopUpEntry!.amountToppedUp.toStringAsFixed(2)}'
      : 'N/A';

  String get formattedProjectedBalance =>
      '$_meterUnit${_projectedBalance.toStringAsFixed(2)}';

  String get formattedDaysSinceLastReading {
    // Round to 1 decimal place for display
    final roundedDays = (_daysSinceLastReading * 10).round() / 10;

    if (roundedDays == 1.0) {
      return '1 day';
    } else if (roundedDays == roundedDays.floor().toDouble()) {
      // If it's a whole number, don't show decimal
      return '${roundedDays.toInt()} days';
    } else {
      // Show one decimal place
      return '${roundedDays.toStringAsFixed(1)} days';
    }
  }

  HomeController(this._repository) {
    // Subscribe to data update and settings change events
    _eventSubscription = EventBus().stream.listen((event) {
      if (event == EventType.dataUpdated) {
        logger.i('HomeController: Received data update event, refreshing data');
        // Force cache invalidation before refresh to ensure fresh data
        DBOptimizer.invalidateCache();
        refresh();
      } else if (event == EventType.settingsUpdated) {
        logger.i(
            'HomeController: Received settings update event, refreshing data');
        // Force cache invalidation before refresh to ensure fresh data
        DBOptimizer.invalidateCache();
        refresh();
      } else if (event == EventType.dateSettingsUpdated) {
        logger.i(
            'HomeController: Received date settings update event, refreshing data');
        // Force cache invalidation before refresh to ensure fresh data
        DBOptimizer.invalidateCache();
        refresh();
      } else if (event == EventType.alertSettingsUpdated) {
        logger.i(
            'HomeController: Received alert settings update event, refreshing data');
        // Force cache invalidation before refresh to ensure fresh data
        DBOptimizer.invalidateCache();
        refresh();
      }
    });

    // Log initialization
    logger.i('HomeController: Initialized and listening for events');
  }

  /// Initialize the controller
  Future<void> init() async {
    logger.i('HomeController: Initializing');
    _isLoading = true;
    _error = '';
    notifyListeners();

    try {
      await _loadData();
      _isLoading = false;
      logger.i('HomeController: Initialized successfully');
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = 'Failed to load data: $e';
      logger.e('HomeController: Failed to initialize', details: e.toString());
      notifyListeners();
    }
  }

  @override
  void dispose() {
    // Cancel event subscription
    _eventSubscription?.cancel();
    super.dispose();
  }

  /// Refresh the data
  Future<void> refresh() async {
    logger.i('HomeController: Refreshing data');
    _isLoading = true;
    _error = '';
    notifyListeners();

    try {
      // Force cache invalidation to ensure fresh data
      DBOptimizer.invalidateCache();

      await _loadData();
      _isLoading = false;
      logger.i('HomeController: Data refreshed successfully');
      logger.d('HomeController: Average usage after refresh: $_averageUsage');
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = 'Failed to refresh data: $e';
      logger.e('HomeController: Failed to refresh data', details: e.toString());
      notifyListeners();
    }
  }

  /// Load all data
  Future<void> _loadData() async {
    logger.d('HomeController: Loading data');
    try {
      // Load data in parallel
      final results = await Future.wait([
        _repository.getMeterTotal(),
        _repository.getAverageUsage(),
        _repository.getShortTermAverageUsage(),
        _repository.getMeterUnit(),
        _repository.getMostRecentMeterEntry(),
        _repository.getMostRecentTopUpEntry(),
        _repository
            .getAllEntries(), // Get all entries to calculate top-ups since last reading
        _repository.getMeterReadingCount(), // Get the count of meter readings
        _repository
            .getAlertThreshold(), // Get alert threshold for notifications
        _repository.getDaysInAdvance(), // Get days in advance for notifications
        _repository.getDaysSinceLastReading(), // Get days since last reading
        _repository.getProjectedBalance(), // Get projected balance
      ]);

      _meterTotal = results[0] as double;
      _averageUsage = results[1] as double;
      _shortTermAverageUsage = results[2] as double;
      _meterUnit = results[3] as String;
      _mostRecentMeterEntry = results[4] as MeterEntry?;
      _mostRecentTopUpEntry = results[5] as MeterEntry?;
      final allEntries = results[6] as List<MeterEntry>;
      _meterReadingCount = results[7] as int; // Set the meter reading count
      final alertThreshold = results[8] as double; // Get alert threshold
      final daysInAdvance = results[9] as int; // Get days in advance
      _daysSinceLastReading = results[10]
          as double; // Set days since last reading with minute precision
      _projectedBalance = results[11] as double; // Set projected balance

      logger.d(
          'HomeController: Data loaded - Meter Total: $_meterTotal, Average Usage: $_averageUsage, Short-Term Average: $_shortTermAverageUsage, Meter Reading Count: $_meterReadingCount');

      logger.d(
          'HomeController: Days since last reading: $_daysSinceLastReading, Projected balance: $_projectedBalance');

      // Log detailed information about average usage
      logger.i(
          'HomeController: Average Usage Value: $_averageUsage, Formatted: $formattedAverageUsage');

      // Calculate top-ups since last reading
      _calculateTopUpsSinceLastReading(allEntries);

      // Calculate date to top up using enhanced method that accounts for reading age
      if (_averageUsage > 0) {
        // Use enhanced calculation that automatically handles projected balance
        _dateToTopUpResult =
            await _repository.getEnhancedDateToTopUp(_averageUsage);

        // Set legacy _dateToTopUp for backward compatibility
        _dateToTopUp = _dateToTopUpResult?.date;

        // Set confidence level
        _topUpDateConfidenceLevel =
            _dateToTopUpResult?.confidenceLevel ?? ConfidenceLevel.medium;

        logger.d(
            'HomeController: Enhanced date to top up calculated: ${_dateToTopUp?.toIso8601String() ?? 'N/A'}, ' +
                'Confidence: $_topUpDateConfidenceLevel, ' +
                'Already below threshold: ${_dateToTopUpResult?.isAlreadyBelowThreshold ?? false}');
      } else {
        // No average usage available
        _dateToTopUpResult = null;
        _dateToTopUp = null;
        logger.d(
            'HomeController: No average usage available, date to top up not calculated');
      }

      // Check if we need to show notifications
      await _checkAndShowNotifications(alertThreshold, daysInAdvance);
    } catch (e) {
      logger.e('HomeController: Error loading data', details: e.toString());
      rethrow; // Rethrow to be caught by the caller
    }
  }

  /// Check if we need to show notifications based on current meter values
  Future<void> _checkAndShowNotifications(
      double alertThreshold, int daysInAdvance) async {
    try {
      final notificationProvider = NotificationProvider();

      // First, check if the user has any meter readings
      final hasMeterReadings = _meterReadingCount > 0;

      // Update the notification service with the meter reading status
      await notificationProvider.setHasMeterReadings(hasMeterReadings);

      // Only show notifications if the user has at least one meter reading
      if (hasMeterReadings) {
        logger.i(
            'HomeController: User has meter readings, checking for notifications');

        // Check for low balance notification based on actual meter total
        if (_meterTotal <= alertThreshold) {
          logger
              .i('HomeController: Low balance detected, showing notification');
          await notificationProvider.showLowBalanceNotification(
            meterUnit: _meterUnit,
            balance: _meterTotal,
            threshold: alertThreshold,
          );
        }
        // Check for projected balance notification
        else if (_daysSinceLastReading >= 3.0 &&
            _projectedBalance <= alertThreshold &&
            _averageUsage > 0) {
          // Only show if it's been at least 3 days since last reading, projected balance is below threshold,
          // and we have a valid average usage
          logger.i(
              'HomeController: Projected balance below threshold, showing check meter now notification');
          await notificationProvider.showCheckMeterNowNotification(
            meterUnit: _meterUnit,
            projectedBalance: _projectedBalance,
            actualBalance: _meterTotal,
            threshold: alertThreshold,
            daysSinceLastReading: _daysSinceLastReading,
            averageUsage: _averageUsage,
          );
        }

        // Check for time to top up notification using enhanced result
        if (_dateToTopUpResult != null && _dateToTopUpResult!.date != null) {
          final now = DateTime.now();

          // Reuse the existing notification provider

          // If already below threshold, show immediate notification
          if (_dateToTopUpResult!.isAlreadyBelowThreshold) {
            logger.i(
                'HomeController: Balance already below threshold, showing immediate notification');

            await notificationProvider.showTimeToTopUpNotification(
              meterUnit: _meterUnit,
              balance: _projectedBalance,
              daysRemaining: 0,
              confidenceLevel: _topUpDateConfidenceLevel,
              daysSinceLastReading: _daysSinceLastReading,
            );
          }
          // Otherwise check if within the days in advance window
          else {
            final daysUntilTopUp =
                _dateToTopUpResult!.date!.difference(now).inDays;

            // Adjust days in advance based on confidence level
            int adjustedDaysInAdvance = daysInAdvance;
            if (_topUpDateConfidenceLevel == ConfidenceLevel.low &&
                _daysSinceLastReading > 7.0) {
              // For low confidence predictions based on old readings, be more conservative
              adjustedDaysInAdvance = (daysInAdvance * 1.5).round();
              logger.d(
                  'HomeController: Adjusted days in advance to $adjustedDaysInAdvance due to low confidence');
            }

            if (daysUntilTopUp <= adjustedDaysInAdvance) {
              logger.i(
                  'HomeController: Time to top up detected, showing notification with confidence level: $_topUpDateConfidenceLevel');

              // Use projected balance for notification
              await notificationProvider.showTimeToTopUpNotification(
                meterUnit: _meterUnit,
                balance: _projectedBalance,
                daysRemaining: daysUntilTopUp,
                confidenceLevel: _topUpDateConfidenceLevel,
                daysSinceLastReading: _daysSinceLastReading,
              );
            }
          }
        }
      } else {
        logger.i(
            'HomeController: No meter readings yet, suppressing low balance and top-up notifications');

        // Reuse the existing notification provider

        // If this is a first-time user with no meter readings, schedule a reminder
        // to enter their first meter reading (if not already scheduled)
        await notificationProvider.scheduleFirstMeterReadingReminder();

        // Also ensure regular meter reading reminders are scheduled if enabled
        // This provides an additional opportunity to reschedule reminders
        await notificationProvider.rescheduleMeterReadingReminders();
      }
    } catch (e) {
      logger.e('HomeController: Error showing notifications',
          details: e.toString());
      // Don't rethrow - we don't want to fail the whole data load if notifications fail
    }
  }

  /// Calculate the total amount topped up since the last meter reading
  void _calculateTopUpsSinceLastReading(List<MeterEntry> entries) {
    _topUpsSinceLastReading = 0.0;

    if (_mostRecentMeterEntry == null || entries.isEmpty) {
      return;
    }

    // Get the date of the most recent meter reading
    final lastReadingDate = _mostRecentMeterEntry!.timestamp;

    // Find all top-ups that occurred after the last meter reading
    final topUpsAfterLastReading = entries
        .where((entry) =>
            entry.amountToppedUp > 0 &&
            entry.timestamp.isAfter(lastReadingDate))
        .toList();

    // Sum up all top-ups
    for (final topUp in topUpsAfterLastReading) {
      _topUpsSinceLastReading += topUp.amountToppedUp;
    }

    logger.d(
        'HomeController: Top-ups since last reading: $_topUpsSinceLastReading');
  }

  /// Calculate the cost of electric for a specific time period
  Future<double> calculateCost(DateTime startDate, DateTime endDate) async {
    logger.i(
        'HomeController: Calculating cost from ${startDate.toIso8601String()} to ${endDate.toIso8601String()}');
    try {
      final cost = await _repository.calculateCost(startDate, endDate);
      logger.i('HomeController: Cost calculated: $cost');
      return cost;
    } catch (e) {
      logger.e('HomeController: Error calculating cost', details: e.toString());
      return 0.0;
    }
  }
}
