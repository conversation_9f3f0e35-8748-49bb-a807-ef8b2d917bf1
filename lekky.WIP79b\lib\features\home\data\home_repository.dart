// File: lib/features/home/<USER>/home_repository.dart
import '../../../core/data/repositories/meter_entry_repository.dart';
import '../../../core/data/repositories/settings_repository.dart';
import '../../../core/models/date_to_top_up_result.dart';
import '../../../core/models/meter_entry.dart';
import '../../../core/utils/average_calculator.dart';
import '../domain/usecases/calculate_average_usage.dart';
import '../domain/usecases/calculate_meter_total.dart';
import '../domain/usecases/calculate_top_up_date.dart';

/// Repository for the home screen
class HomeRepository {
  final MeterEntryRepository _meterEntryRepository;
  final SettingsRepository _settingsRepository;
  final CalculateMeterTotal _calculateMeterTotal;
  final CalculateAverageUsage _calculateAverageUsage;
  final CalculateTopUpDate _calculateTopUpDate;

  HomeRepository({
    required MeterEntryRepository meterEntryRepository,
    required SettingsRepository settingsRepository,
  })  : _meterEntryRepository = meterEntryRepository,
        _settingsRepository = settingsRepository,
        _calculateMeterTotal = CalculateMeterTotal(meterEntryRepository),
        _calculateAverageUsage = CalculateAverageUsage(meterEntryRepository),
        _calculateTopUpDate =
            CalculateTopUpDate(settingsRepository, meterEntryRepository);

  /// Get the current meter total
  Future<double> getMeterTotal() async {
    return await _calculateMeterTotal.execute();
  }

  /// Get the average usage
  Future<double> getAverageUsage() async {
    return await _calculateAverageUsage.execute();
  }

  /// Get the short-term average usage
  Future<double> getShortTermAverageUsage() async {
    return await _calculateAverageUsage.calculateShortTermAverage();
  }

  /// Get the estimated date to top up (legacy method)
  Future<DateTime?> getDateToTopUp(
      double meterTotal, double averageUsage) async {
    return await _calculateTopUpDate.execute(meterTotal, averageUsage);
  }

  /// Get the enhanced estimated date to top up with confidence level
  Future<DateToTopUpResult> getEnhancedDateToTopUp(double averageUsage) async {
    return await _calculateTopUpDate.executeEnhanced(averageUsage);
  }

  /// Get the meter unit
  Future<String> getMeterUnit() async {
    return await _settingsRepository.getMeterUnit();
  }

  /// Get the most recent meter entry
  Future<MeterEntry?> getMostRecentMeterEntry() async {
    final entries = await _meterEntryRepository.getAllEntriesWithCache();
    if (entries.isEmpty) {
      return null;
    }

    // Sort entries by timestamp (newest first)
    entries.sort((a, b) => b.timestamp.compareTo(a.timestamp));

    // Find the most recent meter reading (not top-up)
    for (final entry in entries) {
      if (entry.amountToppedUp == 0) {
        return entry;
      }
    }

    return null;
  }

  /// Get the most recent top-up entry
  Future<MeterEntry?> getMostRecentTopUpEntry() async {
    final entries = await _meterEntryRepository.getAllEntriesWithCache();
    if (entries.isEmpty) {
      return null;
    }

    // Sort entries by timestamp (newest first)
    entries.sort((a, b) => b.timestamp.compareTo(a.timestamp));

    // Find the most recent top-up
    for (final entry in entries) {
      if (entry.amountToppedUp > 0) {
        return entry;
      }
    }

    return null;
  }

  /// Calculate the cost of electric for a specific time period
  Future<double> calculateCost(DateTime startDate, DateTime endDate) async {
    return await _calculateAverageUsage.calculateCost(startDate, endDate);
  }

  /// Get all meter entries
  Future<List<MeterEntry>> getAllEntries() async {
    return await _meterEntryRepository.getAllEntriesWithCache();
  }

  /// Get the count of meter readings (not top-ups)
  Future<int> getMeterReadingCount() async {
    final entries = await _meterEntryRepository.getAllEntriesWithCache();
    return entries.where((entry) => entry.amountToppedUp == 0).length;
  }

  /// Get the alert threshold for low balance notifications
  Future<double> getAlertThreshold() async {
    return await _settingsRepository.getAlertThreshold();
  }

  /// Get the days in advance for time to top up notifications
  Future<int> getDaysInAdvance() async {
    return await _settingsRepository.getDaysInAdvance();
  }

  /// Check if there are any invalid entries
  bool hasInvalidEntries() {
    // Access the meter entry repository to check for invalid entries
    return _meterEntryRepository.hasInvalidEntries();
  }

  /// Get the count of invalid entries
  int getInvalidEntryCount() {
    // Get the count from the meter entry repository
    return _meterEntryRepository.getInvalidEntryCount();
  }

  /// Get the days since the last meter reading with minute precision
  Future<double> getDaysSinceLastReading() async {
    final mostRecentMeterEntry = await getMostRecentMeterEntry();
    if (mostRecentMeterEntry == null) {
      return 0.0; // No readings yet
    }

    return AverageCalculator.calculateDaysSinceLastReading(
        mostRecentMeterEntry.timestamp);
  }

  /// Calculate the projected current balance based on days since last reading and average usage
  Future<double> getProjectedBalance() async {
    final mostRecentMeterEntry = await getMostRecentMeterEntry();
    if (mostRecentMeterEntry == null) {
      return 0.0; // No readings yet
    }

    final averageUsage = await getAverageUsage();
    final entries = await getAllEntries();

    // Calculate top-ups since last reading
    double topUpsSinceLastReading = 0.0;
    final lastReadingDate = mostRecentMeterEntry.timestamp;

    for (final entry in entries) {
      if (entry.amountToppedUp > 0 &&
          entry.timestamp.isAfter(lastReadingDate)) {
        topUpsSinceLastReading += entry.amountToppedUp;
      }
    }

    return AverageCalculator.calculateProjectedBalance(
      lastMeterReading: mostRecentMeterEntry.amountLeft,
      topUpsSinceLastReading: topUpsSinceLastReading,
      lastReadingDate: lastReadingDate,
      averageUsage: averageUsage,
    );
  }
}
