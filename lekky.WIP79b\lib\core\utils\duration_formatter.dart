// File: lib/core/utils/duration_formatter.dart

/// Utility class for formatting durations in a human-readable way
class DurationFormatter {
  /// Format a duration in minutes to a human-readable string with days, hours, and minutes
  ///
  /// Example: "3 days, 5 hours, 30 minutes"
  /// Omits zero values (e.g., "2 days, 30 minutes" instead of "2 days, 0 hours, 30 minutes")
  /// <PERSON><PERSON> pluralization correctly
  static String formatDetailedDuration(int totalMinutes) {
    if (totalMinutes <= 0) {
      return '0 minutes';
    }

    final days = totalMinutes ~/ (24 * 60);
    final hours = (totalMinutes % (24 * 60)) ~/ 60;
    final minutes = totalMinutes % 60;

    // For UI space constraints, we'll use a very compact format
    // Show only the most significant unit to avoid overflow

    if (days > 0) {
      return '$days ${days == 1 ? 'day' : 'days'}';
    } else if (hours > 0) {
      return '$hours ${hours == 1 ? 'hour' : 'hours'}';
    } else {
      return '$minutes ${minutes == 1 ? 'minute' : 'minutes'}';
    }
  }

  /// Format a duration in minutes to a human-readable string with days, hours, and minutes
  /// Shows all non-zero units (days, hours, minutes)
  /// Example: "3 day(s), 5 hour(s), 30 minute(s)"
  /// Omits zero values and handles pluralization
  static String formatHumanReadableDuration(int totalMinutes) {
    if (totalMinutes <= 0) {
      return '0 minute(s)';
    }

    final days = totalMinutes ~/ (24 * 60);
    final hours = (totalMinutes % (24 * 60)) ~/ 60;
    final minutes = totalMinutes % 60;

    final List<String> parts = [];

    if (days > 0) {
      parts.add('$days day(s)');
    }

    if (hours > 0) {
      parts.add('$hours hour(s)');
    }

    if (minutes > 0) {
      parts.add('$minutes minute(s)');
    }

    return parts.join(', ');
  }

  /// Format a duration in minutes to a compact string
  ///
  /// Example: "3d 5h 30m"
  /// Omits zero values
  static String formatCompactDuration(int totalMinutes) {
    if (totalMinutes <= 0) {
      return '0m';
    }

    final days = totalMinutes ~/ (24 * 60);
    final hours = (totalMinutes % (24 * 60)) ~/ 60;
    final minutes = totalMinutes % 60;

    final List<String> parts = [];

    if (days > 0) {
      parts.add('${days}d');
    }

    if (hours > 0) {
      parts.add('${hours}h');
    }

    if (minutes > 0) {
      parts.add('${minutes}m');
    }

    return parts.join(' ');
  }

  /// Format a duration between two DateTime objects to a human-readable string
  ///
  /// Uses minute precision for accurate calculations
  static String formatDateTimeDuration(DateTime startDate, DateTime endDate) {
    final minutes = endDate.difference(startDate).inMinutes;
    return formatDetailedDuration(minutes);
  }
}
