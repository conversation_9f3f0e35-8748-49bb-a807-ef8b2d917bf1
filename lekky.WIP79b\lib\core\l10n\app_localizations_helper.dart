// File: lib/core/l10n/app_localizations_helper.dart
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

/// Helper class to access the generated localization files
class AppLocalizations {
  final Locale locale;

  AppLocalizations(this.locale);

  /// Get the current instance from the BuildContext
  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }
  
  /// Create a delegate for the AppLocalizations
  static const LocalizationsDelegate<AppLocalizations> delegate = _AppLocalizationsDelegate();
  
  // Home tab
  String get home => 'Home';
  
  // Cost tab
  String get cost => 'Cost';
  
  // History tab
  String get history => 'History';
  
  // Settings tab
  String get settings => 'Settings';
  
  // Language
  String get language => 'Language';
  
  // Current
  String get current => 'Current';
  
  // Region
  String get region => 'Region';
  
  // Currency
  String get currency => 'Currency';
  
  // Date Format
  String get dateFormat => 'Date Format';
  
  // Date Info
  String get dateInfo => 'Date Information';
  
  // Alert Threshold
  String get alertThreshold => 'Alert Threshold';
  
  // Days in Advance
  String get daysInAdvance => 'Days in Advance';
  
  // Notifications
  String get notifications => 'Notifications';
  
  // Enable Notifications
  String get enableNotifications => 'Enable Notifications';
  
  // Theme
  String get theme => 'Theme';
  
  // System Default
  String get systemDefault => 'System Default';
  
  // Light Mode
  String get lightMode => 'Light Mode';
  
  // Dark Mode
  String get darkMode => 'Dark Mode';
  
  // Select your preferred language
  String get selectLanguage => 'Select your preferred language';
}

/// A custom LocalizationsDelegate for AppLocalizations
class _AppLocalizationsDelegate extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  bool isSupported(Locale locale) => true;

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(AppLocalizations(locale));
  }

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}
