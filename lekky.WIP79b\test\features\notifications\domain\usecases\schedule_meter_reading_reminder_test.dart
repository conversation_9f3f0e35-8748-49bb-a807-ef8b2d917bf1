// File: test/features/notifications/domain/usecases/schedule_meter_reading_reminder_test.dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:lekky/core/platform/permissions/permission_adapter.dart';
import 'package:lekky/features/notifications/domain/models/reminder_time_model.dart';
import 'package:lekky/features/notifications/domain/repositories/notification_repository.dart';
import 'package:lekky/features/notifications/domain/usecases/schedule_meter_reading_reminder.dart';

// Mock implementation of NotificationRepository
class MockNotificationRepository implements NotificationRepository {
  bool _remindersEnabled = true;
  bool _notificationsEnabled = true;
  int _reminderFrequency = 30;
  ReminderTimeModel _reminderTime = ReminderTimeModel(
    timeOfDay: TimeOfDay(hour: 9, minute: 0),
  );
  DateTime? _lastReminderDate;
  DateTime? _nextReminderDate;
  PermissionStatus _permissionStatus = PermissionStatus.granted;
  bool _schedulingResult = true;
  bool _reschedulingResult = true;

  int scheduleMeterReadingReminderCallCount = 0;
  int rescheduleMeterReadingRemindersCallCount = 0;
  int getPermissionStatusCallCount = 0;
  int areMeterReadingRemindersEnabledCallCount = 0;
  int areNotificationsEnabledCallCount = 0;

  void setRemindersEnabled(bool enabled) {
    _remindersEnabled = enabled;
  }

  void setPermissionStatus(PermissionStatus status) {
    _permissionStatus = status;
  }

  void setSchedulingResult(bool result) {
    _schedulingResult = result;
  }

  void setReschedulingResult(bool result) {
    _reschedulingResult = result;
  }

  @override
  Future<bool> areMeterReadingRemindersEnabled() async {
    areMeterReadingRemindersEnabledCallCount++;
    return _remindersEnabled;
  }

  @override
  Future<bool> areNotificationsEnabled() async {
    areNotificationsEnabledCallCount++;
    return _notificationsEnabled;
  }

  @override
  Future<PermissionStatus> getPermissionStatus() async {
    getPermissionStatusCallCount++;
    return _permissionStatus;
  }

  @override
  Future<int> getMeterReadingReminderFrequency() async {
    return _reminderFrequency;
  }

  @override
  Future<ReminderTimeModel> getMeterReadingReminderTime() async {
    return _reminderTime;
  }

  @override
  Future<DateTime?> getLastMeterReadingReminderDate() async {
    return _lastReminderDate;
  }

  @override
  Future<DateTime?> getNextMeterReadingReminderDate() async {
    return _nextReminderDate;
  }

  @override
  Future<bool> scheduleMeterReadingReminder() async {
    scheduleMeterReadingReminderCallCount++;
    return _schedulingResult;
  }

  @override
  Future<bool> rescheduleMeterReadingReminders(
      {bool forceReschedule = false}) async {
    rescheduleMeterReadingRemindersCallCount++;
    return _reschedulingResult;
  }

  @override
  Future<void> setNotificationsEnabled(bool enabled) async {
    _notificationsEnabled = enabled;
  }

  // Stub implementations of other required methods
  @override
  noSuchMethod(Invocation invocation) => super.noSuchMethod(invocation);
}

void main() {
  late ScheduleMeterReadingReminder useCase;
  late MockNotificationRepository mockRepository;

  setUp(() {
    mockRepository = MockNotificationRepository();
    useCase = ScheduleMeterReadingReminder(mockRepository);
  });

  group('ScheduleMeterReadingReminder', () {
    test('should schedule meter reading reminder successfully', () async {
      // Arrange
      mockRepository.setSchedulingResult(true);

      // Act
      final result = await useCase.execute();

      // Assert
      expect(result, true);
      expect(mockRepository.scheduleMeterReadingReminderCallCount, 1);
    });

    test('should handle scheduling failure', () async {
      // Arrange
      mockRepository.setSchedulingResult(false);

      // Act
      final result = await useCase.execute();

      // Assert
      expect(result, false);
      expect(mockRepository.scheduleMeterReadingReminderCallCount, 1);
    });

    test('should reschedule meter reading reminder successfully', () async {
      // Arrange
      mockRepository.setReschedulingResult(true);

      // Act
      final result = await useCase.executeWithReschedule();

      // Assert
      expect(result, true);
      expect(mockRepository.rescheduleMeterReadingRemindersCallCount, 1);
    });

    test('should handle rescheduling failure', () async {
      // Arrange
      mockRepository.setReschedulingResult(false);

      // Act
      final result = await useCase.executeWithReschedule();

      // Assert
      expect(result, false);
      expect(mockRepository.rescheduleMeterReadingRemindersCallCount, 1);
    });

    test('should reschedule with force parameter', () async {
      // Arrange
      mockRepository.setReschedulingResult(true);

      // Act
      final result = await useCase.executeWithReschedule(forceReschedule: true);

      // Assert
      expect(result, true);
      expect(mockRepository.rescheduleMeterReadingRemindersCallCount, 1);
    });

    test('should handle exceptions during scheduling', () async {
      // Arrange - create a throwing repository
      final mockWithException = MockThrowingRepository();
      final useCaseWithException =
          ScheduleMeterReadingReminder(mockWithException);

      // Act & Assert
      expect(() => useCaseWithException.execute(), throwsException);
    });
  });
}

// Special mock that throws exceptions for testing error handling
class MockThrowingRepository extends MockNotificationRepository {
  @override
  Future<bool> scheduleMeterReadingReminder() async {
    throw Exception('Test exception');
  }

  @override
  Future<bool> rescheduleMeterReadingReminders(
      {bool forceReschedule = false}) async {
    throw Exception('Test exception');
  }
}
