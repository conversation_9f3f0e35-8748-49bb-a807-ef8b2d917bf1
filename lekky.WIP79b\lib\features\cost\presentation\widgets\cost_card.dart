// File: lib/features/cost/presentation/widgets/cost_card.dart
import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/widgets/app_card.dart';
import '../../../../core/widgets/gradient_button.dart';
import '../../../../core/widgets/dialogs/date_picker_dialog.dart'
    as custom_date_picker;
import '../../domain/models/cost_period.dart';
import '../controllers/cost_controller.dart';

/// A card that displays the cost of electricity
class CostCard extends StatefulWidget {
  final CostController controller;

  const CostCard({
    Key? key,
    required this.controller,
  }) : super(key: key);
  @override
  State<CostCard> createState() => _CostCardState();
}

class _CostCardState extends State<CostCard> {
  CostController get controller => widget.controller;
  final FocusNode _screenFocusNode = FocusNode();
  final FocusNode _fromDateFocusNode = FocusNode();
  final FocusNode _toDateFocusNode = FocusNode();

  DateTime? _fromDate;
  DateTime? _toDate;
  bool _isDateRangeActive = false;

  @override
  void initState() {
    super.initState();
    // Set default dates without notifying the controller
    _toDate = widget.controller.toDate ?? DateTime.now();
    _fromDate = widget.controller.fromDate ?? widget.controller.earliestDate;

    // Don't request focus on any element when the screen opens
    // This ensures no buttons are in focus initially

    // Schedule a post-frame callback to update the controller
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Now it's safe to update the controller
      if (_toDate != null) {
        widget.controller.toDate = _toDate;
      }
      if (_fromDate != null) {
        widget.controller.fromDate = _fromDate;
      }
    });
  }

  @override
  void dispose() {
    _screenFocusNode.dispose();
    _fromDateFocusNode.dispose();
    _toDateFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Focus(
      focusNode: _screenFocusNode,
      child: AppCard(
        padding: const EdgeInsets.all(16),
        elevation: 2,
        borderRadius: BorderRadius.circular(16),
        backgroundColor: Colors.white,
        hasShadow: true,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min, // Minimize vertical size
          children: [
            // Fixed height container for cost display to prevent layout shifts
            SizedBox(
              height: 120, // Fixed height for cost display
              child: _buildCostDisplay(),
            ),
            const Divider(height: 20, thickness: 1),
            _buildPeriodSelector(),
            // Removed average usage section
          ],
        ),
      ),
    );
  }

  Widget _buildCostDisplay() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final labelColor =
        isDarkMode ? AppColors.primaryTextDark : AppColors.primary;
    final valueColor = isDarkMode ? AppColors.valueTextDark : AppColors.primary;

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            widget.controller.hasInitialCredit
                ? widget.controller.formattedNetCost
                : widget.controller.formattedCostPerPeriod,
            style: AppTextStyles.valueText.copyWith(
              color: widget.controller.netCost < 0
                  ? AppColors.tertiary
                  : valueColor,
              fontWeight: FontWeight.bold,
              fontSize: 32, // Same size as meter total
            ),
          ),
          Text(
            widget.controller.selectedPeriod == CostPeriod.custom
                ? widget.controller.dateRange.formattedDuration
                : 'per ${widget.controller.periodName.toLowerCase()}',
            style: AppTextStyles.labelLarge.copyWith(
              color: labelColor, // Same color as Last Reading
              fontWeight: FontWeight.w500,
            ),
          ),
          // Only show "based on previous average use" for standard periods
          if (widget.controller.selectedPeriod != CostPeriod.custom)
            Padding(
              padding: const EdgeInsets.only(top: 4),
              child: Text(
                'based on your average use',
                style: AppTextStyles.bodyLarge.copyWith(
                  fontWeight: FontWeight.w500,
                  color: valueColor, // Same color as Last Reading value
                ),
              ),
            ),
          // Show top-up information if available
          if (widget.controller.hasTopUp)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.add_circle,
                    color: AppColors.tertiary,
                    size: 16,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'Includes ${widget.controller.formattedTopUpAmount} top-up',
                    style: AppTextStyles.bodyMedium.copyWith(
                      fontWeight: FontWeight.w500,
                      color: AppColors.tertiary,
                    ),
                  ),
                ],
              ),
            ),

          // Show initial credit information if available
          if (widget.controller.hasInitialCredit)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.account_balance_wallet,
                    color: AppColors.tertiary,
                    size: 16,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'Initial credit: ${widget.controller.formattedInitialCredit}',
                    style: AppTextStyles.bodyMedium.copyWith(
                      fontWeight: FontWeight.w500,
                      color: AppColors.tertiary,
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildPeriodSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Projected cost section
        Container(
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
          decoration: BoxDecoration(
            color: AppColors.surfaceVariant.withOpacity(0.2),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: AppColors.primary.withOpacity(0.2),
              width: 1,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Heading for projected cost - centered
              Center(
                child: Text(
                  'Projected Cost',
                  style: AppTextStyles.titleMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
              ),
              const SizedBox(height: 12),
              // Period buttons in a horizontal row with equal sizing
              Row(
                children: CostPeriod.all.map((period) {
                  final isSelected = widget.controller.selectedPeriod == period;
                  return Expanded(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 4.0),
                      child: SizedBox(
                        height: 44,
                        child: isSelected
                            ? GradientButton(
                                text: period.name,
                                onPressed: () {
                                  setState(() {
                                    _isDateRangeActive = false;
                                  });
                                  widget.controller.updatePeriod(period);
                                },
                                gradientColors: AppColors.primaryGradient,
                                elevation: 2,
                                borderRadius: BorderRadius.circular(10),
                                padding: EdgeInsets.zero,
                                textStyle: const TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 14,
                                ),
                              )
                            : OutlinedButton(
                                onPressed: () {
                                  setState(() {
                                    _isDateRangeActive = false;
                                  });
                                  widget.controller.updatePeriod(period);
                                },
                                style: OutlinedButton.styleFrom(
                                  foregroundColor: AppColors.onSurface,
                                  side: BorderSide(
                                    color: AppColors.outline.withOpacity(0.3),
                                    width: 1,
                                  ),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  padding: EdgeInsets.zero,
                                ),
                                child: Text(
                                  period.name,
                                  style: const TextStyle(
                                    fontWeight: FontWeight.normal,
                                    fontSize: 14,
                                  ),
                                ),
                              ),
                      ),
                    ),
                  );
                }).toList(),
              ),
            ],
          ),
        ),
        const SizedBox(height: 20),
        // Historic cost section
        Container(
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
          decoration: BoxDecoration(
            color: AppColors.surfaceVariant.withOpacity(0.2),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: AppColors.tertiary.withOpacity(0.2),
              width: 1,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Heading for historic cost - centered
              Center(
                child: Text(
                  'Historic Cost',
                  style: AppTextStyles.titleMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.tertiary,
                  ),
                ),
              ),
              const SizedBox(height: 8),
              // Check if there are enough readings for custom date range
              if (widget.controller.hasEnoughReadingsForCustomRange) ...[
                // Date range selectors side by side
                Row(
                  children: [
                    // From date
                    Expanded(
                      child: Card(
                        elevation: 0,
                        margin: EdgeInsets.zero,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10),
                          side: BorderSide(
                            color: _isDateRangeActive
                                ? AppColors.primary.withOpacity(0.7)
                                : AppColors.tertiary.withOpacity(0.3),
                            width: _isDateRangeActive ? 2 : 1,
                          ),
                        ),
                        child: _buildDateInput(
                          context,
                          'From:',
                          _fromDate,
                          widget.controller.earliestDate,
                          widget.controller.latestDate,
                          _fromDateFocusNode,
                          (date) {
                            setState(() {
                              _fromDate = date;
                              _isDateRangeActive = true;
                            });
                            // This will automatically set the period to custom
                            widget.controller.fromDate = date;
                          },
                          isDisabledMessage: 'No meter history available',
                          isActive: _isDateRangeActive,
                        ),
                      ),
                    ),
                    const SizedBox(width: 14),
                    // To date
                    Expanded(
                      child: Card(
                        elevation: 0,
                        margin: EdgeInsets.zero,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10),
                          side: BorderSide(
                            color: _isDateRangeActive
                                ? AppColors.primary.withOpacity(0.7)
                                : AppColors.tertiary.withOpacity(0.3),
                            width: _isDateRangeActive ? 2 : 1,
                          ),
                        ),
                        child: _buildDateInput(
                          context,
                          'To:',
                          _toDate,
                          widget.controller.earliestDate,
                          widget.controller.latestDate,
                          _toDateFocusNode,
                          (date) {
                            setState(() {
                              _toDate = date;
                              _isDateRangeActive = true;
                            });
                            // This will automatically set the period to custom
                            widget.controller.toDate = date;
                          },
                          isDisabledMessage: 'No meter history available',
                          isActive: _isDateRangeActive,
                        ),
                      ),
                    ),
                  ],
                ),
                if (widget.controller.dateRangeError != null)
                  Padding(
                    padding: const EdgeInsets.only(top: 12, left: 4, right: 4),
                    child: Card(
                      elevation: 0,
                      color: AppColors.error.withOpacity(0.1),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                        side: BorderSide(
                          color: AppColors.error.withOpacity(0.3),
                          width: 1,
                        ),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 8),
                        child: Row(
                          children: [
                            const Stack(
                              alignment: Alignment.center,
                              children: [
                                Icon(
                                  Icons.calendar_today,
                                  color: AppColors.error,
                                  size: 18,
                                ),
                                Icon(
                                  Icons.close,
                                  color: AppColors.error,
                                  size: 14,
                                ),
                              ],
                            ),
                            const SizedBox(width: 10),
                            Expanded(
                              child: Text(
                                widget.controller.dateRangeError!,
                                style: AppTextStyles.bodyMedium.copyWith(
                                  color: AppColors.error,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
              ] else ...[
                // Message when not enough readings
                Padding(
                  padding:
                      const EdgeInsets.symmetric(vertical: 16, horizontal: 8),
                  child: Center(
                    child: Column(
                      children: [
                        const Icon(
                          Icons.info_outline,
                          color: AppColors.tertiary,
                          size: 24,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Custom date range requires at least 2 meter readings',
                          textAlign: TextAlign.center,
                          style: AppTextStyles.bodyMedium.copyWith(
                            color: AppColors.tertiary,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDateInput(
      BuildContext context,
      String label,
      DateTime? selectedDate,
      DateTime? firstDate,
      DateTime? lastDate,
      FocusNode focusNode,
      Function(DateTime?) onDateSelected,
      {String isDisabledMessage = 'Disabled',
      bool isActive = false}) {
    // Determine if the date should be disabled
    final bool isDisabled = firstDate == null || lastDate == null;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 2, vertical: 4),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Label with icon
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.calendar_today,
                size: 14,
                color: isDisabled
                    ? AppColors.outline.withOpacity(0.5)
                    : AppColors.tertiary,
              ),
              const SizedBox(width: 4),
              Text(
                label,
                style: AppTextStyles.labelMedium.copyWith(
                  color: isDisabled ? AppColors.outline : AppColors.tertiary,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          // Date selection button
          SizedBox(
            height: 60,
            child: Material(
              color: Colors.transparent,
              child: Tooltip(
                message: isDisabled ? isDisabledMessage : 'Select a date',
                child: InkWell(
                  focusNode: focusNode,
                  borderRadius: BorderRadius.circular(8),
                  onTap: isDisabled
                      ? null
                      : () async {
                          // Calculate valid date range
                          DateTime initialDate;
                          if (selectedDate != null) {
                            // Make sure initialDate is within valid range
                            if (selectedDate.isAfter(lastDate)) {
                              initialDate = lastDate;
                            } else if (selectedDate.isBefore(firstDate)) {
                              initialDate = firstDate;
                            } else {
                              initialDate = selectedDate;
                            }
                          } else if (label == 'To:') {
                            DateTime now = DateTime.now();
                            // lastDate is not null here because isDisabled would be true
                            if (now.isAfter(lastDate)) {
                              initialDate = lastDate;
                            } else if (now.isBefore(firstDate)) {
                              initialDate = firstDate;
                            } else {
                              initialDate = now;
                            }
                          } else {
                            // We know firstDate is not null because isDisabled would be true
                            initialDate = firstDate;
                          }

                          // Check the Date Info setting
                          final dateInfo =
                              await widget.controller.getDateInfo();
                          final includeTime = dateInfo == 'Date and time';

                          // Check if the widget is still mounted before proceeding
                          if (!mounted) return;

                          DateTime? picked;

                          try {
                            // Store the current context in a local variable
                            final currentContext = context;

                            // Use date time picker with mounted check
                            if (!mounted) return;

                            // Use a synchronous function to avoid BuildContext issues
                            picked = await Future<DateTime?>.sync(() async {
                              if (!mounted) return null;
                              return await custom_date_picker.DatePickerDialog
                                  .showDateTimePicker(
                                context: currentContext,
                                title: 'Select Date and Time',
                                initialDate: initialDate,
                                firstDate:
                                    firstDate, // Not null due to isDisabled check
                                lastDate:
                                    lastDate, // Not null due to isDisabled check
                                helpText:
                                    'Select the date${includeTime ? ' and time' : ''}',
                                includeTime:
                                    includeTime, // This will show time picker only if includeTime is true
                              );
                            });
                          } catch (e) {
                            // Handle any errors that might occur during date picking
                            if (mounted) {
                              // Store the current context in a local variable
                              final currentContext = context;

                              // Use a synchronous function to avoid BuildContext issues
                              Future.microtask(() {
                                if (mounted) {
                                  ScaffoldMessenger.of(currentContext)
                                      .showSnackBar(
                                    SnackBar(
                                      content: Text('Error selecting date: $e'),
                                      backgroundColor: Colors.red,
                                    ),
                                  );
                                }
                              });
                            }
                            return;
                          }

                          // Final mounted check before updating state
                          if (!mounted) return;
                          if (picked != null && picked != selectedDate) {
                            onDateSelected(picked);
                          }
                        },
                  child: Container(
                    width: double.infinity,
                    height: double.infinity,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      color: isDisabled
                          ? AppColors.surfaceVariant.withOpacity(0.3)
                          : Colors.transparent,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          selectedDate != null
                              ? '${selectedDate.day}/${selectedDate.month}/${selectedDate.year.toString().substring(2)}'
                              : label == 'To:'
                                  ? 'Today'
                                  : 'Select',
                          style: AppTextStyles.bodyMedium.copyWith(
                            color: isDisabled
                                ? AppColors.outline
                                : isActive
                                    ? AppColors.primary
                                    : AppColors.onSurface,
                            fontWeight:
                                isActive ? FontWeight.bold : FontWeight.w500,
                            fontSize: 15,
                          ),
                        ),
                        if (!isDisabled && selectedDate != null)
                          SizedBox(
                            height: 16,
                            child: Icon(
                              Icons.arrow_drop_down,
                              size: 14,
                              color: AppColors.onSurface.withOpacity(0.7),
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Removed _buildAverageUsage method as it's no longer needed
}
