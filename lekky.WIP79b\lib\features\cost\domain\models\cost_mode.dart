// File: lib/features/cost/domain/models/cost_mode.dart

/// Enum representing the cost calculation mode (past or future)
enum CostMode {
  /// Past mode - calculate cost for past periods
  past,

  /// Future mode - project cost for future periods
  future,
}

/// Extension methods for CostMode
extension CostModeExtension on CostMode {
  /// Get the display name for the mode
  String get displayName {
    switch (this) {
      case CostMode.past:
        return 'Past';
      case CostMode.future:
        return 'Future';
    }
  }

  /// Check if this is past mode
  bool get isPast => this == CostMode.past;

  /// Check if this is future mode
  bool get isFuture => this == CostMode.future;
}
