// File: lib/core/widgets/app_card.dart
import 'package:flutter/material.dart';
import '../theme/app_colors.dart';
import '../utils/responsive_layout.dart';

/// A reusable card widget with consistent styling
class AppCard extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final double? elevation;
  final Color? backgroundColor;
  final BorderRadius? borderRadius;
  final bool hasShadow;
  final VoidCallback? onTap;
  final bool isGradient;
  final List<Color>? gradientColors;
  final Alignment? gradientBegin;
  final Alignment? gradientEnd;

  const AppCard({
    Key? key,
    required this.child,
    this.padding,
    this.margin,
    this.elevation,
    this.backgroundColor,
    this.borderRadius,
    this.hasShadow = true,
    this.onTap,
    this.isGradient = false,
    this.gradientColors,
    this.gradientBegin,
    this.gradientEnd,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final responsiveElevation = elevation ??
        ResponsiveLayout.getCardElevationForScreenType(context);
    final responsiveBorderRadius = borderRadius ??
        BorderRadius.circular(
            ResponsiveLayout.getBorderRadiusForScreenType(context));
    final responsivePadding = padding ??
        ResponsiveLayout.getPaddingForScreenType(context);
    final responsiveMargin = margin ??
        ResponsiveLayout.getMarginForScreenType(context);

    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final defaultBackgroundColor = isDarkMode
        ? AppColors.cardBackgroundDark
        : AppColors.cardBackground;

    if (onTap != null) {
      return InkWell(
        onTap: onTap,
        borderRadius: responsiveBorderRadius,
        child: _buildCard(
          context,
          responsiveElevation,
          responsiveBorderRadius,
          responsivePadding,
          responsiveMargin,
          defaultBackgroundColor,
        ),
      );
    }

    return _buildCard(
      context,
      responsiveElevation,
      responsiveBorderRadius,
      responsivePadding,
      responsiveMargin,
      defaultBackgroundColor,
    );
  }

  Widget _buildCard(
    BuildContext context,
    double elevation,
    BorderRadius borderRadius,
    EdgeInsetsGeometry padding,
    EdgeInsetsGeometry margin,
    Color defaultBackgroundColor,
  ) {
    return Card(
      elevation: hasShadow ? elevation : 0,
      shape: RoundedRectangleBorder(borderRadius: borderRadius),
      margin: margin,
      color: isGradient ? Colors.transparent : (backgroundColor ?? defaultBackgroundColor),
      clipBehavior: Clip.antiAlias,
      child: Container(
        decoration: isGradient
            ? BoxDecoration(
                gradient: LinearGradient(
                  begin: gradientBegin ?? Alignment.topLeft,
                  end: gradientEnd ?? Alignment.bottomRight,
                  colors: gradientColors ??
                      [
                        AppColors.primary.withOpacity(0.7),
                        AppColors.primary,
                      ],
                ),
                borderRadius: borderRadius,
              )
            : null,
        padding: padding,
        child: child,
      ),
    );
  }
}
