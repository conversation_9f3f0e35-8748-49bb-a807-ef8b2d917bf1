// File: lib/core/platform/permissions/permission_adapter.dart

/// Enum representing permission status
enum PermissionStatus {
  granted,
  denied,
  permanentlyDenied,
  restricted, // iOS only
  limited, // iOS only
  unknown,
}

/// Callback for permission request results
typedef PermissionCallback = void Function(bool isGranted);

/// Interface for platform-specific permission handling
abstract class PermissionAdapter {
  /// Initialize the permission adapter
  Future<void> initialize();

  /// Check if notification permissions are granted
  Future<PermissionStatus> checkPermission();

  /// Request notification permission with the appropriate strategy
  Future<bool> requestPermission({PermissionCallback? callback});

  /// Check if permission is permanently denied
  Future<bool> isPermanentlyDenied();

  /// Open app settings
  Future<bool> openAppSettings();

  /// Check when permission was last verified
  Future<DateTime?> getLastPermissionCheckTime();

  /// Save the time when permission was checked
  Future<void> savePermissionCheckTime();

  /// Get permission status as a string
  String getPermissionStatusString(PermissionStatus status);

  /// Show educational UI before requesting permission
  Future<bool> showPermissionEducationUI();

  /// Show alternatives UI when permission is denied
  Future<void> showPermissionDeniedAlternativesUI();

  /// Register for permission status changes
  void registerPermissionStatusListener(
      void Function(PermissionStatus) listener);

  /// Unregister permission status listener
  void unregisterPermissionStatusListener(
      void Function(PermissionStatus) listener);

  /// Check if the app should show permission rationale
  Future<bool> shouldShowRequestPermissionRationale();

  /// Get the current permission status from the system without saving
  Future<PermissionStatus> getSystemPermissionStatus();
}

/// Strategy interface for version-specific permission handling
abstract class PermissionStrategy {
  /// Check permission using strategy-specific logic
  Future<PermissionStatus> checkPermission();

  /// Request permission using strategy-specific logic
  Future<bool> requestPermission();

  /// Handle permanently denied permission
  Future<void> handlePermanentlyDenied();

  /// Get strategy-specific settings page
  Future<bool> openSettings();
}
