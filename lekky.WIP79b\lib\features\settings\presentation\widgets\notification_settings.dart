// File: lib/features/settings/presentation/widgets/notification_settings.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../core/providers/notification_provider.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/widgets/app_card.dart';
import '../../../../core/shared_modules/settings_model.dart';

/// A widget for configuring notification settings
class NotificationSettings extends StatefulWidget {
  final bool defaultOn;

  const NotificationSettings({
    Key? key,
    this.defaultOn = false,
  }) : super(key: key);

  @override
  State<NotificationSettings> createState() => _NotificationSettingsState();
}

class _NotificationSettingsState extends State<NotificationSettings> {
  bool _notificationsEnabled = true;
  bool _lowBalanceAlertsEnabled = true;
  bool _topUpAlertsEnabled = true;
  bool _invalidRecordAlertsEnabled = true;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadSettings();

    // If defaultOn is true, we'll set all notifications to enabled
    // after loading the settings
    if (widget.defaultOn) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _enableAllNotifications();
      });
    }
  }

  // Helper method to enable all notifications
  Future<void> _enableAllNotifications() async {
    final notificationProvider =
        Provider.of<NotificationProvider>(context, listen: false);

    // Pass context to handle permission requests
    await notificationProvider.setNotificationsEnabled(true, context);
    await notificationProvider.setLowBalanceAlertsEnabled(true);
    await notificationProvider.setTopUpAlertsEnabled(true);
    await notificationProvider.setInvalidRecordAlertsEnabled(true);

    setState(() {
      _notificationsEnabled = true;
      _lowBalanceAlertsEnabled = true;
      _topUpAlertsEnabled = true;
      _invalidRecordAlertsEnabled = true;
    });
  }

  Future<void> _loadSettings() async {
    final notificationProvider =
        Provider.of<NotificationProvider>(context, listen: false);

    setState(() {
      _isLoading = true;
    });

    final notificationsEnabled =
        await notificationProvider.areNotificationsEnabled();
    final lowBalanceAlertsEnabled =
        await notificationProvider.areLowBalanceAlertsEnabled();
    final topUpAlertsEnabled =
        await notificationProvider.areTopUpAlertsEnabled();
    final invalidRecordAlertsEnabled =
        await notificationProvider.areInvalidRecordAlertsEnabled();

    setState(() {
      _notificationsEnabled = notificationsEnabled;
      _lowBalanceAlertsEnabled = lowBalanceAlertsEnabled;
      _topUpAlertsEnabled = topUpAlertsEnabled;
      _invalidRecordAlertsEnabled = invalidRecordAlertsEnabled;
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    final notificationProvider = Provider.of<NotificationProvider>(context);

    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    if (_isLoading) {
      return Container(
        decoration: BoxDecoration(
          color: isDarkMode ? Colors.grey[850] : Colors.grey[100],
          borderRadius: BorderRadius.circular(8),
        ),
        padding: const EdgeInsets.all(16),
        child: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Main notification toggle
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
          child: Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Enable Notifications',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Text(
                      _notificationsEnabled
                          ? 'You will receive notifications about meter readings and balance'
                          : 'You will not receive any notifications',
                      style: TextStyle(
                        fontSize: 12,
                        color: Theme.of(context).brightness == Brightness.dark
                            ? Colors.grey
                            : Colors.black,
                      ),
                    ),
                  ],
                ),
              ),
              Transform.scale(
                scale: 0.8, // Scale down the switch by 20%
                child: Switch(
                  value: _notificationsEnabled,
                  onChanged: (value) async {
                    // Get the settings controller to handle permission requests
                    await notificationProvider.setNotificationsEnabled(
                        value, context);
                    setState(() {
                      _notificationsEnabled = value;
                    });
                  },
                  activeColor: Theme.of(context).colorScheme.primary,
                  materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                ),
              ),
            ],
          ),
        ),

        // Show notification options when notifications are enabled
        if (_notificationsEnabled) ...[
          const Divider(height: 1, thickness: 0.5),
          Padding(
            padding:
                const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Enable Low Balance Alerts',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Text(
                        'Get notified when your meter balance falls below your alert threshold',
                        style: TextStyle(
                          fontSize: 12,
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Colors.grey
                              : Colors.black,
                        ),
                      ),
                    ],
                  ),
                ),
                Transform.scale(
                  scale: 0.8, // Scale down the switch by 20%
                  child: Switch(
                    value: _lowBalanceAlertsEnabled,
                    onChanged: (value) async {
                      await notificationProvider
                          .setLowBalanceAlertsEnabled(value);
                      setState(() {
                        _lowBalanceAlertsEnabled = value;
                      });
                    },
                    activeColor: Theme.of(context).colorScheme.primary,
                    materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                ),
              ],
            ),
          ),
          const Divider(height: 1, thickness: 0.5),
          Padding(
            padding:
                const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Enable Time to Top Up Alerts',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Text(
                        'Get notified when it\'s time to top up based on your usage patterns',
                        style: TextStyle(
                          fontSize: 12,
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Colors.grey
                              : Colors.black,
                        ),
                      ),
                    ],
                  ),
                ),
                Transform.scale(
                  scale: 0.8, // Scale down the switch by 20%
                  child: Switch(
                    value: _topUpAlertsEnabled,
                    onChanged: (value) async {
                      await notificationProvider.setTopUpAlertsEnabled(value);
                      setState(() {
                        _topUpAlertsEnabled = value;
                      });
                    },
                    activeColor: Theme.of(context).colorScheme.primary,
                    materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                ),
              ],
            ),
          ),
          const Divider(height: 1, thickness: 0.5),
          Padding(
            padding:
                const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Enable Invalid Record Alerts',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Text(
                        'Get notified when an invalid meter reading or top-up is detected',
                        style: TextStyle(
                          fontSize: 12,
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Colors.grey
                              : Colors.black,
                        ),
                      ),
                    ],
                  ),
                ),
                Transform.scale(
                  scale: 0.8, // Scale down the switch by 20%
                  child: Switch(
                    value: _invalidRecordAlertsEnabled,
                    onChanged: (value) async {
                      await notificationProvider
                          .setInvalidRecordAlertsEnabled(value);
                      setState(() {
                        _invalidRecordAlertsEnabled = value;
                      });
                    },
                    activeColor: Theme.of(context).colorScheme.primary,
                    materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                ),
              ],
            ),
          ),
          const Divider(height: 1, thickness: 0.5),
          Padding(
            padding: const EdgeInsets.all(12.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Note: You may need to grant permission for notifications in your device settings',
                  style: TextStyle(
                    fontStyle: FontStyle.italic,
                    fontSize: 12,
                    color: Theme.of(context).brightness == Brightness.dark
                        ? Colors.grey
                        : Colors.black,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'On Android 13+ devices, you\'ll be asked for permission when enabling notifications',
                  style: TextStyle(
                    fontStyle: FontStyle.italic,
                    fontSize: 12,
                    color: Theme.of(context).brightness == Brightness.dark
                        ? Colors.grey
                        : Colors.black,
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }
}
