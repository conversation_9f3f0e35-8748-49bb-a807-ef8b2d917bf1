// File: test/core/utils/error_handling_test.dart

import 'package:flutter_test/flutter_test.dart';
import 'package:lekky/core/utils/error_handling.dart';

void main() {
  group('AppError', () {
    test('should create an error with default values', () {
      // Act
      final error = AppError(message: 'Test error');

      // Assert
      expect(error.message, 'Test error');
      expect(error.type, ErrorType.unknown);
      expect(error.severity, ErrorSeverity.medium);
      expect(error.details, isNull);
      expect(error.originalException, isNull);
      expect(error.stackTrace, isNotNull);
      expect(error.timestamp, isNotNull);
      expect(error.component, isNull);
      expect(error.operationId, isNull);
    });

    test('should create an error with custom values', () {
      // Arrange
      final exception = Exception('Original exception');
      final stackTrace = StackTrace.current;
      final details = {'key': 'value'};

      // Act
      final error = AppError(
        message: 'Test error',
        type: ErrorType.network,
        severity: ErrorSeverity.high,
        details: details,
        originalException: exception,
        stackTrace: stackTrace,
        component: 'test',
        operationId: 'op123',
      );

      // Assert
      expect(error.message, 'Test error');
      expect(error.type, ErrorType.network);
      expect(error.severity, ErrorSeverity.high);
      expect(error.details, details);
      expect(error.originalException, exception);
      expect(error.stackTrace, stackTrace);
      expect(error.component, 'test');
      expect(error.operationId, 'op123');
    });

    test('should create a network error', () {
      // Act
      final error = AppError.network('Network error');

      // Assert
      expect(error.message, 'Network error');
      expect(error.type, ErrorType.network);
      expect(error.severity, ErrorSeverity.medium);
    });

    test('should create a database error', () {
      // Act
      final error = AppError.database('Database error');

      // Assert
      expect(error.message, 'Database error');
      expect(error.type, ErrorType.database);
      expect(error.severity, ErrorSeverity.high);
    });

    test('should create a permission error', () {
      // Act
      final error = AppError.permission('Permission error');

      // Assert
      expect(error.message, 'Permission error');
      expect(error.type, ErrorType.permission);
      expect(error.severity, ErrorSeverity.high);
    });

    test('should create a validation error', () {
      // Act
      final error = AppError.validation('Validation error');

      // Assert
      expect(error.message, 'Validation error');
      expect(error.type, ErrorType.validation);
      expect(error.severity, ErrorSeverity.low);
    });

    test('should create a timeout error', () {
      // Act
      final error = AppError.timeout(
        'Timeout error',
        duration: Duration(seconds: 30),
      );

      // Assert
      expect(error.message, 'Timeout error');
      expect(error.type, ErrorType.timeout);
      expect(error.severity, ErrorSeverity.medium);
      expect(error.details?['timeoutDuration'], 30000);
    });

    test('should create an error from exception', () {
      // Arrange
      final exception = Exception('Test exception');

      // Act
      final error = AppError.fromException(exception);

      // Assert
      expect(error.message, 'Exception: Test exception');
      expect(error.type, ErrorType.unknown);
      expect(error.severity, ErrorSeverity.medium);
      expect(error.originalException, exception);
    });

    test('should determine error type from exception message', () {
      // Act & Assert
      expect(AppError.fromException(Exception('network error')).type,
          ErrorType.network);
      expect(AppError.fromException(Exception('database error')).type,
          ErrorType.database);
      expect(AppError.fromException(Exception('permission denied')).type,
          ErrorType.permission);
      expect(AppError.fromException(Exception('invalid input')).type,
          ErrorType.validation);
      expect(AppError.fromException(Exception('timeout occurred')).type,
          ErrorType.timeout);
      expect(AppError.fromException(Exception('not found')).type,
          ErrorType.notFound);
      expect(AppError.fromException(Exception('server error')).type,
          ErrorType.server);
      expect(AppError.fromException(Exception('unauthorized')).type,
          ErrorType.authentication);
      expect(AppError.fromException(Exception('random error')).type,
          ErrorType.unknown);
    });

    test('should convert to string', () {
      // Act
      final error = AppError(
        message: 'Test error',
        type: ErrorType.network,
        severity: ErrorSeverity.high,
      );

      // Assert
      expect(error.toString(), 'AppError: Test error (network, high)');
    });
  });

  group('Result', () {
    test('should create a success result', () {
      // Act
      final result = Result<int>.success(42);

      // Assert
      expect(result.isSuccess, true);
      expect(result.isFailure, false);
      expect(result.value, 42);
      expect(() => result.error, throwsException);
    });

    test('should create a failure result', () {
      // Arrange
      final error = AppError(message: 'Test error');

      // Act
      final result = Result<int>.failure(error);

      // Assert
      expect(result.isSuccess, false);
      expect(result.isFailure, true);
      expect(() => result.value, throwsA(same(error)));
      expect(result.error, error);
    });

    test('should map success result', () {
      // Arrange
      final result = Result<int>.success(42);

      // Act
      final mapped = result.map((value) => value.toString());

      // Assert
      expect(mapped.isSuccess, true);
      expect(mapped.value, '42');
    });

    test('should propagate error when mapping failure result', () {
      // Arrange
      final error = AppError(message: 'Test error');
      final result = Result<int>.failure(error);

      // Act
      final mapped = result.map((value) => value.toString());

      // Assert
      expect(mapped.isFailure, true);
      expect(mapped.error, error);
    });

    test('should flat map success result', () {
      // Arrange
      final result = Result<int>.success(42);

      // Act
      final flatMapped = result.flatMap(
        (value) => Result<String>.success(value.toString()),
      );

      // Assert
      expect(flatMapped.isSuccess, true);
      expect(flatMapped.value, '42');
    });

    test('should propagate error when flat mapping failure result', () {
      // Arrange
      final error = AppError(message: 'Test error');
      final result = Result<int>.failure(error);

      // Act
      final flatMapped = result.flatMap(
        (value) => Result<String>.success(value.toString()),
      );

      // Assert
      expect(flatMapped.isFailure, true);
      expect(flatMapped.error, error);
    });

    test('should fold success result', () {
      // Arrange
      final result = Result<int>.success(42);

      // Act
      final folded = result.fold(
        (value) => 'Success: $value',
        (error) => 'Error: ${error.message}',
      );

      // Assert
      expect(folded, 'Success: 42');
    });

    test('should fold failure result', () {
      // Arrange
      final error = AppError(message: 'Test error');
      final result = Result<int>.failure(error);

      // Act
      final folded = result.fold(
        (value) => 'Success: $value',
        (error) => 'Error: ${error.message}',
      );

      // Assert
      expect(folded, 'Error: Test error');
    });

    test('should execute onSuccess callback for success result', () {
      // Arrange
      final result = Result<int>.success(42);
      int callbackValue = 0;

      // Act
      result.onSuccess((value) {
        callbackValue = value;
      });

      // Assert
      expect(callbackValue, 42);
    });

    test('should execute onFailure callback for failure result', () {
      // Arrange
      final error = AppError(message: 'Test error');
      final result = Result<int>.failure(error);
      AppError? callbackError;

      // Act
      result.onFailure((err) {
        callbackError = err;
      });

      // Assert
      expect(callbackError, error);
    });

    test('should recover from failure', () {
      // Arrange
      final error = AppError(message: 'Test error');
      final result = Result<int>.failure(error);

      // Act
      final recovered = result.recover((_) => 42);

      // Assert
      expect(recovered.isSuccess, true);
      expect(recovered.value, 42);
    });

    test('should recover with new result from failure', () {
      // Arrange
      final error = AppError(message: 'Test error');
      final result = Result<int>.failure(error);

      // Act
      final recovered = result.recoverWith(
        (_) => Result<int>.success(42),
      );

      // Assert
      expect(recovered.isSuccess, true);
      expect(recovered.value, 42);
    });
  });

  group('Retry', () {
    test('should return success result on first attempt', () async {
      // Arrange
      int attempts = 0;

      // Act
      final result = await retry<int>(() async {
        attempts++;
        return 42;
      });

      // Assert
      expect(result.isSuccess, true);
      expect(result.value, 42);
      expect(attempts, 1);
    });

    test('should retry on failure and eventually succeed', () async {
      // Arrange
      int attempts = 0;

      // Act
      final result = await retry<int>(() async {
        attempts++;
        if (attempts < 3) {
          throw AppError.network('Temporary network error');
        }
        return 42;
      });

      // Assert
      expect(result.isSuccess, true);
      expect(result.value, 42);
      expect(attempts, 3);
    });

    test('should fail after max attempts', () async {
      // Arrange
      int attempts = 0;
      final config = RetryConfig(maxAttempts: 3);

      // Act
      final result = await retry<int>(() async {
        attempts++;
        throw AppError.network('Persistent network error');
      }, config: config);

      // Assert
      expect(result.isFailure, true);
      expect(result.error.type, ErrorType.network);
      expect(attempts, 3);
    });

    test('should not retry on non-retryable errors', () async {
      // Arrange
      int attempts = 0;
      final config = RetryConfig(
        retryableErrors: [ErrorType.network],
      );

      // Act
      final result = await retry<int>(() async {
        attempts++;
        throw AppError.validation('Validation error');
      }, config: config);

      // Assert
      expect(result.isFailure, true);
      expect(result.error.type, ErrorType.validation);
      expect(attempts, 1);
    });
  });

  group('Timeout', () {
    test('should return success result if operation completes in time',
        () async {
      // Act
      final result = await withTimeout<int>(
        () async {
          await Future.delayed(Duration(milliseconds: 50));
          return 42;
        },
        Duration(seconds: 1),
      );

      // Assert
      expect(result.isSuccess, true);
      expect(result.value, 42);
    });

    test('should return timeout error if operation times out', () async {
      // Act
      final result = await withTimeout<int>(
        () async {
          await Future.delayed(Duration(seconds: 2));
          return 42;
        },
        Duration(milliseconds: 100),
        operationName: 'test-operation',
      );

      // Assert
      expect(result.isFailure, true);
      expect(result.error.type, ErrorType.timeout);
      expect(result.error.message, contains('test-operation'));
      expect(result.error.message, contains('100ms'));
    });
  });

  group('ErrorHandler', () {
    setUp(() {
      ErrorHandler.clearErrors();
    });

    test('should add an error', () {
      // Arrange
      final error = AppError(message: 'Test error');

      // Act
      ErrorHandler.addError(error);

      // Assert
      expect(ErrorHandler.errors.length, 1);
      expect(ErrorHandler.errors.first, error);
    });

    test('should clear errors', () {
      // Arrange
      ErrorHandler.addError(AppError(message: 'Test error 1'));
      ErrorHandler.addError(AppError(message: 'Test error 2'));

      // Act
      ErrorHandler.clearErrors();

      // Assert
      expect(ErrorHandler.errors.length, 0);
    });

    test('should get errors by type', () {
      // Arrange
      ErrorHandler.addError(AppError.network('Network error'));
      ErrorHandler.addError(AppError.database('Database error'));
      ErrorHandler.addError(AppError.network('Another network error'));

      // Act
      final networkErrors = ErrorHandler.getErrorsByType(ErrorType.network);

      // Assert
      expect(networkErrors.length, 2);
      expect(networkErrors[0].message, 'Network error');
      expect(networkErrors[1].message, 'Another network error');
    });

    test('should get errors by severity', () {
      // Arrange
      ErrorHandler.addError(
          AppError(message: 'Error 1', severity: ErrorSeverity.low));
      ErrorHandler.addError(
          AppError(message: 'Error 2', severity: ErrorSeverity.medium));
      ErrorHandler.addError(
          AppError(message: 'Error 3', severity: ErrorSeverity.high));
      ErrorHandler.addError(
          AppError(message: 'Error 4', severity: ErrorSeverity.medium));

      // Act
      final mediumErrors =
          ErrorHandler.getErrorsBySeverity(ErrorSeverity.medium);

      // Assert
      expect(mediumErrors.length, 2);
      expect(mediumErrors[0].message, 'Error 2');
      expect(mediumErrors[1].message, 'Error 4');
    });

    test('should notify error handlers', () {
      // Arrange
      AppError? handledError;
      ErrorHandler.registerErrorHandler((error) {
        handledError = error;
      });
      final error = AppError(message: 'Test error');

      // Act
      ErrorHandler.addError(error);

      // Assert
      expect(handledError, error);
    });

    test('should handle an exception', () {
      // Arrange
      final exception = Exception('Test exception');

      // Act
      final error = ErrorHandler.handleException(exception);

      // Assert
      expect(error.message, 'Exception: Test exception');
      expect(ErrorHandler.errors.length, 1);
      expect(ErrorHandler.errors.first, error);
    });
  });
}
