// File: lib/features/home/<USER>/usecases/calculate_top_up_date.dart
import '../../../../core/data/repositories/meter_entry_repository.dart';
import '../../../../core/data/repositories/settings_repository.dart';
import '../../../../core/models/date_to_top_up_result.dart';
import '../../../../core/utils/average_calculator.dart';

/// Use case for calculating the estimated date to top up
class CalculateTopUpDate {
  final SettingsRepository _settingsRepository;
  final MeterEntryRepository _meterEntryRepository;

  CalculateTopUpDate(this._settingsRepository, this._meterEntryRepository);

  /// Legacy method - maintained for backward compatibility
  Future<DateTime?> execute(double meterTotal, double averageUsage) async {
    if (averageUsage <= 0) {
      return null;
    }

    // Get the alert threshold from settings
    final alertThreshold = await _settingsRepository.getAlertThreshold();

    // Calculate the date to top up
    return AverageCalculator.calculateDateToTopUp(
        meterTotal, alertThreshold, averageUsage);
  }

  /// Enhanced method that accounts for reading age and provides confidence level
  Future<DateToTopUpResult> executeEnhanced(double averageUsage) async {
    if (averageUsage <= 0) {
      return DateToTopUpResult(
        date: null,
        confidenceLevel: ConfidenceLevel.low,
        isAlreadyBelowThreshold: false,
      );
    }

    // Get the alert threshold from settings
    final alertThreshold = await _settingsRepository.getAlertThreshold();

    // Get the most recent meter reading
    final mostRecentMeterEntry =
        await _meterEntryRepository.getLatestMeterReading();
    if (mostRecentMeterEntry == null) {
      return DateToTopUpResult(
        date: null,
        confidenceLevel: ConfidenceLevel.low,
        isAlreadyBelowThreshold: false,
      );
    }

    // Calculate top-ups since last reading
    final entries = await _meterEntryRepository.getAllEntriesWithCache();
    double topUpsSinceLastReading = 0.0;
    final lastReadingDate = mostRecentMeterEntry.timestamp;

    // Sum all top-ups that occurred after the last meter reading
    for (final entry in entries) {
      if (entry.amountToppedUp > 0 &&
          entry.timestamp.isAfter(lastReadingDate)) {
        topUpsSinceLastReading += entry.amountToppedUp;
      }
    }

    // Calculate the date to top up using the enhanced method
    return AverageCalculator.calculateDateToTopUpEnhanced(
      lastMeterReading: mostRecentMeterEntry.amountLeft,
      topUpsSinceLastReading: topUpsSinceLastReading,
      lastReadingDate: lastReadingDate,
      averageUsage: averageUsage,
      alertThreshold: alertThreshold,
    );
  }
}
