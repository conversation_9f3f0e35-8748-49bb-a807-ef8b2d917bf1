// File: lib/core/settings/widgets/settings_dialogs.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/settings_validator.dart';
import '../../../core/utils/date_time_utils.dart';
import 'setting_dialog.dart';

/// A utility class for showing settings dialogs
class SettingsDialogs {
  /// Shows a dialog for selecting the meter unit
  static Future<void> showMeterUnitDialog(
    BuildContext context,
    String currentValue,
    Function(String) onChanged,
  ) async {
    // Define currency options
    final List<Map<String, String>> currencies = [
      {'code': 'USD', 'symbol': '\$', 'name': 'United States Dollar'},
      {'code': 'EUR', 'symbol': '€', 'name': 'Euro'},
      {'code': 'JPY', 'symbol': '¥', 'name': 'Japanese Yen'},
      {'code': 'GBP', 'symbol': '£', 'name': 'British Pound'},
      {'code': 'CNY', 'symbol': 'CN¥', 'name': 'Chinese Yuan'},
      {'code': 'AUD', 'symbol': 'A\$', 'name': 'Australian Dollar'},
      {'code': 'CAD', 'symbol': 'C\$', 'name': 'Canadian Dollar'},
      {'code': 'CHF', 'symbol': 'Fr.', 'name': 'Swiss Franc'},
      {'code': 'HKD', 'symbol': 'HK\$', 'name': 'Hong Kong Dollar'},
      {'code': 'NZD', 'symbol': 'NZ\$', 'name': 'New Zealand Dollar'},
    ];

    // Find the current currency or default to GBP
    String selectedSymbol = currentValue;

    // If the current value isn't in our list, add it as a custom option
    bool hasCustomOption = !currencies.any((c) => c['symbol'] == currentValue);

    return showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) {
          final isDarkMode = Theme.of(context).brightness == Brightness.dark;
          final primaryColor = isDarkMode ? Colors.blue[300] : Colors.blue[700];

          return SettingDialog(
            title: 'Currency',
            subtitle: 'Select the currency symbol to use throughout the app',
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Currency grid
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: [
                    ...currencies.map((currency) {
                      return ChoiceChip(
                        label:
                            Text('${currency['symbol']} (${currency['code']})'),
                        selected: selectedSymbol == currency['symbol'],
                        selectedColor: primaryColor?.withOpacity(0.2),
                        labelStyle: TextStyle(
                          color: selectedSymbol == currency['symbol']
                              ? primaryColor
                              : isDarkMode
                                  ? Colors.white
                                  : Colors.black,
                          fontWeight: selectedSymbol == currency['symbol']
                              ? FontWeight.bold
                              : FontWeight.normal,
                        ),
                        onSelected: (selected) {
                          if (selected) {
                            setState(() {
                              selectedSymbol = currency['symbol']!;
                            });
                          }
                        },
                      );
                    }),

                    // Custom option if current value isn't in the list
                    if (hasCustomOption)
                      ChoiceChip(
                        label: Text('$currentValue (Custom)'),
                        selected: selectedSymbol == currentValue,
                        selectedColor: primaryColor?.withOpacity(0.2),
                        labelStyle: TextStyle(
                          color: selectedSymbol == currentValue
                              ? primaryColor
                              : isDarkMode
                                  ? Colors.white
                                  : Colors.black,
                          fontWeight: selectedSymbol == currentValue
                              ? FontWeight.bold
                              : FontWeight.normal,
                        ),
                        onSelected: (selected) {
                          if (selected) {
                            setState(() {
                              selectedSymbol = currentValue;
                            });
                          }
                        },
                      ),
                  ],
                ),

                const SizedBox(height: 12), // Reduced from 16

                // Custom currency input
                TextField(
                  decoration: const InputDecoration(
                    labelText: 'Custom Currency Symbol',
                    hintText: 'e.g. ₹, ₽, ฿',
                    helperText:
                        'Enter a custom symbol if not in the list above',
                  ),
                  onChanged: (value) {
                    if (value.isNotEmpty) {
                      final validation =
                          SettingsValidator.validateMeterUnit(value);
                      if (validation['isValid']) {
                        setState(() {
                          selectedSymbol = value;
                          hasCustomOption =
                              !currencies.any((c) => c['symbol'] == value);
                        });
                      }
                    }
                  },
                ),
              ],
            ),
            onCancel: () {},
            onSave: () {
              final validation =
                  SettingsValidator.validateMeterUnit(selectedSymbol);
              if (validation['isValid']) {
                onChanged(selectedSymbol);
                Navigator.of(context).pop();
              }
            },
          );
        },
      ),
    );
  }

  /// Shows a dialog for setting the alert threshold
  static Future<void> showAlertThresholdDialog(
    BuildContext context,
    double currentValue,
    String meterUnit,
    Function(double) onChanged,
  ) async {
    final TextEditingController textController = TextEditingController(
      text: currentValue.toStringAsFixed(2),
    );
    String? errorText;

    return showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) {
          return SettingDialog(
            title: 'Alert Threshold',
            subtitle:
                'You will be notified when your meter balance falls below this amount',
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                TextField(
                  controller: textController,
                  decoration: InputDecoration(
                    labelText: 'Threshold Value',
                    hintText: 'e.g. 5.00',
                    prefixText: meterUnit,
                    errorText: errorText,
                  ),
                  keyboardType:
                      const TextInputType.numberWithOptions(decimal: true),
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(
                        RegExp(r'^\d*\.?\d{0,2}$')),
                  ],
                  onChanged: (value) {
                    final validation =
                        SettingsValidator.validateThreshold(value);
                    setState(() {
                      errorText = validation['isValid']
                          ? null
                          : validation['errorMessage'];
                    });
                  },
                ),
                const SizedBox(height: 6), // Reduced from 8
                Text(
                  'Recommended: ${meterUnit}5.00',
                  style: const TextStyle(
                    fontStyle: FontStyle.italic,
                    fontSize: 11, // Reduced from 12
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
            onCancel: () {},
            onSave: () {
              final validation =
                  SettingsValidator.validateThreshold(textController.text);
              if (validation['isValid']) {
                final val = double.tryParse(textController.text);
                if (val != null && val >= 0.0) {
                  // Round to 2 decimal places
                  final roundedValue = double.parse(val.toStringAsFixed(2));
                  onChanged(roundedValue);
                  Navigator.of(context).pop();
                }
              } else {
                setState(() {
                  errorText = validation['errorMessage'];
                });
              }
            },
          );
        },
      ),
    );
  }

  /// Shows a dialog for setting the days in advance
  static Future<void> showDaysInAdvanceDialog(
    BuildContext context,
    int currentValue,
    Function(int) onChanged,
  ) async {
    final TextEditingController textController = TextEditingController(
      text: currentValue.toString(),
    );
    String? errorText;

    return showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) {
          return SettingDialog(
            title: 'Days in Advance',
            subtitle:
                'Set how many days in advance you want to be warned about low balance',
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                TextField(
                  controller: textController,
                  decoration: InputDecoration(
                    labelText: 'Days',
                    hintText: 'e.g. 2',
                    suffixText: 'days',
                    errorText: errorText,
                  ),
                  keyboardType: TextInputType.number,
                  inputFormatters: [
                    FilteringTextInputFormatter.digitsOnly,
                  ],
                  onChanged: (value) {
                    final validation =
                        SettingsValidator.validateDaysInAdvance(value);
                    setState(() {
                      errorText = validation['isValid']
                          ? null
                          : validation['errorMessage'];
                    });
                  },
                ),
                const SizedBox(height: 6), // Reduced from 8
                const Text(
                  'Recommended: 2 days',
                  style: TextStyle(
                    fontStyle: FontStyle.italic,
                    fontSize: 11, // Reduced from 12
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
            onCancel: () {},
            onSave: () {
              final validation =
                  SettingsValidator.validateDaysInAdvance(textController.text);
              if (validation['isValid']) {
                final val = int.tryParse(textController.text);
                if (val != null && val > 0 && val <= 30) {
                  onChanged(val);
                  Navigator.of(context).pop();
                }
              } else {
                setState(() {
                  errorText = validation['errorMessage'];
                });
              }
            },
          );
        },
      ),
    );
  }

  /// Shows a dialog for selecting the date format
  static Future<void> showDateFormatDialog(
    BuildContext context,
    String currentValue,
    Function(String) onChanged,
  ) async {
    final now = DateTime.now();

    return showDialog(
      context: context,
      builder: (context) => SettingDialog(
        title: 'Date Format',
        subtitle: 'Select how dates will be displayed',
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildFormatOption(
              'DD-MM-YYYY',
              DateTimeUtils.formatDate(now, 'dd-MM-yyyy'),
              currentValue,
              context,
              onChanged,
            ),
            _buildFormatOption(
              'MM-DD-YYYY',
              DateTimeUtils.formatDate(now, 'MM-dd-yyyy'),
              currentValue,
              context,
              onChanged,
            ),
            _buildFormatOption(
              'YYYY-MM-DD',
              DateTimeUtils.formatDate(now, 'yyyy-MM-dd'),
              currentValue,
              context,
              onChanged,
            ),
            _buildFormatOption(
              'DD MMM YYYY',
              DateTimeUtils.formatDateWithMonthName(now),
              currentValue,
              context,
              onChanged,
            ),
            _buildFormatOption(
              'MMM DD, YYYY',
              DateTimeUtils.formatDate(now, 'MMM dd, yyyy'),
              currentValue,
              context,
              onChanged,
            ),
          ],
        ),
      ),
    );
  }

  /// Shows a dialog for selecting the date info
  static Future<void> showDateInfoDialog(
    BuildContext context,
    String currentValue,
    Function(String) onChanged,
  ) async {
    return showDialog(
      context: context,
      builder: (context) => SettingDialog(
        title: 'Date Info',
        subtitle: 'Select whether to show date only or date and time',
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<String>(
              title: const Text('Date only',
                  style: TextStyle(fontSize: 14)), // Reduced font size
              value: 'Date only',
              groupValue: currentValue,
              onChanged: (value) {
                if (value != null) {
                  onChanged(value);
                  Navigator.of(context).pop();
                }
              },
              dense: true,
              contentPadding:
                  const EdgeInsets.symmetric(horizontal: 8), // Reduced padding
              visualDensity: VisualDensity.compact, // More compact
            ),
            RadioListTile<String>(
              title: const Text('Date and time',
                  style: TextStyle(fontSize: 14)), // Reduced font size
              value: 'Date and time',
              groupValue: currentValue,
              onChanged: (value) {
                if (value != null) {
                  onChanged(value);
                  Navigator.of(context).pop();
                }
              },
              dense: true,
              contentPadding:
                  const EdgeInsets.symmetric(horizontal: 8), // Reduced padding
              visualDensity: VisualDensity.compact, // More compact
            ),
          ],
        ),
      ),
    );
  }

  /// Shows a dialog for setting the first meter reading
  static Future<void> showInitialMeterCreditDialog(
    BuildContext context,
    double? currentValue,
    String meterUnit,
    Function(double?) onChanged,
  ) async {
    final TextEditingController textController = TextEditingController(
      text: currentValue?.toStringAsFixed(2) ?? '',
    );
    String? errorText;

    return showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) {
          return SettingDialog(
            title: 'First Meter Reading',
            subtitle:
                'Enter your first meter reading in monetary value (leave empty if not applicable)',
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                TextField(
                  controller: textController,
                  decoration: InputDecoration(
                    labelText: 'First Reading',
                    hintText: 'Optional',
                    prefixText: meterUnit,
                    errorText: errorText,
                  ),
                  keyboardType: const TextInputType.numberWithOptions(
                      decimal: true, signed: false),
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(
                        RegExp(r'^\d*\.?\d{0,2}$')),
                  ],
                  onChanged: (value) {
                    if (value.isEmpty) {
                      setState(() {
                        errorText = null;
                      });
                      return;
                    }
                    final validation =
                        SettingsValidator.validateMonetaryAmount(value);
                    setState(() {
                      errorText = validation['isValid']
                          ? null
                          : validation['errorMessage'];
                    });
                  },
                ),
                const SizedBox(height: 6), // Reduced from 8
                const Text(
                  'This value must be positive and will be used to calculate your initial meter balance',
                  style: TextStyle(
                    fontStyle: FontStyle.italic,
                    fontSize: 11, // Reduced from 12
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
            onCancel: () {},
            onSave: () {
              if (textController.text.isEmpty) {
                onChanged(null);
                Navigator.of(context).pop();
                return;
              }

              final validation =
                  SettingsValidator.validateMonetaryAmount(textController.text);
              if (!validation['isValid']) {
                setState(() {
                  errorText = validation['errorMessage'];
                });
                return;
              }

              final val = double.tryParse(textController.text);
              if (val == null) {
                return;
              }

              // Check if value is positive
              if (val <= 0) {
                setState(() {
                  errorText = 'First meter reading must be greater than zero';
                });
                return;
              }

              // Round to 2 decimal places
              final roundedValue = double.parse(val.toStringAsFixed(2));
              onChanged(roundedValue);
              Navigator.of(context).pop();
            },
          );
        },
      ),
    );
  }

  /// Helper method to build a date format option
  static Widget _buildFormatOption(
    String format,
    String example,
    String currentValue,
    BuildContext context,
    Function(String) onChanged,
  ) {
    return RadioListTile<String>(
      title: Text(format,
          style: const TextStyle(fontSize: 14)), // Reduced font size
      subtitle: Text('Example: $example',
          style: const TextStyle(fontSize: 12)), // Reduced font size
      value: format,
      groupValue: currentValue,
      onChanged: (value) {
        if (value != null) {
          onChanged(value);
          Navigator.of(context).pop();
        }
      },
      dense: true,
      contentPadding:
          const EdgeInsets.symmetric(horizontal: 8), // Reduced padding
      visualDensity: VisualDensity.compact, // More compact
    );
  }
}
