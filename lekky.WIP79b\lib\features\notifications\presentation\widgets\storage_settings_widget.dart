// File: lib/features/notifications/presentation/widgets/storage_settings_widget.dart

import 'package:flutter/material.dart';
import '../../data/migration/storage_migration_service.dart';
import '../../di/notification_service_locator.dart';
import 'storage_migration_dialog.dart';

/// Widget for displaying and managing notification storage settings
class StorageSettingsWidget extends StatefulWidget {
  const StorageSettingsWidget({Key? key}) : super(key: key);

  @override
  State<StorageSettingsWidget> createState() => _StorageSettingsWidgetState();
}

class _StorageSettingsWidgetState extends State<StorageSettingsWidget> {
  String _currentStorageType = 'Unknown';
  bool _isMigrationNeeded = false;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadStorageInfo();
  }

  /// Load storage information
  Future<void> _loadStorageInfo() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final migrationService =
          notificationServiceLocator<StorageMigrationService>();

      // Get current storage type
      final storageType = await migrationService.getCurrentStorageType();

      // Check if migration is needed
      final isMigrationNeeded = await migrationService.isMigrationNeeded();

      setState(() {
        _currentStorageType = storageType;
        _isMigrationNeeded = isMigrationNeeded;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _currentStorageType = 'Error: $e';
        _isLoading = false;
      });
    }
  }

  /// Start the migration process
  Future<void> _startMigration() async {
    final migrationService =
        notificationServiceLocator<StorageMigrationService>();

    // Show migration dialog
    final result = await showStorageMigrationDialog(
      context,
      migrationService,
    );

    // Reload storage info after migration
    if (result == true) {
      await _loadStorageInfo();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(8.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Notification Storage',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'Storage type affects performance and reliability of notifications.',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 16),
            _isLoading
                ? const Center(child: CircularProgressIndicator())
                : Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          const Text('Current Storage Type:'),
                          const SizedBox(width: 8),
                          Text(
                            _currentStorageType,
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: _currentStorageType == 'SQLite'
                                  ? Colors.green
                                  : Colors.orange,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      if (_isMigrationNeeded)
                        ElevatedButton(
                          onPressed: _startMigration,
                          child: const Text('Migrate to SQLite Database'),
                        )
                      else if (_currentStorageType == 'SQLite')
                        const Chip(
                          label: Text('Using optimized storage'),
                          backgroundColor: Colors.green,
                          labelStyle: TextStyle(color: Colors.white),
                        )
                      else
                        const Text(
                          'Storage migration not available',
                          style: TextStyle(fontStyle: FontStyle.italic),
                        ),
                    ],
                  ),
          ],
        ),
      ),
    );
  }
}
