// File: lib/core/widgets/home_settings_button.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/theme_provider.dart';

/// A settings button for the home screen
class HomeSettingsButton extends StatelessWidget {
  const HomeSettingsButton({super.key});

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isDarkMode = themeProvider.isDarkMode;

    return IconButton(
      icon: const Icon(
        Icons.settings_outlined,
        color: Colors.white,
        size: 24, // Same size as the edit icon in History screen
      ),
      onPressed: () {
        // Navigate to settings screen
        Navigator.of(context).pushNamed('/settings');
      },
      tooltip: 'Settings',
      padding:
          const EdgeInsets.all(8), // Match padding with notification button
      splashRadius: 24, // Smaller splash radius
    );
  }
}
