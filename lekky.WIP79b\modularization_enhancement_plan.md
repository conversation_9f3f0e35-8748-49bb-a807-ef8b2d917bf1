# RooLekky Flutter Application Modularization and Enhancement Plan

## 1. Overview

This comprehensive plan addresses the identified issues in the <PERSON><PERSON><PERSON><PERSON><PERSON> application, focusing on three key areas:
1. Code modularization and structure
2. Notification system enhancements
3. Performance optimizations

The plan is designed to be implemented in phases with clear dependencies, ensuring minimal disruption to existing functionality.

## 2. Current Architecture Analysis

```mermaid
graph TD
    A[App Entry Point] --> B[Core]
    A --> C[Features]
    
    B --> B1[Platform Adapters]
    B --> B2[Utils]
    B --> B3[Models]
    
    B1 --> B1_1[Notification Adapters]
    B1 --> B1_2[Permission Adapters]
    B1 --> B1_3[Timezone Adapters]
    
    C --> C1[Notifications Feature]
    C --> C2[History Feature]
    C --> C3[Backup Feature]
    
    C1 --> C1_1[Data Layer]
    C1 --> C1_2[Domain Layer]
    C1 --> C1_3[Presentation Layer]
    
    C1_1 --> C1_1_1[Repositories Impl]
    C1_1 --> C1_1_2[Data Sources]
    
    C1_2 --> C1_2_1[Models]
    C1_2 --> C1_2_2[Repositories]
    C1_2 --> C1_2_3[Use Cases]
    
    C1_3 --> C1_3_1[Providers]
    C1_3 --> C1_3_2[Screens]
    C1_3 --> C1_3_3[Widgets]
```

### Current Issues

1. **Notification System Issues**:
   - Meter reminder notifications not working due to permission, timezone, and scheduling issues
   - Low balance notifications not accounting for AT & DIA settings
   - Top-up warning calculations with flawed recent average calculation
   - Invalid entry notifications lacking context
   - Inflexible message templates

2. **Code Structure Issues**:
   - Large files needing refactoring
   - Complex component dependencies
   - Shared logic that should be extracted
   - Mixed state management approaches

3. **Performance Issues**:
   - Bottlenecks in timezone calculations and permission checks
   - Expensive widgets and unnecessary rebuilds
   - Inefficient data loading patterns

## 3. Modularization Strategy

### 3.1 Proposed Module Structure

```mermaid
graph TD
    A[App Entry Point] --> B[Core]
    A --> C[Features]
    
    B --> B1[Platform]
    B --> B2[Utils]
    B --> B3[Models]
    B --> B4[Shared Services]
    B --> B5[Providers]
    
    B1 --> B1_1[Notification]
    B1 --> B1_2[Permissions]
    B1 --> B1_3[Timezone]
    
    B4 --> B4_1[Analytics Service]
    B4 --> B4_2[Error Handling Service]
    B4 --> B4_3[Logging Service]
    B4 --> B4_4[Performance Monitoring]
    
    B5 --> B5_1[App State Provider]
    B5 --> B5_2[Theme Provider]
    B5 --> B5_3[Settings Provider]
    
    C --> C1[Notifications]
    C --> C2[History]
    C --> C3[Backup]
    C --> C4[Settings]
    C --> C5[Usage]
    C --> C6[Home]
    
    C1 --> C1_1[Data]
    C1 --> C1_2[Domain]
    C1 --> C1_3[Presentation]
    
    C1_1 --> C1_1_1[Repositories]
    C1_1 --> C1_1_2[Data Sources]
    
    C1_2 --> C1_2_1[Models]
    C1_2 --> C1_2_2[Repositories]
    C1_2 --> C1_2_3[Use Cases]
    
    C1_3 --> C1_3_1[Providers]
    C1_3 --> C1_3_2[Screens]
    C1_3 --> C1_3_3[Widgets]
```

### 3.2 Module Responsibilities

1. **Core Modules**:
   - **Platform**: Platform-specific adapters (notification, permissions, timezone)
   - **Utils**: Utility functions and helpers
   - **Models**: Core data models shared across features
   - **Shared Services**: Services used by multiple features
   - **Providers**: App-wide state providers using Riverpod

2. **Feature Modules**:
   - **Notifications**: All notification-related functionality
   - **History**: Meter reading history and analysis
   - **Backup**: Data backup and restore
   - **Settings**: User preferences and app configuration
   - **Usage**: Usage tracking and estimation
   - **Home**: Home screen and related functionality

Each feature module follows a clean architecture approach with data, domain, and presentation layers.

## 4. Notification System Enhancement Plan

### 4.1 Meter Reading Reminder Notifications

```mermaid
sequenceDiagram
    participant App
    participant NotificationManager
    participant PermissionHandler
    participant TimezoneService
    participant PlatformAdapter
    
    App->>NotificationManager: scheduleMeterReadingReminder()
    NotificationManager->>PermissionHandler: checkPermissions()
    PermissionHandler-->>NotificationManager: permissionStatus
    
    alt Permission Granted
        NotificationManager->>TimezoneService: getAdjustedScheduleTime()
        TimezoneService-->>NotificationManager: scheduledDateTime
        NotificationManager->>PlatformAdapter: scheduleNotification()
        PlatformAdapter-->>NotificationManager: success/failure
    else Permission Denied
        NotificationManager->>App: showPermissionUI()
    end
    
    NotificationManager-->>App: reminderStatus
```

#### Key Improvements:
1. **Permission Handling**:
   - Implement a robust permission flow with educational UI
   - Add graceful fallbacks when permissions are denied
   - Implement periodic permission re-verification

2. **Timezone Handling**:
   - Enhance timezone change detection with accurate offset calculations
   - Implement proper timezone-aware scheduling
   - Add automatic rescheduling when timezone changes

3. **Scheduling Implementation**:
   - Implement reliable scheduling with exact alarms where available
   - Add support for different reminder frequencies
   - Improve notification message customization

### 4.2 Low Balance Notifications

```mermaid
graph TD
    A[Low Balance Detection] --> B{Check Balance}
    B -->|Below AT| C[Trigger Alert]
    B -->|Above AT| D[Calculate Projected Days]
    D --> E{Days <= DIA?}
    E -->|Yes| F[Trigger Advance Warning]
    E -->|No| G[No Alert Needed]
    
    C --> H[Generate Notification]
    F --> H
    H --> I[Show Notification]
```

#### Key Improvements:
1. **AT & DIA Integration**:
   - Implement Alert Threshold (AT) checking
   - Add Days In Advance (DIA) calculation based on average usage
   - Create combined logic that respects both settings

2. **Timezone Awareness**:
   - Ensure calculations account for timezone changes
   - Store timestamps in UTC and convert for display

3. **Days Since Last Reading**:
   - Incorporate last reading date in calculations
   - Adjust warnings based on reading recency

### 4.3 Reading Age Enhancement for Low Balance Notifications

The low balance notification logic will be enhanced to account for the age of the most recent meter reading:

```dart
class EnhancedLowBalanceNotification {
  final NotificationRepository _notificationRepository;
  final UsageRepository _usageRepository;
  final SettingsRepository _settingsRepository;
  
  EnhancedLowBalanceNotification({
    required NotificationRepository notificationRepository,
    required UsageRepository usageRepository,
    required SettingsRepository settingsRepository,
  }) : 
    _notificationRepository = notificationRepository,
    _usageRepository = usageRepository,
    _settingsRepository = settingsRepository;
  
  Future<bool> execute() async {
    // Get user settings
    final settings = await _settingsRepository.getBalanceSettings();
    
    // If notifications are disabled, exit early
    if (!settings.lowBalanceNotificationsEnabled) {
      return false;
    }
    
    // Get current usage data
    final usageData = await _usageRepository.getCurrentUsageData();
    
    // Calculate days since last reading with minute precision
    final daysSinceLastReading = calculateDaysSinceLastReading(usageData.lastReadingDate);
    
    // Estimate current balance by subtracting estimated usage since last reading
    final averageDailyCost = _calculateAverageDailyCost(usageData);
    final estimatedCurrentBalance = usageData.currentBalance - (daysSinceLastReading * averageDailyCost);
    
    // Check if estimated current balance is below threshold (AT)
    final isBalanceBelowThreshold = estimatedCurrentBalance <= settings.alertThreshold;
    
    // Calculate days until balance reaches threshold (DIA)
    final daysUntilThreshold = _calculateDaysUntilThreshold(
      estimatedCurrentBalance, 
      settings.alertThreshold,
      averageDailyCost
    );
    
    // Get confidence level based on reading age
    final confidenceLevel = _getConfidenceLevel(usageData.lastReadingDate);
    
    // Adjust DIA based on reading age and confidence level
    int adjustedDIA = _getAdjustedDIA(settings.daysInAdvance, usageData.lastReadingDate);
    
    // Check if we should notify based on days in advance (DIA)
    final shouldNotifyBasedOnDIA = daysUntilThreshold <= adjustedDIA;
    
    // Determine notification type and message
    if (isBalanceBelowThreshold) {
      // AT-based notification (immediate alert)
      await _notificationRepository.showLowBalanceNotification(
        balance: estimatedCurrentBalance,
        threshold: settings.alertThreshold,
        currencySymbol: settings.currencySymbol,
        isImmediate: true,
        confidenceLevel: confidenceLevel,
        daysSinceLastReading: daysSinceLastReading,
      );
      return true;
    } else if (shouldNotifyBasedOnDIA) {
      // DIA-based notification (advance warning)
      await _notificationRepository.showLowBalanceNotification(
        balance: estimatedCurrentBalance,
        threshold: settings.alertThreshold,
        daysRemaining: daysUntilThreshold,
        currencySymbol: settings.currencySymbol,
        isImmediate: false,
        confidenceLevel: confidenceLevel,
        daysSinceLastReading: daysSinceLastReading,
      );
      return true;
    }
    
    return false;
  }
  
  // Calculate days until balance reaches threshold based on average usage
  int _calculateDaysUntilThreshold(double estimatedBalance, double threshold, double averageDailyCost) {
    // If no usage or average is zero, return a large number
    if (averageDailyCost <= 0) {
      return 999;
    }
    
    // Calculate remaining balance until threshold
    final remainingBalance = estimatedBalance - threshold;
    
    // If already below threshold, return 0
    if (remainingBalance <= 0) {
      return 0;
    }
    
    // Calculate days until threshold is reached
    return (remainingBalance / averageDailyCost).ceil();
  }
  
  // Calculate average daily cost based on recent usage
  double _calculateAverageDailyCost(UsageData usageData) {
    // If no usage history, return 0
    if (usageData.usageHistory.isEmpty) {
      return 0;
    }
    
    // Get recent usage (last 14 days or available history)
    final now = DateTime.now();
    final recentUsage = usageData.usageHistory
        .where((usage) => now.difference(usage.date).inDays <= 14)
        .toList();
    
    // If no recent usage, use all available history
    final usageToAnalyze = recentUsage.isEmpty ? usageData.usageHistory : recentUsage;
    
    // Calculate total cost
    final totalCost = usageToAnalyze.fold(0.0, (sum, usage) => sum + usage.cost);
    
    // Return average daily cost
    return totalCost / usageToAnalyze.length;
  }
  
  // Get confidence level based on reading age
  ConfidenceLevel _getConfidenceLevel(DateTime lastReadingDate) {
    final daysSinceLastReading = DateTime.now().difference(lastReadingDate).inDays;
    
    if (daysSinceLastReading < 3) {
      return ConfidenceLevel.high;
    } else if (daysSinceLastReading <= 7) {
      return ConfidenceLevel.medium;
    } else {
      return ConfidenceLevel.low;
    }
  }
  
  // Adjust DIA based on reading age
  int _getAdjustedDIA(int configuredDIA, DateTime lastReadingDate) {
    final daysSinceLastReading = DateTime.now().difference(lastReadingDate).inDays;
    
    // If reading is more than 14 days old, adjust DIA
    if (daysSinceLastReading > 14) {
      // Make DIA more conservative (at least 3 days or half the configured value, whichever is greater)
      return max(3, configuredDIA ~/ 2);
    }
    
    return configuredDIA;
  }
}
```

### 4.4 Enhanced Notification Messages Based on Reading Age

```dart
class NotificationRepositoryImpl implements NotificationRepository {
  final PlatformNotificationDataSource _notificationDataSource;
  
  NotificationRepositoryImpl(this._notificationDataSource);
  
  @override
  Future<void> showLowBalanceNotification({
    required double balance,
    required double threshold,
    int? daysRemaining,
    String currencySymbol = '£',
    bool isImmediate = true,
    ConfidenceLevel confidenceLevel = ConfidenceLevel.high,
    double daysSinceLastReading = 0.0,
  }) async {
    final title = isImmediate ? 'Low Balance Alert' : 'Balance Warning';
    
    String baseMessage = isImmediate
        ? 'Your balance is now ${currencySymbol}${balance.toStringAsFixed(2)}, which is below your alert threshold of ${currencySymbol}${threshold.toStringAsFixed(2)}. Please top up soon.'
        : 'Your balance of ${currencySymbol}${balance.toStringAsFixed(2)} is projected to fall below your alert threshold of ${currencySymbol}${threshold.toStringAsFixed(2)} in ${daysRemaining} ${daysRemaining == 1 ? 'day' : 'days'} based on your recent usage.';
    
    // Add confidence information based on reading age
    String message = _addConfidenceInformation(
      baseMessage, 
      confidenceLevel, 
      daysSinceLastReading
    );
    
    await _notificationDataSource.showNotification(
      id: NotificationIds.lowBalanceNotificationId,
      title: title,
      body: message,
      payload: 'low_balance:$balance:$threshold:${daysRemaining ?? 0}:$confidenceLevel',
    );
  }
  
  String _addConfidenceInformation(
    String baseMessage, 
    ConfidenceLevel confidenceLevel, 
    double daysSinceLastReading
  ) {
    // Round to 1 decimal place for display
    final roundedDays = (daysSinceLastReading * 10).round() / 10;
    final daysText = roundedDays == 1.0 
        ? '1 day' 
        : (roundedDays == roundedDays.floor().toDouble() 
            ? '${roundedDays.toInt()} days' 
            : '${roundedDays.toStringAsFixed(1)} days');
    
    switch (confidenceLevel) {
      case ConfidenceLevel.high:
        return baseMessage;
      case ConfidenceLevel.medium:
        return '$baseMessage This estimate is based on a reading from $daysText ago.';
      case ConfidenceLevel.low:
        return '$baseMessage This estimate is based on a reading from $daysText ago. For more accurate predictions, please submit a new meter reading.';
    }
  }
}
```

### 4.5 Usage Examples

#### Example 1: Immediate Alert (AT-based) with Old Reading
- User settings: AT = £20, DIA = 5 days
- Last recorded balance: £50.00 (from 6 days ago)
- Recent average daily usage: £5.00/day

Calculation:
1. Estimated current balance: £50.00 - (6 days × £5.00/day) = £20.00
2. Since the estimated balance equals the Alert Threshold (£20.00), an immediate alert is triggered
3. Confidence Level: Medium (reading is 6 days old)

```
Title: Low Balance Alert
Message: Your balance is now £20.00, which is below your alert threshold of £20.00. Please top up soon. This estimate is based on a reading from 6 days ago.
```

#### Example 2: Advance Warning (DIA-based) with Very Old Reading
- User settings: AT = £20, DIA = 5 days
- Last recorded balance: £50.00 (from 15 days ago)
- Recent average daily usage: £2.00/day

Calculation:
1. Estimated current balance: £50.00 - (15 days × £2.00/day) = £20.00
2. Adjusted DIA: 3 days (reduced from 5 days due to old reading)
3. Days until threshold reached: £20.00 - £20.00 = £0.00 ÷ £2.00 = 0 days
4. Since the days until threshold (0) is less than the adjusted DIA (3), an alert is triggered
5. Confidence Level: Low (reading is 15 days old)

```
Title: Low Balance Alert
Message: Your balance is now £20.00, which is below your alert threshold of £20.00. Please top up soon. This estimate is based on a reading from 15 days ago. For more accurate predictions, please submit a new meter reading.
```

#### Example 3: No Alert Needed with Recent Reading
- User settings: AT = £20, DIA = 5 days
- Last recorded balance: £50.00 (from 2 days ago)
- Recent average daily usage: £2.00/day

Calculation:
1. Estimated current balance: £50.00 - (2 days × £2.00/day) = £46.00
2. Remaining balance until threshold: £46.00 - £20.00 = £26.00
3. Days until threshold reached: £26.00 ÷ £2.00 = 13 days
4. Since the days until threshold (13) is greater than the DIA (5), no notification is triggered
5. Confidence Level: High (reading is only 2 days old)

### 4.6 Homepage Meter Info Card - "Top Up Before" Enhancement

The "Top Up Before" calculation in the Homepage Meter Info Card needs to be verified and potentially modified to ensure it correctly shows the first day when the estimated meter total falls below the Alert Threshold (AT).

Current implementation:

```dart
// In AverageCalculator
static DateTime? calculateDateToTopUp(
    double meterTotal, double alertThreshold, double averageUsage) {
  if (averageUsage <= 0) return null;

  final daysUntilThreshold = (meterTotal - alertThreshold) / averageUsage;
  if (daysUntilThreshold <= 0) return null;

  return DateTime.now().add(Duration(days: daysUntilThreshold.round()));
}
```

The issue with this implementation is that it returns null if the balance is already below the threshold, and it doesn't account for the age of the meter reading. The enhanced implementation will:

1. Use the estimated current balance based on the age of the reading
2. Return the current date if the balance is already below the threshold
3. Provide a confidence level for the prediction

```dart
// Enhanced implementation
static DateToTopUpResult calculateDateToTopUp({
  required double lastMeterReading,
  required double topUpsSinceLastReading,
  required DateTime lastReadingDate,
  required double averageUsage,
  required double alertThreshold,
}) {
  // If average usage is zero or negative, we can't calculate a date
  if (averageUsage <= 0) {
    return DateToTopUpResult(
      date: null,
      confidenceLevel: ConfidenceLevel.low,
      isAlreadyBelowThreshold: false,
    );
  }
  
  // Calculate estimated current balance
  final now = DateTime.now();
  final secondsSinceLastReading = now.difference(lastReadingDate).inSeconds;
  final daysSinceLastReading = secondsSinceLastReading / (24 * 60 * 60);
  
  // If the reading is very recent, just use the actual reading
  final estimatedUsage = secondsSinceLastReading < 3600 ? 0 : daysSinceLastReading * averageUsage;
  final estimatedCurrentBalance = lastMeterReading + topUpsSinceLastReading - estimatedUsage;
  
  // Check if already below threshold
  if (estimatedCurrentBalance <= alertThreshold) {
    return DateToTopUpResult(
      date: now,
      confidenceLevel: _getConfidenceLevelForDays(daysSinceLastReading),
      isAlreadyBelowThreshold: true,
    );
  }
  
  // Calculate days until threshold
  final remainingBalance = estimatedCurrentBalance - alertThreshold;
  final daysUntilThreshold = remainingBalance / averageUsage;
  
  // Calculate the date
  final dateToTopUp = now.add(Duration(days: daysUntilThreshold.round()));
  
  return DateToTopUpResult(
    date: dateToTopUp,
    confidenceLevel: _getConfidenceLevelForDays(daysSinceLastReading),
    isAlreadyBelowThreshold: false,
  );
}

static ConfidenceLevel _getConfidenceLevelForDays(double days) {
  if (days < 3) {
    return ConfidenceLevel.high;
  } else if (days <= 7) {
    return ConfidenceLevel.medium;
  } else {
    return ConfidenceLevel.low;
  }
}
```

This enhanced implementation will ensure that the "Top Up Before" date in the Homepage Meter Info Card accurately reflects when the balance will fall below the Alert Threshold, properly accounting for the age of the meter reading.

### 4.7 Top-Up Warning Calculations

#### Key Improvements:
1. **Recent Average Calculation**:
   - Implement weighted average based on recent usage patterns
   - Account for seasonal variations where possible
   - Use configurable time periods for average calculation

2. **Replace Hard-Coded Values**:
   - Make threshold values configurable
   - Implement user-specific settings

### 4.8 Invalid Entry Notifications

#### Key Improvements:
1. **Contextual Information**:
   - Include specific details about the invalid entry
   - Add actionable guidance on how to correct the issue

2. **Actionable Notifications**:
   - Add deep links to relevant screens
   - Implement quick actions for common corrections

### 4.9 Message Templates

#### Key Improvements:
1. **Flexible Templates**:
   - Implement a template system with placeholders
   - Add support for different frequencies and notification types
   - Create a message template manager

## 5. State Management and Dependency Injection

### 5.1 Riverpod Implementation

```mermaid
graph TD
    A[App] --> B[ProviderScope]
    B --> C[Core Providers]
    B --> D[Feature Providers]
    
    C --> C1[AppStateProvider]
    C --> C2[ThemeProvider]
    C --> C3[LoggingProvider]
    
    D --> D1[NotificationProvider]
    D --> D2[HistoryProvider]
    D --> D3[BackupProvider]
    
    D1 --> D1_1[ReminderProvider]
    D1 --> D1_2[PermissionProvider]
    D1 --> D1_3[NotificationStateProvider]
```

#### Key Components:
1. **Provider Organization**:
   - Core providers for app-wide state
   - Feature-specific providers
   - Hierarchical provider structure

2. **State Classes**:
   - Immutable state classes
   - Proper state updates with copyWith methods
   - Clear separation of UI state and business logic

3. **Repository Integration**:
   - Repository providers
   - UseCase providers
   - Clean separation of concerns

### 5.2 Dependency Injection

1. **Service Locator Pattern**:
   - Implement a service locator for non-UI dependencies
   - Register platform adapters and services

2. **Provider-Based DI**:
   - Use Riverpod's provider system for UI dependencies
   - Implement proper provider overrides for testing

## 6. Performance Optimization Strategy

### 6.1 Timezone Calculation Optimizations

1. **Caching**:
   - Cache timezone offsets
   - Implement efficient timezone lookup

2. **Batch Processing**:
   - Process timezone updates in batches
   - Minimize timezone recalculations

### 6.2 Permission Check Optimizations

1. **Throttling**:
   - Implement throttling for permission checks
   - Cache permission status with expiration

2. **Selective Checking**:
   - Only check permissions when needed
   - Batch permission checks where possible

### 6.3 Widget Optimizations

1. **Widget Rebuilds**:
   - Use const constructors where appropriate
   - Implement proper keys for widget identity
   - Use RepaintBoundary for complex UI elements

2. **State Management**:
   - Ensure granular state updates with Riverpod
   - Avoid unnecessary rebuilds with select()

### 6.4 Data Loading Patterns

1. **Lazy Loading**:
   - Implement lazy loading for lists
   - Use pagination where appropriate

2. **Caching**:
   - Implement a data cache layer
   - Use memory and disk caching strategies

## 7. Testing and Documentation Plan

### 7.1 Testing Strategy

1. **Unit Tests**:
   - Test all use cases
   - Test repository implementations
   - Test utility functions

2. **Widget Tests**:
   - Test key UI components
   - Test permission flows
   - Test notification UI

3. **Integration Tests**:
   - Test notification scheduling
   - Test timezone handling
   - Test permission flows

### 7.2 Documentation Approach

1. **Code Documentation**:
   - Document all public APIs
   - Add class and method documentation
   - Document complex algorithms

2. **Architecture Documentation**:
   - Create architecture diagrams
   - Document module responsibilities
   - Document cross-module interactions

## 8. Implementation Phases

### Phase 1: Core Infrastructure (Weeks 1-2)
- Set up Riverpod providers
- Refactor platform adapters
- Implement service locator

### Phase 2: Notification System Enhancements (Weeks 3-4)
- Fix meter reading reminder notifications
- Enhance permission handling
- Implement timezone-aware scheduling
- Enhance "Top Up Before" calculation in the Homepage Meter Info Card
- Implement reading age enhancements for low balance notifications

### Phase 3: Feature Modularization (Weeks 5-6)
- Refactor into feature modules
- Extract shared services
- Implement clean architecture patterns

### Phase 4: Performance Optimizations (Weeks 7-8)
- Optimize timezone calculations
- Implement widget optimizations
- Enhance data loading patterns

### Phase 5: Testing and Refinement (Weeks 9-10)
- Complete unit and widget tests
- Perform integration testing
- Finalize documentation

## 9. Success Criteria

1. **Functional Criteria**:
   - All notification types work reliably
   - Timezone changes are handled correctly
   - Permission flows are user-friendly
   - "Top Up Before" date correctly shows when balance will fall below AT

2. **Technical Criteria**:
   - Code is properly modularized
   - State management is consistent
   - Performance metrics show improvement

3. **User Experience Criteria**:
   - Notifications are timely and relevant
   - Error messages are clear and actionable
   - App remains responsive

## 10. Risks and Mitigations

| Risk | Impact | Likelihood | Mitigation |
|------|--------|------------|------------|
| Platform-specific notification issues | High | Medium | Thorough testing on multiple devices/OS versions |
| State management migration complexity | Medium | Medium | Incremental migration with parallel implementations |
| Performance regression | High | Low | Benchmark before and after changes |
| Data migration issues | High | Low | Implement version-aware migrations with fallbacks |
| Permission model changes in future OS versions | Medium | Medium | Design flexible permission handling system |