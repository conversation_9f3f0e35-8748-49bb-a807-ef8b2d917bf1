import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/providers/settings_provider.dart';
import '../../../../core/shared/widgets/currency_input_field.dart';
import '../../../../core/shared/widgets/integer_input_field.dart';
import '../../../../core/shared/widgets/settings_toggle.dart';

/// Notifications settings screen
class NotificationsSettingsScreen extends ConsumerStatefulWidget {
  /// Constructor
  const NotificationsSettingsScreen({super.key});

  @override
  ConsumerState<NotificationsSettingsScreen> createState() =>
      _NotificationsSettingsScreenState();
}

class _NotificationsSettingsScreenState
    extends ConsumerState<NotificationsSettingsScreen> {
  @override
  Widget build(BuildContext context) {
    final settingsAsync = ref.watch(settingsProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Alerts & Notifications'),
        backgroundColor: const Color(0xFF424242),
        foregroundColor: Colors.white,
      ),
      body: settingsAsync.when(
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => Center(
          child: Text('Error loading settings: $error'),
        ),
        data: (settings) {
          return ListView(
            padding: const EdgeInsets.all(16.0),
            children: [
              // Alert Threshold section
              Card(
                margin: const EdgeInsets.only(bottom: 16.0),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          const Icon(Icons.warning, color: Colors.amber),
                          const SizedBox(width: 16),
                          const Text(
                            'Alert Threshold',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const Spacer(),
                          Text(
                            'Current: ${settings.currencySymbol}${settings.alertThreshold.toStringAsFixed(2)}',
                            style: TextStyle(
                              fontSize: 14,
                              color:
                                  Theme.of(context).textTheme.bodySmall?.color,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        'Get notified when balance falls below this amount',
                        style: TextStyle(
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Alert threshold input
                      CurrencyInputField(
                        value: settings.alertThreshold,
                        onChanged: (value) {
                          if (value != null &&
                              value >= 1.00 &&
                              value <= 999.99) {
                            ref
                                .read(settingsProvider.notifier)
                                .updateAlertThreshold(value);
                          }
                        },
                        currencySymbol: settings.currencySymbol,
                        labelText: 'Alert Threshold',
                        helperText:
                            'Enter a value between ${settings.currencySymbol} 1.00 and ${settings.currencySymbol} 999.99',
                        minValue: 1.00,
                        maxValue: 999.99,
                      ),
                    ],
                  ),
                ),
              ),

              // Days in Advance section
              Card(
                margin: const EdgeInsets.only(bottom: 16.0),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          const Icon(Icons.timelapse, color: Colors.blue),
                          const SizedBox(width: 16),
                          const Text(
                            'Days in Advance',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const Spacer(),
                          Text(
                            'Current: ${settings.daysInAdvance} days',
                            style: TextStyle(
                              fontSize: 14,
                              color:
                                  Theme.of(context).textTheme.bodySmall?.color,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        'Get notified this many days before you run out',
                        style: TextStyle(
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Days in advance input
                      IntegerInputField(
                        value: settings.daysInAdvance,
                        onChanged: (value) {
                          if (value != null) {
                            ref
                                .read(settingsProvider.notifier)
                                .updateDaysInAdvance(value);
                          }
                        },
                        suffixText: 'days',
                        labelText: 'Days in Advance',
                        minValue: 0,
                        maxValue: 99,
                      ),
                    ],
                  ),
                ),
              ),

              // Notifications section
              Card(
                margin: const EdgeInsets.only(bottom: 16.0),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          const Icon(Icons.notifications, color: Colors.blue),
                          const SizedBox(width: 16),
                          const Text(
                            'Notifications',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        'Configure notification preferences',
                        style: TextStyle(
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Notifications toggle
                      SettingsToggle(
                        title: 'Enable Notifications',
                        description: 'Master toggle for all notifications',
                        value: settings.notificationsEnabled,
                        onChanged: (value) {
                          ref
                              .read(settingsProvider.notifier)
                              .updateNotificationsEnabled(value,
                                  context: context);
                        },
                      ),

                      if (settings.notificationsEnabled) ...[
                        const Divider(),
                        SettingsToggle(
                          title: 'Low Balance Alerts',
                          description:
                              'Alerts when you have less than 24 hours of credit remaining',
                          value: settings.lowBalanceAlertsEnabled,
                          onChanged: (value) {
                            ref
                                .read(settingsProvider.notifier)
                                .updateLowBalanceAlertsEnabled(value,
                                    context: context);
                          },
                        ),
                        SettingsToggle(
                          title: 'Time to Top Up Alerts',
                          description:
                              'Alerts when your alert threshold will be reached in your specified days in advance',
                          value: settings.timeToTopUpAlertsEnabled,
                          onChanged: (value) {
                            ref
                                .read(settingsProvider.notifier)
                                .updateTimeToTopUpAlertsEnabled(value,
                                    context: context);
                          },
                        ),
                        SettingsToggle(
                          title: 'Invalid Record Alerts',
                          description:
                              'Alerts for suspicious or invalid entries',
                          value: settings.invalidRecordAlertsEnabled,
                          onChanged: (value) {
                            ref
                                .read(settingsProvider.notifier)
                                .updateInvalidRecordAlertsEnabled(value,
                                    context: context);
                          },
                        ),
                      ],
                    ],
                  ),
                ),
              ),

              // Reminders section
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Row(
                        children: [
                          Icon(Icons.alarm, color: Colors.blue),
                          SizedBox(width: 16),
                          Text(
                            'Reminders',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        'Set up reminders to check your meter',
                        style: TextStyle(
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Reminders toggle
                      SettingsToggle(
                        title: 'Enable Meter Reminders',
                        description: 'Master toggle for reminders',
                        value: settings.remindersEnabled,
                        onChanged: (value) {
                          ref
                              .read(settingsProvider.notifier)
                              .updateRemindersEnabled(value, context: context);
                        },
                      ),

                      if (settings.remindersEnabled) ...[
                        const Divider(),
                        // Reminder frequency
                        const ListTile(
                          title: Text('Reminder Frequency'),
                          subtitle: Text('How often to remind you'),
                        ),
                        RadioListTile<String>(
                          title: const Text('Daily'),
                          value: 'daily',
                          groupValue: settings.reminderFrequency,
                          onChanged: (value) {
                            if (value != null) {
                              ref
                                  .read(settingsProvider.notifier)
                                  .updateReminderFrequency(value);
                            }
                          },
                        ),
                        RadioListTile<String>(
                          title: const Text('Weekly'),
                          value: 'weekly',
                          groupValue: settings.reminderFrequency,
                          onChanged: (value) {
                            if (value != null) {
                              ref
                                  .read(settingsProvider.notifier)
                                  .updateReminderFrequency(value);
                            }
                          },
                        ),
                        RadioListTile<String>(
                          title: const Text('Bi-weekly'),
                          value: 'bi-weekly',
                          groupValue: settings.reminderFrequency,
                          onChanged: (value) {
                            if (value != null) {
                              ref
                                  .read(settingsProvider.notifier)
                                  .updateReminderFrequency(value);
                            }
                          },
                        ),
                        RadioListTile<String>(
                          title: const Text('Monthly'),
                          value: 'monthly',
                          groupValue: settings.reminderFrequency,
                          onChanged: (value) {
                            if (value != null) {
                              ref
                                  .read(settingsProvider.notifier)
                                  .updateReminderFrequency(value);
                            }
                          },
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}
