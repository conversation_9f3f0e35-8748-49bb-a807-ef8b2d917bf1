# Core Platform Adapters

This directory contains platform-specific adapters for various device capabilities, following a clean architecture approach.

## Structure

- **notification**: Platform-specific notification adapters
  - `notification_adapter.dart`: Base adapter interface
  - `android_notification_adapter.dart`: Android-specific implementation
  - `ios_notification_adapter.dart`: iOS-specific implementation

- **permissions**: Platform-specific permission adapters
  - `permission_adapter.dart`: Base adapter interface
  - `android_permission_adapter.dart`: Android-specific implementation
  - `ios_permission_adapter.dart`: iOS-specific implementation

- **timezone**: Platform-specific timezone adapters
  - `timezone_adapter.dart`: Base adapter interface
  - `timezone_handler.dart`: Common timezone handling logic

## Purpose

These adapters provide a clean interface for platform-specific functionality, allowing the rest of the application to remain platform-agnostic while still leveraging platform-specific features.