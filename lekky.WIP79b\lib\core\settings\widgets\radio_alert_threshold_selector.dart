// File: lib/core/settings/widgets/radio_alert_threshold_selector.dart
import 'package:flutter/material.dart';
import '../../theme/app_colors.dart';
import '../../theme/app_text_styles.dart';
import '../../settings/validators/settings_validator.dart';

/// A widget that allows selecting an alert threshold using radio buttons
class RadioAlertThresholdSelector extends StatefulWidget {
  final double currentValue;
  final ValueChanged<double> onChanged;
  final String currencySymbol;
  final String? errorText;
  final bool showTitle;
  final bool showHelperText;

  // Common threshold options
  static const List<double> thresholdOptions = [
    1.0,
    2.0,
    5.0,
    10.0,
    15.0,
    20.0
  ];
  // Special value to indicate custom option
  static const double customOptionValue = -1.0;

  const RadioAlertThresholdSelector({
    Key? key,
    required this.currentValue,
    required this.onChanged,
    required this.currencySymbol,
    this.errorText,
    this.showTitle = true,
    this.showHelperText = true,
  }) : super(key: key);

  @override
  State<RadioAlertThresholdSelector> createState() =>
      _RadioAlertThresholdSelectorState();
}

class _RadioAlertThresholdSelectorState
    extends State<RadioAlertThresholdSelector> {
  /// Check if the current value is one of the standard options
  bool _isStandardOption(double value) {
    return RadioAlertThresholdSelector.thresholdOptions.contains(value);
  }

  /// Show a dialog to enter a custom threshold value
  void _showCustomThresholdDialog() {
    showDialog(
      context: context,
      builder: (context) {
        final controller = TextEditingController(
          text: _isStandardOption(widget.currentValue)
              ? ''
              : widget.currentValue.toStringAsFixed(2),
        );
        String? errorText;

        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('Custom Alert Threshold'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'Enter a custom alert threshold between 1 and 999',
                    style: TextStyle(
                      fontSize: 14,
                      color: Theme.of(context).brightness == Brightness.dark
                          ? Colors.grey[300]
                          : Colors.grey[700],
                    ),
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: controller,
                    keyboardType:
                        const TextInputType.numberWithOptions(decimal: true),
                    decoration: InputDecoration(
                      labelText: 'Alert Threshold',
                      prefixText: widget.currencySymbol,
                      errorText: errorText,
                      border: const OutlineInputBorder(),
                    ),
                    autofocus: true,
                    onChanged: (value) {
                      final validation =
                          SettingsValidator.validateThreshold(value);
                      setState(() {
                        errorText = validation['isValid']
                            ? null
                            : validation['errorMessage'];
                      });
                    },
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Cancel'),
                ),
                TextButton(
                  onPressed: () {
                    final value = controller.text;
                    final validation =
                        SettingsValidator.validateThreshold(value);
                    if (validation['isValid']) {
                      final parsedValue = double.parse(value);
                      widget.onChanged(parsedValue);
                      Navigator.of(context).pop();
                    } else {
                      setState(() {
                        errorText = validation['errorMessage'];
                      });
                    }
                  },
                  child: const Text('Save'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final primaryColor = Theme.of(context).colorScheme.primary;
    final textColor = isDarkMode ? Colors.white : Colors.black87;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.showTitle) ...[
          Row(
            children: [
              const Icon(Icons.notifications_active, size: 20),
              const SizedBox(width: 8),
              Text(
                'Alert Threshold',
                style: AppTextStyles.titleMedium.copyWith(
                  color: isDarkMode
                      ? AppColors.primaryTextDark
                      : AppColors.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
        ],

        if (widget.showHelperText) ...[
          Text(
            'Get notified when your meter balance falls below this amount',
            style: TextStyle(
              fontSize: 14,
              color: isDarkMode ? Colors.grey[300] : Colors.grey[700],
            ),
          ),
          const SizedBox(height: 16),
        ],

        // Two-column grid of radio buttons
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            for (final threshold
                in RadioAlertThresholdSelector.thresholdOptions)
              SizedBox(
                width: MediaQuery.of(context).size.width *
                    0.4, // Approximately half the width
                child: RadioListTile<double>(
                  title: Text(
                    '${widget.currencySymbol}${threshold.toStringAsFixed(2)}',
                    style: TextStyle(
                      color: textColor,
                      fontWeight: widget.currentValue == threshold
                          ? FontWeight.bold
                          : FontWeight.normal,
                    ),
                  ),
                  value: threshold,
                  groupValue: _isStandardOption(widget.currentValue)
                      ? widget.currentValue
                      : null,
                  activeColor: primaryColor,
                  onChanged: (value) {
                    if (value != null) {
                      widget.onChanged(value);
                    }
                  },
                  dense: true,
                  contentPadding: const EdgeInsets.symmetric(horizontal: 8),
                ),
              ),
            // Custom option
            SizedBox(
              width: MediaQuery.of(context).size.width *
                  0.4, // Approximately half the width
              child: RadioListTile<double>(
                title: Text(
                  'Custom...',
                  style: TextStyle(
                    color: textColor,
                    fontWeight: !_isStandardOption(widget.currentValue)
                        ? FontWeight.bold
                        : FontWeight.normal,
                  ),
                ),
                value: RadioAlertThresholdSelector.customOptionValue,
                groupValue: _isStandardOption(widget.currentValue)
                    ? null
                    : RadioAlertThresholdSelector.customOptionValue,
                activeColor: primaryColor,
                onChanged: (_) {
                  _showCustomThresholdDialog();
                },
                dense: true,
                contentPadding: const EdgeInsets.symmetric(horizontal: 8),
              ),
            ),
          ],
        ),

        if (widget.showHelperText) ...[
          const SizedBox(height: 8),
          Text(
            'Tip: Set this to the amount you typically top up with to get reminders at the right time.',
            style: TextStyle(
              fontSize: 12,
              fontStyle: FontStyle.italic,
              color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
            ),
          ),
        ],

        if (widget.errorText != null) ...[
          const SizedBox(height: 8),
          Text(
            widget.errorText!,
            style: TextStyle(color: Theme.of(context).colorScheme.error),
          ),
        ],
      ],
    );
  }
}
