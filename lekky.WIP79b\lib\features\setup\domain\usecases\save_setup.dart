// File: lib/features/setup/domain/usecases/save_setup.dart
import '../../../../core/data/repositories/settings_repository.dart';
import '../models/setup_config.dart';

/// Use case for saving setup configuration
class SaveSetup {
  final SettingsRepository _repository;

  SaveSetup(this._repository);

  /// Execute the use case
  Future<void> execute(SetupConfig config) async {
    // Save all settings at once
    await _repository.setMultipleSettings({
      'meter_unit': config.meterUnit,
      'alert_threshold': config.alertThreshold,
      'days_in_advance': config.daysInAdvance,
      'date_format': config.dateFormat,
      'date_info': config.dateInfo,
      'notifications_enabled': config.notificationsEnabled,
      'setup_completed': true,
      'initial_credit': config.initialMeterCredit,
    });
  }
}
