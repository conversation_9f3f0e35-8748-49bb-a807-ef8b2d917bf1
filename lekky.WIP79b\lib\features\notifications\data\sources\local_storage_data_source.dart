// File: lib/features/notifications/data/sources/local_storage_data_source.dart

import 'package:flutter/material.dart';
import '../../domain/models/notification_model.dart';
import '../../domain/models/reminder_time_model.dart';
import '../../domain/models/timezone_aware_reminder_time.dart';

/// Interface for local storage data sources
abstract class LocalStorageDataSource {
  /// Initialize the data source
  Future<void> initialize();

  /// Get all notifications
  Future<List<NotificationModel>> getNotifications();

  /// Add a notification
  Future<void> addNotification(NotificationModel notification);

  /// Mark a notification as read
  Future<void> markAsRead(String id);

  /// Mark all notifications as read
  Future<void> markAllAsRead();

  /// Remove a notification
  Future<void> removeNotification(String id);

  /// Clear all notifications
  Future<void> clearAll();

  /// Check if notifications are enabled
  Future<bool> areNotificationsEnabled();

  /// Enable or disable notifications
  Future<void> setNotificationsEnabled(bool enabled);

  /// Check if meter reading reminders are enabled
  Future<bool> areMeterReadingRemindersEnabled();

  /// Enable or disable meter reading reminders
  Future<void> setMeterReadingRemindersEnabled(bool enabled);

  /// Get the meter reading reminder frequency in days
  Future<int> getMeterReadingReminderFrequency();

  /// Set the meter reading reminder frequency in days
  Future<void> setMeterReadingReminderFrequency(int days);

  /// Get the last date a meter reading reminder was shown
  Future<DateTime?> getLastMeterReadingReminderDate();

  /// Set the last date a meter reading reminder was shown
  Future<void> setLastMeterReadingReminderDate(DateTime date);

  /// Get the time of day for meter reading reminders
  Future<ReminderTimeModel> getMeterReadingReminderTime();

  /// Set the time of day for meter reading reminders
  Future<void> setMeterReadingReminderTime(ReminderTimeModel time);

  /// Get the timezone-aware reminder time for meter readings
  Future<TimezoneAwareReminderTime?> getTimezoneAwareReminderTime();

  /// Set the timezone-aware reminder time for meter readings
  Future<void> setTimezoneAwareReminderTime(TimezoneAwareReminderTime time);

  /// Save the next scheduled meter reading reminder date
  Future<void> saveNextMeterReadingReminderDate(DateTime date);

  /// Save the next scheduled meter reading reminder date with timezone info
  Future<void> saveNextMeterReadingReminderDateWithTimezone(
      DateTime date, String timezone, bool isDST);

  /// Get the next scheduled meter reading reminder date
  Future<DateTime?> getNextMeterReadingReminderDate();

  /// Get the timezone information for the next scheduled reminder
  Future<Map<String, dynamic>?> getNextMeterReadingReminderTimezoneInfo();
}
