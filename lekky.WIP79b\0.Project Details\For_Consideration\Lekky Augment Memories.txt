Augment Memories

Lekky Flutter app

# Project Details
- Project Path: D:\000.Workspace\lekky
- Focus Areas: Functionality, security, performance, user experience, modularity, robustness, and maintainability.
- Security Priorities: Add authorization checks and secure admin authentication
- Preferred Workflow: Document implementation plans in Markdown before editing the codebase
- The project should focus on restructuring and rewriting the Flutter codebase for modularity, robustness, and maintainability.

# Architecture and Code Structure
- The Lekky app should follow a modular architecture with files under 600 lines (ideal: <300)
- Separate UI, state, and business logic into folders: /screens/, /widgets/, /models/, /providers/, /services/, /utils/, and /theme/
- Use layered architecture (Presentation, Domain, Data layers) with feature-based modules
- Implement dependency injection to improve testability and replaceability
- Follow Single Responsibility Principle: each module serves one purpose

# UI/UX Guidelines
- Design Principles: Simplicity, clarity, consistent design, intuitive navigation, readable text, touch-friendly
- Theme: Consistent fonts, colors, shadows with standardized margins and padding
- Dark Mode: Toggle in Setup/Settings, use dark_mode_background.png, ensure text visibility
- Fixed bottom navigation bar that stays visible when keyboard appears
- Consistent button styles (green for "Save" functions)
- Validate input values and automatically unfocus input field on done

# App Flow and Features
- Startup flow: SplashScreen → WelcomePage → SetupPage → HomePage (SplashScreen only on cold start)
- Use SharedPreferences to track onboarding
- Implement notifications using flutter_local_notifications with BigTextStyleInformation
- PayPal Donations: Sandbox mode with preset/custom amounts and thank-you dialog
- Cost of Electric feature: Estimate monetary usage over different time periods
- Keep the Cost of Electric button on the Homepage.
- History Entry Editing: Reusable edit module with validation and visual inconsistency markers
- The default state of the edit toggle on the History page should be off when the page is loaded.

# Data Validation and Display
- Implement comprehensive validation with severity levels for meter readings and top-ups
- Track prepaid meter readings that must decrease over time
- Alert users of invalid entries through dialogs, snackbars, and visual indicators
- Restrict date selection to range between earliest and latest meter readings
- Improve table layouts by making tables wider with better alignment and vertical lines
- When edit mode is off and invalid entry is clicked, show dialog explaining the issue
- Ensure dialog boxes are properly formatted to prevent overflow errors

# Usage Calculation
- The app calculates short-term usage by dividing the difference between consecutive meter readings by days elapsed.
- The app calculates total average usage by dividing cumulative usage by total days since the first reading.

# When making changes to app
- ✨ UI & Layout Rules (Look Great)
- Use a centralized theme with shared colors, font sizes, paddings.
- Use responsive layouts that adapt to screen sizes using MediaQuery, LayoutBuilder, or Flexible.
- Split large UI into reusable custom widgets.
- Use animated widgets like AnimatedContainer and FadeTransition for smoother UX.
- Provide loading indicators for long operations.

- ⚙️ Architecture & Logic (Work Well)
- Separate logic from UI using a structured pattern (e.g., MVVM, MVC).
- Use state management like Provider or Riverpod for shared app state.
- Avoid duplicating code by extracting reusable widgets and helper methods.

- 💾 Data & Storage
- Structure models cleanly with fromJson and toJson methods if needed.
- Validate and sanitize all user input.
- Store persistent data using sqflite, hive, or SharedPreferences.

- 🧪 Testing & Debugging
- Keep core functions small and testable.
- Use Flutter DevTools to monitor performance and layout behavior.
- Fix layout overflows and warnings early.

- 🧼 Code Hygiene
- Follow Dart style guide using flutter analyze and dart format.
- Use null safety features correctly (?, !, late).
- Comment thoughtfully with clear doc comments where needed.
- Name files, variables, and functions meaningfully and clearly.



Augment Memories

Lekky Flutter app

#Project Overview

Project Path: D:\000.Workspace\lekky

Focus on restructuring Flutter codebase for modularity, robustness, maintainability, security, and user experience

Implement authorization checks and secure admin authentication

Document implementation plans in Markdown before editing code



#Architecture & Development Practices

Follow modular architecture with files under 300-600 lines

Use layered architecture (Presentation, Domain, Data) with feature-based modules

Organize code into /screens/, /widgets/, /models/, /providers/, /services/, /utils/, and /theme/

Implement dependency injection for improved testability

Follow Single Responsibility Principle for modules

Use state management (Provider/Riverpod) to separate UI from business logic

Structure models with fromJson/toJson methods where needed

Store persistent data using sqflite, hive, or SharedPreferences

Enforce Dart style guide with proper null safety usage (flutter analyze, dart format)



#App Flow & Core Features

Startup flow: SplashScreen → WelcomePage → SetupPage → HomePage (Splash only on cold start)

Track onboarding completion via SharedPreferences

Implement notifications using flutter_local_notifications with BigTextStyleInformation

Meter Tracking: record readings, top-ups, compute short/total averages

Cost of Electric: estimate costs for custom date ranges, show historic and projected costs

History Editing: reusable edit module with validation, visual inconsistency markers

PayPal Donations: sandbox mode with preset/custom amounts and thank-you dialog



#UI/UX Guidelines

Design for simplicity, clarity, and intuitive navigation

Use a centralized theme: shared colors, fonts, shadows, margins, and padding

Support Dark Mode toggle with dark_mode_background.png

Fixed bottom navigation bar stays visible with keyboard

Consistent button styles (green for Save, orange for Reset)

Validate inputs and auto-unfocus on done

Provide loading indicators for long operations

Use responsive layouts (MediaQuery, LayoutBuilder, Flexible)

Use animated widgets (AnimatedContainer, FadeTransition) for smooth UX



#Data Validation & Calculations

Validate meter readings and top-ups with severity-based visual cues

Ensure meter readings decrease over time; restrict date selection within recorded range

Calculate short-term usage: (Δ reading) / days elapsed

Calculate total average usage: (cumulative usage) / total days since first reading

Historic cost displays actual usage; top-ups separate; projected cost uses short averages

Allow negative cost only when inheriting starting credit



#Screen Continuity

Maintain consistent header and bottom nav across screens

Preserve state when navigating (e.g., filter selections, edit mode)

Ensure keyboard interactions do not hide critical controls

Animate transitions between related screens for context continuity



#Additional Considerations

Write unit and widget tests for critical logic and UI components

Profile performance with Flutter DevTools; eliminate layout overflows

Keep file sizes small and focused; refactor large widgets into reusable components

Prioritize accessibility: readable text sizes, sufficient contrast, touch target sizes

Log key events and errors for debugging and analytics
If not sure ask; never assume
Make the best code
