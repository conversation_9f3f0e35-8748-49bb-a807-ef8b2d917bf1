// File: lib/features/history/presentation/widgets/entry_detail_dialog.dart

import 'package:flutter/material.dart';
import '../../../../core/models/meter_entry.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/utils/date_time_utils.dart';
import '../../../../core/utils/dialog_button_styles.dart';
import '../controllers/history_controller.dart';
import '../../../../core/widgets/dialogs/entry_edit_dialog.dart';

/// A dialog that displays detailed information about a meter entry
class EntryDetailDialog extends StatelessWidget {
  final MeterEntry entry;
  final HistoryController controller;

  const EntryDetailDialog({
    Key? key,
    required this.entry,
    required this.controller,
  }) : super(key: key);

  /// Show the dialog
  static Future<void> show({
    required BuildContext context,
    required MeterEntry entry,
    required HistoryController controller,
  }) async {
    await showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        insetPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
        child: Container(
          constraints: BoxConstraints(
            maxWidth: MediaQuery.of(context).size.width * 0.9,
            maxHeight: MediaQuery.of(context).size.height * 0.8,
          ),
          child: EntryDetailDialog(
            entry: entry,
            controller: controller,
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final isTopUp = entry.amountToppedUp > 0;
    final isValid = controller.isEntryValid(entry.id ?? -1);
    final validationMessage =
        !isValid ? controller.getValidationErrorMessage(entry.id ?? -1) : null;
    final validationSeverity = controller.getValidationSeverity(entry.id ?? -1);

    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // Header
        Padding(
          padding: const EdgeInsets.fromLTRB(24, 16, 24, 0),
          child: Row(
            children: [
              Icon(
                isTopUp ? Icons.add_circle : Icons.remove_circle,
                color: isTopUp
                    ? AppColors.costTab
                    : (isDarkMode ? AppColors.primaryDark : AppColors.primary),
              ),
              const SizedBox(width: 10),
              Text(
                isTopUp ? 'Top-up Details' : 'Meter Reading Details',
                style: AppTextStyles.titleMedium.copyWith(
                  fontWeight: FontWeight.bold,
                  color: isTopUp
                      ? AppColors.costTab
                      : (isDarkMode
                          ? AppColors.primaryDark
                          : AppColors.primary),
                ),
              ),
              const Spacer(),
              IconButton(
                icon: Icon(
                  Icons.close,
                  color: isDarkMode ? Colors.white70 : AppColors.onSurface,
                ),
                onPressed: () => Navigator.of(context).pop(),
                tooltip: 'Close',
                constraints: const BoxConstraints(),
                padding: EdgeInsets.zero,
                iconSize: 20,
              ),
            ],
          ),
        ),

        // Content
        Padding(
          padding: const EdgeInsets.fromLTRB(24, 16, 24, 24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Entry type description
              Text(
                isTopUp
                    ? 'This entry represents a top-up amount added to your meter.'
                    : 'This entry represents a meter reading showing your remaining balance.',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: isDarkMode ? Colors.white : null,
                ),
              ),
              const SizedBox(height: 16),

              // Date and time
              _buildInfoRow(
                context,
                'Date and Time:',
                DateTimeUtils.formatDateWithTime(entry.timestamp),
                icon: Icons.calendar_today,
              ),
              const SizedBox(height: 12),

              // Value
              _buildInfoRow(
                context,
                isTopUp ? 'Amount Added:' : 'Meter Reading:',
                '${controller.meterUnit}${isTopUp ? entry.amountToppedUp.toStringAsFixed(2) : entry.reading.toStringAsFixed(2)}',
                icon: isTopUp ? Icons.add_circle_outline : Icons.speed,
                valueColor: isTopUp
                    ? AppColors.costTab
                    : (isDarkMode ? AppColors.primaryDark : AppColors.primary),
              ),

              // Show averages for meter readings
              if (!isTopUp) ...[
                const SizedBox(height: 12),
                if (entry.shortAverageAfterTopUp != null)
                  _buildInfoRow(
                    context,
                    'Recent Average:',
                    '${controller.meterUnit}${entry.shortAverageAfterTopUp!.toStringAsFixed(2)}/day',
                    icon: Icons.trending_up,
                  ),
                if (entry.shortAverageAfterTopUp != null)
                  const SizedBox(height: 12),
                if (entry.totalAverageUpToThisPoint != null)
                  _buildInfoRow(
                    context,
                    'Total Average:',
                    '${controller.meterUnit}${entry.totalAverageUpToThisPoint!.toStringAsFixed(2)}/day',
                    icon: Icons.show_chart,
                  ),
              ],

              // Validation warning if applicable
              if (!isValid) ...[
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: validationSeverity == 'error'
                        ? AppColors.error.withOpacity(0.1)
                        : AppColors.warning.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: validationSeverity == 'error'
                          ? AppColors.error
                          : AppColors.warning,
                      width: 1,
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        validationSeverity == 'error'
                            ? Icons.error_outline
                            : Icons.warning_amber_outlined,
                        color: validationSeverity == 'error'
                            ? AppColors.error
                            : AppColors.warning,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          validationMessage ?? 'Invalid entry',
                          style: AppTextStyles.bodySmall.copyWith(
                            color: validationSeverity == 'error'
                                ? AppColors.error
                                : AppColors.warning,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
        ),

        // Actions
        Padding(
          padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              // Edit Now button (positioned first)
              Expanded(
                child: ElevatedButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                    controller.toggleEditMode(); // Enable edit mode

                    // Get the parent context to access the history screen methods
                    final historyScreenContext = Navigator.of(context).context;

                    // Use Future.delayed to ensure the dialog is fully closed before opening the edit dialog
                    Future.delayed(Duration.zero, () {
                      // Find the _showEntryEditDialog method in the history screen
                      if (historyScreenContext.mounted) {
                        // This will open the edit dialog directly
                        EntryEditDialog.show(
                          context: historyScreenContext,
                          entry: entry,
                          controller: controller,
                          onSave: (updatedEntry) =>
                              controller.addEntry(updatedEntry),
                          onDelete: entry.id != null
                              ? (id) => controller.deleteEntry(id)
                              : null,
                        );
                      }
                    });
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.costTab,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                  child: const Text(
                    'Edit Entry',
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              // Close button with blue outline (positioned second)
              Expanded(
                child: OutlinedButton(
                  onPressed: () => Navigator.of(context).pop(),
                  style: OutlinedButton.styleFrom(
                    foregroundColor:
                        isDarkMode ? Colors.white : AppColors.primary,
                    side: BorderSide(
                      color: isDarkMode ? Colors.white70 : AppColors.primary,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                  child: Text(
                    'Close',
                    style: TextStyle(
                      color: isDarkMode ? Colors.white : AppColors.primary,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildInfoRow(BuildContext context, String label, String value,
      {IconData? icon, Color? valueColor}) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (icon != null) ...[
          Icon(icon,
              size: 18,
              color: isDarkMode ? Colors.white70 : AppColors.onSurfaceVariant),
          const SizedBox(width: 8),
        ],
        SizedBox(
          width: 120,
          child: Text(
            label,
            style: AppTextStyles.bodyMedium.copyWith(
              fontWeight: FontWeight.bold,
              color: isDarkMode ? Colors.white : null,
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: AppTextStyles.bodyMedium.copyWith(
              color: valueColor ?? (isDarkMode ? Colors.white : null),
              fontWeight:
                  valueColor != null ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        ),
      ],
    );
  }
}
