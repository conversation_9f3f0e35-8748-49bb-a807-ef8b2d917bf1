# Lekky App Color Palette

This document defines the complete color palette for the Lekky app in both light and dark modes. The colors are organized by their semantic roles to ensure consistent usage throughout the application.

## Tab and Screen Colors

| Screen | Primary Color | Gradient | Description |
|--------|--------------|----------|-------------|
| Home | `#49D941` (Green) | `[#49D941, #36D1DC]` | Vibrant green for the home screen |
| Meter | `#36D1DC` (Blue) | `[#36D1DC, #5B86E5]` | Blue for meter readings |
| Top-Up | `#FF9800` (Orange) | `[#FF9800, #FFB74D]` | Orange for top-ups |
| History | `#9C27B0` (Purple) | `[#9C27B0, #BA68C8]` | Darker purple for history |
| Cost | `#E65100` (Dark Orange) | `[#E65100, #FF8A65]` | Darker orange for cost |
| Settings | `#D8DEDB` (Light Grey) | `[#424242, #616161]` | Grey for settings |
| Setup | N/A | `[#424242, #616161]` | Dark grey for setup |
| Welcome | N/A | `[#003087, #0057B8]` | Primary blue for welcome screen |
| Primary Gradient | N/A | `[#003087, #0057B8]` | Primary gradient for various UI elements. |
| Secondary Gradient | N/A | `[#43E97B, #38F9D7]` | Secondary gradient for various UI elements. |
| Tertiary Gradient | N/A | `[#FF9800, #FFB74D]` | Tertiary gradient for various UI elements. |
| Meter Gradient | N/A | `[#36D1DC, #5B86E5]` | Meter gradient for various UI elements. |
| Top Up Gradient | N/A | `[#FF9800, #FFB74D]` | Top Up gradient for various UI elements. |
| History Gradient | N/A | `[#C5A8E7, #D4BFF0]` | History gradient for various UI elements. |
| Settings Gradient | N/A | `[#D8DEDB, #E5E9E7]` | Settings gradient for various UI elements. |

## Light Mode

### Text

| Role | Hex Code | Description |
|------|----------|-------------|
| `--color-text-primary` | `#212121` | Primary text color for headings and body text. Used for most text content. |
| `--color-text-secondary` | `#757575` | Secondary text color for less important information, subtitles, and hints. |
| `--color-text-disabled` | `#BDBDBD` | Used for disabled text elements. Should only be applied to non-interactive elements. |
| `--color-text-link` | `#1976D2` | Used for links and interactive text elements. |
| `--color-text-error` | `#D32F2F` | Used for error messages and validation text. |
| `--color-text-success` | `#388E3C` | Used for success messages and confirmations. |
| `--color-text-on-primary` | `#FFFFFF` | Text color used on primary colored backgrounds. |
| `--color-text-on-accent` | `#FFFFFF` | Text color used on accent colored backgrounds. |

### Backgrounds

| Role | Hex Code | Description |
|------|----------|-------------|
| `--color-bg-app` | `#F5F5F5` | Main application background color. Used for the overall app background. |
| `--color-bg-card` | `#FFFFFF` | Used for card backgrounds, providing subtle elevation against the app background. |
| `--color-bg-surface` | `#FFFFFF` | Used for surface elements like dialogs, sheets, and menus. |
| `--color-bg-input` | `#FFFFFF` | Background color for input fields and form elements. |
| `--color-bg-overlay` | `rgba(0, 0, 0, 0.5)` | Semi-transparent overlay for modals and dialogs. |
| `--color-bg-disabled` | `#F5F5F5` | Background color for disabled elements and controls. |
| `--color-bg-hover` | `rgba(0, 0, 0, 0.04)` | Subtle hover state background for interactive elements. |
| `--color-bg-selected` | `rgba(25, 118, 210, 0.08)` | Background color for selected items or active states. |

### Other

| Role | Hex Code | Description |
|------|----------|-------------|
| `--color-primary` | `#003087` | Primary brand color used for main actions, buttons, and key UI elements. |
| `--color-secondary` | `#43E97B` | Secondary color for accents and secondary UI elements. |
| `--color-tertiary` | `#FF9800` | Tertiary color for highlights and less prominent UI elements. |
| `--color-error` | `#BA1A1A` | Used for error states, destructive actions, and critical alerts. |
| `--color-background` | `#F8F9FA` | Used for main app background. |
| `--color-surface` | `#FFFFFF` | Used for cards, dialogs, and other surface elements. |
| `--color-on-primary` | `#FFFFFF` | Text color used on primary colored backgrounds. |
| `--color-on-secondary` | `#000000` | Text color used on secondary colored backgrounds. |
| `--color-on-tertiary` | `#000000` | Text color used on tertiary colored backgrounds. |
| `--color-on-error` | `#FFFFFF` | Text color used on error colored backgrounds. |
| `--color-on-background` | `#1C1B1F` | Text color used on background. |
| `--color-on-surface` | `#1C1B1F` | Text color used on surface elements. |
| `--color-surface-variant` | `#E7E0EC` | Used for surfaces with a subtle variation from the main surface. |
| `--color-outline` | `#79747E` | Used for borders, dividers, and separators throughout the interface. |
| `--color-success` | `#4CAF50` | Used for success states, confirmations, and positive actions. |
| `--color-warning` | `#FFC107` | Used for warnings and cautionary messages. |
| `--color-info` | `#2196F3` | Used for informational messages and neutral notifications. |
| `--color-shadow` | `rgba(0, 0, 0, 0.1)` | Used for shadows to create elevation and depth in the interface. |
| `--color-semi-transparent-white` | `rgba(255, 255, 255, 0.7)` | Semi-transparent white color. |
| `--color-semi-transparent-black` | `rgba(0, 0, 0, 0.7)` | Semi-transparent black color. |
| `--color-card-background` | `rgba(255, 255, 255, 0.85)` | Background color for cards. |
| `--color-surface-container` | `#EAE0EC` | Used for container surfaces. |
| `--color-input-text` | `#212121` | Dark gray for input text in light mode. |
| `--color-input-label` | `#212121` | Dark gray for input labels in light mode. |
| `--color-input-hint` | `#757575` | Medium gray for input hints in light mode. |
| `--color-input-border` | `#757575` | Medium gray for input borders in light mode. |
| `--color-input-focused-border` | `#0288D1` | Blue for focused input borders in light mode. |
| `--color-date-picker-text` | `#212121` | Dark gray for date picker text in light mode. |

## Dark Mode

### Text

| Role | Hex Code | Description |
|------|----------|-------------|
| `--color-text-primary-dark` | `#FFFFFF` | Primary text color for headings and body text in dark mode. |
| `--color-text-secondary-dark` | `#B0B0B0` | Secondary text color for less important information in dark mode. |
| `--color-text-disabled-dark` | `#757575` | Used for disabled text elements in dark mode. Should only be applied to non-interactive elements. |
| `--color-text-link-dark` | `#90CAF9` | Used for links and interactive text elements in dark mode. |
| `--color-text-error-dark` | `#EF9A9A` | Used for error messages in dark mode. |
| `--color-text-success-dark` | `#A5D6A7` | Used for success messages in dark mode. |
| `--color-text-on-primary-dark` | `#000000` | Text color used on primary colored backgrounds in dark mode. |
| `--color-text-on-accent-dark` | `#000000` | Text color used on accent colored backgrounds in dark mode. |

### Backgrounds

| Role | Hex Code | Description |
|------|----------|-------------|
| `--color-bg-app-dark` | `#1C1B1F` | Main application background color in dark mode.  |
| `--color-bg-card-dark` | `#2D2D2D` | Used for card backgrounds in dark mode. |
| `--color-bg-surface-dark` | `#2D2D2D` | Used for surface elements in dark mode. |
| `--color-bg-input-dark` | `#2A2A2A` | Background color for input fields and form elements in dark mode. |
| `--color-bg-overlay-dark` | `rgba(0, 0, 0, 0.7)` | Semi-transparent overlay for modals and dialogs in dark mode. |
| `--color-bg-disabled-dark` | `#2A2A2A` | Background color for disabled elements and controls in dark mode. |
| `--color-bg-hover-dark` | `rgba(255, 255, 255, 0.08)` | Subtle hover state background for interactive elements in dark mode. |
| `--color-bg-selected-dark` | `rgba(64, 196, 255, 0.16)` | Background color for selected items or active states in dark mode. |

### Other

| Role | Hex Code | Description |
|------|----------|-------------|
| `--color-primary-dark` | `#4D82D6` | Primary brand color in dark mode. |
| `--color-secondary-dark` | `#43E97B` | Secondary color for accents and secondary UI elements in dark mode. |
| `--color-tertiary-dark` | `#FFB74D` | Tertiary color for highlights and less prominent UI elements in dark mode. |
| `--color-error-dark` | `#FFB4AB` | Used for error states in dark mode. |
| `--color-background-dark` | `#1C1B1F` | Used for main app background in dark mode. |
| `--color-surface-dark` | `#2D2D2D` | Used for cards, dialogs, and other surface elements in dark mode. |
| `--color-on-primary-dark` | `#000000` | Text color used on primary colored backgrounds in dark mode. |
| `--color-on-secondary-dark` | `#000000` | Text color used on secondary colored backgrounds in dark mode. |
| `--color-on-tertiary-dark` | `#000000` | Text color used on tertiary colored backgrounds in dark mode. |
| `--color-on-error-dark` | `#000000` | Text color used on error colored backgrounds in dark mode. |
| `--color-on-background-dark` | `#E6E1E5` | Text color used on background in dark mode. |
| `--color-on-surface-dark` | `#E6E1E5` | Text color used on surface elements in dark mode. |
| `--color-surface-variant-dark` | `#49454F` | Used for surface elements in dark mode with a subtle variation. |
| `--color-outline-dark` | `#938F99` | Used for borders, dividers, and separators throughout the interface in dark mode. |
| `--color-success-dark` | `#66BB6A` | Used for success states in dark mode. |
| `--color-warning-dark` | `#FFD54F` | Used for warnings in dark mode. |
| `--color-info-dark` | `#64B5F6` | Used for informational messages in dark mode. |
| `--color-shadow-dark` | `rgba(0, 0, 0, 0.3)` | Used for shadows in dark mode. |
| `--color-last-reading-label-dark` | `#90CAF9` | Light blue for better contrast. |
| `--color-meter-total-label-dark` | `#FFD54F` | Light amber for better contrast. |
| `--color-value-text-dark` | `#E1F5FE` | Very light blue for values. |
| `--color-primary-text-dark` | `#81D4FA` | Light blue for primary text. |
| `--color-card-background-dark` | `rgba(45, 45, 45, 0.85)` | Dark background color for cards. |
| `--color-surface-container-dark` | `#3A3A3A` | Dark surface container color. |
| `--color-input-text-dark` | `#FFFFFF` | White for input text in dark mode. |
| `--color-input-label-dark` | `#FFFFFF` | White for input labels in dark mode. |
| `--color-input-hint-dark` | `#B0B0B0` | Light gray for input hints in dark mode. |
| `--color-input-border-dark` | `#757575` | Medium gray for input borders in dark mode. |
| `--color-input-focused-border-dark` | `#42A5F5` | Light blue for focused input borders in dark mode. |
| `--color-date-picker-text-dark` | `#FFFFFF` | White for date picker text in dark mode. |
| `--color-table-header-light` | `#E1E1E1` | Light gray for table headers in light mode. |
| `--color-table-header-dark` | `#303030` | Dark gray for table headers in dark mode. |
| `--color-table-date-text-light` | `#212121` | Dark gray for date text in light mode. |
| `--color-table-date-text-dark` | `#E6E1E5` | Light gray for date text in dark mode. |
| `--color-table-average-text-light` | `rgba(0, 0, 0, 0.87)` | Dark gray for average values in light mode. |
| `--color-table-average-text-dark` | `#E6E1E5` | Light gray for average values in dark mode. |
| `--color-table-header-text-light` | `#0288D1` | Blue for header text in light mode. |
| `--color-table-header-text-dark` | `#42A5F5` | Light blue for header text in dark mode. |

## Usage Guidelines

1.  Always use the semantic color roles rather than hardcoded hex values.
2.  Ensure sufficient contrast between text and background colors (minimum 4.5:1 for normal text, 3:1 for large text).
3.  Use primary colors for main actions and key UI elements.
4.  Use accent colors sparingly to highlight important elements or calls to action.
5.  For dark mode, automatically switch to the corresponding dark mode color roles.
6.  Maintain consistent use of colors for similar UI elements and states throughout the app.
7.  Use success, warning, and error colors consistently for their respective states.
8.  For text colors, use `context.textColor` or `context.secondaryTextColor` from the context extensions to ensure proper dark mode support.
9.  When using text styles from `AppTextStyles`, always apply `.copyWith(color: context.textColor)` to ensure proper dark mode support.
10. In Setup screens, ensure all headings and text use white color in dark mode for better visibility.
