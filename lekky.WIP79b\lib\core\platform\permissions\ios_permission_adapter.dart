// File: lib/core/platform/permissions/ios_permission_adapter.dart

import 'dart:io';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/services.dart';
import 'permission_adapter.dart';

/// iOS implementation of the PermissionAdapter
class IOSPermissionAdapter implements PermissionAdapter {
  final MethodChannel _channel =
      const MethodChannel('com.roolekky/permissions');
  final List<void Function(PermissionStatus)> _listeners = [];
  SharedPreferences? _prefs;

  static const String _lastPermissionCheckKey = 'last_permission_check_time';
  static const String _permissionRequestCountKey =
      'ios_permission_request_count';
  static const String _permissionStatusKey = 'ios_permission_status';

  @override
  Future<void> initialize() async {
    _prefs = await SharedPreferences.getInstance();

    // Setup platform callback handler
    _channel.setMethodCallHandler((call) async {
      if (call.method == 'onPermissionStatusChanged') {
        final statusValue = call.arguments['status'] as int;
        final status = _intToPermissionStatus(statusValue);

        // Save the current status
        await _prefs?.setInt(_permissionStatusKey, statusValue);

        _notifyListeners(status);
      }
      return null;
    });

    // Register for notification settings changes
    try {
      await _channel
          .invokeMethod<void>('registerForNotificationSettingsChanges');
    } catch (e) {
      debugPrint('Error registering for notification settings changes: $e');
    }
  }

  @override
  Future<PermissionStatus> checkPermission() async {
    try {
      final result =
          await _channel.invokeMethod<int>('checkNotificationPermission');
      final status = _intToPermissionStatus(result ?? -1);

      // Save the current status
      if (result != null) {
        await _prefs?.setInt(_permissionStatusKey, result);
      }

      return status;
    } catch (e) {
      debugPrint('Error checking notification permission: $e');

      // Try to get the cached status
      final cachedStatus = _prefs?.getInt(_permissionStatusKey) ?? -1;
      return _intToPermissionStatus(cachedStatus);
    }
  }

  @override
  Future<bool> requestPermission({PermissionCallback? callback}) async {
    try {
      // Check if we've already requested permission
      final requestCount = _prefs?.getInt(_permissionRequestCountKey) ?? 0;

      // Show educational UI before requesting permission if this is not the first request
      if (requestCount > 0) {
        final shouldProceed = await showPermissionEducationUI();
        if (!shouldProceed) {
          if (callback != null) {
            callback(false);
          }
          return false;
        }
      }

      // Increment request count
      await _prefs?.setInt(_permissionRequestCountKey, requestCount + 1);

      // Request permission
      final result =
          await _channel.invokeMethod<bool>('requestNotificationPermission');
      final isGranted = result ?? false;

      // Save the time we checked
      if (isGranted) {
        await savePermissionCheckTime();
      }

      if (callback != null) {
        callback(isGranted);
      }

      return isGranted;
    } catch (e) {
      debugPrint('Error requesting notification permission: $e');
      if (callback != null) {
        callback(false);
      }
      return false;
    }
  }

  @override
  Future<bool> isPermanentlyDenied() async {
    // iOS doesn't have a concept of permanently denied permissions
    // But we can check if the user has denied the permission multiple times
    final requestCount = _prefs?.getInt(_permissionRequestCountKey) ?? 0;
    final status = await checkPermission();

    // If the user has denied the permission multiple times, we can consider it as permanently denied
    return status == PermissionStatus.denied && requestCount >= 2;
  }

  @override
  Future<bool> openAppSettings() async {
    try {
      final result = await _channel.invokeMethod<bool>('openAppSettings');
      return result ?? false;
    } catch (e) {
      debugPrint('Error opening app settings: $e');
      return false;
    }
  }

  @override
  Future<DateTime?> getLastPermissionCheckTime() async {
    final timestamp = _prefs?.getInt(_lastPermissionCheckKey);
    if (timestamp == null) {
      return null;
    }
    return DateTime.fromMillisecondsSinceEpoch(timestamp);
  }

  @override
  Future<void> savePermissionCheckTime() async {
    final now = DateTime.now().millisecondsSinceEpoch;
    await _prefs?.setInt(_lastPermissionCheckKey, now);
  }

  @override
  String getPermissionStatusString(PermissionStatus status) {
    switch (status) {
      case PermissionStatus.granted:
        return 'Granted';
      case PermissionStatus.denied:
        return 'Denied';
      case PermissionStatus.permanentlyDenied:
        return 'Denied in Settings';
      case PermissionStatus.restricted:
        return 'Restricted by Parental Controls';
      case PermissionStatus.limited:
        return 'Limited Access';
      case PermissionStatus.unknown:
        return 'Unknown';
    }
  }

  @override
  Future<bool> showPermissionEducationUI() async {
    // This would be implemented in the UI layer
    // For now, we'll just return true to indicate the user wants to proceed
    // In a real implementation, this would show a dialog explaining why the permission is needed
    return true;
  }

  @override
  Future<void> showPermissionDeniedAlternativesUI() async {
    // This would be implemented in the UI layer
    // For now, we'll just do nothing
    // In a real implementation, this would show a dialog with alternative options
  }

  @override
  void registerPermissionStatusListener(
      void Function(PermissionStatus) listener) {
    _listeners.add(listener);
  }

  @override
  void unregisterPermissionStatusListener(
      void Function(PermissionStatus) listener) {
    _listeners.remove(listener);
  }

  @override
  Future<bool> shouldShowRequestPermissionRationale() async {
    // iOS doesn't have an API for this
    // We'll use the request count to determine if we should show rationale
    final requestCount = _prefs?.getInt(_permissionRequestCountKey) ?? 0;
    return requestCount > 0;
  }

  @override
  Future<PermissionStatus> getSystemPermissionStatus() async {
    return await checkPermission();
  }

  // Helper method to convert int to PermissionStatus
  PermissionStatus _intToPermissionStatus(int value) {
    switch (value) {
      case 0:
        return PermissionStatus.granted;
      case 1:
        return PermissionStatus.denied;
      case 2:
        return PermissionStatus.permanentlyDenied;
      case 3:
        return PermissionStatus.restricted;
      case 4:
        return PermissionStatus.limited;
      default:
        return PermissionStatus.unknown;
    }
  }

  // Helper method to notify listeners of permission status changes
  void _notifyListeners(PermissionStatus status) {
    for (final listener in _listeners) {
      listener(status);
    }
  }
}
