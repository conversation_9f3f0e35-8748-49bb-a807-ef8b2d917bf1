# Lekky App - Updated Modular Architecture Plan (2025)

---

## 1. Project Overview

Lekky is a modular, offline-first Flutter application that enables users to track and manage their electricity meter readings and costs. It focuses on scalability, clean architecture, performance, and excellent user experience.

---

## 2. Key Upgrades to Architecture

- **Adopt Riverpod**: Full switch to Riverpod for dependency injection and state management.
- **Core Environment Setup**: Central configuration of app environments (dev, staging, production).
- **Shared L10n/i18n Layer**: Global localization setup integrated with Riverpod.
- **Blueprint Diagram**: Quick visual map provided.

---

## 3. Summary of Improvements

### Technical Architecture Upgrades
- **Riverpod** for scalable, testable, and decoupled state management.
- **Environment Setup** for easy switching of config (dev, prod, etc.).
- **Centralized Localization** to prepare for multilingual support.

### Organizational Benefits
- Cleaner dependencies
- Faster environment switching
- Easier onboarding for new developers
- Future-proof for feature expansions

---

## 4. Updated Project Structure

```
lib/
├── app.dart                 # App root (MaterialApp + global providers)
├── main.dart                # Entry point (bootstrapping environment)
├── app_scaffold.dart        # Main app scaffold (navigation, bottom nav)
│
├── core/                    # App-wide core functionality
│   ├── constants/           # Static constants (assets, keys, etc.)
│   ├── di/                  # Dependency injection (Riverpod providers)
│   ├── environment/         # Environment config (env.dart)
│   ├── errors/              # Error handling utilities
│   ├── logging/             # Logging setup
│   ├── navigation/          # App-wide navigation services
│   ├── storage/             # Local storage (preferences, DB wrappers)
│   ├── theme/               # Theming and design tokens
│   ├── utils/               # Utility classes and helpers
│   └── l10n/                # Localization (Riverpod driven)
│       ├── app_localizations.dart
│       ├── l10n_delegate.dart
│       └── supported_locales.dart
│
├── data/                    # Core models and repositories
│   ├── models/              # Data models (MeterEntry, Settings, etc.)
│   ├── repositories/        # Abstracted repositories
│   └── sources/             # DB sources, APIs, local/remote
│
├── services/                # Cross-cutting services
│   ├── analytics/
│   ├── backup/
│   ├── notifications/
│   ├── permissions/
│   └── preferences/
│
├── shared/                  # Shared UI, animations, extensions
│   ├── widgets/
│   ├── animations/
│   └── extensions/
│
└── features/                # Modular Feature Areas
    ├── splash/
    ├── welcome/
    ├── setup/
    ├── home/
    ├── meter/
    ├── history/
    ├── cost/
    ├── settings/
    ├── backup/
    ├── notifications/
    ├── help/
    └── future (ocr/, cloud_sync/, insights/)
```

---

## 5. Blueprint Architecture Diagram (Visual Map)

```plaintext
[main.dart]
    └──> [core/environment/] -- setup environment configs (dev/staging/prod)
    └──> [core/di/] -- init global Riverpod providers
        └──> [core/l10n/] -- Riverpod L10n
            └──> Supported Locales

    └──> [app.dart] -> MaterialApp(theme, router, localizations)

        └──> [shared/widgets/]
        └──> [core/navigation/]

        └──> [features/]
            ├── splash/ (SplashScreen)
            ├── welcome/ (Onboarding)
            ├── setup/ (Initial meter setup)
            ├── home/ (Home dashboard)
            ├── meter/ (Meter management)
            ├── history/ (History views)
            ├── cost/ (Cost calculators)
            ├── settings/ (Preferences and settings)
            ├── backup/ (Backup & restore)
            ├── notifications/ (Alerts management)
            └── help/ (Help and FAQs)

[services/] -- Analytics, Notifications, Preferences, Backup
[data/] -- Models, Repositories, Sources
```

---

## 6. State Management

- **Riverpod** for all providers
- Feature-level `Provider`, `Notifier`, or `AsyncNotifier`
- Scoped providers inside feature modules
- Global app providers for things like theme, language, user settings

---

## 7. Dependency Injection (Riverpod)

- Centralized in `core/di/`
- Use `Provider`, `StateNotifierProvider`, `AsyncNotifierProvider`
- Lazy-load heavy dependencies
- Environment setup (`Environment.instance`) accessible via providers

---

## 8. Localization (L10n) with Riverpod

- Localizations powered via a `LocalizationProvider`
- Switch languages at runtime without full restart
- `l10n/` holds `.arb` translation files and delegates

---

## 9. Future Scalability

- OCR Meter Readings, Cloud Sync, Insights ready via new feature modules
- Add or remove features without touching core infrastructure
- Prepared for multi-platform expansion (iOS, Web)

---

🎨 10. Visual Consistency & Design Tokens Plan
Objective:
Ensure that all visual elements (typography, colors, icons, spacing) across the app are unified, scalable, and maintainable.

📄 Centralize Styles
Theme Tokens:
Create a core/theme/tokens/ folder for all design tokens:

colors.dart → Primary, Secondary, Background, Error, etc.

text_styles.dart → Heading1, Heading2, Body, Caption, Button Text

spacings.dart → Standard paddings, margins

border_radius.dart → Consistent radii for buttons/cards

elevation.dart → Standardized elevations for shadows

App Theme (theme.dart):

Combines tokens into a final ThemeData

Light & Dark mode ready

📝 Typography (Text Consistency)

Element	Style
App Titles	Heading1
Section Titles	Heading2
Standard Body	BodyText1
Small Descriptions	Caption
Buttons	ButtonText
All sourced from text_styles.dart.

🎨 Color Palette

Purpose	Color Token
Primary Accent	LekkyColors.primary
Secondary Accent	LekkyColors.secondary
Background Light	LekkyColors.backgroundLight
Background Dark	LekkyColors.backgroundDark
Error/Alert	LekkyColors.error
🔥 Icons Standardization
Place all icons inside assets/icons/

Name icons consistently (e.g., icon_meter.svg, icon_history.svg)

Wrap them via an IconAssets utility class for easier access.