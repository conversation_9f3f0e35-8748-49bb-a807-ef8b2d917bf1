// File: lib/core/settings/widgets/date_format_selector.dart
import 'package:flutter/material.dart';
import '../../theme/app_text_styles.dart';
import '../../utils/date_time_utils.dart';
import '../../widgets/app_card.dart';
import '../../widgets/setting_dialog.dart';

/// A shared widget for selecting date format
/// Can be used in both Setup and Settings screens
class DateFormatSelector extends StatelessWidget {
  final String currentValue;
  final Function(String) onChanged;
  final bool useDialog;
  final bool showCard;

  const DateFormatSelector({
    Key? key,
    required this.currentValue,
    required this.onChanged,
    this.useDialog = false,
    this.showCard = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (useDialog) {
      return ListTile(
        title: const Text('Date Format'),
        subtitle: Text(currentValue),
        leading: const Icon(Icons.date_range),
        trailing: const Icon(Icons.chevron_right),
        onTap: () => _showDateFormatDialog(context),
      );
    }

    final content = Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Date Format',
          style: AppTextStyles.titleMedium,
        ),
        const SizedBox(height: 8),
        Text(
          'Select how dates will be displayed',
          style: AppTextStyles.bodyMedium,
        ),
        const SizedBox(height: 16),
        _buildDateFormatOptions(context),
      ],
    );

    if (showCard) {
      return AppCard(
        padding: const EdgeInsets.all(16),
        child: content,
      );
    }

    return content;
  }

  Widget _buildDateFormatOptions(BuildContext context) {
    final now = DateTime.now();

    return Column(
      children: [
        _buildFormatOption(
          'DD-MM-YYYY',
          DateTimeUtils.formatDate(now, 'dd-MM-yyyy'),
          context,
        ),
        _buildFormatOption(
          'MM-DD-YYYY',
          DateTimeUtils.formatDate(now, 'MM-dd-yyyy'),
          context,
        ),
        _buildFormatOption(
          'YYYY-MM-DD',
          DateTimeUtils.formatDate(now, 'yyyy-MM-dd'),
          context,
        ),
      ],
    );
  }

  Widget _buildFormatOption(
      String format, String example, BuildContext context) {
    final isSelected = currentValue == format;
    final theme = Theme.of(context);

    return RadioListTile<String>(
      title: Text(format),
      subtitle: Text('Example: $example'),
      value: format,
      groupValue: currentValue,
      onChanged: (value) {
        if (value != null) {
          onChanged(value);
          if (useDialog) {
            Navigator.of(context).pop();
          }
        }
      },
      activeColor: theme.colorScheme.primary,
      selected: isSelected,
      dense: true,
    );
  }

  /// Show a dialog for selecting the date format
  void _showDateFormatDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => SettingDialog(
        title: 'Date Format',
        subtitle: 'Select how dates will be displayed',
        content: DateFormatSelector(
          currentValue: currentValue,
          onChanged: (value) {
            onChanged(value);
            Navigator.of(context).pop();
          },
          useDialog: false,
          showCard: false,
        ),
      ),
    );
  }

  /// Static method to show a date format selection dialog
  static Future<void> showDateFormatDialog(
    BuildContext context,
    String currentValue,
    Function(String) onChanged,
  ) async {
    return showDialog(
      context: context,
      builder: (context) => SettingDialog(
        title: 'Date Format',
        subtitle: 'Select how dates will be displayed',
        content: DateFormatSelector(
          currentValue: currentValue,
          onChanged: (value) {
            onChanged(value);
            Navigator.of(context).pop();
          },
          useDialog: false,
          showCard: false,
        ),
      ),
    );
  }
}
