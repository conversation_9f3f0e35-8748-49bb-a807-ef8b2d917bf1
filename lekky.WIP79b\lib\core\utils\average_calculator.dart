// File: lib/core/utils/average_calculator.dart
import '../models/meter_entry.dart';
import '../models/date_to_top_up_result.dart';
import 'cost_interval.dart';

/// Utility class for calculating electricity usage averages and cost with exact interval logic
class AverageCalculator {
  /// Calculates the Short-Term Usage per Day between a meter reading and the previous meter reading.
  static double calculateShortAverage(List<MeterEntry> entries, int index) {
    if (index <= 0 ||
        index >= entries.length ||
        entries[index].amountToppedUp > 0) {
      return 0.0;
    }
    // Find previous meter reading
    int prevIndex = index - 1;
    double sumTopUps = 0.0;
    // Sum any top-ups between the two readings
    for (int j = prevIndex + 1; j < index; j++) {
      sumTopUps += entries[j].amountToppedUp;
    }
    final prev = entries[prevIndex];
    final curr = entries[index];

    // Calculate time difference in seconds and convert to days with decimal precision
    final seconds = curr.timestamp.difference(prev.timestamp).inSeconds;
    final days = seconds / (24 * 60 * 60); // Convert seconds to days

    // Skip if time difference is too small (less than 1 hour)
    if (seconds < 3600) return 0.0; // 3600 seconds = 1 hour

    final usage = prev.amountLeft + sumTopUps - curr.amountLeft;
    return usage > 0 ? usage / days : 0.0;
  }

  /// Calculates the Total Average usage per day from the first reading up to upToIndex.
  static double calculateTotalAverage(List<MeterEntry> entries,
      [int upToIndex = -1]) {
    if (entries.length < 2) return 0.0;
    final end = upToIndex >= 0 && upToIndex < entries.length
        ? upToIndex
        : entries.length - 1;
    double totalUsage = 0.0;
    double totalDays = 0.0;
    for (int i = 1; i <= end; i++) {
      if (entries[i].amountToppedUp > 0) continue;
      // find previous reading index
      int prev = i - 1;
      while (prev >= 0 && entries[prev].amountToppedUp > 0) {
        prev--;
      }
      if (prev < 0) continue;
      // Calculate time difference in seconds and convert to days with decimal precision
      final seconds =
          entries[i].timestamp.difference(entries[prev].timestamp).inSeconds;
      final days = seconds / (24 * 60 * 60); // Convert seconds to days

      // Skip if time difference is too small (less than 1 hour)
      if (seconds < 3600) continue; // 3600 seconds = 1 hour

      double sumTopUps = 0.0;
      for (int j = prev + 1; j < i; j++) {
        sumTopUps += entries[j].amountToppedUp;
      }
      final usage =
          entries[prev].amountLeft + sumTopUps - entries[i].amountLeft;
      if (usage > 0) {
        totalUsage += usage;
        totalDays += days;
      }
    }
    return totalDays > 0 ? totalUsage / totalDays : 0.0;
  }

  /// Calculates cost using a modular approach with interval-based calculation
  static double calculateCost(
      List<MeterEntry> entries, DateTime startDate, DateTime endDate) {
    // Validate inputs
    if (entries.isEmpty || startDate.isAfter(endDate)) {
      return 0.0;
    }

    // Create a sorted copy of entries
    final sorted = List<MeterEntry>.from(entries)
      ..sort((a, b) => a.timestamp.compareTo(b.timestamp));

    // Extract meter readings and top-ups
    final readings = sorted.where((e) => e.amountToppedUp == 0).toList();
    final topUps = sorted.where((e) => e.amountToppedUp > 0).toList();

    // If we don't have at least 2 readings, we can't calculate rates
    if (readings.length < 2) {
      return 0.0;
    }

    // Calculate total average for future projections
    final totalAverage = calculateTotalAverage(sorted);

    // Get the last historical date
    final lastDate = sorted.last.timestamp;

    // Handle future-only calculations
    if (startDate.isAfter(lastDate)) {
      // For dates entirely in the future, use total average
      // Calculate time difference in seconds and convert to days with decimal precision
      final seconds = endDate.difference(startDate).inSeconds;
      final days = (seconds / (24 * 60 * 60)) +
          (1.0 /
              24.0); // Convert to days and add 1 hour to include both endpoints
      return totalAverage * days;
    }

    // Create a list of intervals between meter readings
    List<CostInterval> intervals = [];

    // Build intervals between consecutive meter readings
    for (int i = 0; i < readings.length - 1; i++) {
      final current = readings[i];
      final next = readings[i + 1];
      // Calculate time difference in seconds and convert to days with decimal precision
      final seconds = next.timestamp.difference(current.timestamp).inSeconds;
      final days = seconds / (24 * 60 * 60); // Convert seconds to days

      if (seconds < 3600) {
        continue; // Skip intervals less than 1 hour (3600 seconds)
      }

      // Find top-ups in this interval
      double sumTopUps = 0.0;
      for (var topUp in topUps) {
        if (topUp.timestamp.isAfter(current.timestamp) &&
            topUp.timestamp.isBefore(next.timestamp)) {
          sumTopUps += topUp.amountToppedUp;
        }
      }

      // Calculate usage and daily rate
      final usage = current.amountLeft - next.amountLeft + sumTopUps;
      final rate = usage > 0 ? usage / days : 0.0;

      // Create interval
      intervals.add(CostInterval(
        startDate: current.timestamp,
        endDate: next.timestamp,
        dailyRate: rate,
        days: days,
      ));
    }

    // Calculate cost for the requested date range
    double totalCost = 0.0;

    // Process each interval that overlaps with the requested date range
    for (var interval in intervals) {
      // Check if this interval overlaps with the requested date range
      if (interval.endDate.isBefore(startDate) ||
          interval.startDate.isAfter(endDate)) {
        continue; // No overlap
      }

      // Calculate overlap start and end
      final overlapStart = interval.startDate.isBefore(startDate)
          ? startDate
          : interval.startDate;
      final overlapEnd =
          interval.endDate.isAfter(endDate) ? endDate : interval.endDate;

      // Calculate time difference in seconds and convert to days with decimal precision
      final overlapSeconds = overlapEnd.difference(overlapStart).inSeconds;
      final overlapDays =
          overlapSeconds / (24 * 60 * 60); // Convert seconds to days

      // Skip if overlap is less than 1 hour
      if (overlapSeconds < 3600) {
        continue; // 3600 seconds = 1 hour
      }

      // Add cost for this interval
      totalCost += interval.dailyRate * overlapDays;
    }

    // Handle future projection if endDate is after the last reading
    if (endDate.isAfter(readings.last.timestamp)) {
      final futureStart = readings.last.timestamp;
      final futureEnd = endDate;
      // Calculate time difference in seconds and convert to days with decimal precision
      final futureSeconds = futureEnd.difference(futureStart).inSeconds;
      final futureDays =
          futureSeconds / (24 * 60 * 60); // Convert seconds to days

      if (futureSeconds >= 3600) {
        // Only add if at least 1 hour (3600 seconds)
        totalCost += totalAverage * futureDays;
      }
    }

    return totalCost;
  }

  /// Calculates the Short-Term Usage per Day for all meter readings (excluding the oldest one).
  /// This method updates the shortAverageAfterTopUp field in each MeterEntry object.
  static List<MeterEntry> calculateShortAveragesAfterTopUps(
      List<MeterEntry> entries) {
    if (entries.length < 2) return entries;

    // Create a copy of the entries list to avoid modifying the original
    final updatedEntries = List<MeterEntry>.from(entries);

    // Sort entries by timestamp to ensure chronological order
    updatedEntries.sort((a, b) => a.timestamp.compareTo(b.timestamp));

    // Find all meter readings (not top-ups)
    final meterReadings =
        updatedEntries.where((e) => e.amountToppedUp == 0).toList();

    // If we have fewer than 2 meter readings, we can't calculate any short averages
    if (meterReadings.length < 2) return updatedEntries;

    // Calculate short averages for all meter readings except the first one
    for (int i = 1; i < meterReadings.length; i++) {
      final currentReading = meterReadings[i];
      final previousReading = meterReadings[i - 1];

      // Calculate time difference in seconds and convert to days with decimal precision
      final seconds = currentReading.timestamp
          .difference(previousReading.timestamp)
          .inSeconds;
      final days = seconds / (24 * 60 * 60); // Convert seconds to days

      // Skip if time difference is too small (less than 1 hour)
      if (seconds < 3600) {
        continue; // 3600 seconds = 1 hour
      }

      // Find all top-ups between these two meter readings
      double sumOfTopUpsBetween = 0.0;

      // Find the indices of the current and previous meter readings in the original entries list
      final currentIndex = updatedEntries.indexWhere((e) =>
          e.id == currentReading.id && e.timestamp == currentReading.timestamp);
      final prevIndex = updatedEntries.indexWhere((e) =>
          e.id == previousReading.id &&
          e.timestamp == previousReading.timestamp);

      // Sum all top-ups between the previous meter reading and the current one
      for (int j = prevIndex + 1; j < currentIndex; j++) {
        if (updatedEntries[j].amountToppedUp > 0) {
          sumOfTopUpsBetween += updatedEntries[j].amountToppedUp;
        }
      }

      // Calculate usage: Previous Amount Left + Sum of Top-Ups Between - Current Amount Left
      final usage = previousReading.amountLeft +
          sumOfTopUpsBetween -
          currentReading.amountLeft;

      // Calculate daily average (or 0.0 if usage is negative)
      final dailyAverage = usage > 0 ? usage / days : 0.0;

      // Update the short average for the current meter reading
      final currentEntryIndex = updatedEntries.indexWhere((e) =>
          e.id == currentReading.id && e.timestamp == currentReading.timestamp);
      if (currentEntryIndex >= 0) {
        updatedEntries[currentEntryIndex] =
            updatedEntries[currentEntryIndex].copyWith(
          shortAverageAfterTopUp: dailyAverage,
        );
      }
    }

    // Ensure the first meter reading has null short average
    if (meterReadings.isNotEmpty) {
      final firstReading = meterReadings[0];
      final firstReadingIndex = updatedEntries.indexWhere((e) =>
          e.id == firstReading.id && e.timestamp == firstReading.timestamp);
      if (firstReadingIndex >= 0) {
        updatedEntries[firstReadingIndex] =
            updatedEntries[firstReadingIndex].copyWith(
          shortAverageAfterTopUp: null,
        );
      }
    }

    // Ensure all top-ups have null short average
    for (int i = 0; i < updatedEntries.length; i++) {
      if (updatedEntries[i].amountToppedUp > 0) {
        updatedEntries[i] = updatedEntries[i].copyWith(
          shortAverageAfterTopUp: null,
        );
      }
    }

    return updatedEntries;
  }

  /// Gets the most recent total average from the entries
  static double getMostRecentTotalAverage(List<MeterEntry> entries) {
    return calculateTotalAverage(entries);
  }

  /// Calculates the estimated date to top up based on the current meter total and average usage.
  ///
  /// Legacy method - maintained for backward compatibility
  static DateTime? calculateDateToTopUp(
      double meterTotal, double alertThreshold, double averageUsage) {
    if (averageUsage <= 0) return null;

    final daysUntilThreshold = (meterTotal - alertThreshold) / averageUsage;
    if (daysUntilThreshold <= 0) return null;

    return DateTime.now().add(Duration(days: daysUntilThreshold.round()));
  }

  /// Enhanced method that calculates the estimated date to top up with reading age consideration
  ///
  /// Returns a DateToTopUpResult that includes confidence level and additional context
  static DateToTopUpResult calculateDateToTopUpEnhanced({
    required double lastMeterReading,
    required double topUpsSinceLastReading,
    required DateTime lastReadingDate,
    required double averageUsage,
    required double alertThreshold,
  }) {
    // If average usage is zero or negative, we can't calculate a date
    if (averageUsage <= 0) {
      return DateToTopUpResult(
        date: null,
        confidenceLevel: ConfidenceLevel.low,
        isAlreadyBelowThreshold: false,
      );
    }

    // Calculate estimated current balance
    final now = DateTime.now();
    final secondsSinceLastReading = now.difference(lastReadingDate).inSeconds;
    final daysSinceLastReading = secondsSinceLastReading / (24 * 60 * 60);

    // If the reading is very recent, just use the actual reading
    final estimatedUsage = secondsSinceLastReading < 3600
        ? 0
        : daysSinceLastReading * averageUsage;
    final estimatedCurrentBalance =
        lastMeterReading + topUpsSinceLastReading - estimatedUsage;

    // Get confidence level based on reading age
    final confidenceLevel = _getConfidenceLevelForDays(daysSinceLastReading);

    // Check if already below threshold
    if (estimatedCurrentBalance <= alertThreshold) {
      return DateToTopUpResult(
        date: now,
        confidenceLevel: confidenceLevel,
        isAlreadyBelowThreshold: true,
      );
    }

    // Calculate days until threshold
    final remainingBalance = estimatedCurrentBalance - alertThreshold;
    final daysUntilThreshold = remainingBalance / averageUsage;

    // Calculate the date
    final dateToTopUp = now.add(Duration(days: daysUntilThreshold.round()));

    return DateToTopUpResult(
      date: dateToTopUp,
      confidenceLevel: confidenceLevel,
      isAlreadyBelowThreshold: false,
    );
  }

  /// Get confidence level based on days since last reading
  static ConfidenceLevel _getConfidenceLevelForDays(double days) {
    if (days < 3) {
      return ConfidenceLevel.high;
    } else if (days <= 7) {
      return ConfidenceLevel.medium;
    } else {
      return ConfidenceLevel.low;
    }
  }

  /// Calculates the projected current balance based on days since last reading and average usage.
  ///
  /// [lastMeterReading] The last recorded meter reading value
  /// [topUpsSinceLastReading] The sum of all top-ups since the last reading
  /// [lastReadingDate] The date of the last meter reading
  /// [averageUsage] The average daily usage
  ///
  /// Returns the projected current balance
  static double calculateProjectedBalance({
    required double lastMeterReading,
    required double topUpsSinceLastReading,
    required DateTime lastReadingDate,
    required double averageUsage,
  }) {
    // If average usage is zero or negative, just return the last reading plus top-ups
    if (averageUsage <= 0) {
      return lastMeterReading + topUpsSinceLastReading;
    }

    // Calculate time difference in seconds and convert to days with decimal precision
    final now = DateTime.now();
    final secondsSinceLastReading = now.difference(lastReadingDate).inSeconds;
    final daysSinceLastReading =
        secondsSinceLastReading / (24 * 60 * 60); // Convert seconds to days

    // If the reading was taken less than an hour ago, just return the last reading plus top-ups
    if (secondsSinceLastReading < 3600) {
      // 3600 seconds = 1 hour
      return lastMeterReading + topUpsSinceLastReading;
    }

    // Calculate estimated usage since last reading
    final estimatedUsage = daysSinceLastReading * averageUsage;

    // Calculate projected balance
    final projectedBalance =
        lastMeterReading + topUpsSinceLastReading - estimatedUsage;

    // Ensure we don't return a negative balance (minimum is 0)
    return projectedBalance > 0 ? projectedBalance : 0;
  }

  /// Calculates the days since the last meter reading with seconds precision
  static double calculateDaysSinceLastReading(DateTime lastReadingDate) {
    final now = DateTime.now();
    final seconds = now.difference(lastReadingDate).inSeconds;
    return seconds /
        (24 * 60 * 60); // Convert seconds to days with decimal precision
  }
}
