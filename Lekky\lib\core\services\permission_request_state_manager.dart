import 'package:shared_preferences/shared_preferences.dart';
import '../utils/logger.dart';

/// Manages global permission request state to prevent conflicts and track attempts
class PermissionRequestStateManager {
  static final PermissionRequestStateManager _instance =
      PermissionRequestStateManager._internal();

  factory PermissionRequestStateManager() => _instance;
  PermissionRequestStateManager._internal();

  // State tracking
  bool _isDialogShowing = false;
  String? _currentRequestType;
  final List<String> _requestQueue = [];

  // SharedPreferences keys
  static const String _retryCountPrefix = 'permission_retry_count_';
  static const String _dontAskAgainPrefix = 'permission_dont_ask_';
  static const String _lastRequestTimePrefix = 'permission_last_request_';

  /// Check if a permission dialog is currently showing
  bool get isDialogShowing => _isDialogShowing;

  /// Get current request type being processed
  String? get currentRequestType => _currentRequestType;

  /// Check if request queue has pending items
  bool get hasQueuedRequests => _requestQueue.isNotEmpty;

  /// Mark dialog as showing for specific request type
  void markDialogShowing(String requestType) {
    _isDialogShowing = true;
    _currentRequestType = requestType;
    Logger.info(
        'PermissionRequestStateManager: Dialog showing for $requestType');
  }

  /// Mark dialog as dismissed
  void markDialogDismissed() {
    final previousType = _currentRequestType;
    _isDialogShowing = false;
    _currentRequestType = null;
    Logger.info(
        'PermissionRequestStateManager: Dialog dismissed for $previousType');
  }

  /// Add request to queue if dialog is showing
  bool tryQueueRequest(String requestType) {
    if (_isDialogShowing) {
      if (!_requestQueue.contains(requestType)) {
        _requestQueue.add(requestType);
        Logger.info(
            'PermissionRequestStateManager: Queued request for $requestType');
      }
      return true; // Request was queued
    }
    return false; // No queue needed, can proceed immediately
  }

  /// Get next queued request
  String? getNextQueuedRequest() {
    if (_requestQueue.isNotEmpty) {
      final next = _requestQueue.removeAt(0);
      Logger.info(
          'PermissionRequestStateManager: Processing queued request for $next');
      return next;
    }
    return null;
  }

  /// Clear all queued requests
  void clearQueue() {
    if (_requestQueue.isNotEmpty) {
      Logger.info(
          'PermissionRequestStateManager: Clearing ${_requestQueue.length} queued requests');
      _requestQueue.clear();
    }
  }

  /// Get retry count for specific notification type
  Future<int> getRetryCount(String notificationType) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getInt('$_retryCountPrefix$notificationType') ?? 0;
    } catch (e) {
      Logger.error('Error getting retry count for $notificationType: $e');
      return 0;
    }
  }

  /// Increment retry count for specific notification type
  Future<void> incrementRetryCount(String notificationType) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final currentCount = await getRetryCount(notificationType);
      await prefs.setInt(
          '$_retryCountPrefix$notificationType', currentCount + 1);
      await prefs.setInt('$_lastRequestTimePrefix$notificationType',
          DateTime.now().millisecondsSinceEpoch);
      Logger.info(
          'PermissionRequestStateManager: Incremented retry count for $notificationType to ${currentCount + 1}');
    } catch (e) {
      Logger.error('Error incrementing retry count for $notificationType: $e');
    }
  }

  /// Check if user has chosen "don't ask again" for specific notification type
  Future<bool> isDontAskAgain(String notificationType) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool('$_dontAskAgainPrefix$notificationType') ?? false;
    } catch (e) {
      Logger.error('Error checking dont ask again for $notificationType: $e');
      return false;
    }
  }

  /// Set "don't ask again" preference for specific notification type
  Future<void> setDontAskAgain(String notificationType, bool value) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('$_dontAskAgainPrefix$notificationType', value);
      Logger.info(
          'PermissionRequestStateManager: Set dont ask again for $notificationType to $value');
    } catch (e) {
      Logger.error('Error setting dont ask again for $notificationType: $e');
    }
  }

  /// Check if should show "don't ask again" option (after 3 attempts)
  Future<bool> shouldShowDontAskAgain(String notificationType) async {
    final retryCount = await getRetryCount(notificationType);
    return retryCount >= 3;
  }

  /// Reset permission request data for specific notification type
  Future<void> resetPermissionData(String notificationType) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('$_retryCountPrefix$notificationType');
      await prefs.remove('$_dontAskAgainPrefix$notificationType');
      await prefs.remove('$_lastRequestTimePrefix$notificationType');
      Logger.info(
          'PermissionRequestStateManager: Reset permission data for $notificationType');
    } catch (e) {
      Logger.error('Error resetting permission data for $notificationType: $e');
    }
  }

  /// Reset all permission request data
  Future<void> resetAllPermissionData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys();

      for (final key in keys) {
        if (key.startsWith(_retryCountPrefix) ||
            key.startsWith(_dontAskAgainPrefix) ||
            key.startsWith(_lastRequestTimePrefix)) {
          await prefs.remove(key);
        }
      }

      Logger.info('PermissionRequestStateManager: Reset all permission data');
    } catch (e) {
      Logger.error('Error resetting all permission data: $e');
    }
  }

  /// Get summary of permission request state for debugging
  Future<Map<String, dynamic>> getPermissionSummary() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys();
      final summary = <String, dynamic>{};

      for (final key in keys) {
        if (key.startsWith(_retryCountPrefix) ||
            key.startsWith(_dontAskAgainPrefix) ||
            key.startsWith(_lastRequestTimePrefix)) {
          summary[key] = prefs.get(key);
        }
      }

      summary['current_dialog_showing'] = _isDialogShowing;
      summary['current_request_type'] = _currentRequestType;
      summary['queued_requests'] = List.from(_requestQueue);

      return summary;
    } catch (e) {
      Logger.error('Error getting permission summary: $e');
      return {};
    }
  }
}
