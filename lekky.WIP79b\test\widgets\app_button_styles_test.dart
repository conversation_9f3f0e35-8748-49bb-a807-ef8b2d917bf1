import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:lekky/utils/app_button_styles.dart';

void main() {
  testWidgets('AppButtonStyles.createPrimaryButton creates a button with correct properties',
      (WidgetTester tester) async {
    bool buttonPressed = false;
    
    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: Center(
            child: AppButtonStyles.createPrimaryButton(
              text: 'Test Button',
              onPressed: () {
                buttonPressed = true;
              },
              icon: Icons.check,
              backgroundColor: Colors.blue,
            ),
          ),
        ),
      ),
    );

    // Verify button exists with correct text
    expect(find.text('Test Button'), findsOneWidget);
    
    // Verify icon exists
    expect(find.byIcon(Icons.check), findsOneWidget);
    
    // Tap the button and verify callback is called
    await tester.tap(find.text('Test Button'));
    expect(buttonPressed, true);
  });

  testWidgets('AppButtonStyles.createSecondaryButton creates a button with correct properties',
      (WidgetTester tester) async {
    bool buttonPressed = false;
    
    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: Center(
            child: AppButtonStyles.createSecondaryButton(
              text: 'Secondary Button',
              onPressed: () {
                buttonPressed = true;
              },
              icon: Icons.settings,
              foregroundColor: Colors.green,
              borderColor: Colors.green,
            ),
          ),
        ),
      ),
    );

    // Verify button exists with correct text
    expect(find.text('Secondary Button'), findsOneWidget);
    
    // Verify icon exists
    expect(find.byIcon(Icons.settings), findsOneWidget);
    
    // Tap the button and verify callback is called
    await tester.tap(find.text('Secondary Button'));
    expect(buttonPressed, true);
  });

  testWidgets('AppButtonStyles.createTertiaryButton creates a button with correct properties',
      (WidgetTester tester) async {
    bool buttonPressed = false;
    
    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: Center(
            child: AppButtonStyles.createTertiaryButton(
              text: 'Tertiary Button',
              onPressed: () {
                buttonPressed = true;
              },
              icon: Icons.info,
              foregroundColor: Colors.orange,
            ),
          ),
        ),
      ),
    );

    // Verify button exists with correct text
    expect(find.text('Tertiary Button'), findsOneWidget);
    
    // Verify icon exists
    expect(find.byIcon(Icons.info), findsOneWidget);
    
    // Tap the button and verify callback is called
    await tester.tap(find.text('Tertiary Button'));
    expect(buttonPressed, true);
  });

  testWidgets('AppButtonStyles.createIconButton creates a button with correct properties',
      (WidgetTester tester) async {
    bool buttonPressed = false;
    
    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: Center(
            child: AppButtonStyles.createIconButton(
              icon: Icons.delete,
              onPressed: () {
                buttonPressed = true;
              },
              semanticLabel: 'Delete',
              foregroundColor: Colors.red,
            ),
          ),
        ),
      ),
    );
    
    // Verify icon exists
    expect(find.byIcon(Icons.delete), findsOneWidget);
    
    // Tap the button and verify callback is called
    await tester.tap(find.byIcon(Icons.delete));
    expect(buttonPressed, true);
  });

  testWidgets('AppButtonStyles.createPrimaryButton with loading state shows progress indicator',
      (WidgetTester tester) async {
    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: Center(
            child: AppButtonStyles.createPrimaryButton(
              text: 'Loading Button',
              onPressed: () {},
              loading: true,
            ),
          ),
        ),
      ),
    );

    // Verify text is not visible when loading
    expect(find.text('Loading Button'), findsNothing);
    
    // Verify progress indicator is shown
    expect(find.byType(CircularProgressIndicator), findsOneWidget);
  });
}
