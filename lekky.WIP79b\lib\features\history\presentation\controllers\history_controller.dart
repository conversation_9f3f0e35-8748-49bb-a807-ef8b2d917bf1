// File: lib/features/history/presentation/controllers/history_controller.dart
import 'dart:async';
import 'package:flutter/material.dart';
import '../../../../core/models/meter_entry.dart';
import '../../../../core/utils/date_time_utils.dart';
import '../../../../core/utils/event_bus.dart';
import '../../../../core/utils/logger.dart' show logger;
import '../../../../core/providers/notification_provider.dart';
import '../../data/history_repository.dart';
import '../../domain/models/history_filter.dart';
import '../../domain/models/validation_result.dart';
import '../../domain/models/related_validation_result.dart';

/// Controller for the history screen
class HistoryController extends ChangeNotifier {
  final HistoryRepository _repository;

  // State variables
  List<MeterEntry> _allEntries = [];
  List<MeterEntry> _filteredEntries = [];
  HistoryFilter _filter = const HistoryFilter();
  Map<int, ValidationResult> _validationResults = {};
  String _dateFormat = 'DD-MM-YYYY';
  bool _isLoading = true;
  bool _isEditMode = false;
  String _error = '';
  int _selectedEntryId = -1;
  int _currentPage = 0;
  final int _itemsPerPage = 15;

  // We'll get the currency from the SettingsProvider instead of storing it here

  // Getters
  List<MeterEntry> get allEntries => _allEntries;
  List<MeterEntry> get filteredEntries => _filteredEntries;
  HistoryFilter get filter => _filter;
  Map<int, ValidationResult> get validationResults => _validationResults;

  /// Debug method to print validation results
  void debugPrintValidationResults() {
    print('DEBUG: Validation Results:');
    for (final entry in _validationResults.entries) {
      print(
          'Entry ID: ${entry.key}, Valid: ${entry.value.isValid}, Message: ${entry.value.errorMessage}');
      if (entry.value is RelatedValidationResult) {
        final relatedResult = entry.value as RelatedValidationResult;
        print('  Related Entry ID: ${relatedResult.relatedEntryId}');
      }
    }
  }

  // We don't store the currency symbol anymore, it's provided by the SettingsProvider
  // This getter is kept for backward compatibility
  String get meterUnit => '£'; // Default value, but this won't be used anymore
  String get dateFormat => _dateFormat;
  bool get isLoading => _isLoading;
  bool get isEditMode => _isEditMode;
  String get error => _error;
  int get selectedEntryId => _selectedEntryId;
  int get currentPage => _currentPage;
  int get itemsPerPage => _itemsPerPage;

  // Computed getter for total pages
  int get totalPages => (_filteredEntries.length / _itemsPerPage).ceil() > 0
      ? (_filteredEntries.length / _itemsPerPage).ceil()
      : 1; // At least one page even if empty

  // Computed getters
  bool get hasEntries => _allEntries.isNotEmpty;
  bool get hasFilteredEntries => _filteredEntries.isNotEmpty;
  int get entryCount => _filteredEntries.length;
  bool get hasValidationErrors =>
      _validationResults.values.any((result) => !result.isValid);

  /// Count of invalid entries, including both directly invalid entries and
  /// entries that are related to invalid entries, without double-counting
  int get invalidEntryCount {
    // Create a set of all entry IDs that are either directly invalid or related to invalid entries
    final Set<int> allInvalidEntryIds = {};

    // Add directly invalid entries
    for (final entry in _validationResults.entries) {
      if (!entry.value.isValid) {
        allInvalidEntryIds.add(entry.key);
      }
    }

    // Add related entries
    for (final result in _validationResults.values) {
      if (!result.isValid &&
          result is RelatedValidationResult &&
          result.relatedEntryId != null) {
        allInvalidEntryIds.add(result.relatedEntryId!);
      }
    }

    // Return the size of the set (which automatically deduplicates)
    return allInvalidEntryIds.length;
  }

  /// Check if there are enough meter readings to show date range filter
  /// We need at least 2 meter readings to show date range filter
  bool get hasEnoughReadingsForDateRange {
    // Filter to only include meter readings (not top-ups)
    final readings = _allEntries
        .where((e) => e.reading > 0 && e.amountToppedUp == 0)
        .toList();
    // Need at least 2 readings for date range
    return readings.length >= 2;
  }

  // Event subscription
  StreamSubscription<EventType>? _eventSubscription;

  HistoryController(this._repository) {
    // Subscribe to data update and date settings events
    _eventSubscription = EventBus().stream.listen((event) {
      if (event == EventType.dataUpdated) {
        // Refresh data when entries are added/edited/deleted
        logger.i(
            'HistoryController: Received data update event, refreshing data');
        refresh();
      } else if (event == EventType.dateSettingsUpdated) {
        // Refresh when date settings are updated
        logger.i(
            'HistoryController: Received date settings update event, refreshing data');
        refresh();
      } else if (event == EventType.settingsUpdated) {
        // Refresh when general settings are updated
        logger.i(
            'HistoryController: Received settings update event, refreshing data');
        refresh();
      }
    });
  }

  @override
  void dispose() {
    // Cancel event subscription
    _eventSubscription?.cancel();
    super.dispose();
  }

  /// Initialize the controller
  Future<void> init() async {
    _isLoading = true;
    _isEditMode = false; // Ensure edit mode is off by default
    _error = '';
    notifyListeners();

    try {
      await _loadData();
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = 'Failed to load data: $e';
      notifyListeners();
    }
  }

  /// Refresh the data
  Future<void> refresh() async {
    _isLoading = true;
    _isEditMode = false; // Reset edit mode on refresh
    _error = '';
    notifyListeners();

    try {
      await _loadData();
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = 'Failed to refresh data: $e';
      notifyListeners();
    }
  }

  /// Load all data
  Future<void> _loadData() async {
    // Load data in parallel
    final results = await Future.wait([
      _repository.getAllEntries(),
      _repository.getDateFormat(),
    ]);

    _allEntries = results[0] as List<MeterEntry>;
    _dateFormat = results[1] as String;

    // Calculate averages
    print("DEBUG: Before calculating averages: ${_allEntries.length} entries");
    for (final entry in _allEntries) {
      if (entry.amountToppedUp == 0) {
        // Only check meter readings
        print("DEBUG: Before: Entry ${entry.id}: Date=${DateTimeUtils.formatDateDefault(entry.timestamp)}, " +
            "Reading=${entry.reading}, ShortAvg=${entry.shortAverageAfterTopUp}, TotalAvg=${entry.totalAverageUpToThisPoint}");
      }
    }

    _allEntries = _repository.calculateAverages(_allEntries);

    print("DEBUG: After calculating averages: ${_allEntries.length} entries");
    for (final entry in _allEntries) {
      if (entry.amountToppedUp == 0) {
        // Only check meter readings
        print("DEBUG: After: Entry ${entry.id}: Date=${DateTimeUtils.formatDateDefault(entry.timestamp)}, " +
            "Reading=${entry.reading}, ShortAvg=${entry.shortAverageAfterTopUp}, TotalAvg=${entry.totalAverageUpToThisPoint}");
      }
    }

    // Apply filter
    _applyFilter();

    // Validate entries
    await _validateEntries();
  }

  /// Apply the current filter
  Future<void> _applyFilter() async {
    _filteredEntries = await _repository.getFilteredEntries(_filter);
    notifyListeners();
  }

  /// Update the filter
  Future<void> updateFilter(HistoryFilter filter) async {
    _filter = filter;
    await _applyFilter();
  }

  /// Reset the filter
  Future<void> resetFilter() async {
    _filter = _filter.reset();
    await _applyFilter();
  }

  /// Validate all entries
  Future<void> _validateEntries() async {
    _validationResults = await _repository.validateEntries(_allEntries);

    // Debug validation results
    debugPrintValidationResults();

    // Check for invalid entries and send notifications if needed
    _checkForInvalidEntriesAndNotify();

    notifyListeners();
  }

  /// Check for invalid entries and send notifications if needed
  Future<void> _checkForInvalidEntriesAndNotify() async {
    // Only proceed if there are validation results
    if (_validationResults.isEmpty) return;

    // Find invalid entries
    final invalidEntries = _validationResults.entries
        .where((entry) => !entry.value.isValid)
        .toList();

    // If there are invalid entries, queue them for grouped notification
    if (invalidEntries.isNotEmpty) {
      // Create a set to store unique dates of invalid entries
      final Set<String> invalidDates = {};
      final notificationProvider = NotificationProvider();

      // Collect dates of all invalid entries and queue them
      for (final entry in invalidEntries) {
        final entryId = entry.key;

        // Find the corresponding meter entry
        final meterEntry = _allEntries.firstWhere(
          (e) => e.id == entryId,
          orElse: () => MeterEntry(
            reading: 0,
            timestamp: DateTime.now(),
            amountToppedUp: 0,
          ),
        );

        // Format the date and add to the set
        final formattedDate =
            DateTimeUtils.formatDateDefault(meterEntry.timestamp);
        invalidDates.add(formattedDate);

        // Queue each invalid entry notification
        await notificationProvider.queueInvalidRecordNotification(
          message: 'Invalid entry on $formattedDate',
          date: formattedDate,
        );
      }

      // Show the grouped notification
      await notificationProvider.showGroupedInvalidRecordNotifications();
    }
  }

  /// Toggle edit mode
  void toggleEditMode() {
    _isEditMode = !_isEditMode;
    notifyListeners();
  }

  /// Select an entry
  void selectEntry(int id) {
    _selectedEntryId = id;
    notifyListeners();
  }

  /// Clear selection
  void clearSelection() {
    _selectedEntryId = -1;
    notifyListeners();
  }

  /// Add a new entry
  Future<void> addEntry(MeterEntry entry) async {
    try {
      await _repository.addEntry(entry);
      await refresh();

      // Notify other parts of the app that data has changed
      EventBus().fire(EventType.dataUpdated);
      logger.i('HistoryController: Added entry and fired dataUpdated event');
    } catch (e) {
      _error = 'Failed to add entry: $e';
      logger.e('HistoryController: Failed to add entry', details: e.toString());
      notifyListeners();
    }
  }

  /// Add multiple entries in bulk with optimized processing
  ///
  /// This method is optimized for adding large numbers of entries at once.
  /// It uses the bulkAddEntries method of HistoryRepository for efficient processing.
  ///
  /// @param entries The list of MeterEntry objects to add
  /// @param replace Whether to replace existing entries (true) or append (false)
  /// @param onProgress Optional callback for progress updates (0.0 to 1.0)
  Future<bool> bulkAddEntries(
    List<MeterEntry> entries, {
    bool replace = false,
    Function(double progress)? onProgress,
  }) async {
    try {
      logger.i(
          'HistoryController: Starting bulk add of ${entries.length} entries (replace: $replace)');

      // Report initial progress
      if (onProgress != null) {
        onProgress(0.0);
      }

      // Use the optimized bulk add method from repository
      final success = await _repository.bulkAddEntries(
        entries,
        replace: replace,
        onProgress: (progress) {
          // Forward progress updates, but scale to 80% for repository operations
          if (onProgress != null) {
            onProgress(progress * 0.8);
          }
        },
      );

      // Report progress
      if (onProgress != null) {
        onProgress(0.8); // 80% progress after repository operations
      }

      if (success) {
        // Refresh data
        await refresh();

        // Report progress
        if (onProgress != null) {
          onProgress(0.9); // 90% progress after refresh
        }

        // Notify other parts of the app that data has changed
        EventBus().fire(EventType.dataUpdated);
        logger.i(
            'HistoryController: Completed bulk add and fired dataUpdated event');

        // Report completion
        if (onProgress != null) {
          onProgress(1.0); // 100% progress after completion
        }

        return true;
      } else {
        _error = 'Failed to bulk add entries';
        notifyListeners();
        return false;
      }
    } catch (e) {
      _error = 'Failed to bulk add entries: $e';
      logger.e('HistoryController: Failed to bulk add entries',
          details: e.toString());
      notifyListeners();
      return false;
    }
  }

  /// Delete an entry
  Future<void> deleteEntry(int id) async {
    try {
      await _repository.deleteEntry(id);
      await refresh();

      // Notify other parts of the app that data has changed
      EventBus().fire(EventType.dataUpdated);
      logger.i('HistoryController: Deleted entry and fired dataUpdated event');
    } catch (e) {
      _error = 'Failed to delete entry: $e';
      logger.e('HistoryController: Failed to delete entry',
          details: e.toString());
      notifyListeners();
    }
  }

  /// Format a date according to the current date format
  String formatDate(DateTime date) {
    switch (_dateFormat) {
      case 'DD-MM-YYYY':
        return DateTimeUtils.formatDateDefault(date);
      case 'MM-DD-YYYY':
        return DateTimeUtils.formatDate(date, 'MM-dd-yyyy');
      case 'YYYY-MM-DD':
        return DateTimeUtils.formatDate(date, 'yyyy-MM-dd');
      case 'DD MMM YYYY':
        return DateTimeUtils.formatDateWithMonthName(date);
      case 'MMM DD, YYYY':
        return DateTimeUtils.formatDate(date, 'MMM dd, yyyy');
      default:
        return DateTimeUtils.formatDateDefault(date);
    }
  }

  /// Get the current date info setting
  Future<String> getDateInfo() async {
    return await _repository.getDateInfo();
  }

  /// Get the validation result for an entry
  ValidationResult getValidationResult(int id) {
    return _validationResults[id] ?? ValidationResult.valid();
  }

  /// Filter to show only invalid entries
  Future<void> filterInvalidEntries(HistoryFilter filter) async {
    // First update the filter state with the isInvalidFilter flag set to true
    _filter = filter.copyWith(isInvalidFilter: true);

    // Get all entries
    final allEntries = await _repository.getAllEntries();

    // Apply date filter first if date range is specified
    List<MeterEntry> dateFilteredEntries = allEntries;
    if (filter.startDate != null || filter.endDate != null) {
      // Apply date filtering first
      if (filter.startDate != null) {
        final startOfDay = DateTimeUtils.startOfDay(filter.startDate!);
        dateFilteredEntries = dateFilteredEntries.where((entry) {
          return entry.timestamp.isAfter(startOfDay) ||
              entry.timestamp.isAtSameMomentAs(startOfDay);
        }).toList();
      }

      if (filter.endDate != null) {
        final endOfDay = DateTimeUtils.endOfDay(filter.endDate!);
        dateFilteredEntries = dateFilteredEntries.where((entry) {
          return entry.timestamp.isBefore(endOfDay) ||
              entry.timestamp.isAtSameMomentAs(endOfDay);
        }).toList();
      }
    }

    // Find IDs of entries related to invalid entries
    final Set<int> relatedEntryIds = {};
    for (final result in _validationResults.values) {
      if (!result.isValid &&
          result is RelatedValidationResult &&
          result.relatedEntryId != null) {
        relatedEntryIds.add(result.relatedEntryId!);
      }
    }

    // Filter to include both directly invalid entries and related entries
    // but only within the date-filtered subset
    _filteredEntries = dateFilteredEntries.where((entry) {
      if (entry.id == null) return false;

      // Check if the entry is directly invalid
      final result = _validationResults[entry.id!];
      final isDirectlyInvalid = result != null && !result.isValid;

      // Check if the entry is related to an invalid entry
      final isRelatedToInvalidEntry = relatedEntryIds.contains(entry.id);

      return isDirectlyInvalid || isRelatedToInvalidEntry;
    }).toList();

    // Sort by date (newest first)
    _filteredEntries.sort((a, b) => b.timestamp.compareTo(a.timestamp));

    // Even if there are no invalid entries, keep the filter applied
    // This will show an empty list with the filter chip still active
    notifyListeners();
  }

  /// Check if an entry is valid
  bool isEntryValid(int id) {
    // First check if the entry itself is invalid
    if (_validationResults[id]?.isValid == false) {
      logger.d("Entry $id is directly invalid");
      return false;
    }

    // Then check if this entry is related to any invalid entry
    // (i.e., it was used to validate against an invalid entry)
    for (final result in _validationResults.values) {
      // Skip valid results
      if (result.isValid) continue;

      // Check if this is a RelatedValidationResult with a relatedEntryId
      if (result is RelatedValidationResult && result.relatedEntryId == id) {
        logger.d("Entry $id is related to an invalid entry");
        return false; // This entry is related to an invalid entry
      }
    }

    return true;
  }

  /// Get the validation error message for an entry
  String? getValidationErrorMessage(int id) {
    return _validationResults[id]?.errorMessage;
  }

  /// Get the validation severity for an entry
  String getValidationSeverity(int id) {
    return _validationResults[id]?.severity ?? 'none';
  }

  /// Go to a specific page
  void goToPage(int page) {
    if (page >= 0 && page < totalPages) {
      _currentPage = page;
      notifyListeners();
    }
  }

  /// Validate a single entry
  Future<ValidationResult> validateEntry(MeterEntry entry) async {
    try {
      return await _repository.validateEntry(entry);
    } catch (e) {
      _error = 'Failed to validate entry: $e';
      notifyListeners();
      return ValidationResult.error('Failed to validate entry');
    }
  }
}
