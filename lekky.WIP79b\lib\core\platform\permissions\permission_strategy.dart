// File: lib/core/platform/permissions/permission_strategy.dart

import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'permission_adapter.dart';

/// Base class for version-specific permission strategies
abstract class PermissionStrategy {
  /// Check permission using strategy-specific logic
  Future<PermissionStatus> checkPermission();

  /// Request permission using strategy-specific logic
  Future<bool> requestPermission();

  /// <PERSON><PERSON> permanently denied permission
  Future<void> handlePermanentlyDenied();

  /// Get strategy-specific settings page
  Future<bool> openSettings();
}

/// Permission strategy for Android 13+ (API level 33+)
class Android13PermissionStrategy implements PermissionStrategy {
  final MethodChannel _channel;
  final SharedPreferences _prefs;

  static const String _permissionRequestCountKey =
      'android13_permission_request_count';

  Android13PermissionStrategy(this._channel, this._prefs);

  @override
  Future<PermissionStatus> checkPermission() async {
    try {
      final result =
          await _channel.invokeMethod<int>('checkNotificationPermission');
      switch (result) {
        case 0:
          return PermissionStatus.granted;
        case 1:
          return PermissionStatus.denied;
        case 2:
          return PermissionStatus.permanentlyDenied;
        default:
          return PermissionStatus.unknown;
      }
    } catch (e) {
      return PermissionStatus.unknown;
    }
  }

  @override
  Future<bool> requestPermission() async {
    try {
      // Increment request count
      final requestCount = _prefs.getInt(_permissionRequestCountKey) ?? 0;
      await _prefs.setInt(_permissionRequestCountKey, requestCount + 1);

      // For Android 13+, we need to request the POST_NOTIFICATIONS permission
      final result =
          await _channel.invokeMethod<bool>('requestNotificationPermission');
      return result ?? false;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<void> handlePermanentlyDenied() async {
    // Reset request count
    await _prefs.setInt(_permissionRequestCountKey, 0);

    // Show a dialog explaining how to enable permissions in settings
    await _channel.invokeMethod<void>('showSettingsInstructionsDialog');
  }

  @override
  Future<bool> openSettings() async {
    try {
      final result =
          await _channel.invokeMethod<bool>('openNotificationSettings');
      return result ?? false;
    } catch (e) {
      return false;
    }
  }

  /// Get the number of times permission has been requested
  Future<int> getRequestCount() async {
    return _prefs.getInt(_permissionRequestCountKey) ?? 0;
  }
}

/// Permission strategy for Android 12 (API level 31-32)
class Android12PermissionStrategy implements PermissionStrategy {
  final MethodChannel _channel;
  final SharedPreferences _prefs;

  Android12PermissionStrategy(this._channel, this._prefs);

  @override
  Future<PermissionStatus> checkPermission() async {
    try {
      // For Android 12, we check if notifications are enabled
      final result =
          await _channel.invokeMethod<bool>('areNotificationsEnabled');
      return result == true
          ? PermissionStatus.granted
          : PermissionStatus.denied;
    } catch (e) {
      return PermissionStatus.unknown;
    }
  }

  @override
  Future<bool> requestPermission() async {
    try {
      // For Android 12, we need to guide the user to notification settings
      // Show educational UI first
      final showEducation =
          await _channel.invokeMethod<bool>('showNotificationEducationUI');

      if (showEducation == true) {
        // Open notification settings
        return await openSettings();
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<void> handlePermanentlyDenied() async {
    // Show a dialog explaining how to enable notifications in settings
    await _channel
        .invokeMethod<void>('showNotificationSettingsInstructionsDialog');
  }

  @override
  Future<bool> openSettings() async {
    try {
      // Open notification channel settings
      final result =
          await _channel.invokeMethod<bool>('openNotificationChannelSettings');
      return result ?? false;
    } catch (e) {
      return false;
    }
  }
}

/// Permission strategy for older Android versions (API level < 31)
class AndroidLegacyPermissionStrategy implements PermissionStrategy {
  final MethodChannel _channel;
  final SharedPreferences _prefs;

  AndroidLegacyPermissionStrategy(this._channel, this._prefs);

  @override
  Future<PermissionStatus> checkPermission() async {
    try {
      // For older Android versions, we check if notifications are enabled
      final result =
          await _channel.invokeMethod<bool>('areNotificationsEnabled');
      return result == true
          ? PermissionStatus.granted
          : PermissionStatus.denied;
    } catch (e) {
      return PermissionStatus.unknown;
    }
  }

  @override
  Future<bool> requestPermission() async {
    try {
      // For older Android versions, there's no runtime permission for notifications
      // We just need to ensure the notification channels are created
      final result =
          await _channel.invokeMethod<bool>('createNotificationChannels');
      return result ?? false;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<void> handlePermanentlyDenied() async {
    // Show a dialog explaining how to enable notifications in settings
    await _channel
        .invokeMethod<void>('showNotificationSettingsInstructionsDialog');
  }

  @override
  Future<bool> openSettings() async {
    try {
      // Open app settings
      final result = await _channel.invokeMethod<bool>('openAppSettings');
      return result ?? false;
    } catch (e) {
      return false;
    }
  }
}
