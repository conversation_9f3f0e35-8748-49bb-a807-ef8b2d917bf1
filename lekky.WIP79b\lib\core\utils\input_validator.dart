// File: lib/core/utils/input_validator.dart

/// Utility class for validating user input
class InputValidator {
  // Private constructor to prevent instantiation
  InputValidator._();

  /// Validates a meter reading
  /// Returns a map with isValid and errorMessage
  static Map<String, dynamic> validateMeterReading(String value) {
    if (value.isEmpty) {
      return {
        'isValid': false,
        'errorMessage': 'Meter reading cannot be empty',
        'severity': 'error'
      };
    }

    final double? reading = double.tryParse(value);
    if (reading == null) {
      return {
        'isValid': false,
        'errorMessage': 'Meter reading must be a valid number',
        'severity': 'error'
      };
    }

    if (reading < 0) {
      return {
        'isValid': false,
        'errorMessage': 'Meter reading cannot be negative',
        'severity': 'error'
      };
    }

    if (reading == 0) {
      return {
        'isValid': false,
        'errorMessage': 'Meter reading cannot be zero',
        'severity': 'warning'
      };
    }

    // Check for unreasonably high values (over 10,000)
    if (reading > 10000) {
      return {
        'isValid': false,
        'errorMessage': 'Meter reading seems too high. Please check the value.',
        'severity': 'warning'
      };
    }

    return {'isValid': true, 'errorMessage': null, 'severity': 'none'};
  }

  /// Validates a top-up amount
  /// Returns a map with isValid and errorMessage
  static Map<String, dynamic> validateTopUpAmount(String value) {
    if (value.isEmpty) {
      return {
        'isValid': false,
        'errorMessage': 'Top-up amount cannot be empty',
        'severity': 'error'
      };
    }

    final double? amount = double.tryParse(value);
    if (amount == null) {
      return {
        'isValid': false,
        'errorMessage': 'Top-up amount must be a valid number',
        'severity': 'error'
      };
    }

    if (amount <= 0) {
      return {
        'isValid': false,
        'errorMessage': 'Top-up amount must be greater than zero',
        'severity': 'error'
      };
    }

    // Check for unreasonably high values (over 1,000)
    if (amount > 1000) {
      return {
        'isValid': false,
        'errorMessage': 'Top-up amount seems too high. Please check the value.',
        'severity': 'warning'
      };
    }

    return {'isValid': true, 'errorMessage': null, 'severity': 'none'};
  }

  /// Validates a date
  /// Returns a map with isValid and errorMessage
  static Map<String, dynamic> validateDate(DateTime? date) {
    if (date == null) {
      return {
        'isValid': false,
        'errorMessage': 'Date cannot be empty',
        'severity': 'error'
      };
    }

    final now = DateTime.now();
    if (date.isAfter(now)) {
      return {
        'isValid': false,
        'errorMessage': 'Date cannot be in the future',
        'severity': 'warning'
      };
    }

    return {'isValid': true, 'errorMessage': null, 'severity': 'none'};
  }

  /// Validates a date range
  /// Returns a map with isValid and errorMessage
  static Map<String, dynamic> validateDateRange(
      DateTime? startDate, DateTime? endDate) {
    if (startDate == null) {
      return {
        'isValid': false,
        'errorMessage': 'Start date cannot be empty',
        'severity': 'error'
      };
    }

    if (endDate == null) {
      return {
        'isValid': false,
        'errorMessage': 'End date cannot be empty',
        'severity': 'error'
      };
    }

    if (startDate.isAfter(endDate)) {
      return {
        'isValid': false,
        'errorMessage': 'Start date cannot be after end date',
        'severity': 'error'
      };
    }

    final now = DateTime.now();
    if (endDate.isAfter(now)) {
      return {
        'isValid': false,
        'errorMessage': 'End date cannot be in the future',
        'severity': 'warning'
      };
    }

    return {'isValid': true, 'errorMessage': null, 'severity': 'none'};
  }

  /// Validates a threshold value
  /// Returns a map with isValid and errorMessage
  static Map<String, dynamic> validateThreshold(String value) {
    if (value.isEmpty) {
      return {
        'isValid': false,
        'errorMessage': 'Threshold cannot be empty',
        'severity': 'error'
      };
    }

    final double? threshold = double.tryParse(value);
    if (threshold == null) {
      return {
        'isValid': false,
        'errorMessage': 'Threshold must be a valid number',
        'severity': 'error'
      };
    }

    if (threshold < 0) {
      return {
        'isValid': false,
        'errorMessage': 'Threshold cannot be negative',
        'severity': 'error'
      };
    }

    return {'isValid': true, 'errorMessage': null, 'severity': 'none'};
  }

  /// Validates days in advance
  /// Returns a map with isValid and errorMessage
  static Map<String, dynamic> validateDaysInAdvance(String value) {
    if (value.isEmpty) {
      return {
        'isValid': false,
        'errorMessage': 'Days in advance cannot be empty',
        'severity': 'error'
      };
    }

    final int? days = int.tryParse(value);
    if (days == null) {
      return {
        'isValid': false,
        'errorMessage': 'Days in advance must be a valid number',
        'severity': 'error'
      };
    }

    if (days < 0) {
      return {
        'isValid': false,
        'errorMessage': 'Days in advance cannot be negative',
        'severity': 'error'
      };
    }

    if (days > 30) {
      return {
        'isValid': false,
        'errorMessage': 'Days in advance cannot be more than 30',
        'severity': 'warning'
      };
    }

    return {'isValid': true, 'errorMessage': null, 'severity': 'none'};
  }

  /// Validates a donation amount
  /// Returns a map with isValid and errorMessage
  static Map<String, dynamic> validateDonationAmount(String value) {
    if (value.isEmpty) {
      return {
        'isValid': false,
        'errorMessage': 'Donation amount cannot be empty',
        'severity': 'error'
      };
    }

    final double? amount = double.tryParse(value);
    if (amount == null) {
      return {
        'isValid': false,
        'errorMessage': 'Donation amount must be a valid number',
        'severity': 'error'
      };
    }

    if (amount <= 0) {
      return {
        'isValid': false,
        'errorMessage': 'Donation amount must be greater than zero',
        'severity': 'error'
      };
    }

    if (amount > 100) {
      return {
        'isValid': false,
        'errorMessage': 'Donation amount cannot be more than 100',
        'severity': 'warning'
      };
    }

    return {'isValid': true, 'errorMessage': null, 'severity': 'none'};
  }

  /// Validates a monetary amount (can be negative)
  /// Returns a map with isValid and errorMessage
  static Map<String, dynamic> validateMonetaryAmount(String value) {
    if (value.isEmpty) {
      return {
        'isValid': false,
        'errorMessage': 'Amount cannot be empty',
        'severity': 'error'
      };
    }

    final double? amount = double.tryParse(value);
    if (amount == null) {
      return {
        'isValid': false,
        'errorMessage': 'Amount must be a valid number',
        'severity': 'error'
      };
    }

    // Check if the amount has more than 2 decimal places
    final decimalPlaces = value.contains('.') ? value.split('.')[1].length : 0;

    if (decimalPlaces > 2) {
      return {
        'isValid': false,
        'errorMessage': 'Amount cannot have more than 2 decimal places',
        'severity': 'error'
      };
    }

    return {'isValid': true, 'errorMessage': null, 'severity': 'none'};
  }
}
