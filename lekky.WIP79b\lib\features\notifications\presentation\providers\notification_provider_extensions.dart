// File: lib/features/notifications/presentation/providers/notification_provider_extensions.dart

import '../../domain/usecases/migrate_notification_storage.dart';
import 'notification_provider.dart';

/// Extensions for NotificationProvider
extension NotificationProviderExtensions on NotificationProvider {
  /// Migrate notification storage from SharedPreferences to SQLite
  Future<bool> migrateStorageToSQLite({
    Function(double progress)? onProgress,
    required MigrateNotificationStorage migrateStorageUseCase,
  }) async {
    try {
      // Check if migration is already completed
      final isAlreadyMigrated =
          await migrateStorageUseCase.isMigrationCompleted();
      if (isAlreadyMigrated) {
        return true;
      }

      // Execute migration
      final result = await migrateStorageUseCase.execute(
        onProgress: onProgress,
      );

      // Refresh notifications after migration
      if (result) {
        await refresh();
      }

      return result;
    } catch (e) {
      return false;
    }
  }

  /// Get the current storage type
  Future<String> getCurrentStorageType({
    required MigrateNotificationStorage migrateStorageUseCase,
  }) async {
    return await migrateStorageUseCase.getCurrentStorageType();
  }

  /// Check if storage has been migrated to SQLite
  Future<bool> isStorageMigratedToSQLite({
    required MigrateNotificationStorage migrateStorageUseCase,
  }) async {
    return await migrateStorageUseCase.isMigrationCompleted();
  }
}
