// File: lib/core/theme/app_colors.dart
import 'package:flutter/material.dart';

/// App color palette
class AppColors {
  // Private constructor to prevent instantiation
  AppColors._();

  // Light Theme Colors - Energy-themed palette
  static const Color primary =
      Color(0xFF0288D1); // Blue 600 - evokes electricity and trust
  static const Color secondary =
      Color(0xFFFFA000); // Amber 700 - for energy-themed CTAs
  static const Color tertiary =
      Color(0xFFFFCA28); // Amber 300 - for badges, info chips
  static const Color error =
      Color(0xFFD32F2F); // Red 700 - meets contrast on white
  static const Color background =
      Color(0xFFF8F9FA); // Very light gray - reduces glare
  static const Color surface =
      Color(0xFFFFFFFF); // Pure white - for cards/sheets
  static const Color surfaceVariant =
      Color(0xFFE7E0EC); // Light variant for surfaces
  static const Color onPrimary =
      Color(0xFFFFFFFF); // White - for text on primary
  static const Color onSecondary =
      Color(0xFF000000); // Black - for text on secondary
  static const Color onTertiary =
      Color(0xFF000000); // Black - for text on tertiary
  static const Color onError = Color(0xFFFFFFFF); // White - for text on error
  static const Color onBackground =
      Color(0xFF212121); // Dark gray - for primary text
  static const Color onSurface =
      Color(0xFF212121); // Dark gray - for primary text
  static const Color onSurfaceVariant = Color(0xFF49454F);
  static const Color outline = Color(0xFF79747E);
  static const Color textSecondary =
      Color(0xFF757575); // Medium gray - for secondary text
  static const Color success =
      Color(0xFF388E3C); // Green 700 - for confirmations

  // Tab Colors
  static const Color homeTab = Color(0xFF49D941); // Green
  static const Color meterTab = Color(0xFF36D1DC); // Blue (same as settings)
  static const Color topUpTab = Color(0xFFFF9800); // Orange
  static const Color historyTab =
      Color(0xFF9C27B0); // Darker Purple - more in keeping with Homepage blue
  static const Color costTab =
      Color(0xFFE65100); // Darker Orange - more in keeping with Homepage blue
  static const Color settingsTab =
      Color(0xFFD8DEDB); // Lighter Grey (from #bcc4c1)

  // Gradient Colors
  static const List<Color> primaryGradient = [
    Color(0xFF003087),
    Color(0xFF0057B8)
  ];
  static const List<Color> secondaryGradient = [
    Color(0xFF43E97B),
    Color(0xFF38F9D7)
  ];
  static const List<Color> tertiaryGradient = [
    Color(0xFFFF9800),
    Color(0xFFFFB74D)
  ];
  static const List<Color> meterGradient = [
    Color(0xFF36D1DC),
    Color(0xFF5B86E5)
  ]; // Same as settingsGradient
  static const List<Color> topUpGradient = [
    Color(0xFFFF9800),
    Color(0xFFFFB74D)
  ];
  static const List<Color> historyGradient = [
    Color(0xFF9C27B0), // Darker Purple
    Color(0xFFBA68C8) // Medium Purple
  ];
  static const List<Color> settingsGradient = [
    Color(0xFF424242), // Dark grey
    Color(0xFF616161) // Medium grey
  ];
  static const List<Color> costGradient = [
    Color(0xFFE65100), // Darker Orange
    Color(0xFFFF8A65) // Medium Orange
  ];
  static const List<Color> welcomeGradient = [
    Color(0xFF49D941), // Green
    Color(0xFF36D1DC) // Blue
  ];
  static const List<Color> setupGradient = [
    Color(0xFF424242), // Dark grey
    Color(0xFF616161) // Medium grey
  ];

  // Dark Theme Colors - Reduced eye strain palette
  static const Color primaryDark =
      Color(0xFF42A5F5); // Blue 400 - brighter in dark mode for visibility
  static const Color secondaryDark =
      Color(0xFF90CAF9); // Blue 200 - lighter variant for dark mode
  static const Color tertiaryDark =
      Color(0xFFFFB74D); // Amber 300 - for focal actions
  static const Color errorDark =
      Color(0xFFE57373); // Red 300 - for errors in dark mode
  static const Color backgroundDark =
      Color(0xFF121212); // Dark gray - reduces eye strain
  static const Color surfaceDark =
      Color(0xFF1E1E1E); // Slightly lighter gray - for cards
  static const Color surfaceVariantDark =
      Color(0xFF242424); // Variant for surfaces
  static const Color onPrimaryDark =
      Color(0xFF000000); // Black - for text on primary
  static const Color onSecondaryDark =
      Color(0xFF000000); // Black - for text on secondary
  static const Color onTertiaryDark =
      Color(0xFF000000); // Black - for text on tertiary
  static const Color onErrorDark =
      Color(0xFF000000); // Black - for text on error
  static const Color onBackgroundDark =
      Color(0xFFFFFFFF); // White - for primary text on dark backgrounds
  static const Color onSurfaceDark =
      Color(0xFFFFFFFF); // White - for primary text on dark surfaces
  static const Color onSurfaceVariantDark =
      Color(0xFFB0B0B0); // Light gray - for secondary text
  static const Color outlineDark =
      Color(0xFF424242); // Dark gray - for borders and dividers
  static const Color textSecondaryDark =
      Color(0xFFB0B0B0); // Medium-light gray - for secondary text
  static const Color successDark =
      Color(0xFF66BB6A); // Green 400 - for success messages
  static const Color warningDark =
      Color(0xFFFFD54F); // Amber 300 - for warnings
  static const Color infoDark =
      Color(0xFF64B5F6); // Blue 300 - for informational messages

  // Adaptive Text Colors for Dark Mode
  static const Color lastReadingLabelDark =
      Color(0xFF90CAF9); // Light blue for better contrast
  static const Color meterTotalLabelDark =
      Color(0xFFFFD54F); // Light amber for better contrast
  static const Color valueTextDark =
      Color(0xFFE1F5FE); // Very light blue for values
  static const Color primaryTextDark =
      Color(0xFF81D4FA); // Light blue for primary text

  // Semantic Colors
  static const Color warning = Color(0xFFFFC107); // Amber
  static const Color info = Color(0xFF2196F3); // Blue

  // Transparent Colors
  static Color semiTransparentWhite = Colors.white.withOpacity(0.7);
  static Color semiTransparentBlack = Colors.black.withOpacity(0.7);
  static const Color transparent = Colors.transparent;

  // Card Background Colors
  static Color cardBackground = Colors.white.withOpacity(0.85);
  static Color cardBackgroundDark = const Color(0xFF1E1E1E).withOpacity(0.85);

  // Container Colors
  static const Color surfaceContainer = Color(0xFFEAE0EC);
  static const Color surfaceContainerDark = Color(0xFF2C2C2C);

  // Shadow Colors
  static Color shadowColor = Colors.black.withOpacity(0.1);
  static Color shadowColorDark = Colors.black.withOpacity(0.2);

  // Helper Text Colors
  static const Color helperTextLight =
      Color(0xFF757575); // Medium gray for helper text in light mode
  static const Color helperTextDark =
      Color(0xFFB0B0B0); // Light gray for helper text in dark mode

  // Message Bar Colors
  static const Color messageBannerLight = Color(0xFFF5F5F5);
  static const Color messageBannerDark = Color(0xFF303030);
  static const Color messageTextLight = Color(0xDE000000); // black87
  static const Color messageTextDark = Color(0xB3FFFFFF); // white70

  // Table Row Colors - Light Mode
  static const Color tableRowInvalid = Color(0xFFFFF9C4); // Light yellow
  static const Color tableRowTopUp = Color(0xFFFFF8E1); // Light beige
  static const Color tableRowEven = Color(0xFFFFFFFF); // White
  static const Color tableRowOdd = Color(0xFFF5F5F5); // Light gray
  static const Color tableHeaderLight = Color(0xFFE1E1E1); // Light gray header

  // Table Row Colors - Dark Mode
  static const Color tableRowInvalidDark = Color(0xFF5D4037); // Dark brown
  static const Color tableRowTopUpDark = Color(0xFF455A64); // Dark blue-gray
  static const Color tableRowEvenDark = Color(0xFF2C2C2C); // Dark gray
  static const Color tableRowOddDark = Color(0xFF212121); // Darker gray
  static const Color tableHeaderDark = Color(0xFF303030); // Dark gray header

  // Table Cell Text Colors
  static const Color tableReadingText = Color(0xFF003087); // Blue for readings
  static const Color tableTopUpText = Color(0xFFFF9800); // Orange for top-ups
  static const Color tableReadingTextDark =
      Color(0xFF64B5F6); // Light blue for readings in dark mode
  static const Color tableTopUpTextDark =
      Color(0xFFFFB74D); // Light orange for top-ups in dark mode
  static const Color tableAverageText =
      Colors.black87; // For average values in light mode
  static const Color tableAverageTextDark =
      Color(0xFFE6E1E5); // For average values in dark mode
  static const Color tableDateText =
      Color(0xFF212121); // For date text in light mode
  static const Color tableDateTextDark =
      Color(0xFFE6E1E5); // For date text in dark mode
  static const Color tableHeaderText =
      Color(0xFF0288D1); // For header text in light mode
  static const Color tableHeaderTextDark =
      Color(0xFF42A5F5); // For header text in dark mode

  // Dialog Action Button Colors
  static const Color cancelButtonOutline = Color(0xFF0288D1); // Blue outline
  static const Color deleteButtonBackground =
      Color(0xFFD32F2F); // Red background
  static const Color editButtonBackground =
      Color(0xFFFF9800); // Dark orange background

  // Form Input Colors
  static const Color inputTextLight =
      Color(0xFF212121); // Dark gray for input text in light mode
  static const Color inputTextDark =
      Color(0xFFFFFFFF); // White for input text in dark mode
  static const Color inputLabelLight =
      Color(0xFF212121); // Dark gray for input labels in light mode
  static const Color inputLabelDark =
      Color(0xFFFFFFFF); // White for input labels in dark mode
  static const Color inputHintLight =
      Color(0xFF757575); // Medium gray for input hints in light mode
  static const Color inputHintDark =
      Color(0xFFB0B0B0); // Light gray for input hints in dark mode
  static const Color inputBorderLight =
      Color(0xFF757575); // Medium gray for input borders in light mode
  static const Color inputBorderDark =
      Color(0xFF757575); // Medium gray for input borders in dark mode
  static const Color inputFocusedBorderLight =
      Color(0xFF0288D1); // Blue for focused input borders in light mode
  static const Color inputFocusedBorderDark =
      Color(0xFF42A5F5); // Light blue for focused input borders in dark mode

  // Date Picker Colors
  static const Color datePickerTextLight =
      Color(0xFF212121); // Dark gray for date picker text in light mode
  static const Color datePickerTextDark =
      Color(0xFFFFFFFF); // White for date picker text in dark mode
}
