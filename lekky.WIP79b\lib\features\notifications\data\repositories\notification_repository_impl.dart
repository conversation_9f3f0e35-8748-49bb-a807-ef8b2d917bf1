// File: lib/features/notifications/data/repositories/notification_repository_impl.dart

import 'package:flutter/material.dart';
import '../../../../core/platform/notification/notification_adapter.dart';
import '../../../../core/platform/permissions/permission_adapter.dart';
import '../../../../core/platform/timezone/timezone_adapter.dart';
import '../../../../core/platform/timezone/timezone_change_service.dart';
import '../../../../core/utils/logger.dart';
import '../../domain/models/notification_model.dart';
import '../../domain/models/reminder_time_model.dart';
import '../../domain/models/timezone_aware_reminder_time.dart';
import '../../domain/repositories/notification_repository.dart';
import '../sources/local_storage_data_source.dart';
import '../sources/platform_notification_data_source.dart';
import '../sources/sql_notification_data_source.dart';
import '../sources/storage_factory.dart';
import 'dart:async';

/// Implementation of the notification repository
class NotificationRepositoryImpl implements NotificationRepository {
  final StorageFactory _storageFactory;
  late LocalStorageDataSource _localDataSource;
  final PlatformNotificationDataSource _platformDataSource;
  final NotificationAdapter _notificationAdapter;
  final PermissionAdapter _permissionAdapter;
  final TimezoneAdapter _timezoneAdapter;

  bool _isInitialized = false;

  // Track if we're currently processing a timezone change
  bool _processingTimezoneChange = false;

  // Track permission request attempts
  int _permissionRequestAttempts = 0;
  static const int _maxPermissionRequestAttempts = 3;

  // Stream controller for permission status changes
  final StreamController<PermissionStatus> _permissionStatusController =
      StreamController<PermissionStatus>.broadcast();

  NotificationRepositoryImpl({
    required StorageFactory storageFactory,
    required PlatformNotificationDataSource platformDataSource,
    required NotificationAdapter notificationAdapter,
    required PermissionAdapter permissionAdapter,
    required TimezoneAdapter timezoneAdapter,
  })  : _storageFactory = storageFactory,
        _platformDataSource = platformDataSource,
        _notificationAdapter = notificationAdapter,
        _permissionAdapter = permissionAdapter,
        _timezoneAdapter = timezoneAdapter;

  @override
  Future<void> initialize() async {
    if (_isInitialized) return;

    // Get the appropriate data source based on migration status
    _localDataSource = await _storageFactory.createStorageDataSource();

    await _localDataSource.initialize();
    await _platformDataSource.initialize();
    await _notificationAdapter.initialize();
    await _permissionAdapter.initialize();
    await _timezoneAdapter.initialize();

    // Register for permission status changes
    _permissionAdapter
        .registerPermissionStatusListener(_onPermissionStatusChanged);

    _isInitialized = true;

    // Register for timezone change notifications
    TimezoneChangeService.addTimezoneChangeListener(_onTimezoneChanged);

    // Check if timezone has changed while app was closed
    final hasTimezoneChanged =
        await TimezoneChangeService.checkForTimezoneChangesSinceLastOpen();
    if (hasTimezoneChanged) {
      // Reschedule reminders due to timezone change
      await _handleTimezoneChange();
    }

    // Check current permission status
    final permissionStatus = await _permissionAdapter.checkPermission();
    _permissionStatusController.add(permissionStatus);
  }

  @override
  Future<List<NotificationModel>> getNotifications() async {
    await initialize();
    return await _localDataSource.getNotifications();
  }

  @override
  Future<void> addNotification({
    required String title,
    required String message,
    required NotificationPriority priority,
    String? actionType,
  }) async {
    await initialize();

    final notification = NotificationModel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      title: title,
      message: message,
      timestamp: DateTime.now(),
      priority: priority,
      actionType: actionType,
    );

    await _localDataSource.addNotification(notification);
  }

  @override
  Future<void> markAsRead(String id) async {
    await initialize();
    await _localDataSource.markAsRead(id);
  }

  @override
  Future<void> markAllAsRead() async {
    await initialize();
    await _localDataSource.markAllAsRead();
  }

  @override
  Future<void> removeNotification(String id) async {
    await initialize();
    await _localDataSource.removeNotification(id);
  }

  @override
  Future<void> clearAll() async {
    await initialize();
    await _localDataSource.clearAll();
  }

  @override
  Future<bool> areNotificationsEnabled() async {
    await initialize();
    return await _localDataSource.areNotificationsEnabled();
  }

  @override
  Future<void> setNotificationsEnabled(bool enabled) async {
    await initialize();

    // If enabling notifications, check and request permissions if needed
    if (enabled) {
      final permissionStatus = await _permissionAdapter.checkPermission();

      if (permissionStatus != PermissionStatus.granted) {
        // Show educational UI before requesting permission
        final shouldProceed =
            await _permissionAdapter.showPermissionEducationUI();
        if (shouldProceed) {
          final isGranted = await _requestPermissionWithRetry();

          // If permission is still not granted after retries, show alternatives
          if (!isGranted) {
            await _permissionAdapter.showPermissionDeniedAlternativesUI();

            // If permission is permanently denied, guide the user to settings
            if (await _permissionAdapter.isPermanentlyDenied()) {
              // This would typically be handled by the UI layer
              // For now, we'll just log it
              print(
                  'Permission is permanently denied. User should be guided to settings.');
            }
          }
        }
      }
    }

    await _localDataSource.setNotificationsEnabled(enabled);

    // If disabling notifications, cancel all scheduled notifications
    if (!enabled) {
      await _notificationAdapter.cancelAllNotifications();
    }
  }

  /// Request permission with retry mechanism
  Future<bool> _requestPermissionWithRetry() async {
    _permissionRequestAttempts = 0;
    bool isGranted = false;

    while (_permissionRequestAttempts < _maxPermissionRequestAttempts &&
        !isGranted) {
      _permissionRequestAttempts++;

      isGranted =
          await _permissionAdapter.requestPermission(callback: (granted) {
        // Update the permission status when we get a result
        _permissionStatusController
            .add(granted ? PermissionStatus.granted : PermissionStatus.denied);
      });

      if (isGranted) break;

      // If we've reached the max attempts or the permission is permanently denied, stop retrying
      if (_permissionRequestAttempts >= _maxPermissionRequestAttempts ||
          await _permissionAdapter.isPermanentlyDenied()) {
        break;
      }

      // Wait a bit before trying again
      await Future.delayed(const Duration(seconds: 1));
    }

    return isGranted;
  }

  /// Handler for permission status changes
  void _onPermissionStatusChanged(PermissionStatus status) {
    _permissionStatusController.add(status);
  }

  /// Get a stream of permission status changes
  Stream<PermissionStatus> getPermissionStatusStream() {
    return _permissionStatusController.stream;
  }

  @override
  Future<bool> areMeterReadingRemindersEnabled() async {
    await initialize();
    return await _localDataSource.areMeterReadingRemindersEnabled();
  }

  @override
  Future<void> setMeterReadingRemindersEnabled(bool enabled) async {
    await initialize();
    await _localDataSource.setMeterReadingRemindersEnabled(enabled);

    // If enabling reminders, schedule the next reminder
    if (enabled) {
      await scheduleMeterReadingReminder();
    } else {
      // If disabling reminders, cancel any scheduled reminders
      await _notificationAdapter
          .cancelNotification(4); // 4 is meterReadingReminderNotificationId
    }
  }

  @override
  Future<int> getMeterReadingReminderFrequency() async {
    await initialize();
    return await _localDataSource.getMeterReadingReminderFrequency();
  }

  @override
  Future<void> setMeterReadingReminderFrequency(int days) async {
    await initialize();
    await _localDataSource.setMeterReadingReminderFrequency(days);

    // Reschedule reminder with new frequency if reminders are enabled
    final enabled = await areMeterReadingRemindersEnabled();
    if (enabled) {
      await scheduleMeterReadingReminder();
    }
  }

  @override
  Future<DateTime?> getLastMeterReadingReminderDate() async {
    await initialize();
    return await _localDataSource.getLastMeterReadingReminderDate();
  }

  @override
  Future<void> setLastMeterReadingReminderDate(DateTime date) async {
    await initialize();
    await _localDataSource.setLastMeterReadingReminderDate(date);
  }

  @override
  Future<ReminderTimeModel> getMeterReadingReminderTime() async {
    await initialize();
    return await _localDataSource.getMeterReadingReminderTime();
  }

  /// Get the timezone-aware meter reading reminder time
  Future<TimezoneAwareReminderTime> getTimezoneAwareReminderTime() async {
    final reminderTime = await getMeterReadingReminderTime();
    final lastKnownTimezone = await _timezoneAdapter.getLastKnownTimezone() ??
        _timezoneAdapter.getCurrentTimezone();

    return TimezoneAwareReminderTime.fromReminderTimeModel(
      model: reminderTime,
      currentTimezone: lastKnownTimezone,
    );
  }

  @override
  Future<void> setMeterReadingReminderTime(ReminderTimeModel time) async {
    await initialize();
    await _localDataSource.setMeterReadingReminderTime(time);

    // Reschedule reminder with new time if reminders are enabled
    final enabled = await areMeterReadingRemindersEnabled();
    if (enabled) {
      await scheduleMeterReadingReminder();
    }
  }

  @override
  Future<bool> scheduleMeterReadingReminder() async {
    await initialize();

    // Check if notifications and reminders are enabled
    final notificationsEnabled = await areNotificationsEnabled();
    final remindersEnabled = await areMeterReadingRemindersEnabled();

    if (!notificationsEnabled || !remindersEnabled) {
      return false;
    }

    // Check notification permissions
    final permissionStatus = await _permissionAdapter.checkPermission();
    if (permissionStatus != PermissionStatus.granted) {
      // Try to request permission if not granted
      final isGranted = await _requestPermissionWithRetry();
      if (!isGranted) {
        return false;
      }
    }

    // Get reminder frequency and timezone-aware time
    final frequency = await getMeterReadingReminderFrequency();
    final timezoneAwareReminderTime = await getTimezoneAwareReminderTime();

    // Get the last reminder date or use now
    final lastReminderDate =
        await getLastMeterReadingReminderDate() ?? DateTime.now();

    // Calculate next reminder date using timezone-aware calculation
    final scheduledDate = timezoneAwareReminderTime.getNextOccurrence(
      fromDate: lastReminderDate,
      frequencyInDays: frequency,
      timezoneAdapter: _timezoneAdapter,
    );

    // Update the last reminder date
    await setLastMeterReadingReminderDate(DateTime.now());

    // Schedule the notification
    return await _platformDataSource
        .scheduleMeterReadingReminder(scheduledDate);
  }

  @override
  Future<bool> rescheduleMeterReadingReminders(
      {bool forceReschedule = false}) async {
    await initialize();

    // Check if reminders are enabled
    final remindersEnabled = await areMeterReadingRemindersEnabled();
    if (!remindersEnabled && !forceReschedule) {
      return false;
    }

    // Cancel any existing reminders
    await _notificationAdapter
        .cancelNotification(4); // 4 is meterReadingReminderNotificationId

    // Schedule new reminder
    return await scheduleMeterReadingReminder();
  }

  /// Handle timezone change
  Future<void> _handleTimezoneChange() async {
    // Prevent re-entry
    if (_processingTimezoneChange) return;

    _processingTimezoneChange = true;
    try {
      print('Handling timezone change');

      // Clear the timezone change detection flag
      await TimezoneChangeService.clearTimezoneChangeDetection();

      // Reschedule reminders
      final remindersEnabled = await areMeterReadingRemindersEnabled();
      if (remindersEnabled) {
        await rescheduleMeterReadingReminders(forceReschedule: true);
      }
    } finally {
      _processingTimezoneChange = false;
    }
  }

  /// Callback for timezone changes
  void _onTimezoneChanged() {
    // Schedule the timezone change handling
    _handleTimezoneChange();
  }

  @override
  Future<DateTime?> getNextMeterReadingReminderDate() async {
    await initialize();
    return await _platformDataSource.getNextMeterReadingReminderDate();
  }

  @override
  Future<void> showMeterReadingReminderNotification() async {
    await initialize();

    // Check if notifications and reminders are enabled
    final notificationsEnabled = await areNotificationsEnabled();
    final remindersEnabled = await areMeterReadingRemindersEnabled();

    if (!notificationsEnabled || !remindersEnabled) {
      return;
    }

    // Show the notification
    await _platformDataSource.showMeterReadingReminderNotification();

    // Update the last reminder date
    await setLastMeterReadingReminderDate(DateTime.now());

    // Schedule the next reminder
    await scheduleMeterReadingReminder();
  }

  @override
  Future<bool> hasTimeZoneChanged() async {
    await initialize();
    return await _timezoneAdapter.hasTimezoneChanged();
  }

  /// Get the current timezone
  String getCurrentTimezone() {
    return _timezoneAdapter.getCurrentTimezone();
  }

  /// Get timezone information for a date
  Map<String, dynamic> getTimezoneInfo(DateTime dateTime) {
    return {
      'timezone': _timezoneAdapter.getCurrentTimezone(),
      'offset': _timezoneAdapter.getTimezoneOffset(dateTime),
      'abbreviation': _timezoneAdapter.getTimezoneAbbreviation(dateTime),
      'isDST': _timezoneAdapter.isInDaylightSavingTime(dateTime),
    };
  }

  @override
  Future<PermissionStatus> getPermissionStatus() async {
    await initialize();
    return await _permissionAdapter.checkPermission();
  }

  @override
  Future<bool> openAppSettings() async {
    await initialize();
    return await _permissionAdapter.openAppSettings();
  }

  @override
  Future<void> dispose() async {
    // Unregister permission status listener
    _permissionAdapter
        .unregisterPermissionStatusListener(_onPermissionStatusChanged);

    // Unregister timezone change listener
    TimezoneChangeService.removeTimezoneChangeListener(_onTimezoneChanged);

    // Close the stream controller
    await _permissionStatusController.close();
  }

  /// Migrate from SharedPreferences to SQLite
  /// This method should be called during app startup or from a settings screen
  @override
  Future<bool> migrateToSQLite({
    Function(double progress)? onProgress,
  }) async {
    await initialize();

    try {
      logger.i('NotificationRepositoryImpl: Starting migration to SQLite');

      // Perform the migration
      final success = await _storageFactory.migrateToSQLite(
        onProgress: onProgress,
      );

      if (success) {
        // Update the local data source to use the SQLite implementation
        _localDataSource = await _storageFactory.createStorageDataSource();
        await _localDataSource.initialize();

        logger
            .i('NotificationRepositoryImpl: Migration completed successfully');
      } else {
        logger.e('NotificationRepositoryImpl: Migration failed');
      }

      return success;
    } catch (e) {
      logger.e('NotificationRepositoryImpl: Error during migration',
          details: e.toString());
      return false;
    }
  }

  /// Get the current storage type (SQLite or SharedPreferences)
  @override
  Future<String> getStorageType() async {
    await initialize();

    // Check if the local data source is using SQLite
    if (_localDataSource is SQLNotificationDataSource) {
      return 'SQLite';
    } else {
      return 'SharedPreferences';
    }
  }

  /// Check if data has been migrated to SQLite
  @override
  Future<bool> isMigratedToSQLite() async {
    await initialize();
    return _localDataSource is SQLNotificationDataSource;
  }
}
