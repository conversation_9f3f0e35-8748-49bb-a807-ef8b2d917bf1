# Lekky Palette Audit Report

This report analyzes the color usage in the Lekky app compared to the defined color palette in `lekky_pallet.md`. It identifies discrepancies and provides recommendations for both project updates and palette enhancements.

## File-by-File Analysis

### Core Theme Files

- **File:** `lib/core/theme/app_colors.dart`
  - **Issue:** The app uses a different color naming convention than the palette document. The app uses Material Design-inspired names (primary, secondary, tertiary) while the palette uses semantic role names (--color-text-primary, --color-bg-app).
  - **Recommendation:** Refactor `app_colors.dart` to align with the semantic naming convention in the palette document, while maintaining backward compatibility through getters.

- **File:** `lib/core/theme/app_theme.dart`
  - **Issue:** Uses `Colors.transparent` in multiple places instead of a semantic transparent color.
  - **Recommendation:** Replace `Colors.transparent` with `AppColors.transparent` after adding this color to the AppColors class.

- **File:** `lib/core/theme/theme_service.dart`
  - **Issue:** Directly uses color opacity modifications like `AppColors.onBackground.withOpacity(0.1)` instead of using semantic colors for different opacity levels.
  - **Recommendation:** Add semantic colors for different opacity levels to AppColors.

- **File:** `lib/core/theme/app_text_styles.dart`
  - **Issue:** None. This file correctly uses the theme's text styles without hardcoded colors.

### Core Widget Files

- **File:** `lib/core/widgets/theme_toggle.dart`
  - **Issue:** Uses hardcoded colors for theme option backgrounds and icons.
  - **Recommendation:** Replace hardcoded colors with semantic colors from AppColors:
    ```dart
    backgroundColor: isDarkMode
        ? AppColors.surfaceContainerDark
        : AppColors.surfaceContainer,
    iconColor: AppColors.secondary,
    ```

- **File:** `lib/core/widgets/bottom_nav_bar.dart`
  - **Issue:** Uses `Colors.black.withOpacity(0.1)` for shadow color.
  - **Recommendation:** Replace with a semantic shadow color from AppColors.

- **File:** `lib/core/widgets/gradient_button.dart`
  - **Issue:** Uses `Colors.transparent` for the Material color.
  - **Recommendation:** Replace with `AppColors.transparent`.

- **File:** `lib/core/widgets/solid_save_button.dart`
  - **Issue:** Uses `Colors.white` directly for text and loading indicator color.
  - **Recommendation:** Replace with `AppColors.onPrimary`.

- **File:** `lib/core/widgets/app_dialog.dart`
  - **Issue:** None. This file correctly uses theme colors through the theme system.

- **File:** `lib/core/widgets/app_bottom_nav_bar.dart`
  - **Issue:** None. This file correctly uses AppColors for tab colors.

### Feature Module Files

- **File:** `lib/features/history/presentation/widgets/paginated_history_table.dart`
  - **Issue:** Uses hardcoded colors for row backgrounds:
    ```dart
    rowColor = const Color(0xFFFFF9C4); // Light yellow for invalid entries
    rowColor = const Color(0xFFFFF8E1); // Light beige for top-ups
    rowColor = Colors.white; // White for even rows
    rowColor = const Color(0xFFF5F5F5); // Light gray for odd rows
    ```
  - **Recommendation:** Add semantic colors to AppColors for table row states and use those instead.

- **File:** `lib/features/cost/presentation/widgets/cost_card.dart`
  - **Issue:** None. This file correctly uses adaptive colors from AppColors.

- **File:** `lib/features/cost/presentation/screens/cost_screen.dart`
  - **Issue:** None. This file correctly uses adaptive colors from AppColors.

- **File:** `lib/features/home/<USER>/widgets/meter_info_card.dart`
  - **Issue:** None. This file correctly uses adaptive colors from AppColors.

- **File:** `lib/features/home/<USER>/screens/home_screen.dart`
  - **Issue:** In some versions, uses `Colors.white` directly for icon color.
  - **Recommendation:** Replace with `AppColors.onPrimary` or appropriate semantic color.

- **File:** `lib/features/settings/presentation/screens/settings_screen.dart`
  - **Issue:** Uses direct color references in ChoiceChip widgets:
    ```dart
    isDarkMode ? Colors.white : Colors.black
    ```
  - **Recommendation:** Replace with semantic text colors from AppColors.

- **File:** `lib/features/settings/presentation/widgets/theme_selector.dart`
  - **Issue:** None. This file correctly uses theme colors.

- **File:** `lib/features/setup/presentation/widgets/initial_meter_credit_input.dart`
  - **Issue:** None. This file correctly uses theme colors.

### Other Files

- **File:** `lib/core/shared_modules/currency_selector.dart`
  - **Issue:** Uses direct color manipulation with `primaryColor.withOpacity(0.2)`.
  - **Recommendation:** Add a semantic color for selected chip backgrounds to AppColors.

- **File:** `lib/core/utils/dialog_button_styles.dart`
  - **Issue:** None. This file correctly uses theme colors.

## Project-Wide Issues

### Color Definition Files

- **Issue:** Multiple color definition files exist across different project versions with inconsistent color values.
  - **Recommendation:** Consolidate color definitions into a single source of truth that follows the palette document.

### Theme Implementation Structure

- **Issue:** The app uses a well-structured theme system with `app_theme.dart`, `app_colors.dart`, and `app_text_styles.dart`, but doesn't fully leverage Flutter's ThemeExtension for custom color schemes.
  - **Recommendation:** Create a custom ThemeExtension that maps directly to the semantic color roles in the palette document, making it easier to access theme colors throughout the app.

### Tab and Screen Colors

- **Issue:** The tab colors in the app match the palette document, but the gradient definitions are scattered across different files.
  - **Current Implementation:**
    ```dart
    static const Color homeTab = Color(0xFF49D941); // Green
    static const Color historyTab = Color(0xFF9C27B0); // Darker Purple
    static const Color costTab = Color(0xFFE65100); // Darker Orange
    ```
  - **Recommendation:** Create a dedicated section in `app_colors.dart` that directly references the palette document's tab color definitions.

### Light Mode Colors

- **Issue:** The app's light mode colors don't consistently match the semantic roles defined in the palette.
  - **Current Implementation:**
    ```dart
    static const Color primary = Color(0xFF0288D1); // Blue 600
    ```
    vs. Palette:
    ```
    --color-primary: #1976D2
    ```
  - **Recommendation:** Update color values to match the palette document exactly.

### Dark Mode Colors

- **Issue:** Dark mode colors have naming inconsistencies with the palette document.
  - **Current Implementation:**
    ```dart
    static const Color primaryDark = Color(0xFF42A5F5);
    ```
    vs. Palette:
    ```
    --color-primary-dark: #42A5F5
    ```
  - **Recommendation:** Align naming conventions and ensure values match the palette document.

### Transparent and Opacity-Based Colors

- **Issue:** The app defines several transparent colors that aren't explicitly defined in the palette:
  ```dart
  static Color semiTransparentWhite = Colors.white.withOpacity(0.7);
  static Color semiTransparentBlack = Colors.black.withOpacity(0.7);
  static Color cardBackground = Colors.white.withOpacity(0.85);
  ```
  - **Recommendation:** Add these to the palette document with semantic roles (e.g., `--color-overlay-light`, `--color-overlay-dark`).

## Palette Updates

### Semantic Role Additions

- **Suggestion:** Add the following semantic roles to the palette document:
  - `--color-shadow-light`: `rgba(0, 0, 0, 0.1)` - For subtle shadows in light mode
  - `--color-overlay-light`: `rgba(255, 255, 255, 0.7)` - For semi-transparent white overlays
  - `--color-overlay-dark`: `rgba(0, 0, 0, 0.7)` - For semi-transparent black overlays
  - `--color-card-background-light`: `rgba(255, 255, 255, 0.85)` - For card backgrounds in light mode
  - `--color-card-background-dark`: `rgba(30, 30, 30, 0.85)` - For card backgrounds in dark mode

### Tab Color Organization

- **Suggestion:** Create a dedicated "Tab Colors" section in the palette document that includes both the primary colors and gradients for each tab:
  ```
  | Tab | Primary Color | Gradient | Text Color |
  |-----|--------------|----------|------------|
  | Home | `--color-tab-home` | `--gradient-tab-home` | `--color-text-on-tab-home` |
  ```

### Gradient Definitions

- **Suggestion:** Add a dedicated "Gradients" section to the palette document:
  ```
  ## Gradients

  | Role | Colors | Description |
  |------|--------|-------------|
  | `--gradient-primary` | `[#003087, #0057B8]` | Primary brand gradient |
  | `--gradient-secondary` | `[#43E97B, #38F9D7]` | Secondary action gradient |
  ```

### Usage Examples

- **Suggestion:** Add code examples for each semantic role to improve clarity:
  ```
  ### Text

  | Role | Hex Code | Usage Example |
  |------|----------|---------------|
  | `--color-text-primary` | `#212121` | `Text(style: TextStyle(color: AppColors.textPrimary))` |
  ```

### Accessibility Information

- **Suggestion:** Add contrast ratio information for color combinations:
  ```
  | Combination | Contrast Ratio | WCAG Level |
  |-------------|----------------|------------|
  | Text Primary on Background | 16:1 | AAA |
  | Text Secondary on Background | 4.6:1 | AA |
  ```

## Implementation Recommendations

### 1. Centralize Color Definitions

Create a single source of truth for colors that directly maps to the palette document:

```dart
// lib/core/theme/app_colors.dart
class AppColors {
  // Private constructor to prevent instantiation
  AppColors._();

  // Text colors - Light Mode
  static const Color textPrimary = Color(0xFF212121); // --color-text-primary
  static const Color textSecondary = Color(0xFF757575); // --color-text-secondary
  static const Color textDisabled = Color(0xFFBDBDBD); // --color-text-disabled
  static const Color textLink = Color(0xFF1976D2); // --color-text-link
  static const Color textError = Color(0xFFD32F2F); // --color-text-error
  static const Color textSuccess = Color(0xFF388E3C); // --color-text-success
  static const Color textOnPrimary = Color(0xFFFFFFFF); // --color-text-on-primary
  static const Color textOnAccent = Color(0xFFFFFFFF); // --color-text-on-accent

  // Text colors - Dark Mode
  static const Color textPrimaryDark = Color(0xFFFFFFFF); // --color-text-primary-dark
  // ...

  // Transparent Colors
  static const Color transparent = Color(0x00000000); // For use instead of Colors.transparent
  static const Color overlayLight = Color(0xB3FFFFFF); // --color-overlay-light (70% white)
  static const Color overlayDark = Color(0xB3000000); // --color-overlay-dark (70% black)

  // Tab Colors with Gradients
  static const Color tabHome = Color(0xFF49D941); // --color-tab-home
  static const List<Color> tabHomeGradient = [Color(0xFF49D941), Color(0xFF36D1DC)]; // --gradient-tab-home
}
```

### 2. Create Semantic Getters for Backward Compatibility

Provide backward compatibility through getters that map Material Design names to semantic roles:

```dart
// In AppColors class
// Backward compatibility with Material Design naming
static Color get primary => AppColors.brandPrimary; // Maps to --color-primary
static Color get onPrimary => AppColors.textOnPrimary; // Maps to --color-text-on-primary
static Color get secondary => AppColors.brandSecondary; // Maps to --color-accent
static Color get onSecondary => AppColors.textOnAccent; // Maps to --color-text-on-accent
```

### 3. Implement Theme Extension for Custom Colors

Use Flutter's ThemeExtension for better type safety and theme switching:

```dart
// lib/core/theme/app_color_scheme.dart
class AppColorScheme extends ThemeExtension<AppColorScheme> {
  final Color textPrimary;
  final Color textSecondary;
  final Color textDisabled;
  final Color textLink;
  final Color textError;
  final Color textSuccess;
  final Color bgApp;
  final Color bgCard;
  final Color bgSurface;
  // ... other semantic colors

  const AppColorScheme({
    required this.textPrimary,
    required this.textSecondary,
    required this.textDisabled,
    required this.textLink,
    required this.textError,
    required this.textSuccess,
    required this.bgApp,
    required this.bgCard,
    required this.bgSurface,
    // ... other required parameters
  });

  static AppColorScheme light() {
    return AppColorScheme(
      textPrimary: AppColors.textPrimary,
      textSecondary: AppColors.textSecondary,
      textDisabled: AppColors.textDisabled,
      textLink: AppColors.textLink,
      textError: AppColors.textError,
      textSuccess: AppColors.textSuccess,
      bgApp: AppColors.bgApp,
      bgCard: AppColors.bgCard,
      bgSurface: AppColors.bgSurface,
      // ... other light mode colors
    );
  }

  static AppColorScheme dark() {
    return AppColorScheme(
      textPrimary: AppColors.textPrimaryDark,
      textSecondary: AppColors.textSecondaryDark,
      textDisabled: AppColors.textDisabledDark,
      textLink: AppColors.textLinkDark,
      textError: AppColors.textErrorDark,
      textSuccess: AppColors.textSuccessDark,
      bgApp: AppColors.bgAppDark,
      bgCard: AppColors.bgCardDark,
      bgSurface: AppColors.bgSurfaceDark,
      // ... other dark mode colors
    );
  }

  @override
  ThemeExtension<AppColorScheme> copyWith({
    Color? textPrimary,
    Color? textSecondary,
    // ... other parameters
  }) {
    return AppColorScheme(
      textPrimary: textPrimary ?? this.textPrimary,
      textSecondary: textSecondary ?? this.textSecondary,
      // ... other parameters
    );
  }

  @override
  ThemeExtension<AppColorScheme> lerp(ThemeExtension<AppColorScheme>? other, double t) {
    if (other is! AppColorScheme) {
      return this;
    }

    return AppColorScheme(
      textPrimary: Color.lerp(textPrimary, other.textPrimary, t)!,
      textSecondary: Color.lerp(textSecondary, other.textSecondary, t)!,
      // ... other lerp parameters
    );
  }
}
```

### 4. Update ThemeData to Include the Extension

Modify the `app_theme.dart` file to include the custom color scheme extension:

```dart
// In AppTheme class
static ThemeData get lightTheme {
  return ThemeData(
    // ... existing theme data
    extensions: [
      AppColorScheme.light(),
    ],
  );
}

static ThemeData get darkTheme {
  return ThemeData(
    // ... existing theme data
    extensions: [
      AppColorScheme.dark(),
    ],
  );
}
```

### 5. Update Widget Usage

Refactor widgets to use the semantic color roles:

```dart
// Before
Text(
  'Hello World',
  style: TextStyle(color: Colors.black),
)

// After
Text(
  'Hello World',
  style: TextStyle(color: Theme.of(context).extension<AppColorScheme>()!.textPrimary),
)

// Or create extension methods for easier access
extension ThemeExtensions on BuildContext {
  AppColorScheme get appColors => Theme.of(this).extension<AppColorScheme>()!;
}

// Then use it like this
Text(
  'Hello World',
  style: TextStyle(color: context.appColors.textPrimary),
)
```

## Conclusion

The Lekky app has a well-defined color palette in `lekky_pallet.md` and a solid theming foundation in the codebase, but there are opportunities to better align the implementation with the palette document. The app already uses a structured approach with `app_colors.dart`, `app_text_styles.dart`, and `app_theme.dart`, but could benefit from more consistent naming and the use of Flutter's ThemeExtension API.

Key findings:
1. The app uses Material Design naming conventions while the palette uses semantic role names
2. There are instances of hardcoded colors in UI components, particularly in the theme toggle widget
3. The app has a comprehensive theme implementation but doesn't fully leverage Flutter's ThemeExtension
4. The app defines several transparent and opacity-based colors that aren't explicitly in the palette

Key recommendations:
1. Align color naming conventions between the code and palette document
2. Replace direct color references with semantic color roles
3. Enhance the palette document with additional roles for transparent colors and gradients
4. Implement a custom ThemeExtension for better type safety and theme switching
5. Create extension methods for easier access to theme colors throughout the app

By implementing these recommendations, the Lekky app will achieve better design consistency, improved maintainability, and enhanced accessibility. The changes will create a more cohesive design system that's easier to maintain and extend in the future, while ensuring that the app's visual appearance matches the intended design across both light and dark modes.
