// File: test/core/utils/error_handler_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:lekky/core/utils/error_handler.dart';

void main() {
  group('AppError', () {
    test('should create an error with default values', () {
      final error = AppError(message: 'Test error');
      
      expect(error.message, 'Test error');
      expect(error.severity, ErrorSeverity.medium);
      expect(error.type, ErrorType.unknown);
      expect(error.details, null);
      expect(error.stackTrace, isNotNull);
      expect(error.timestamp, isNotNull);
    });

    test('should create an error with custom values', () {
      final error = AppError(
        message: 'Test error',
        severity: ErrorSeverity.high,
        type: ErrorType.network,
        details: 'Test details',
      );
      
      expect(error.message, 'Test error');
      expect(error.severity, ErrorSeverity.high);
      expect(error.type, ErrorType.network);
      expect(error.details, 'Test details');
      expect(error.stackTrace, isNotNull);
      expect(error.timestamp, isNotNull);
    });

    test('should create a network error', () {
      final error = AppError.network('Network error');
      
      expect(error.message, 'Network error');
      expect(error.severity, ErrorSeverity.medium);
      expect(error.type, ErrorType.network);
    });

    test('should create a database error', () {
      final error = AppError.database('Database error');
      
      expect(error.message, 'Database error');
      expect(error.severity, ErrorSeverity.high);
      expect(error.type, ErrorType.database);
    });

    test('should create a validation error', () {
      final error = AppError.validation('Validation error');
      
      expect(error.message, 'Validation error');
      expect(error.severity, ErrorSeverity.low);
      expect(error.type, ErrorType.validation);
    });

    test('should convert to string', () {
      final error = AppError(message: 'Test error');
      
      expect(error.toString(), 'AppError: Test error (unknown, medium)');
    });
  });

  group('ErrorHandler', () {
    setUp(() {
      ErrorHandler.clearErrors();
    });

    test('should add an error', () {
      final error = AppError(message: 'Test error');
      ErrorHandler.addError(error);
      
      expect(ErrorHandler.errors.length, 1);
      expect(ErrorHandler.errors.first, error);
    });

    test('should clear errors', () {
      ErrorHandler.addError(AppError(message: 'Test error 1'));
      ErrorHandler.addError(AppError(message: 'Test error 2'));
      
      expect(ErrorHandler.errors.length, 2);
      
      ErrorHandler.clearErrors();
      
      expect(ErrorHandler.errors.length, 0);
    });

    test('should get errors by type', () {
      ErrorHandler.addError(AppError.network('Network error'));
      ErrorHandler.addError(AppError.database('Database error'));
      ErrorHandler.addError(AppError.network('Another network error'));
      
      final networkErrors = ErrorHandler.getErrorsByType(ErrorType.network);
      
      expect(networkErrors.length, 2);
      expect(networkErrors[0].message, 'Network error');
      expect(networkErrors[1].message, 'Another network error');
    });

    test('should get errors by severity', () {
      ErrorHandler.addError(AppError(message: 'Error 1', severity: ErrorSeverity.low));
      ErrorHandler.addError(AppError(message: 'Error 2', severity: ErrorSeverity.medium));
      ErrorHandler.addError(AppError(message: 'Error 3', severity: ErrorSeverity.high));
      ErrorHandler.addError(AppError(message: 'Error 4', severity: ErrorSeverity.medium));
      
      final mediumErrors = ErrorHandler.getErrorsBySeverity(ErrorSeverity.medium);
      
      expect(mediumErrors.length, 2);
      expect(mediumErrors[0].message, 'Error 2');
      expect(mediumErrors[1].message, 'Error 4');
    });

    test('should handle an exception', () {
      final error = ErrorHandler.handleException(Exception('Test exception'));
      
      expect(error.message, 'Exception: Test exception');
      expect(ErrorHandler.errors.length, 1);
      expect(ErrorHandler.errors.first, error);
    });
  });
}
