// File: lib/features/notifications/data/sources/storage_factory.dart

import 'package:shared_preferences/shared_preferences.dart';
import '../../../../core/data/database/notification_db_helper.dart';
import '../../../../core/utils/logger.dart';
import 'local_notification_data_source.dart';
import 'local_storage_data_source.dart';
import 'sql_notification_data_source.dart';

/// Factory class for creating the appropriate storage data source
class StorageFactory {
  // Private constructor
  StorageFactory._privateConstructor();

  // Static instance
  static final StorageFactory _instance = StorageFactory._privateConstructor();

  // Factory constructor to return the same instance
  factory StorageFactory() {
    return _instance;
  }

  // Migration status key
  static const String _migrationStatusKey = 'notification_storage_migrated';
  static const String _migrationVersionKey = 'notification_storage_version';
  static const int _currentMigrationVersion = 1;

  // Cache for data source instances
  LocalStorageDataSource? _sqlDataSource;
  LocalStorageDataSource? _sharedPrefsDataSource;

  // Flag to track if migration is in progress
  bool _isMigrationInProgress = false;

  /// Create the appropriate storage data source
  Future<LocalStorageDataSource> createStorageDataSource() async {
    // Check if the migration has been completed
    final migrated = await _isMigrationComplete();

    if (migrated) {
      // Use SQLite implementation
      logger.d('StorageFactory: Using SQLite implementation');
      return _getSQLDataSource();
    } else {
      // Use SharedPreferences implementation
      logger.d('StorageFactory: Using SharedPreferences implementation');
      return _getSharedPrefsDataSource();
    }
  }

  /// Get the SQLite data source
  LocalStorageDataSource _getSQLDataSource() {
    _sqlDataSource ??= SQLNotificationDataSource(
      dbHelper: NotificationDBHelper(),
    );
    return _sqlDataSource!;
  }

  /// Get the SharedPreferences data source
  LocalStorageDataSource _getSharedPrefsDataSource() {
    _sharedPrefsDataSource ??= LocalNotificationDataSource();
    return _sharedPrefsDataSource!;
  }

  /// Check if the migration has been completed
  Future<bool> _isMigrationComplete() async {
    final prefs = await SharedPreferences.getInstance();
    final migrated = prefs.getBool(_migrationStatusKey) ?? false;
    final version = prefs.getInt(_migrationVersionKey) ?? 0;

    return migrated && version >= _currentMigrationVersion;
  }

  /// Migrate data from SharedPreferences to SQLite
  Future<bool> migrateToSQLite({
    Function(double progress)? onProgress,
  }) async {
    if (_isMigrationInProgress) {
      logger.w('StorageFactory: Migration already in progress');
      return false;
    }

    // Check if migration is already complete
    if (await _isMigrationComplete()) {
      logger.i('StorageFactory: Migration already completed');
      if (onProgress != null) {
        onProgress(1.0);
      }
      return true;
    }

    _isMigrationInProgress = true;

    try {
      logger.i('StorageFactory: Starting migration to SQLite');
      if (onProgress != null) {
        onProgress(0.1);
      }

      // Get data sources
      final srcDataSource = _getSharedPrefsDataSource();
      final destDataSource = _getSQLDataSource();

      // Initialize both data sources
      await srcDataSource.initialize();
      await destDataSource.initialize();

      if (onProgress != null) {
        onProgress(0.2);
      }

      // 1. Migrate notifications
      final notifications = await srcDataSource.getNotifications();
      logger
          .d('StorageFactory: Migrating ${notifications.length} notifications');

      for (final notification in notifications) {
        await destDataSource.addNotification(notification);
      }

      if (onProgress != null) {
        onProgress(0.4);
      }

      // 2. Migrate notification settings
      final notificationsEnabled =
          await srcDataSource.areNotificationsEnabled();
      await destDataSource.setNotificationsEnabled(notificationsEnabled);

      if (onProgress != null) {
        onProgress(0.5);
      }

      // 3. Migrate reminder settings
      final reminderEnabled =
          await srcDataSource.areMeterReadingRemindersEnabled();
      await destDataSource.setMeterReadingRemindersEnabled(reminderEnabled);

      final reminderFrequency =
          await srcDataSource.getMeterReadingReminderFrequency();
      await destDataSource.setMeterReadingReminderFrequency(reminderFrequency);

      final lastReminderDate =
          await srcDataSource.getLastMeterReadingReminderDate();
      if (lastReminderDate != null) {
        await destDataSource.setLastMeterReadingReminderDate(lastReminderDate);
      }

      final reminderTime = await srcDataSource.getMeterReadingReminderTime();
      await destDataSource.setMeterReadingReminderTime(reminderTime);

      if (onProgress != null) {
        onProgress(0.7);
      }

      // 4. Migrate timezone-aware reminder settings
      final timezoneAwareTime =
          await srcDataSource.getTimezoneAwareReminderTime();
      if (timezoneAwareTime != null) {
        await destDataSource.setTimezoneAwareReminderTime(timezoneAwareTime);
      }

      final nextReminderDate =
          await srcDataSource.getNextMeterReadingReminderDate();
      if (nextReminderDate != null) {
        await destDataSource.saveNextMeterReadingReminderDate(nextReminderDate);
      }

      final timezoneInfo =
          await srcDataSource.getNextMeterReadingReminderTimezoneInfo();
      if (timezoneInfo != null && nextReminderDate != null) {
        await destDataSource.saveNextMeterReadingReminderDateWithTimezone(
          nextReminderDate,
          timezoneInfo['timezone'] ?? 'UTC',
          timezoneInfo['isDST'] ?? false,
        );
      }

      if (onProgress != null) {
        onProgress(0.9);
      }

      // Mark migration as complete
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_migrationStatusKey, true);
      await prefs.setInt(_migrationVersionKey, _currentMigrationVersion);

      logger.i('StorageFactory: Migration completed successfully');

      if (onProgress != null) {
        onProgress(1.0);
      }

      _isMigrationInProgress = false;
      return true;
    } catch (e) {
      logger.e('StorageFactory: Error during migration', details: e.toString());
      _isMigrationInProgress = false;
      return false;
    }
  }

  /// Revert to SharedPreferences (for testing/debugging purposes only)
  Future<void> revertToSharedPreferences() async {
    logger
        .w('StorageFactory: Reverting to SharedPreferences (for testing only)');
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_migrationStatusKey, false);
    await prefs.setInt(_migrationVersionKey, 0);
  }
}
