// File: lib/core/shared_modules/shared_modules.dart
// This file exports all shared modules for easy importing

export 'settings_model.dart';
export 'settings_service.dart';
export 'settings_provider.dart';
export 'base_settings_widget.dart';
export 'currency_selector.dart' hide CurrencySelector;
export 'currency_selector_adapter.dart'; // New radio-based currency selector adapter
export 'radio_selectors_adapter.dart'; // New radio-based selectors adapter
export 'alert_threshold_selector.dart';
export 'days_in_advance_selector.dart';
export 'radio_alert_threshold_selector_adapter.dart'; // New radio-based alert threshold selector adapter
export 'radio_days_in_advance_selector_adapter.dart'; // New radio-based days in advance selector adapter
// Removed dialog-based selector adapters in favor of inline input fields
export 'date_format_selector.dart';
export 'date_info_selector.dart';
export 'appearance_selector.dart';
export 'notification_settings_selector.dart';
export 'data_import_widget.dart';
export 'language_selector.dart';
export 'initial_meter_credit_selector.dart';
