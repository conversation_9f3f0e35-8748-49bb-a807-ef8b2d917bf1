// File: lib/core/platform/notification/notification_adapter.dart

import 'package:flutter/material.dart';
import '../../models/notification_model.dart';
import '../../models/reminder_time_model.dart';

/// Interface for platform-specific notification functionality
abstract class NotificationAdapter {
  /// Initialize the notification adapter
  Future<void> initialize();

  /// Check if notification permissions are granted
  Future<bool> checkNotificationPermissions();

  /// Request notification permissions
  Future<bool> requestNotificationPermissions();

  /// Check if notification permission is permanently denied
  Future<bool> isNotificationPermissionPermanentlyDenied();

  /// Open app settings
  Future<bool> openNotificationSettings();

  /// Show a notification immediately
  Future<void> showNotification({
    required int id,
    required String title,
    required String body,
    String? payload,
  });

  /// Schedule a notification for a future date
  Future<bool> scheduleNotification({
    required int id,
    required String title,
    required String body,
    required DateTime scheduledDate,
    String? payload,
    int maxRetries = 3,
  });

  /// Reschedule a notification by ID
  Future<bool> rescheduleNotification(int id);

  /// Reschedule all pending notifications
  Future<int> rescheduleAllNotifications();

  /// Cancel a notification
  Future<void> cancelNotification(int id);

  /// Cancel all notifications
  Future<void> cancelAllNotifications();

  /// Create notification channels (Android-specific, no-op on iOS)
  Future<void> createNotificationChannels();

  /// Check if the time zone has changed since the last app run
  Future<bool> hasTimeZoneChanged();

  /// Get current time zone
  String getCurrentTimeZone();

  /// Get notification channel settings (Android-specific, returns default on iOS)
  Future<Map<String, dynamic>> getChannelSettings(String channelId);

  /// Are exact alarms allowed (Android 12+, always true on iOS)
  Future<bool> canScheduleExactAlarms();

  /// Request exact alarm permission (Android 12+, no-op on iOS)
  Future<void> requestExactAlarmPermission();
}
