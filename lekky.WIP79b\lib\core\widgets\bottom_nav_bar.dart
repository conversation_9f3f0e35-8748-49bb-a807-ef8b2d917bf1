// File: lib/core/widgets/bottom_nav_bar.dart
import 'package:flutter/material.dart';
import '../theme/app_colors.dart';
import '../theme/app_text_styles.dart';

/// A custom bottom navigation bar that stays visible when the keyboard appears
class BottomNavBar extends StatelessWidget {
  final int currentIndex;
  final Function(int) onTap;
  final List<BottomNavItem> items;
  final Color? backgroundColor;
  final Color? selectedItemColor;
  final Color? unselectedItemColor;
  final double? elevation;
  final double? iconSize;
  final TextStyle? selectedLabelStyle;
  final TextStyle? unselectedLabelStyle;
  final double? height;
  final bool showLabels;
  final bool showSelectedLabels;
  final bool showUnselectedLabels;

  const BottomNavBar({
    Key? key,
    required this.currentIndex,
    required this.onTap,
    required this.items,
    this.backgroundColor,
    this.selectedItemColor,
    this.unselectedItemColor,
    this.elevation,
    this.iconSize,
    this.selectedLabelStyle,
    this.unselectedLabelStyle,
    this.height,
    this.showLabels = true,
    this.showSelectedLabels = true,
    this.showUnselectedLabels = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final defaultBackgroundColor =
        isDarkMode ? AppColors.surfaceDark : AppColors.surface;
    final defaultSelectedItemColor =
        isDarkMode ? AppColors.primaryDark : AppColors.primary;
    final defaultUnselectedItemColor = isDarkMode
        ? AppColors.onSurfaceVariantDark
        : AppColors.onSurfaceVariant;

    return Container(
      height: height ?? 60,
      decoration: BoxDecoration(
        color: backgroundColor ?? defaultBackgroundColor,
        boxShadow: [
          BoxShadow(
            color:
                isDarkMode ? AppColors.shadowColorDark : AppColors.shadowColor,
            blurRadius: elevation ?? 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: items.asMap().entries.map((entry) {
          final index = entry.key;
          final item = entry.value;
          final isSelected = index == currentIndex;

          return Expanded(
            child: InkWell(
              onTap: () => onTap(index),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    isSelected ? item.activeIcon : item.icon,
                    color: isSelected
                        ? (item.activeColor ??
                            selectedItemColor ??
                            defaultSelectedItemColor)
                        : (unselectedItemColor ?? defaultUnselectedItemColor),
                    size: iconSize ?? 24,
                  ),
                  if (showLabels &&
                      ((isSelected && showSelectedLabels) ||
                          (!isSelected && showUnselectedLabels)))
                    Padding(
                      padding: const EdgeInsets.only(top: 4),
                      child: Text(
                        item.label,
                        style: isSelected
                            ? (selectedLabelStyle ??
                                AppTextStyles.labelMedium.copyWith(
                                  color: item.activeColor ??
                                      selectedItemColor ??
                                      defaultSelectedItemColor,
                                ))
                            : (unselectedLabelStyle ??
                                AppTextStyles.labelMedium.copyWith(
                                  color: unselectedItemColor ??
                                      defaultUnselectedItemColor,
                                )),
                        textAlign: TextAlign.center,
                      ),
                    ),
                ],
              ),
            ),
          );
        }).toList(),
      ),
    );
  }
}

/// A model class for bottom navigation bar items
class BottomNavItem {
  final String label;
  final IconData icon;
  final IconData activeIcon;
  final Color? activeColor;

  const BottomNavItem({
    required this.label,
    required this.icon,
    IconData? activeIcon,
    this.activeColor,
  }) : activeIcon = activeIcon ?? icon;
}
