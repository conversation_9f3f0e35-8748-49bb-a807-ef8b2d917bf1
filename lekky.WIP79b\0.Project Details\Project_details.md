# Lekky Flutter App - Project Details

## 📋 1. Project Metadata

### Project Name
- **Internal Name**: lekky
- **Public App Name**: Lekky Meter

### Version and Build Number
- **Version**: 1.0.1 beta
- **Build Number**: 1

### Package Name
- **Android applicationId**: com.example.lekky
- **iOS bundleId**: com.example.lekky

### Supported Platforms
- Android
- iOS
- Web (potential support)
- Desktop (potential support via macOS, Linux, Windows)

### Minimum SDK Versions
- **Android**: minSdkVersion 21 (Android 5.0 Lollipop)
- **iOS**: deployment_target 12.0

## 🏗️ 2. Architecture & Codebase Setup

### State Management
- **Provider Pattern** with ChangeNotifier
- Feature-specific controllers:
  - HomeController
  - HistoryController
  - CostController
  - SettingsController
  - SetupController
  - NotificationProvider
  - ThemeProvider
- **EventBus** for cross-component communication

### Folder Structure
- **Feature-first Modular Architecture** with layered approach:
  ```
  lib/
  ├── core/                  # Core functionality used across the app
  │   ├── constants/         # App-wide constants
  │   ├── data/              # Core data sources and repositories
  │   ├── di/                # Dependency injection
  │   ├── extensions/        # Dart extensions
  │   ├── models/            # Shared data models
  │   ├── providers/         # App-wide providers
  │   ├── services/          # Core services
  │   ├── settings/          # Settings implementation
  │   ├── shared_modules/    # Shared modules
  │   ├── theme/             # Theme definitions
  │   ├── utils/             # Utility functions
  │   └── widgets/           # Shared widgets
  ├── features/              # Feature modules
  │   ├── backup/            # Backup feature
  │   ├── cost/              # Cost feature
  │   ├── history/           # History feature
  │   ├── home/              # Home feature
  │   ├── settings/          # Settings feature
  │   ├── setup/             # Setup feature
  │   ├── splash/            # Splash feature
  │   └── welcome/           # Welcome feature
  ├── app_scaffold.dart      # Main app scaffold
  ├── main.dart              # Entry point
  └── test_notification.dart # Notification testing utility
  ```

### Feature Module Structure
Each feature follows a consistent structure:
```
feature_name/
├── data/                # Data layer
│   └── repositories     # Feature-specific repositories
├── domain/             # Domain layer
│   ├── models/         # Feature-specific models
│   └── usecases/       # Business logic
└── presentation/       # UI layer
    ├── controllers/    # Screen controllers
    ├── screens/        # Full screens
    └── widgets/        # Feature-specific widgets
```

### App Flow
- **Flow**: SplashScreen → WelcomePage → SetupPage → HomePage (first launch) or SplashScreen → HomePage (subsequent)
- **Navigation**: Navigator 1.0 with named routes
- **Bottom Navigation**: Fixed bottom navigation bar that remains visible with keyboard

### Dependency Injection
- **get_it** package for service location and dependency injection
- Well-organized `service_locator.dart` for registering and retrieving dependencies

### Backend Connection
- **Local-only** application
- No remote backend or API connections
- Data stored locally using SQLite and SharedPreferences

## 🎨 3. UI & UX Guidelines

### Theme Standardization
- **Material Design 3** with custom ColorScheme
- **Dark Mode Support** with dedicated dark theme
- Consistent color palette defined in `AppColors` class
- Standardized text styles in `AppTextStyles` class
- Button styles in `AppButtonStyles` class

### Widget Consistency
- **Standardized Components**:
  - Custom buttons (GradientButton, OutlinedCancelButton, SolidSaveButton)
  - Consistent card styles (AppCard, MeterValueCard, CostCard)
  - Standardized input fields (AppTextField)
  - Message banner (MessageBanner)
  - Dialog components (ConfirmationDialog, DatePickerDialog, etc.)
  - Toggle switches for settings
  - Expandable sections in settings

### Layout Guidelines
- Portrait orientation only
- Fixed screen banners should be 96px in height with message bar at 32px high
- Fixed bottom navigation bar that remains visible with keyboard
- Dialog boxes should have an 'X' close button in top right corner
- Position dialogs 82px closer to top of screen, make 40px taller while maintaining bottom position

### Responsiveness Rules
- Responsive layouts that adapt to different screen sizes
- Minimum supported width: Standard smartphone width
- Support for system font scaling

### Accessibility
- High contrast colors for readability
- Semantic labels for screen readers

## 🛠️ 4. Development Tools & Configs

### Flutter SDK Version
- Flutter 3.19.0 or higher

### Dart SDK Version
- Dart 3.3.3 or higher

### Linter Ruleset
- flutter_lints package (version 3.0.0)
- Default analysis_options.yaml

### Code Formatting Style
- Standard Dart formatting (dart format)
- 80-character line length
- 2-space indentation

### Testing Frameworks
- **Unit Testing**: flutter_test
- **Widget Testing**: flutter_test
- **Mocking**: mockito (version 5.4.4)
- **Build Runner**: build_runner (version 2.4.8)

## 📦 5. Dependencies

### State Management
- **provider**: ^6.1.2

### Storage
- **shared_preferences**: ^2.2.0
- **sqflite**: ^2.3.0
- **path_provider**: ^2.1.0

### UI Components
- **cupertino_icons**: ^1.0.6
- **fl_chart**: ^0.66.2
- **table_calendar**: ^3.0.9

### Utilities
- **intl**: ^0.18.1
- **timezone**: ^0.9.2
- **csv**: ^5.1.1
- **get_it**: ^7.6.7
- **permission_handler**: ^11.3.0
- **file_picker**: ^8.0.0
- **url_launcher**: ^6.2.5

### Notifications
- **flutter_local_notifications**: ^17.2.3

### Payments
- **flutter_paypal**: ^0.2.0

### Development Dependencies
- **flutter_launcher_icons**: ^0.13.1
- **flutter_lints**: ^3.0.0
- **mockito**: ^5.4.4
- **build_runner**: ^2.4.8
- **plugin_platform_interface**: ^2.1.8

## 📡 6. Services & Integrations

### Notification System
- **flutter_local_notifications** for local notifications
- Notification types:
  - Meter reading reminders (configurable frequency)
  - Low balance alerts (based on threshold and days in advance)
- Notification scheduling during app startup
- Notification service for centralized management

### Data Backup & Export
- Export to CSV format
- Backup files stored in Downloads folder as lekky_export_[version].csv
- Version information included in header
- Numeric codes for data types (0 = Meter Reading, 1 = Top Up)
- Import functionality with validation

### Payments
- **PayPal** integration for donations
  - Using flutter_paypal package
  - Sandbox mode for testing
  - Preset/custom amounts
  - Thank-you dialog
  - Client ID: EJIOu0NyQ-v0gAvmYOshVBUCDQME_lVqjeEtF40mCkpqNCrnYWxXXgz8kMhbx61aPMNL2YiBWGHbHlFd

### Error Handling
- Custom error handling via ErrorHandler class
- Result wrapper for operation outcomes
- Logging system with Logger class

## 🔒 7. Security & Privacy

### Secrets Management
- API keys and credentials stored directly in code (PayPal client ID)
- No environment variables or secure storage implementation

### User Data Policy
- All data stored locally on the device
- No data transmitted to external servers
- Backup files stored in Downloads folder with standardized naming

### Local Storage Strategy
- **SQLite** (via sqflite) for meter entries and historical data
- **SharedPreferences** for app settings and configuration

### Permissions List
- **Storage**: Required for exporting data to files
- **Notifications**: Required for alerts and reminders

### Privacy Policy
- Available in privacy_policy.md
- Key points:
  - No personal data collection
  - Local-only storage
  - Limited permissions
  - No data sharing with third parties

## 📱 8. Features & Functionality

### Core Features
- **Meter Reading Tracking**: Track prepaid meter readings that decrease with usage and increase with top-ups
- **Usage Calculation**: Calculate recent and total average usage
- **Cost Estimation**: Estimate electricity costs based on usage patterns
- **History View**: View and filter historical entries with validation
- **Notifications**: Meter reading reminders and low balance alerts
- **Data Backup**: Export and import functionality
- **Settings**: Comprehensive settings for customization

### Data Validation
- Validation of meter readings for consistency
- Warning indicators for potentially invalid entries
- Validation based on previous and subsequent entries

### Calculations
- Recent Average: ((LastMeterReadingValue + TotalTopUpCount) - NewMeterReadingValue) / DaysBetweenReadings
- Total Average: ((first reading + all top ups before current row) - current row reading) / days between first and current

### Settings Options
- **Region**: Currency selection
- **Alerts & Notifications**: Alert threshold, days in advance, notification toggles
- **Date Settings**: Date format, date and time display
- **Appearance**: Theme selection (light/dark/system)
- **Data Management**: Backup, import, clear data

## 📄 9. Assets & Resources

### Images
- **background.png**: Main background image
- **dark_mode_background.png**: Background image for dark mode
- **icon.png**: App icon
- **splash.png**: Splash screen image

### Configuration Files
- **pubspec.yaml**: Dependencies and app metadata
- **analysis_options.yaml**: Linter rules
- **android/app/build.gradle**: Android build configuration
- **ios/Runner.xcodeproj/project.pbxproj**: iOS build configuration

### Development Scripts
- **run_app.bat**: Script to run the app on a specific device

## 🏷️ 10. Publishing Details

### Play Store Setup
- **Package name**: com.example.lekky
- **Release tracks**: Not specified
- **App assets**:
  - Icon: assets/icon.png
  - Screenshots: Not specified

### App Store Connect Setup
- **Bundle ID**: com.example.lekky
- **App Icons**: Configured via flutter_launcher_icons
- **Provisioning Profiles**: Not specified
- **TestFlight**: Not specified

## 🧹 11. Cleanup Recommendations

### Files to Review
1. **Backup Files**: Remove any files with `.dartBU` extension as they are backups not needed for production:
   - `lib/core/shared_modules/alert_threshold_selector.dartBU`
   - `lib/features/history/presentation/widgets/entry_edit_dialogWIP.dart`
   - `lib/features/history/presentation/widgets/paginated_history_table.dartBU`

2. **Duplicate Implementations**:
   - Review and consolidate duplicate implementations in the settings widgets
   - Consider merging similar selector implementations

3. **Repository Fixes**:
   - Review and clean up the cost repository files:
     - `cost_repository.dart`
     - `cost_repository_fix.dart`
     - `cost_repository_fixed.dart`

4. **Temporary Directories**:
   - Review and clean up any temporary files in the `For_Consideration` directory

5. **Auto-generated Directories**:
   - `.dart_tool/`
   - `.idea/`
   - `build/`
   - Platform-specific ephemeral directories

### Code Quality Improvements
1. Ensure all test files have corresponding implementation files
2. Standardize event emissions across all settings
3. Ensure SettingsProvider is the single source of truth
4. Implement consistent dark mode across all screens
5. Ensure all text in Setup screens uses theme-aware colors

## 📈 12. Project Health & Status

### Known Bugs/Issues
- Settings screen has issues displaying correct user-selected values for various settings
- Add/Edit Entry dialogs do not correctly show both date and time pickers when 'Date and time' is selected in settings
- History filter should respect the Date & Time setting option

### Next Milestones
- **Short-term**:
  - Fix date and time picker issues across the app
  - Improve notification system reliability
  - Enhance backup and restore functionality
- **Long-term**:
  - Potential internationalization improvements
  - Enhanced data visualization
  - Improved validation and error handling
