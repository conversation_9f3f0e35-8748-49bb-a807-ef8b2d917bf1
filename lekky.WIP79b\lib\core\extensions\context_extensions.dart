// File: lib/core/extensions/context_extensions.dart
import 'package:flutter/material.dart';
import '../theme/app_colors.dart';

/// Extensions on BuildContext for easier theme access
extension ThemeExtensions on BuildContext {
  /// Check if dark mode is active
  bool get isDarkMode => Theme.of(this).brightness == Brightness.dark;

  /// Get the color scheme
  ColorScheme get colorScheme => Theme.of(this).colorScheme;

  /// Get the text theme
  TextTheme get textTheme => Theme.of(this).textTheme;

  /// Get theme-aware text color for primary text
  Color get textColor =>
      isDarkMode ? AppColors.onBackgroundDark : AppColors.onBackground;

  /// Get theme-aware text color for secondary text
  Color get secondaryTextColor =>
      isDarkMode ? AppColors.textSecondaryDark : AppColors.textSecondary;

  /// Get theme-aware text color for input fields
  Color get inputTextColor =>
      isDarkMode ? AppColors.inputTextDark : AppColors.inputTextLight;

  /// Get theme-aware text color for input labels
  Color get inputLabelColor =>
      isDarkMode ? AppColors.inputLabelDark : AppColors.inputLabelLight;

  /// Get theme-aware text color for input hints
  Color get inputHintColor =>
      isDarkMode ? AppColors.inputHintDark : AppColors.inputHintLight;

  /// Get theme-aware color for input borders
  Color get inputBorderColor =>
      isDarkMode ? AppColors.inputBorderDark : AppColors.inputBorderLight;

  /// Get theme-aware color for focused input borders
  Color get inputFocusedBorderColor => isDarkMode
      ? AppColors.inputFocusedBorderDark
      : AppColors.inputFocusedBorderLight;

  /// Get theme-aware color for date picker text
  Color get datePickerTextColor =>
      isDarkMode ? AppColors.datePickerTextDark : AppColors.datePickerTextLight;

  /// Get theme-aware color for helper text
  Color get helperTextColor =>
      isDarkMode ? AppColors.helperTextDark : AppColors.helperTextLight;
}
