// File: test/features/notifications/data/repositories/notification_repository_impl_test.dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:lekky/core/platform/notification/notification_adapter.dart';
import 'package:lekky/core/platform/permissions/permission_adapter.dart';
import 'package:lekky/core/platform/timezone/timezone_adapter.dart';
import 'package:lekky/features/notifications/data/repositories/notification_repository_impl.dart';
import 'package:lekky/features/notifications/data/sources/local_storage_data_source.dart';
import 'package:lekky/features/notifications/data/sources/platform_notification_data_source.dart';
import 'package:lekky/features/notifications/data/sources/sql_notification_data_source.dart';
import 'package:lekky/features/notifications/data/sources/storage_factory.dart';
import 'package:lekky/features/notifications/domain/models/reminder_time_model.dart';
import 'package:lekky/features/notifications/domain/models/timezone_aware_reminder_time.dart';

// Mock implementations
class MockStorageFactory implements StorageFactory {
  LocalStorageDataSource dataSource;
  bool migrateToSQLiteResult = true;
  bool revertToSharedPreferencesResult = true;
  int migrateToSQLiteCallCount = 0;
  int revertToSharedPreferencesCallCount = 0;

  MockStorageFactory(this.dataSource);

  @override
  Future<LocalStorageDataSource> createStorageDataSource() async {
    return dataSource;
  }

  @override
  Future<bool> migrateToSQLite({Function(double)? onProgress}) async {
    migrateToSQLiteCallCount++;
    if (onProgress != null) {
      onProgress(0.5);
    }
    return migrateToSQLiteResult;
  }

  @override
  Future<bool> revertToSharedPreferences() async {
    revertToSharedPreferencesCallCount++;
    return revertToSharedPreferencesResult;
  }
}

class MockLocalStorageDataSource implements LocalStorageDataSource {
  bool areRemindersEnabledResult = true;
  int reminderFrequency = 30;
  ReminderTimeModel reminderTimeModel = ReminderTimeModel(
    timeOfDay: TimeOfDay(hour: 19, minute: 0),
  );
  DateTime? lastReminderDate;
  DateTime? nextReminderDate;
  bool areNotificationsEnabledResult = true;
  String storageType = "shared_prefs";
  bool isMigratedResult = false;
  TimezoneAwareReminderTime? timezoneAwareReminderTime;

  int setRemindersEnabledCallCount = 0;
  int setReminderFrequencyCallCount = 0;
  int setReminderTimeCallCount = 0;
  int setLastReminderDateCallCount = 0;
  int setNextReminderDateCallCount = 0;
  int saveTimezoneAwareReminderTimeCallCount = 0;

  @override
  Future<void> initialize() async {}

  @override
  Future<bool> areMeterReadingRemindersEnabled() async {
    return areRemindersEnabledResult;
  }

  @override
  Future<int> getMeterReadingReminderFrequency() async {
    return reminderFrequency;
  }

  @override
  Future<ReminderTimeModel> getMeterReadingReminderTime() async {
    return reminderTimeModel;
  }

  @override
  Future<DateTime?> getLastMeterReadingReminderDate() async {
    return lastReminderDate;
  }

  @override
  Future<DateTime?> getNextMeterReadingReminderDate() async {
    return nextReminderDate;
  }

  @override
  Future<bool> areNotificationsEnabled() async {
    return areNotificationsEnabledResult;
  }

  @override
  Future<void> setMeterReadingRemindersEnabled(bool enabled) async {
    setRemindersEnabledCallCount++;
    areRemindersEnabledResult = enabled;
  }

  @override
  Future<void> setMeterReadingReminderFrequency(int frequency) async {
    setReminderFrequencyCallCount++;
    reminderFrequency = frequency;
  }

  @override
  Future<void> setMeterReadingReminderTime(ReminderTimeModel time) async {
    setReminderTimeCallCount++;
    reminderTimeModel = time;
  }

  @override
  Future<void> setLastMeterReadingReminderDate(DateTime date) async {
    setLastReminderDateCallCount++;
    lastReminderDate = date;
  }

  @override
  Future<TimezoneAwareReminderTime?> getTimezoneAwareReminderTime() async {
    return timezoneAwareReminderTime;
  }

  @override
  Future<void> setNotificationsEnabled(bool enabled) async {
    areNotificationsEnabledResult = enabled;
  }

  // Stub implementation of other required methods
  @override
  noSuchMethod(Invocation invocation) => super.noSuchMethod(invocation);
}

class MockPlatformNotificationDataSource
    implements PlatformNotificationDataSource {
  bool checkPermissionsResult = true;
  bool requestPermissionsResult = true;
  bool isPermanentlyDeniedResult = false;
  bool scheduleNotificationResult = true;
  bool hasTimeZoneChangedResult = false;

  int initializeCallCount = 0;
  int checkPermissionsCallCount = 0;
  int requestPermissionsCallCount = 0;
  int scheduleMeterReadingReminderCallCount = 0;
  int cancelNotificationCallCount = 0;
  DateTime? lastScheduledDate;

  @override
  Future<void> initialize() async {
    initializeCallCount++;
  }

  @override
  Future<bool> checkNotificationPermissions() async {
    checkPermissionsCallCount++;
    return checkPermissionsResult;
  }

  @override
  Future<bool> requestNotificationPermissions() async {
    requestPermissionsCallCount++;
    return requestPermissionsResult;
  }

  @override
  Future<bool> isNotificationPermissionPermanentlyDenied() async {
    return isPermanentlyDeniedResult;
  }

  @override
  Future<bool> scheduleMeterReadingReminder(DateTime scheduledDate) async {
    scheduleMeterReadingReminderCallCount++;
    lastScheduledDate = scheduledDate;
    return scheduleNotificationResult;
  }

  @override
  Future<void> cancelNotification(int id) async {
    cancelNotificationCallCount++;
  }

  @override
  Future<bool> hasTimeZoneChanged() async {
    return hasTimeZoneChangedResult;
  }

  @override
  Future<DateTime?> getNextMeterReadingReminderDate() async {
    return null;
  }

  // Stub implementation of other required methods
  @override
  noSuchMethod(Invocation invocation) => super.noSuchMethod(invocation);
}

class MockNotificationAdapter implements NotificationAdapter {
  int initializeCallCount = 0;
  int cancelNotificationCallCount = 0;
  int cancelAllNotificationsCallCount = 0;

  @override
  Future<void> initialize() async {
    initializeCallCount++;
  }

  @override
  Future<void> cancelNotification(int id) async {
    cancelNotificationCallCount++;
  }

  @override
  Future<void> cancelAllNotifications() async {
    cancelAllNotificationsCallCount++;
  }

  // Stub implementation of other required methods
  @override
  noSuchMethod(Invocation invocation) => super.noSuchMethod(invocation);
}

class MockPermissionAdapter implements PermissionAdapter {
  PermissionStatus permissionStatus = PermissionStatus.granted;
  bool requestPermissionResult = true;
  bool isPermanentlyDeniedResult = false;
  bool openAppSettingsResult = true;
  bool showEducationUIResult = true;

  int initializeCallCount = 0;
  int checkPermissionCallCount = 0;
  int requestPermissionCallCount = 0;
  int isPermanentlyDeniedCallCount = 0;
  int openAppSettingsCallCount = 0;
  int showEducationUICallCount = 0;
  int showAlternativesUICallCount = 0;
  Function(PermissionStatus)? registeredListener;

  @override
  Future<void> initialize() async {
    initializeCallCount++;
  }

  @override
  Future<PermissionStatus> checkPermission() async {
    checkPermissionCallCount++;
    return permissionStatus;
  }

  @override
  Future<bool> requestPermission({PermissionCallback? callback}) async {
    requestPermissionCallCount++;
    if (callback != null) {
      callback(requestPermissionResult);
    }
    return requestPermissionResult;
  }

  @override
  Future<bool> isPermanentlyDenied() async {
    isPermanentlyDeniedCallCount++;
    return isPermanentlyDeniedResult;
  }

  @override
  Future<bool> openAppSettings() async {
    openAppSettingsCallCount++;
    return openAppSettingsResult;
  }

  @override
  Future<bool> showPermissionEducationUI() async {
    showEducationUICallCount++;
    return showEducationUIResult;
  }

  @override
  Future<void> showPermissionDeniedAlternativesUI() async {
    showAlternativesUICallCount++;
  }

  @override
  void registerPermissionStatusListener(
      void Function(PermissionStatus) listener) {
    registeredListener = listener;
  }

  @override
  void unregisterPermissionStatusListener(
      void Function(PermissionStatus) listener) {
    if (registeredListener == listener) {
      registeredListener = null;
    }
  }

  // Stub implementation of other required methods
  @override
  noSuchMethod(Invocation invocation) => super.noSuchMethod(invocation);
}

class MockTimezoneAdapter implements TimezoneAdapter {
  String currentTimezone = 'Europe/London';
  String? lastKnownTimezone;
  bool hasTimezoneChangedResult = false;
  bool isInDSTResult = false;
  double timezoneOffset = 0.0;
  String timezoneAbbreviation = 'GMT';

  int initializeCallCount = 0;
  int getCurrentTimezoneCallCount = 0;
  int getLastKnownTimezoneCallCount = 0;
  int hasTimezoneChangedCallCount = 0;
  Function()? timezoneChangeCallback;

  @override
  Future<void> initialize() async {
    initializeCallCount++;
  }

  @override
  String getCurrentTimezone() {
    getCurrentTimezoneCallCount++;
    return currentTimezone;
  }

  @override
  Future<String?> getLastKnownTimezone() async {
    getLastKnownTimezoneCallCount++;
    return lastKnownTimezone;
  }

  @override
  Future<bool> hasTimezoneChanged() async {
    hasTimezoneChangedCallCount++;
    return hasTimezoneChangedResult;
  }

  @override
  bool isInDaylightSavingTime(DateTime dateTime) {
    return isInDSTResult;
  }

  @override
  double getTimezoneOffset(DateTime dateTime) {
    return timezoneOffset;
  }

  @override
  String getTimezoneAbbreviation(DateTime dateTime) {
    return timezoneAbbreviation;
  }

  @override
  void registerTimezoneChangeCallback(void Function() callback) {
    timezoneChangeCallback = callback;
  }

  @override
  void unregisterTimezoneChangeCallback(void Function() callback) {
    if (timezoneChangeCallback == callback) {
      timezoneChangeCallback = null;
    }
  }

  // Stub implementation of other required methods
  @override
  noSuchMethod(Invocation invocation) => super.noSuchMethod(invocation);
}

void main() {
  late NotificationRepositoryImpl repository;
  late MockLocalStorageDataSource mockLocalDataSource;
  late MockPlatformNotificationDataSource mockPlatformDataSource;
  late MockNotificationAdapter mockNotificationAdapter;
  late MockPermissionAdapter mockPermissionAdapter;
  late MockTimezoneAdapter mockTimezoneAdapter;
  late MockStorageFactory mockStorageFactory;

  setUp(() {
    mockLocalDataSource = MockLocalStorageDataSource();
    mockPlatformDataSource = MockPlatformNotificationDataSource();
    mockNotificationAdapter = MockNotificationAdapter();
    mockPermissionAdapter = MockPermissionAdapter();
    mockTimezoneAdapter = MockTimezoneAdapter();
    mockStorageFactory = MockStorageFactory(mockLocalDataSource);

    repository = NotificationRepositoryImpl(
      storageFactory: mockStorageFactory,
      platformDataSource: mockPlatformDataSource,
      notificationAdapter: mockNotificationAdapter,
      permissionAdapter: mockPermissionAdapter,
      timezoneAdapter: mockTimezoneAdapter,
    );
  });

  group('NotificationRepositoryImpl', () {
    test('should initialize all dependencies', () async {
      // Act
      await repository.initialize();

      // Assert
      expect(mockPlatformDataSource.initializeCallCount, 1);
      expect(mockNotificationAdapter.initializeCallCount, 1);
      expect(mockPermissionAdapter.initializeCallCount, 1);
      expect(mockTimezoneAdapter.initializeCallCount, 1);
    });

    group('Reminder Settings', () {
      test('should get meter reading reminder settings', () async {
        // Arrange
        mockLocalDataSource.areRemindersEnabledResult = true;
        mockLocalDataSource.reminderFrequency = 30;
        mockLocalDataSource.reminderTimeModel = ReminderTimeModel(
          timeOfDay: TimeOfDay(hour: 19, minute: 0),
        );

        // Act
        final isEnabled = await repository.areMeterReadingRemindersEnabled();
        final frequency = await repository.getMeterReadingReminderFrequency();
        final reminderTime = await repository.getMeterReadingReminderTime();

        // Assert
        expect(isEnabled, true);
        expect(frequency, 30);
        expect(reminderTime.timeOfDay.hour, 19);
        expect(reminderTime.timeOfDay.minute, 0);
      });

      test('should set meter reading reminder settings', () async {
        // Arrange
        final newReminderTime = ReminderTimeModel(
          timeOfDay: TimeOfDay(hour: 20, minute: 30),
        );

        // Act
        await repository.setMeterReadingRemindersEnabled(false);
        await repository.setMeterReadingReminderFrequency(14);
        await repository.setMeterReadingReminderTime(newReminderTime);

        // Assert
        expect(mockLocalDataSource.setRemindersEnabledCallCount, 1);
        expect(mockLocalDataSource.setReminderFrequencyCallCount, 1);
        expect(mockLocalDataSource.setReminderTimeCallCount, 1);
        expect(mockLocalDataSource.areRemindersEnabledResult, false);
        expect(mockLocalDataSource.reminderFrequency, 14);
        expect(mockLocalDataSource.reminderTimeModel.timeOfDay.hour, 20);
        expect(mockLocalDataSource.reminderTimeModel.timeOfDay.minute, 30);
      });
    });

    group('Reminder Scheduling', () {
      test('should schedule meter reading reminder', () async {
        // Arrange
        mockLocalDataSource.areRemindersEnabledResult = true;
        mockLocalDataSource.reminderFrequency = 30;
        mockLocalDataSource.reminderTimeModel = ReminderTimeModel(
          timeOfDay: TimeOfDay(hour: 19, minute: 0),
        );
        mockPlatformDataSource.scheduleNotificationResult = true;

        // Act
        final result = await repository.scheduleMeterReadingReminder();

        // Assert
        expect(result, true);
        expect(mockPlatformDataSource.scheduleMeterReadingReminderCallCount, 1);
        expect(mockLocalDataSource.setLastReminderDateCallCount, 1);
        expect(mockPlatformDataSource.lastScheduledDate, isNotNull);
      });

      test('should not schedule reminder when reminders are disabled',
          () async {
        // Arrange
        mockLocalDataSource.areRemindersEnabledResult = false;

        // Act
        final result = await repository.scheduleMeterReadingReminder();

        // Assert
        expect(result, false);
        expect(mockPlatformDataSource.scheduleMeterReadingReminderCallCount, 0);
      });

      test('should reschedule meter reading reminders', () async {
        // Arrange
        mockLocalDataSource.areRemindersEnabledResult = true;
        mockPlatformDataSource.scheduleNotificationResult = true;

        // Act
        final result = await repository.rescheduleMeterReadingReminders();

        // Assert
        expect(result, true);
        expect(mockNotificationAdapter.cancelNotificationCallCount, 1);
        expect(mockPlatformDataSource.scheduleMeterReadingReminderCallCount, 1);
      });
    });

    group('Permission Handling', () {
      test('should check permission status', () async {
        // Arrange
        mockPermissionAdapter.permissionStatus = PermissionStatus.granted;

        // Act
        final status = await repository.getPermissionStatus();

        // Assert
        expect(status, PermissionStatus.granted);
        expect(mockPermissionAdapter.checkPermissionCallCount, 1);
      });

      test('should open app settings', () async {
        // Arrange
        mockPermissionAdapter.openAppSettingsResult = true;

        // Act
        final result = await repository.openAppSettings();

        // Assert
        expect(result, true);
        expect(mockPermissionAdapter.openAppSettingsCallCount, 1);
      });
    });

    group('Storage Migration', () {
      test('should migrate from SharedPreferences to SQLite', () async {
        // Arrange
        mockStorageFactory.migrateToSQLiteResult = true;
        double? reportedProgress;

        // Act
        final result = await repository.migrateToSQLite(
          onProgress: (progress) {
            reportedProgress = progress;
          },
        );

        // Assert
        expect(result, true);
        expect(mockStorageFactory.migrateToSQLiteCallCount, 1);
        expect(reportedProgress, 0.5);
      });

      test('should check if migrated to SQLite', () async {
        // Act
        await repository.isMigratedToSQLite();

        // Assert
        // This is difficult to test directly since it checks if _localDataSource is SQLNotificationDataSource
        // We can only verify the method doesn't throw an exception
      });

      test('should get storage type', () async {
        // Act
        final result = await repository.getStorageType();

        // Assert
        // Similar to the above, this is difficult to test directly
        expect(result, isNotNull);
      });
    });
  });
}
