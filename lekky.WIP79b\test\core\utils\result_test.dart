// File: test/core/utils/result_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:lekky/core/utils/error_handler.dart';
import 'package:lekky/core/utils/result.dart';

void main() {
  group('Result', () {
    test('should create a success result', () {
      final result = Result.success(42);
      
      expect(result.isSuccess, true);
      expect(result.isFailure, false);
      expect(result.value, 42);
    });

    test('should create a failure result', () {
      final error = AppError(message: 'Test error');
      final result = Result.failure(error);
      
      expect(result.isSuccess, false);
      expect(result.isFailure, true);
      expect(result.error, error);
    });

    test('should throw when accessing value of a failure result', () {
      final error = AppError(message: 'Test error');
      final result = Result.failure(error);
      
      expect(() => result.value, throwsException);
    });

    test('should throw when accessing error of a success result', () {
      final result = Result.success(42);
      
      expect(() => result.error, throwsException);
    });

    test('should map a success result', () {
      final result = Result.success(42);
      final mapped = result.map((value) => value.toString());
      
      expect(mapped.isSuccess, true);
      expect(mapped.value, '42');
    });

    test('should map a failure result', () {
      final error = AppError(message: 'Test error');
      final result = Result.failure(error);
      final mapped = result.map((value) => value.toString());
      
      expect(mapped.isFailure, true);
      expect(mapped.error, error);
    });

    test('should flat map a success result', () {
      final result = Result.success(42);
      final flatMapped = result.flatMap((value) => Result.success(value.toString()));
      
      expect(flatMapped.isSuccess, true);
      expect(flatMapped.value, '42');
    });

    test('should flat map a failure result', () {
      final error = AppError(message: 'Test error');
      final result = Result.failure(error);
      final flatMapped = result.flatMap((value) => Result.success(value.toString()));
      
      expect(flatMapped.isFailure, true);
      expect(flatMapped.error, error);
    });

    test('should fold a success result', () {
      final result = Result.success(42);
      final folded = result.fold(
        onSuccess: (value) => 'Success: $value',
        onFailure: (error) => 'Failure: ${error.message}',
      );
      
      expect(folded, 'Success: 42');
    });

    test('should fold a failure result', () {
      final error = AppError(message: 'Test error');
      final result = Result.failure(error);
      final folded = result.fold(
        onSuccess: (value) => 'Success: $value',
        onFailure: (error) => 'Failure: ${error.message}',
      );
      
      expect(folded, 'Failure: Test error');
    });

    test('should handle onSuccess for a success result', () {
      final result = Result.success(42);
      var handled = false;
      
      result.onSuccess((value) {
        expect(value, 42);
        handled = true;
      });
      
      expect(handled, true);
    });

    test('should not handle onSuccess for a failure result', () {
      final error = AppError(message: 'Test error');
      final result = Result.failure(error);
      var handled = false;
      
      result.onSuccess((value) {
        handled = true;
      });
      
      expect(handled, false);
    });

    test('should handle onFailure for a failure result', () {
      final error = AppError(message: 'Test error');
      final result = Result.failure(error);
      var handled = false;
      
      result.onFailure((e) {
        expect(e, error);
        handled = true;
      });
      
      expect(handled, true);
    });

    test('should not handle onFailure for a success result', () {
      final result = Result.success(42);
      var handled = false;
      
      result.onFailure((error) {
        handled = true;
      });
      
      expect(handled, false);
    });
  });
}
