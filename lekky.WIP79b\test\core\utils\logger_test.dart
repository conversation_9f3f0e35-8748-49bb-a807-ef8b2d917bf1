// File: test/core/utils/logger_test.dart
import 'dart:io';
import 'package:flutter_test/flutter_test.dart';
import 'package:lekky/core/utils/logger.dart';
import 'package:lekky/core/utils/error_handler.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path_provider_platform_interface/path_provider_platform_interface.dart';
import 'package:plugin_platform_interface/plugin_platform_interface.dart';

class MockPathProviderPlatform extends Mock
    with MockPlatformInterfaceMixin
    implements PathProviderPlatform {
  @override
  Future<String?> getApplicationDocumentsPath() async {
    return './test_logs';
  }
}

class Mock extends Fake implements PathProviderPlatform {}

void main() {
  setUp(() {
    ErrorHandler.clearErrors();
    PathProviderPlatform.instance = MockPathProviderPlatform();
  });

  test('Logger should initialize correctly', () async {
    await logger.init(logToConsole: true, logToFile: true);
    expect(logger.logFilePath, isNotNull);
    expect(logger.logFilePath, contains('lekky_'));
  });

  test('Logger should log messages', () async {
    await logger.init(logToConsole: true, logToFile: false);
    
    logger.d('Debug message');
    logger.i('Info message');
    logger.w('Warning message');
    logger.e('Error message');
    logger.f('Fatal message');
    
    // Verify that error and fatal messages are added to the error handler
    expect(ErrorHandler.errors.length, 2);
    expect(ErrorHandler.errors[0].message, 'Error message');
    expect(ErrorHandler.errors[1].message, 'Fatal message');
  });

  test('Logger should write to file', () async {
    // Create test directory
    final testDir = Directory('./test_logs');
    if (!await testDir.exists()) {
      await testDir.create(recursive: true);
    }
    
    await logger.init(logToConsole: false, logToFile: true);
    
    // Clear log file
    await logger.clearLogFile();
    
    // Log some messages
    logger.i('Test message 1');
    logger.w('Test message 2');
    
    // Wait for file operations to complete
    await Future.delayed(const Duration(milliseconds: 100));
    
    // Read log file content
    final content = await logger.getLogFileContent();
    
    expect(content, contains('INFO: Test message 1'));
    expect(content, contains('WARNING: Test message 2'));
    
    // Clean up
    if (await testDir.exists()) {
      await testDir.delete(recursive: true);
    }
  });
}
