// File: lib/core/widgets/solid_save_button.dart
import 'package:flutter/material.dart';
import '../theme/app_colors.dart';
import '../theme/app_text_styles.dart';

/// A standardized solid save button with blue background and white text
/// 
/// This button is designed to be used across the app for save actions
/// in dialogs and forms, providing a consistent look and feel.
class SolidSaveButton extends StatelessWidget {
  /// The callback when the button is pressed
  final VoidCallback? onPressed;
  
  /// The text to display on the button (defaults to 'Save')
  final String text;
  
  /// The height of the button (defaults to 40)
  final double height;
  
  /// The width of the button (defaults to null, which means it will size to its content)
  final double? width;
  
  /// The border radius of the button (defaults to 8)
  final double borderRadius;
  
  /// Whether the button is in a loading state
  final bool isLoading;
  
  /// Creates a solid save button
  const SolidSaveButton({
    Key? key,
    required this.onPressed,
    this.text = 'Save',
    this.height = 40,
    this.width,
    this.borderRadius = 8,
    this.isLoading = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Use the primary color (blue) for the save button
    final saveColor = AppColors.primary;
    
    return SizedBox(
      height: height,
      width: width,
      child: ElevatedButton(
        onPressed: isLoading ? null : onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: saveColor,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadius),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 16),
        ),
        child: isLoading
            ? const SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : Text(
                text,
                style: AppTextStyles.labelLarge.copyWith(
                  color: Colors.white,
                ),
              ),
      ),
    );
  }
}
