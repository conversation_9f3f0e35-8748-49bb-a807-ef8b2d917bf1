// File: lib/core/providers/localization_provider.dart
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

import '../constants/app_constants.dart';
import '../data/repositories/settings_repository.dart';
import '../l10n/supported_locales.dart';
import '../utils/logger.dart';

/// Provider for managing app localization
class LocalizationProvider extends ChangeNotifier {
  final SettingsRepository _settingsRepository;

  Locale _locale = const Locale('en');
  bool _isLoading = true;
  String _error = '';

  /// Constructor
  LocalizationProvider(this._settingsRepository) {
    _loadSavedLocale();
  }

  /// Get the current locale
  Locale get locale => _locale;

  /// Get loading state
  bool get isLoading => _isLoading;

  /// Get error message
  String get error => _error;

  /// Get the text direction based on the current locale
  TextDirection get textDirection {
    return SupportedLocales.isRtl(_locale.languageCode)
        ? TextDirection.rtl
        : TextDirection.ltr;
  }

  /// Load the saved locale from settings
  Future<void> _loadSavedLocale() async {
    _isLoading = true;
    _error = '';
    notifyListeners();

    try {
      final languageCode = await _settingsRepository.getLanguage();
      _setLocale(Locale(languageCode));

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      logger.e('Error loading locale', details: e.toString());
      _error = 'Error loading locale: $e';
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Set the locale
  Future<void> setLocale(Locale locale) async {
    if (!SupportedLocales.isSupported(locale)) {
      _error = 'Locale not supported: ${locale.languageCode}';
      notifyListeners();
      return;
    }

    try {
      await _settingsRepository.setLanguage(locale.languageCode);
      _setLocale(locale);
      notifyListeners();
    } catch (e) {
      logger.e('Error setting locale', details: e.toString());
      _error = 'Error setting locale: $e';
      notifyListeners();
    }
  }

  /// Set the locale without saving to settings
  void _setLocale(Locale locale) {
    _locale = locale;
  }

  /// Detect the device locale and set it if supported
  Future<void> detectAndSetDeviceLocale() async {
    try {
      final deviceLocale = window.locale;

      if (SupportedLocales.isSupported(deviceLocale)) {
        await setLocale(deviceLocale);
        return;
      }

      // If the exact locale is not supported, try with just the language code
      final languageLocale = Locale(deviceLocale.languageCode);
      if (SupportedLocales.isSupported(languageLocale)) {
        await setLocale(languageLocale);
        return;
      }

      // If still not supported, use default (English)
      await setLocale(const Locale('en'));
    } catch (e) {
      logger.e('Error detecting device locale', details: e.toString());
      _error = 'Error detecting device locale: $e';
      notifyListeners();
    }
  }

  /// Get the localization delegates
  static List<LocalizationsDelegate<dynamic>> get localizationsDelegates => [
        AppLocalizations.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ];

  /// Get the supported locales
  static const List<Locale> supportedLocales = SupportedLocales.locales;
}
