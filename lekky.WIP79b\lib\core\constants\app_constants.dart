// File: lib/core/constants/app_constants.dart
class AppConstants {
  // SharedPreferences keys
  static const String keySetupCompleted = 'setup_completed';
  static const String keyIsFirstLaunch = 'is_first_launch';
  static const String keyAppInitialized = 'app_initialized';
  static const String keyMeterUnit = 'meter_unit';
  static const String keyAlertThreshold = 'alert_threshold';
  static const String keyDaysInAdvance = 'days_in_advance';
  static const String keyDateInfo = 'date_info';
  static const String keyDateFormat = 'date_format';
  static const String keyLanguage = 'language';
  static const String keyThemeMode = 'theme_mode';
  static const String keyNotificationsEnabled = 'notifications_enabled';
  static const String keyReminderEnabled = 'reminder_enabled';
  static const String keyReminderFrequency = 'reminder_frequency';
  static const String keyLastReminderDate = 'last_reminder_date';
  static const String keyLastActiveDate = 'last_active_date';
  static const String keyOnboardingCompleted = 'onboarding_completed';
  static const String keyAccessibilityEnabled = 'accessibility_enabled';
  static const String keyTextScaleFactor = 'text_scale_factor';
  static const String keyEditExplanationShown = 'edit_explanation_shown';
  static const String keyInitialCredit = 'initial_credit';

  // Navigation routes
  static const String routeSplash = '/splash';
  static const String routeWelcome = '/welcome';
  static const String routeSetup = '/setup';
  static const String routeHome = '/home';
  static const String routeInput = '/input';
  static const String routeTopUp = '/topup';
  static const String routeHistory = '/history';
  static const String routeSettings = '/settings';
  static const String routeDebug = '/debug';
  static const String routeOnboarding = '/onboarding';
  static const String routeCost = '/cost';

  // Animation durations
  static const int splashAnimationDuration = 3000; // milliseconds
  static const int pageTransitionDuration = 500; // milliseconds
  static const int cardAnimationDuration = 300; // milliseconds
  static const int buttonAnimationDuration = 200; // milliseconds

  // Notification settings
  static const int notificationReminderDays = 7; // days
  static const int notificationLowBalanceThreshold = 10; // units
  static const int notificationLowBalanceDaysInAdvance = 3; // days
  static const int notificationIdLowBalance = 1;
  static const int notificationIdTopUp = 2;

  // Performance settings
  static const int cacheExpiryMinutes = 5; // minutes
  static const int maxDatabaseEntries = 600; // entries
  static const int debounceTimeMilliseconds = 300; // milliseconds
  static const int throttleTimeMilliseconds = 500; // milliseconds

  // Accessibility settings
  static const double minTextScaleFactor = 0.8;
  static const double maxTextScaleFactor = 1.5;
  static const double defaultTextScaleFactor = 1.0;
}
