// File: lib/core/widgets/notification_dialog.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/notification_model.dart';
import '../providers/notification_provider.dart';
import '../theme/app_colors.dart';
import '../theme/app_text_styles.dart';
import '../utils/dialog_button_styles.dart';
import 'dialogs/notification_confirmation_dialog.dart';

/// A dialog that displays a list of notifications
class NotificationDialog extends StatefulWidget {
  final VoidCallback? onClose;

  const NotificationDialog({Key? key, this.onClose}) : super(key: key);

  @override
  State<NotificationDialog> createState() => _NotificationDialogState();

  /// Show the notification dialog
  static Future<void> show(BuildContext context) {
    return showDialog(
      context: context,
      builder: (context) => const NotificationDialog(),
    );
  }
}

class _NotificationDialogState extends State<NotificationDialog> {
  late NotificationProvider _notificationProvider;
  List<NotificationModel> _notifications = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _notificationProvider =
        Provider.of<NotificationProvider>(context, listen: false);
    _loadNotifications();
  }

  Future<void> _loadNotifications() async {
    setState(() {
      _isLoading = true;
    });

    await _notificationProvider.refresh();
    final notifications = _notificationProvider.notifications;

    setState(() {
      _notifications = notifications;
      _isLoading = false;
    });
  }

  Future<void> _clearAll() async {
    // Show confirmation dialog
    final bool? confirmed = await NotificationConfirmationDialog.show(
      context: context,
      title: 'Confirm',
      message: 'All notifications will be deleted',
    );

    // If confirmed, clear all notifications
    if (confirmed == true) {
      await _notificationProvider.clearAll();
      await _loadNotifications();

      // Close the dialog
      if (mounted) {
        Navigator.of(context).pop();
        if (widget.onClose != null) {
          widget.onClose!();
        }
      }
    }
  }

  /// Remove a specific notification by ID
  Future<void> _removeNotification(String id) async {
    await _notificationProvider.removeNotification(id);
    await _loadNotifications();
  }

  /// Mark a notification as read
  Future<void> _markAsRead(String id) async {
    await _notificationProvider.markAsRead(id);
    await _loadNotifications();
  }

  @override
  Widget build(BuildContext context) {
    final backgroundColor = Theme.of(context).cardColor;
    final textColor =
        Theme.of(context).textTheme.bodyLarge?.color ?? Colors.black;

    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      elevation: 3,
      backgroundColor: backgroundColor,
      child: Container(
        width: double.maxFinite,
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.7,
          maxWidth: 500,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Dialog header
            Container(
              padding: const EdgeInsets.fromLTRB(24, 16, 24, 8),
              child: Center(
                child: Text(
                  'Notifications',
                  style: AppTextStyles.titleLarge.copyWith(color: textColor),
                ),
              ),
            ),

            const Divider(),

            // Notification list
            if (_isLoading)
              const Padding(
                padding: EdgeInsets.all(24.0),
                child: Center(child: CircularProgressIndicator()),
              )
            else if (_notifications.isEmpty)
              Padding(
                padding: const EdgeInsets.all(24.0),
                child: Center(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.notifications_off_outlined,
                        size: 48,
                        color: textColor.withOpacity(0.5),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'No notifications.',
                        style: AppTextStyles.bodyLarge.copyWith(
                          color: textColor.withOpacity(0.7),
                        ),
                      ),
                    ],
                  ),
                ),
              )
            else
              Expanded(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Hint text for swipe-to-delete
                    Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16.0, vertical: 8.0),
                      child: Text(
                        'Swipe left or right to delete a notification',
                        style: AppTextStyles.labelSmall.copyWith(
                          color: textColor.withOpacity(0.6),
                          fontStyle: FontStyle.italic,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    // Notification list
                    Expanded(
                      child: ListView.separated(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        shrinkWrap: true,
                        itemCount: _notifications.length,
                        separatorBuilder: (context, index) =>
                            const Divider(height: 1),
                        itemBuilder: (context, index) {
                          final notification = _notifications[index];
                          return Dismissible(
                            key: Key(notification.id),
                            direction: DismissDirection
                                .horizontal, // Allow both left and right swipe
                            // Background for right to left swipe (swipe right)
                            background: Container(
                              alignment: Alignment.centerLeft,
                              padding: const EdgeInsets.only(left: 20.0),
                              color: AppColors.error.withOpacity(0.7),
                              child: const Icon(
                                Icons.delete,
                                color: Colors.white,
                              ),
                            ),
                            // Background for left to right swipe (swipe left)
                            secondaryBackground: Container(
                              alignment: Alignment.centerRight,
                              padding: const EdgeInsets.only(right: 20.0),
                              color: AppColors.error.withOpacity(0.7),
                              child: const Icon(
                                Icons.delete,
                                color: Colors.white,
                              ),
                            ),
                            confirmDismiss: (direction) async {
                              // Show a confirmation dialog
                              return await NotificationConfirmationDialog.show(
                                    context: context,
                                    title: 'Confirm',
                                    message:
                                        'This notification will be deleted',
                                  ) ??
                                  false; // Default to false if dialog is dismissed
                            },
                            onDismissed: (direction) async {
                              // Remove the notification
                              await _removeNotification(notification.id);
                            },
                            child: _NotificationItem(
                              notification: notification,
                              onTap: () async {
                                await _markAsRead(notification.id);
                              },
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),

            // Action buttons
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                        if (widget.onClose != null) {
                          widget.onClose!();
                        }
                      },
                      style: OutlinedButton.styleFrom(
                        foregroundColor: AppColors.primary,
                        side: const BorderSide(color: AppColors.primary),
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text('Close'),
                    ),
                  ),
                  if (_notifications.isNotEmpty) ...[
                    const SizedBox(width: 12),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: _clearAll,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.error,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: const Text('Clear All'),
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// A widget that displays a single notification item
class _NotificationItem extends StatelessWidget {
  final NotificationModel notification;
  final VoidCallback onTap;

  const _NotificationItem({
    required this.notification,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final textColor =
        Theme.of(context).textTheme.bodyLarge?.color ?? Colors.black;
    final priorityColor = notification.getColor();

    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: notification.isRead ? null : priorityColor.withOpacity(0.1),
          border: Border(
            left: BorderSide(
              color: priorityColor,
              width: 4,
            ),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Text(
                    notification.title,
                    style: AppTextStyles.titleMedium.copyWith(
                      color: textColor,
                      fontWeight: notification.isRead
                          ? FontWeight.w600
                          : FontWeight.w700,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  notification.getFormattedTimestamp(),
                  style: AppTextStyles.labelSmall.copyWith(
                    color: textColor.withOpacity(0.6),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Text(
              notification.message,
              style: AppTextStyles.bodyMedium.copyWith(
                color: textColor.withOpacity(0.8),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
