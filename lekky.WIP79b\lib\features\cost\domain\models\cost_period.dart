// File: lib/features/cost/domain/models/cost_period.dart

/// Represents a time period for cost calculation
class CostPeriod {
  final String name;
  final int
      days; // Number of days for standard periods (used for creating date ranges)
  final bool isPast; // Whether this is a past or future period

  const CostPeriod({
    required this.name,
    required this.days,
    this.isPast = true, // Default to past periods
  });

  /// Predefined past periods
  static const CostPeriod pastDay =
      CostPeriod(name: 'Day', days: 1, isPast: true);
  static const CostPeriod pastWeek =
      CostPeriod(name: 'Week', days: 7, isPast: true);
  static const CostPeriod pastMonth =
      CostPeriod(name: 'Month', days: 30, isPast: true);
  static const CostPeriod pastYear =
      CostPeriod(name: 'Year', days: 365, isPast: true);

  /// Predefined future periods
  static const CostPeriod futureDay =
      CostPeriod(name: 'Day', days: 1, isPast: false);
  static const CostPeriod futureWeek =
      CostPeriod(name: 'Week', days: 7, isPast: false);
  static const CostPeriod futureMonth =
      CostPeriod(name: 'Month', days: 30, isPast: false);
  static const CostPeriod futureYear =
      CostPeriod(name: 'Year', days: 365, isPast: false);

  /// Custom period for date range selection
  static const CostPeriod custom = CostPeriod(name: 'Custom', days: 0);

  /// All available past periods
  static const List<CostPeriod> pastPeriods = [
    pastDay,
    pastWeek,
    pastMonth,
    pastYear
  ];

  /// All available future periods
  static const List<CostPeriod> futurePeriods = [
    futureDay,
    futureWeek,
    futureMonth,
    futureYear
  ];

  /// All available periods including custom
  static const List<CostPeriod> all = [
    pastDay,
    pastWeek,
    pastMonth,
    pastYear,
    futureDay,
    futureWeek,
    futureMonth,
    futureYear,
    custom
  ];

  /// Get periods based on mode
  static List<CostPeriod> getPeriodsByMode(bool isPastMode) {
    return isPastMode ? pastPeriods : futurePeriods;
  }

  /// Get a period by name and mode
  static CostPeriod getPeriodByNameAndMode(String name, bool isPastMode) {
    final periods = getPeriodsByMode(isPastMode);
    return periods.firstWhere(
      (period) => period.name == name,
      orElse: () => isPastMode ? pastMonth : futureMonth,
    );
  }

  @override
  String toString() => name;
}
