// File: lib/core/widgets/dialogs/import_options_dialog.dart
import 'package:flutter/material.dart';
import '../../theme/app_colors.dart';
import '../../theme/app_text_styles.dart';

/// A specialized dialog for selecting import options.
///
/// This dialog presents a clear title, concise message, and three buttons:
/// - Replace (destructive action)
/// - Cancel (secondary action)
/// - Append (primary action)
class ImportOptionsDialog {
  /// Shows an import options dialog with the specified title and message.
  ///
  /// Parameters:
  /// - [context]: The build context.
  /// - [title]: The title of the dialog, should clearly indicate the action.
  /// - [message]: A concise message explaining the import options.
  /// - [replaceText]: The text for the replace button (default: "Replace").
  /// - [cancelText]: The text for the cancel button (default: "Cancel").
  /// - [appendText]: The text for the append button (default: "Append").
  /// - [icon]: An optional icon to display in the dialog.
  /// - [barrierDismissible]: Whether the dialog can be dismissed by tapping outside (default: true).
  static Future<String?> show({
    required BuildContext context,
    required String title,
    required String message,
    String replaceText = 'Replace',
    String cancelText = 'Cancel',
    String appendText = 'Append',
    IconData? icon,
    bool barrierDismissible = true,
  }) async {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return showDialog<String>(
      context: context,
      barrierDismissible: barrierDismissible,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          insetPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Header
              Padding(
                padding: const EdgeInsets.fromLTRB(24, 16, 24, 0),
                child: Row(
                  children: [
                    Icon(
                      icon ?? Icons.file_download,
                      color: isDarkMode ? AppColors.primaryDark : AppColors.primary,
                    ),
                    const SizedBox(width: 10),
                    Text(
                      title,
                      style: AppTextStyles.titleMedium.copyWith(
                        fontWeight: FontWeight.bold,
                        color: isDarkMode ? AppColors.primaryDark : AppColors.primary,
                      ),
                    ),
                    const Spacer(),
                    IconButton(
                      icon: const Icon(Icons.close, color: AppColors.onSurface),
                      onPressed: () => Navigator.of(context).pop(null),
                      tooltip: 'Close',
                      constraints: const BoxConstraints(),
                      padding: EdgeInsets.zero,
                      iconSize: 20,
                    ),
                  ],
                ),
              ),

              // Content
              Padding(
                padding: const EdgeInsets.fromLTRB(24, 16, 24, 24),
                child: Text(
                  message,
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: isDarkMode
                        ? AppColors.onSurfaceDark.withOpacity(0.8)
                        : AppColors.onSurface.withOpacity(0.8),
                  ),
                ),
              ),

              // Actions
              Padding(
                padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                child: LayoutBuilder(
                  builder: (context, constraints) {
                    // Calculate button width based on available space
                    final buttonCount = 3;
                    final buttonWidth =
                        (constraints.maxWidth - (buttonCount - 1) * 8) / buttonCount;

                    // Common button style properties
                    const buttonHeight = 40.0;
                    const buttonPadding =
                        EdgeInsets.symmetric(horizontal: 8, vertical: 0);
                    const buttonTextStyle = TextStyle(fontSize: 14);

                    return Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // Replace button (destructive action)
                        SizedBox(
                          width: buttonWidth,
                          height: buttonHeight,
                          child: ElevatedButton(
                            onPressed: () => Navigator.of(context).pop('replace'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.red,
                              foregroundColor: Colors.white,
                              padding: buttonPadding,
                              minimumSize: Size(buttonWidth, buttonHeight),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            child: Text(
                              replaceText,
                              textAlign: TextAlign.center,
                              style: buttonTextStyle,
                            ),
                          ),
                        ),
                        const SizedBox(width: 8), // Spacing between buttons

                        // Cancel button
                        SizedBox(
                          width: buttonWidth,
                          height: buttonHeight,
                          child: OutlinedButton(
                            onPressed: () => Navigator.of(context).pop(null),
                            style: OutlinedButton.styleFrom(
                              foregroundColor: AppColors.primary,
                              side: const BorderSide(color: AppColors.primary),
                              padding: buttonPadding,
                              minimumSize: Size(buttonWidth, buttonHeight),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            child: Text(
                              cancelText,
                              textAlign: TextAlign.center,
                              style: buttonTextStyle,
                            ),
                          ),
                        ),
                        const SizedBox(width: 8), // Spacing between buttons

                        // Append button (primary action)
                        SizedBox(
                          width: buttonWidth,
                          height: buttonHeight,
                          child: ElevatedButton(
                            onPressed: () => Navigator.of(context).pop('append'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppColors.primary,
                              foregroundColor: Colors.white,
                              padding: buttonPadding,
                              minimumSize: Size(buttonWidth, buttonHeight),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            child: Text(
                              appendText,
                              textAlign: TextAlign.center,
                              style: buttonTextStyle,
                            ),
                          ),
                        ),
                      ],
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
