// File: lib/core/utils/dialog_button_styles.dart
import 'package:flutter/material.dart';
import '../theme/app_colors.dart';
import '../theme/app_text_styles.dart';
import '../widgets/outlined_cancel_button.dart';
import '../widgets/solid_save_button.dart';

/// A utility class for creating standardized dialog buttons
class DialogButtonStyles {
  /// Creates a standardized cancel button with blue outline and text
  static Widget createCancelButton({
    required BuildContext context,
    required VoidCallback onPressed,
    String text = 'Cancel',
    double height = 40,
    double? width,
  }) {
    return OutlinedCancelButton(
      onPressed: onPressed,
      text: text,
      height: height,
      width: width,
    );
  }

  /// Creates a standardized confirm button with primary color background
  static Widget createConfirmButton({
    required BuildContext context,
    required VoidCallback onPressed,
    String text = 'Confirm',
    double height = 40,
    double? width,
    bool isLoading = false,
    Color? backgroundColor,
  }) {
    final theme = Theme.of(context);

    return SizedBox(
      height: height,
      width: width,
      child: ElevatedButton(
        onPressed: isLoading ? null : onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: backgroundColor ?? theme.colorScheme.primary,
          foregroundColor: theme.colorScheme.onPrimary,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 16),
        ),
        child: isLoading
            ? const SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : Text(
                text,
                style: AppTextStyles.labelLarge.copyWith(
                  color: theme.colorScheme.onPrimary,
                ),
              ),
      ),
    );
  }

  /// Creates a standardized delete/destructive button with error color
  static Widget createDestructiveButton({
    required BuildContext context,
    required VoidCallback onPressed,
    String text = 'Delete',
    double height = 40,
    double? width,
    bool isLoading = false,
  }) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final buttonColor = isDarkMode ? AppColors.errorDark : AppColors.error;

    return SizedBox(
      height: height,
      width: width,
      child: ElevatedButton(
        onPressed: isLoading ? null : onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: buttonColor,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 16),
        ),
        child: isLoading
            ? const SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : Text(
                text,
                style: AppTextStyles.labelLarge.copyWith(
                  color: Colors.white,
                ),
              ),
      ),
    );
  }

  /// Creates a standardized save button with blue background and white text
  static Widget createSaveButton({
    required BuildContext context,
    required VoidCallback onPressed,
    String text = 'Save',
    double height = 40,
    double? width,
    bool isLoading = false,
  }) {
    return SolidSaveButton(
      onPressed: onPressed,
      text: text,
      height: height,
      width: width,
      isLoading: isLoading,
    );
  }
}
