// File: lib/core/shared_modules/base_settings_widget.dart
import 'package:flutter/material.dart';
import '../extensions/context_extensions.dart';
import '../theme/app_colors.dart';
import '../theme/app_text_styles.dart';
import 'settings_model.dart';

/// Base class for all settings widgets
abstract class BaseSettingsWidget extends StatelessWidget {
  final SettingsDisplayMode displayMode;
  final bool showHelperText;
  final bool showTitle;

  const BaseSettingsWidget({
    Key? key,
    this.displayMode = SettingsDisplayMode.compact,
    this.showHelperText = false,
    this.showTitle = true,
  }) : super(key: key);

  /// Build the widget based on display mode
  @override
  Widget build(BuildContext context) {
    return displayMode == SettingsDisplayMode.expanded
        ? buildExpandedView(context)
        : buildCompactView(context);
  }

  /// Build the expanded view (for Setup page)
  Widget buildExpandedView(BuildContext context);

  /// Build the compact view (for Settings page)
  Widget buildCompactView(BuildContext context);

  /// Helper method to build a standard label
  Widget buildLabel(BuildContext context, String text) {
    return Text(
      text,
      style: (displayMode == SettingsDisplayMode.expanded
              ? AppTextStyles.titleMedium
              : AppTextStyles.labelLarge)
          .copyWith(
        color: context.textColor,
      ),
    );
  }

  /// Helper method to build a standard helper text
  Widget buildHelperText(BuildContext context, String text) {
    if (!showHelperText) return const SizedBox.shrink();

    return Padding(
      padding: const EdgeInsets.only(top: 4.0),
      child: Text(
        text,
        style: (displayMode == SettingsDisplayMode.expanded
                ? AppTextStyles.helperTextExpanded
                : AppTextStyles.helperText)
            .copyWith(
          color: context.secondaryTextColor,
        ),
      ),
    );
  }

  /// Helper method to build a standard error text
  Widget buildErrorText(BuildContext context, String? error) {
    if (error == null) return const SizedBox.shrink();

    return Padding(
      padding: const EdgeInsets.only(top: 4.0),
      child: Text(
        error,
        style: AppTextStyles.errorText,
      ),
    );
  }

  /// Helper method to build a standard section title
  Widget buildSectionTitle(BuildContext context, String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Text(
        text,
        style: (displayMode == SettingsDisplayMode.expanded
                ? AppTextStyles.titleLarge
                : AppTextStyles.titleMedium)
            .copyWith(
          color: context.textColor,
        ),
      ),
    );
  }
}
