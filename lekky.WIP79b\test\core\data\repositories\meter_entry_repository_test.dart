// File: test/core/data/repositories/meter_entry_repository_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:lekky/core/data/database/db_helper.dart';
import 'package:lekky/core/data/repositories/meter_entry_repository.dart';
import 'package:lekky/core/models/meter_entry.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'meter_entry_repository_test.mocks.dart';

@GenerateMocks([DBHelper])
void main() {
  group('MeterEntryRepository', () {
    late MockDBHelper mockDBHelper;
    late MeterEntryRepository repository;
    
    setUp(() async {
      // Set up SharedPreferences for testing
      SharedPreferences.setMockInitialValues({});
      
      // Create a mock DBHelper
      mockDBHelper = MockDBHelper();
      
      // Create a new instance of MeterEntryRepository with the mock DBHelper
      repository = MeterEntryRepository(dbHelper: mockDBHelper);
    });
    
    test('bulkAddEntries should call DBHelper.bulkImportEntries', () async {
      // Create test entries
      final entries = [
        MeterEntry(
          reading: 100.0,
          amountToppedUp: 0.0,
          timestamp: DateTime(2023, 1, 1),
          shortAverageAfterTopUp: null,
          totalAverageUpToThisPoint: null,
        ),
        MeterEntry(
          reading: 0.0,
          amountToppedUp: 50.0,
          timestamp: DateTime(2023, 1, 2),
          shortAverageAfterTopUp: null,
          totalAverageUpToThisPoint: null,
        ),
      ];
      
      // Set up the mock to return a successful result
      when(mockDBHelper.bulkImportEntries(
        any,
        replace: anyNamed('replace'),
        onProgress: anyNamed('onProgress'),
      )).thenAnswer((_) async {});
      
      // Call the method
      final result = await repository.bulkAddEntries(entries);
      
      // Verify that the method was called with the correct parameters
      verify(mockDBHelper.bulkImportEntries(
        entries,
        replace: false,
        onProgress: anyNamed('onProgress'),
      )).called(1);
      
      // Verify that the method returns true on success
      expect(result, isTrue);
    });
    
    test('bulkAddEntries should handle progress updates', () async {
      // Create test entries
      final entries = [
        MeterEntry(
          reading: 100.0,
          amountToppedUp: 0.0,
          timestamp: DateTime(2023, 1, 1),
          shortAverageAfterTopUp: null,
          totalAverageUpToThisPoint: null,
        ),
        MeterEntry(
          reading: 0.0,
          amountToppedUp: 50.0,
          timestamp: DateTime(2023, 1, 2),
          shortAverageAfterTopUp: null,
          totalAverageUpToThisPoint: null,
        ),
      ];
      
      // Set up the mock to call the progress callback
      when(mockDBHelper.bulkImportEntries(
        any,
        replace: anyNamed('replace'),
        onProgress: anyNamed('onProgress'),
      )).thenAnswer((invocation) async {
        final onProgress = invocation.namedArguments[Symbol('onProgress')] as Function(double)?;
        if (onProgress != null) {
          onProgress(0.5);
          onProgress(1.0);
        }
      });
      
      // Track progress updates
      final progressUpdates = <double>[];
      
      // Call the method with progress tracking
      await repository.bulkAddEntries(
        entries,
        onProgress: (progress) {
          progressUpdates.add(progress);
        },
      );
      
      // Verify that progress updates were received
      expect(progressUpdates, isNotEmpty);
      
      // Verify that progress updates were scaled correctly
      expect(progressUpdates[0], equals(0.5 * 0.8)); // 0.5 scaled to 80%
      expect(progressUpdates[1], equals(1.0 * 0.8)); // 1.0 scaled to 80%
    });
    
    test('bulkAddEntries should handle errors', () async {
      // Create test entries
      final entries = [
        MeterEntry(
          reading: 100.0,
          amountToppedUp: 0.0,
          timestamp: DateTime(2023, 1, 1),
          shortAverageAfterTopUp: null,
          totalAverageUpToThisPoint: null,
        ),
      ];
      
      // Set up the mock to throw an exception
      when(mockDBHelper.bulkImportEntries(
        any,
        replace: anyNamed('replace'),
        onProgress: anyNamed('onProgress'),
      )).thenThrow(Exception('Test error'));
      
      // Call the method
      final result = await repository.bulkAddEntries(entries);
      
      // Verify that the method returns false on error
      expect(result, isFalse);
    });
  });
}
