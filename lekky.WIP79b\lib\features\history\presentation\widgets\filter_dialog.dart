// File: lib/features/history/presentation/widgets/filter_dialog.dart
import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/utils/date_time_utils.dart';
import '../../../../core/widgets/dialogs/selection_dialog.dart';
import '../../../../core/widgets/dialogs/date_picker_dialog.dart'
    as custom_date_picker;
import '../../../../core/extensions/context_extensions.dart';
import '../../domain/models/history_filter.dart';
import '../controllers/history_controller.dart';

/// A dialog for filtering history entries
class FilterDialog extends StatefulWidget {
  final HistoryFilter filter;
  final Function(HistoryFilter) onApply;
  final DateTime? earliestDate;
  final DateTime? latestDate;
  final bool showDateRangeFilter;
  final HistoryController controller;

  const FilterDialog({
    super.key,
    required this.filter,
    required this.onApply,
    required this.controller,
    this.earliestDate,
    this.latestDate,
    this.showDateRangeFilter = true,
  });

  static Future<void> show({
    required BuildContext context,
    required HistoryFilter filter,
    required Function(HistoryFilter) onApply,
    required HistoryController controller,
    DateTime? earliestDate,
    DateTime? latestDate,
    bool showDateRangeFilter = true,
  }) async {
    await SelectionDialog.showCustom(
      context: context,
      title: 'Filter Options',
      icon: Icons.filter_list,
      content: FilterDialog(
        filter: filter,
        onApply: onApply,
        controller: controller,
        earliestDate: earliestDate,
        latestDate: latestDate,
        showDateRangeFilter: showDateRangeFilter,
      ),
      barrierDismissible: true,
    );
  }

  @override
  State<FilterDialog> createState() => _FilterDialogState();
}

class _FilterDialogState extends State<FilterDialog> {
  late HistoryFilter _filter;

  @override
  void initState() {
    super.initState();
    // Initialize with the widget filter but ensure isInvalidFilter is set correctly
    // This ensures that when the dialog opens, no specific filter type is selected by default
    _filter = widget.filter.copyWith(
      isInvalidFilter: widget.filter.isInvalidFilter,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.showDateRangeFilter) ...[
          _buildDateRangeFilter(),
          const SizedBox(height: 16),
        ] else ...[
          _buildDateRangeUnavailableMessage(),
          const SizedBox(height: 16),
        ],
        _buildEntryTypeFilter(),
        const SizedBox(height: 24),
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            SizedBox(
              width: 100,
              child: TextButton(
                onPressed: () => Navigator.of(context).pop(),
                style: TextButton.styleFrom(
                  foregroundColor:
                      context.isDarkMode ? Colors.white70 : Colors.blue,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                    side: BorderSide(
                      color: context.isDarkMode ? Colors.white70 : Colors.blue,
                    ),
                  ),
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
                child: Text(
                  'Cancel',
                  style: TextStyle(
                    color: context.isDarkMode ? Colors.white70 : Colors.blue,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
            const SizedBox(width: 16),
            SizedBox(
              width: 100,
              child: ElevatedButton(
                onPressed: () {
                  // Check if we're in invalid entries mode
                  if (_getEntryTypeValue() == 'invalid') {
                    // Pass the filter to the parent, but the parent will need to
                    // apply special filtering logic for invalid entries
                    widget.onApply(_filter);

                    // Close the dialog
                    Navigator.of(context).pop();

                    // Show a snackbar to inform the user
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Showing invalid entries'),
                        duration: Duration(seconds: 2),
                        backgroundColor: Colors.red,
                      ),
                    );
                  } else {
                    // Normal filter application
                    widget.onApply(_filter);
                    Navigator.of(context).pop();
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor:
                      Theme.of(context).brightness == Brightness.dark
                          ? AppColors.primaryDark
                          : AppColors.primary,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
                child: const Text(
                  'Apply',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildDateRangeFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Date Range',
          style: AppTextStyles.titleSmall.copyWith(
            color: context.isDarkMode ? Colors.white : AppColors.tertiary,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: InkWell(
                onTap: () => _selectStartDate(context),
                child: InputDecorator(
                  decoration: InputDecoration(
                    labelText: 'From Date',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                  ),
                  child: FutureBuilder<String>(
                    future: widget.controller.getDateInfo(),
                    builder: (context, snapshot) {
                      final includeTime = snapshot.data == 'Date and time';
                      return Text(
                        _filter.startDate != null
                            ? includeTime
                                ? DateTimeUtils.formatDateWithTime(
                                    _filter.startDate!)
                                : DateTimeUtils.formatDateDefault(
                                    _filter.startDate!)
                            : 'Select Date',
                      );
                    },
                  ),
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: InkWell(
                onTap: () => _selectEndDate(context),
                child: InputDecorator(
                  decoration: InputDecoration(
                    labelText: 'To Date',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                  ),
                  child: FutureBuilder<String>(
                    future: widget.controller.getDateInfo(),
                    builder: (context, snapshot) {
                      final includeTime = snapshot.data == 'Date and time';
                      return Text(
                        _filter.endDate != null
                            ? includeTime
                                ? DateTimeUtils.formatDateWithTime(
                                    _filter.endDate!)
                                : DateTimeUtils.formatDateDefault(
                                    _filter.endDate!)
                            : 'Select Date',
                      );
                    },
                  ),
                ),
              ),
            ),
          ],
        ),
        if (_hasDateRangeError())
          Padding(
            padding: const EdgeInsets.only(top: 8),
            child: Text(
              'From Date must be before To Date',
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.error,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildDateRangeUnavailableMessage() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Date Range',
          style: AppTextStyles.titleSmall.copyWith(
            color: context.isDarkMode ? Colors.white : AppColors.tertiary,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: AppColors.surfaceVariant.withOpacity(0.3),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: AppColors.tertiary.withOpacity(0.3),
            ),
          ),
          child: Row(
            children: [
              const Icon(
                Icons.info_outline,
                color: AppColors.tertiary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Date range filtering requires at least 2 meter readings. Add more meter readings to enable this feature.',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: Theme.of(context).brightness == Brightness.dark
                        ? Colors
                            .white70 // Lighter color for better readability in dark mode
                        : AppColors.onSurfaceVariant,
                    fontSize:
                        13.0, // Slightly larger font size for better readability
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildEntryTypeFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Entry Type',
          style: AppTextStyles.titleSmall.copyWith(
            color: context.isDarkMode ? Colors.white : AppColors.tertiary,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        RadioListTile<String>(
          title: Text(
            'All Entries',
            style: TextStyle(
              color: context.isDarkMode ? Colors.white : Colors.black,
            ),
          ),
          value: 'all',
          groupValue: _getEntryTypeValue(),
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _filter = _filter.copyWith(
                  showMeterReadings: true,
                  showTopUps: true,
                  isInvalidFilter:
                      false, // Explicitly set to false to ensure it's not an invalid entries filter
                );
              });
            }
          },
          activeColor: context.isDarkMode ? Colors.white : Colors.black,
          dense: true,
        ),
        RadioListTile<String>(
          title: Text(
            'Meter Readings Only',
            style: TextStyle(
              color: context.isDarkMode ? Colors.white : Colors.black,
            ),
          ),
          value: 'readings',
          groupValue: _getEntryTypeValue(),
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _filter = _filter.copyWith(
                  showMeterReadings: true,
                  showTopUps: false,
                  isInvalidFilter: false, // Not an invalid entries filter
                );
              });
            }
          },
          activeColor:
              context.isDarkMode ? AppColors.primaryDark : AppColors.primary,
          dense: true,
        ),
        RadioListTile<String>(
          title: Text(
            'Top-Ups Only',
            style: TextStyle(
              color: context.isDarkMode ? Colors.white : Colors.black,
            ),
          ),
          value: 'topups',
          groupValue: _getEntryTypeValue(),
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _filter = _filter.copyWith(
                  showMeterReadings: false,
                  showTopUps: true,
                  showInvalidEntries: true, // Reset invalid entries filter
                  isInvalidFilter: false, // Not an invalid entries filter
                );
              });
            }
          },
          activeColor:
              context.isDarkMode ? AppColors.costTab : AppColors.tertiary,
          dense: true,
        ),
        RadioListTile<String>(
          title: Text(
            'Invalid Entries Only',
            style: TextStyle(
              color: context.isDarkMode ? Colors.white : Colors.black,
            ),
          ),
          value: 'invalid',
          groupValue: _getEntryTypeValue(),
          onChanged: (value) {
            if (value != null) {
              setState(() {
                // Reset any date filters
                _filter = _filter.copyWith(
                  startDate: null,
                  endDate: null,
                  showMeterReadings: true,
                  showTopUps: true,
                  showInvalidEntries: true,
                  isInvalidFilter:
                      true, // Set the flag to indicate this is an invalid entries filter
                  // The actual filtering of invalid entries is done in the controller
                  // This just marks that we want to show only invalid entries
                );
              });
            }
          },
          activeColor: AppColors.error,
          dense: true,
        ),
      ],
    );
  }

  String _getEntryTypeValue() {
    // Check if we're in invalid entries mode using the isInvalidFilter flag
    if (_filter.isInvalidFilter) {
      return 'invalid';
    } else if (_filter.showMeterReadings && !_filter.showTopUps) {
      return 'readings';
    } else if (!_filter.showMeterReadings && _filter.showTopUps) {
      return 'topups';
    } else if (_filter.showMeterReadings && _filter.showTopUps) {
      return 'all';
    } else {
      // Default to all if nothing is selected
      return 'all';
    }
  }

  bool _hasDateRangeError() {
    if (_filter.startDate != null && _filter.endDate != null) {
      return _filter.startDate!.isAfter(_filter.endDate!);
    }
    return false;
  }

  // Helper method to show error snackbar without BuildContext issues
  void _showErrorSnackBar(String message) {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  // Helper method to show date picker without BuildContext issues
  Future<DateTime?> _showDatePicker({
    required String title,
    required DateTime initialDate,
    required DateTime firstDate,
    required DateTime lastDate,
    required String helpText,
  }) {
    if (!mounted) return Future.value(null);

    return custom_date_picker.DatePickerDialog.showDatePicker(
      context: context,
      title: title,
      initialDate: initialDate,
      firstDate: firstDate,
      lastDate: lastDate,
      confirmText: 'Select',
      cancelText: 'Cancel',
      helpText: helpText,
    );
  }

  // Helper method to show date and time picker without BuildContext issues
  Future<DateTime?> _showDateTimePicker({
    required String title,
    required DateTime initialDate,
    required DateTime firstDate,
    required DateTime lastDate,
    required String helpText,
  }) {
    if (!mounted) return Future.value(null);

    // Store the current context in a local variable
    final currentContext = context;

    // Use a synchronous function to avoid BuildContext issues
    return Future<DateTime?>.sync(() async {
      if (!mounted) return null;
      return await custom_date_picker.DatePickerDialog.showDateTimePicker(
        context: currentContext,
        title: title,
        initialDate: initialDate,
        firstDate: firstDate,
        lastDate: lastDate,
        currentTime: TimeOfDay.fromDateTime(initialDate),
        includeTime: true,
        addCurrentSecond: true, // Add current second to avoid duplicates
        confirmText: 'Select',
        cancelText: 'Cancel',
        helpText: helpText,
      );
    });
  }

  Future<void> _selectStartDate(BuildContext context) async {
    // Use the earliest date from entries if available, otherwise default to 1 year ago
    final firstDate = widget.earliestDate ??
        DateTime.now().subtract(const Duration(days: 365));

    // Use the latest date from entries if available, otherwise default to today
    final lastDate = widget.latestDate ?? DateTime.now();

    // For initial date, use the filter's start date if set, otherwise use the earliest date
    final initialDate = _filter.startDate ??
        (_filter.endDate != null && _filter.endDate!.isAfter(firstDate)
            ? firstDate
            : (_filter.endDate ?? firstDate));

    // Check the Date Info setting
    final dateInfo = await widget.controller.getDateInfo();
    final includeTime = dateInfo == 'Date and time';

    DateTime? selectedDate;

    try {
      if (includeTime) {
        // Show date and time picker if Date Info is set to "Date and time"
        selectedDate = await _showDateTimePicker(
          title: 'Select Start Date and Time',
          initialDate: initialDate,
          firstDate: firstDate,
          lastDate: _filter.endDate ?? lastDate,
          helpText: 'Choose a start date and time for filtering',
        );
      } else {
        // Show only date picker if Date Info is set to "Date only"
        final pickedDate = await _showDatePicker(
          title: 'Select Start Date',
          initialDate: initialDate,
          firstDate: firstDate,
          lastDate: _filter.endDate ?? lastDate,
          helpText: 'Choose a start date for filtering',
        );

        if (pickedDate != null) {
          // Use current time when Date Info is set to "Date only"
          final now = DateTime.now();
          selectedDate = DateTime(
            pickedDate.year,
            pickedDate.month,
            pickedDate.day,
            now.hour,
            now.minute,
            now.second,
          );
        }
      }

      if (selectedDate != null && mounted) {
        setState(() {
          _filter = _filter.copyWith(startDate: selectedDate);
        });
      }
    } catch (e) {
      // Handle any errors that might occur during date picking
      _showErrorSnackBar('Error selecting date: $e');
    }
  }

  Future<void> _selectEndDate(BuildContext context) async {
    // Use the earliest date from entries if available, otherwise default to 1 year ago
    final firstDate = widget.earliestDate ??
        DateTime.now().subtract(const Duration(days: 365));

    // Use the latest date from entries if available, otherwise default to today
    final lastDate = widget.latestDate ?? DateTime.now();

    // For initial date, use the filter's end date if set, otherwise use the latest date
    final initialDate = _filter.endDate ??
        (_filter.startDate != null && _filter.startDate!.isBefore(lastDate)
            ? lastDate
            : (_filter.startDate ?? lastDate));

    // Check the Date Info setting
    final dateInfo = await widget.controller.getDateInfo();
    final includeTime = dateInfo == 'Date and time';

    DateTime? selectedDate;

    try {
      if (includeTime) {
        // Show date and time picker if Date Info is set to "Date and time"
        selectedDate = await _showDateTimePicker(
          title: 'Select End Date and Time',
          initialDate: initialDate,
          firstDate: _filter.startDate ?? firstDate,
          lastDate: lastDate,
          helpText: 'Choose an end date and time for filtering',
        );
      } else {
        // Show only date picker if Date Info is set to "Date only"
        final pickedDate = await _showDatePicker(
          title: 'Select End Date',
          initialDate: initialDate,
          firstDate: _filter.startDate ?? firstDate,
          lastDate: lastDate,
          helpText: 'Choose an end date for filtering',
        );

        if (pickedDate != null) {
          // Use end of day when Date Info is set to "Date only" for end date
          selectedDate = DateTime(
            pickedDate.year,
            pickedDate.month,
            pickedDate.day,
            23, // Hour: 23 (11 PM)
            59, // Minute: 59
            59, // Second: 59
          );
        }
      }

      if (selectedDate != null && mounted) {
        setState(() {
          _filter = _filter.copyWith(endDate: selectedDate);
        });
      }
    } catch (e) {
      // Handle any errors that might occur during date picking
      _showErrorSnackBar('Error selecting date: $e');
    }
  }
}
