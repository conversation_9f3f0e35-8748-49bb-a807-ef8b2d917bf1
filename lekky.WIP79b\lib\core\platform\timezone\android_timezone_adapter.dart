// File: lib/core/platform/timezone/android_timezone_adapter.dart

import 'dart:async';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:timezone/data/latest.dart' as tz_data;

import 'timezone_adapter.dart';

/// Android implementation of the TimezoneAdapter
class AndroidTimezoneAdapter implements TimezoneAdapter {
  static const String _lastKnownTimezoneKey = 'last_known_timezone';
  static const String _timezoneChangeTimestampKey = 'timezone_change_timestamp';

  final Set<Function()> _timezoneChangeCallbacks = {};
  late SharedPreferences _prefs;
  late StreamSubscription<dynamic>? _timezoneChangeSubscription;
  bool _isInitialized = false;

  // Method channel for native timezone change detection
  static const MethodChannel _channel = MethodChannel('com.roolekky/timezone');

  @override
  Future<void> initialize() async {
    if (_isInitialized) return;

    // Initialize timezone database
    tz_data.initializeTimeZones();

    // Initialize shared preferences
    _prefs = await SharedPreferences.getInstance();

    // Initialize timezone change detection
    try {
      // Set up method channel for timezone change broadcasts
      _channel.setMethodCallHandler(_handleMethodCall);

      // Register for timezone change broadcasts on the Android side
      await _channel.invokeMethod('registerTimezoneChangeReceiver');
    } catch (e) {
      print('Error setting up timezone change detection: $e');
    }

    // Save current timezone if it's the first run
    final lastKnownTimezone = await getLastKnownTimezone();
    if (lastKnownTimezone == null) {
      await saveCurrentTimezone(getCurrentTimezone());
    }

    _isInitialized = true;
  }

  /// Handle method calls from the native side
  Future<dynamic> _handleMethodCall(MethodCall call) async {
    switch (call.method) {
      case 'onTimezoneChanged':
        final newTimezone = call.arguments as String;
        print('Timezone changed to: $newTimezone');

        // Save the new timezone
        await saveCurrentTimezone(newTimezone);

        // Notify callbacks
        for (final callback in _timezoneChangeCallbacks) {
          callback();
        }
        break;
      default:
        print('Unknown method ${call.method}');
    }
  }

  @override
  String getCurrentTimezone() {
    try {
      return tz.local.name;
    } catch (e) {
      // Fallback to a default timezone if there's an error
      return 'UTC';
    }
  }

  @override
  Future<String?> getLastKnownTimezone() async {
    return _prefs.getString(_lastKnownTimezoneKey);
  }

  @override
  Future<void> saveCurrentTimezone(String timezone) async {
    // Save both the timezone and the timestamp of the change
    await _prefs.setString(_lastKnownTimezoneKey, timezone);
    await _prefs.setInt(
        _timezoneChangeTimestampKey, DateTime.now().millisecondsSinceEpoch);
  }

  @override
  Future<bool> hasTimezoneChanged() async {
    final lastKnownTimezone = await getLastKnownTimezone();
    if (lastKnownTimezone == null) return false;

    final currentTimezone = getCurrentTimezone();
    return lastKnownTimezone != currentTimezone;
  }

  @override
  DateTime convertToUtc(DateTime localDateTime) {
    return localDateTime.toUtc();
  }

  @override
  DateTime convertToLocal(DateTime utcDateTime) {
    return utcDateTime.toLocal();
  }

  @override
  bool isInDaylightSavingTime(DateTime dateTime) {
    try {
      final location = tz.getLocation(getCurrentTimezone());
      final tzDateTime = tz.TZDateTime.from(dateTime, location);
      return location.timeZone(tzDateTime.millisecondsSinceEpoch).isDst;
    } catch (e) {
      print('Error checking DST: $e');
      return false;
    }
  }

  @override
  double getTimezoneOffset(DateTime dateTime) {
    try {
      final location = tz.getLocation(getCurrentTimezone());
      final tzDateTime = tz.TZDateTime.from(dateTime, location);
      return location.timeZone(tzDateTime.millisecondsSinceEpoch).offset /
          3600000.0;
    } catch (e) {
      print('Error getting timezone offset: $e');
      return 0.0;
    }
  }

  @override
  String getIanaTimezoneName() {
    try {
      return tz.local.name;
    } catch (e) {
      print('Error getting IANA timezone name: $e');
      return 'UTC';
    }
  }

  @override
  String getTimezoneAbbreviation(DateTime dateTime) {
    try {
      final location = tz.getLocation(getCurrentTimezone());
      final tzDateTime = tz.TZDateTime.from(dateTime, location);
      return location.timeZone(tzDateTime.millisecondsSinceEpoch).abbreviation;
    } catch (e) {
      print('Error getting timezone abbreviation: $e');
      return 'UTC';
    }
  }

  @override
  void registerTimezoneChangeCallback(void Function() callback) {
    _timezoneChangeCallbacks.add(callback);
  }

  @override
  void unregisterTimezoneChangeCallback(void Function() callback) {
    _timezoneChangeCallbacks.remove(callback);
  }

  @override
  DateTime getAdjustedDateTime(DateTime original) {
    // If the original time is in UTC, convert it to local time
    if (original.isUtc) {
      return original.toLocal();
    }

    // If it's already in local time, return it as is
    return original;
  }

  @override
  bool isScheduledTimeValid(DateTime scheduledTime) {
    // Check if the scheduled time is in the past
    return scheduledTime.isAfter(DateTime.now());
  }

  @override
  DateTime rescheduleAfterTimezoneChange(DateTime original) {
    final now = DateTime.now();

    // If the original time is in the past, reschedule it
    if (original.isBefore(now)) {
      // Keep the same time of day but move to today or tomorrow
      final today = DateTime(
        now.year,
        now.month,
        now.day,
        original.hour,
        original.minute,
      );

      // If today's time has already passed, schedule for tomorrow
      if (today.isBefore(now)) {
        return today.add(const Duration(days: 1));
      } else {
        return today;
      }
    }

    // If the original time is still in the future, keep it
    return original;
  }

  /// Dispose of resources
  void dispose() {
    _timezoneChangeSubscription?.cancel();
    _timezoneChangeCallbacks.clear();
  }
}
