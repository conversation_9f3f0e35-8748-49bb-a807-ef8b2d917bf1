// File: lib/core/shared_modules/notification_settings_selector.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/notification_provider.dart';
import '../theme/app_text_styles.dart';
import 'base_settings_widget.dart';
import 'settings_model.dart';

class NotificationSettingsSelector extends BaseSettingsWidget {
  final String? errorText;

  const NotificationSettingsSelector({
    Key? key,
    this.errorText,
    SettingsDisplayMode displayMode = SettingsDisplayMode.compact,
    bool showHelperText = false,
  }) : super(
            key: key, displayMode: displayMode, showHelperText: showHelperText);

  @override
  Widget buildExpandedView(BuildContext context) {
    return _NotificationSettingsContent(
      displayMode: displayMode,
      showHelperText: showHelperText,
      errorText: errorText,
    );
  }

  @override
  Widget buildCompactView(BuildContext context) {
    return _NotificationSettingsContent(
      displayMode: displayMode,
      showHelperText: showHelperText,
      errorText: errorText,
    );
  }
}

class _NotificationSettingsContent extends StatefulWidget {
  final SettingsDisplayMode displayMode;
  final bool showHelperText;
  final String? errorText;

  const _NotificationSettingsContent({
    Key? key,
    required this.displayMode,
    required this.showHelperText,
    this.errorText,
  }) : super(key: key);

  @override
  State<_NotificationSettingsContent> createState() =>
      _NotificationSettingsContentState();
}

class _NotificationSettingsContentState
    extends State<_NotificationSettingsContent> {
  bool _notificationsEnabled = true;
  bool _lowBalanceAlertsEnabled = true;
  bool _topUpAlertsEnabled = true;
  bool _invalidRecordAlertsEnabled = true;
  bool _remindersEnabled = false;
  int _reminderFrequency = 7; // Default to weekly
  DateTime? _lastReminderDate;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    final notificationProvider =
        Provider.of<NotificationProvider>(context, listen: false);

    setState(() {
      _isLoading = true;
    });

    final notificationsEnabled =
        await notificationProvider.areNotificationsEnabled();
    final lowBalanceAlertsEnabled =
        await notificationProvider.areLowBalanceAlertsEnabled();
    final topUpAlertsEnabled =
        await notificationProvider.areTopUpAlertsEnabled();
    final invalidRecordAlertsEnabled =
        await notificationProvider.areInvalidRecordAlertsEnabled();
    final remindersEnabled =
        await notificationProvider.areMeterReadingRemindersEnabled();
    final reminderFrequency =
        await notificationProvider.getMeterReadingReminderFrequency();
    final lastReminderDate =
        await notificationProvider.getLastMeterReadingReminderDate();

    setState(() {
      _notificationsEnabled = notificationsEnabled;
      _lowBalanceAlertsEnabled = lowBalanceAlertsEnabled;
      _topUpAlertsEnabled = topUpAlertsEnabled;
      _invalidRecordAlertsEnabled = invalidRecordAlertsEnabled;
      _remindersEnabled = remindersEnabled;
      _reminderFrequency = reminderFrequency;
      _lastReminderDate = lastReminderDate;
      _isLoading = false;
    });
  }

  String _getFrequencyText(int days) {
    switch (days) {
      case 1:
        return 'Daily';
      case 7:
        return 'Weekly';
      case 14:
        return 'Bi-weekly';
      case 30:
        return 'Monthly';
      default:
        return 'Every $days days';
    }
  }

  Future<void> _showFrequencyDialog() async {
    final notificationProvider =
        Provider.of<NotificationProvider>(context, listen: false);
    final selectedFrequency = await showDialog<int>(
      context: context,
      builder: (context) => SimpleDialog(
        title: const Text('Reminder Frequency'),
        children: [
          _buildFrequencyOption(context, 1, 'Daily'),
          _buildFrequencyOption(context, 7, 'Weekly'),
          _buildFrequencyOption(context, 14, 'Bi-weekly'),
          _buildFrequencyOption(context, 30, 'Monthly'),
        ],
      ),
    );

    if (selectedFrequency != null && mounted) {
      await notificationProvider
          .setMeterReadingReminderFrequency(selectedFrequency);

      setState(() {
        _reminderFrequency = selectedFrequency;
      });
    }
  }

  Widget _buildFrequencyOption(BuildContext context, int days, String label) {
    return SimpleDialogOption(
      onPressed: () => Navigator.pop(context, days),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8.0),
        child: Row(
          children: [
            Radio<int>(
              value: days,
              groupValue: _reminderFrequency,
              onChanged: (value) => Navigator.pop(context, value),
            ),
            const SizedBox(width: 8),
            Text(label),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final notificationProvider = Provider.of<NotificationProvider>(context);
    final primaryColor = Theme.of(context).colorScheme.primary;
    final isExpanded = widget.displayMode == SettingsDisplayMode.expanded;

    if (_isLoading) {
      return Center(
        child: CircularProgressIndicator(
          color: primaryColor,
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (isExpanded) ...[
          Text(
            'Notification Settings',
            style: AppTextStyles.titleMedium,
          ),
          const SizedBox(height: 8),
          Text(
            'Configure which notifications you want to receive',
            style: AppTextStyles.bodyMedium,
          ),
          const SizedBox(height: 16),
        ] else ...[
          Text(
            'Notifications',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
          Text(
            _notificationsEnabled
                ? 'Notifications are enabled'
                : 'Notifications are disabled',
            style: TextStyle(
              fontSize: 12,
              color: _notificationsEnabled ? primaryColor : Colors.grey,
            ),
          ),
          if (widget.showHelperText)
            Text(
              'Configure notification settings',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey,
                fontStyle: FontStyle.italic,
              ),
            ),
          const SizedBox(height: 8),
        ],

        // Master toggle
        SwitchListTile(
          title: const Text('Enable Notifications'),
          subtitle: Text(
            _notificationsEnabled
                ? 'You will receive alerts based on your settings below'
                : 'You will not receive any alerts',
            style: TextStyle(fontSize: isExpanded ? 14 : 12),
          ),
          value: _notificationsEnabled,
          onChanged: (value) async {
            await notificationProvider.setNotificationsEnabled(value);
            setState(() {
              _notificationsEnabled = value;
            });
          },
          dense: !isExpanded,
          contentPadding: isExpanded ? null : EdgeInsets.zero,
          activeColor: primaryColor,
        ),

        if (_notificationsEnabled) ...[
          const Divider(),

          // Low balance alerts
          SwitchListTile(
            title: const Text('Low Balance Alerts'),
            subtitle: Text(
              'Get notified when your meter balance falls below your alert threshold',
              style: TextStyle(fontSize: isExpanded ? 14 : 12),
            ),
            value: _lowBalanceAlertsEnabled,
            onChanged: (value) async {
              await notificationProvider.setLowBalanceAlertsEnabled(value);
              setState(() {
                _lowBalanceAlertsEnabled = value;
              });
            },
            dense: !isExpanded,
            contentPadding: isExpanded ? null : EdgeInsets.zero,
            activeColor: primaryColor,
          ),

          // Time to top up alerts
          SwitchListTile(
            title: const Text('Time to Top Up Alerts'),
            subtitle: Text(
              'Get notified when it\'s time to top up based on your usage patterns',
              style: TextStyle(fontSize: isExpanded ? 14 : 12),
            ),
            value: _topUpAlertsEnabled,
            onChanged: (value) async {
              await notificationProvider.setTopUpAlertsEnabled(value);
              setState(() {
                _topUpAlertsEnabled = value;
              });
            },
            dense: !isExpanded,
            contentPadding: isExpanded ? null : EdgeInsets.zero,
            activeColor: primaryColor,
          ),

          // Invalid record alerts
          SwitchListTile(
            title: const Text('Invalid Record Alerts'),
            subtitle: Text(
              'Get notified when an invalid meter reading or top-up is detected',
              style: TextStyle(fontSize: isExpanded ? 14 : 12),
            ),
            value: _invalidRecordAlertsEnabled,
            onChanged: (value) async {
              await notificationProvider.setInvalidRecordAlertsEnabled(value);
              setState(() {
                _invalidRecordAlertsEnabled = value;
              });
            },
            dense: !isExpanded,
            contentPadding: isExpanded ? null : EdgeInsets.zero,
            activeColor: primaryColor,
          ),

          if (isExpanded)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'Note: You may need to grant permission for notifications in your device settings',
                style: TextStyle(
                  fontStyle: FontStyle.italic,
                  fontSize: 12,
                ),
              ),
            ),

          // Add spacing before meter reading reminder settings
          const SizedBox(height: 24),

          // Meter Reading Reminders section
          if (isExpanded) ...[
            Text(
              'Meter Reading Reminders',
              style: AppTextStyles.titleMedium,
            ),
            const SizedBox(height: 8),
            Text(
              'Get reminded to record your meter readings regularly',
              style: AppTextStyles.bodyMedium,
            ),
            const SizedBox(height: 16),
          ] else ...[
            Text(
              'Meter Reading Reminders',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
          ],

          SwitchListTile(
            title: const Text('Enable Meter Reading Reminders'),
            subtitle: Text(
              _remindersEnabled
                  ? 'Reminders every ${_getFrequencyText(_reminderFrequency).toLowerCase()}'
                  : 'Reminders disabled',
              style: TextStyle(fontSize: isExpanded ? 14 : 12),
            ),
            value: _remindersEnabled,
            onChanged: (value) async {
              await notificationProvider.setMeterReadingRemindersEnabled(value);
              setState(() {
                _remindersEnabled = value;
              });
            },
            dense: !isExpanded,
            contentPadding: isExpanded ? null : EdgeInsets.zero,
            activeColor: primaryColor,
          ),

          if (_remindersEnabled) ...[
            const Divider(),
            ListTile(
              title: const Text('Reminder Frequency'),
              subtitle: Text(_getFrequencyText(_reminderFrequency)),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap: _showFrequencyDialog,
              dense: !isExpanded,
              contentPadding: isExpanded ? null : EdgeInsets.zero,
            ),
            if (_lastReminderDate != null) ...[
              const Divider(),
              ListTile(
                title: const Text('Last Reminder'),
                subtitle: Text(
                  '${_lastReminderDate!.day}/${_lastReminderDate!.month}/${_lastReminderDate!.year}',
                ),
                dense: !isExpanded,
                contentPadding: isExpanded ? null : EdgeInsets.zero,
              ),
            ],
            if (isExpanded) ...[
              const Divider(),
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                child: ElevatedButton(
                  onPressed: () async {
                    await notificationProvider
                        .showMeterReadingReminderNotification();
                    if (mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Reminder sent'),
                        ),
                      );
                      await _loadSettings(); // Reload to update last reminder date
                    }
                  },
                  child: const Text('Send Reminder Now'),
                ),
              ),
            ],
          ],
        ],

        if (widget.errorText != null)
          Padding(
            padding: const EdgeInsets.only(top: 8.0),
            child: Text(
              widget.errorText!,
              style: TextStyle(
                color: Theme.of(context).colorScheme.error,
                fontSize: 12,
              ),
            ),
          ),
      ],
    );
  }
}
