// File: lib/features/setup/presentation/widgets/days_advance_input.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/extensions/context_extensions.dart';
import '../../../../core/utils/input_validator.dart';
import '../../../../core/widgets/app_card.dart';
import '../../../../core/widgets/app_text_field.dart';

/// A widget for inputting the days in advance
class DaysAdvanceInput extends StatefulWidget {
  final int daysInAdvance;
  final Function(int) onDaysChanged;

  const DaysAdvanceInput({
    Key? key,
    required this.daysInAdvance,
    required this.onDaysChanged,
  }) : super(key: key);

  @override
  State<DaysAdvanceInput> createState() => _DaysAdvanceInputState();
}

class _DaysAdvanceInputState extends State<DaysAdvanceInput> {
  late TextEditingController _controller;
  String? _errorText;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.daysInAdvance.toString());
  }

  @override
  void didUpdateWidget(DaysAdvanceInput oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.daysInAdvance != widget.daysInAdvance) {
      _controller.text = widget.daysInAdvance.toString();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AppCard(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Days in Advance',
            style: AppTextStyles.titleMedium.copyWith(
              color: context.textColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'How many days in advance would you like to be notified about topping up?',
            style: AppTextStyles.bodyMedium.copyWith(
              color: context.textColor,
            ),
          ),
          const SizedBox(height: 16),
          AppTextField(
            controller: _controller,
            keyboardType: TextInputType.number,
            inputFormatters: [
              FilteringTextInputFormatter.digitsOnly,
            ],
            errorText: _errorText,
            onChanged: _validateAndUpdate,
          ),
          const SizedBox(height: 8),
          Text(
            'Recommended: 2 days',
            style: AppTextStyles.bodySmall.copyWith(
              fontStyle: FontStyle.italic,
              color: context.secondaryTextColor,
            ),
          ),
        ],
      ),
    );
  }

  void _validateAndUpdate(String value) {
    final validation = InputValidator.validateDaysInAdvance(value);

    setState(() {
      _errorText = validation['isValid'] ? null : validation['errorMessage'];
    });

    if (validation['isValid']) {
      final days = int.tryParse(value) ?? widget.daysInAdvance;
      widget.onDaysChanged(days);
    }
  }
}
