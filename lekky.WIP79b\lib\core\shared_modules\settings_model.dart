// File: lib/core/shared_modules/settings_model.dart
import 'package:flutter/material.dart';

/// Enum defining the display mode for settings components
enum SettingsDisplayMode {
  /// Expanded mode for Setup page - more detailed, more guidance
  expanded,

  /// Compact mode for Settings page - more concise, less guidance
  compact
}

/// Base class for all settings models
abstract class BaseSettingsModel {
  /// Validates the current setting value
  bool isValid();

  /// Returns a user-friendly error message if invalid
  String? getValidationError();
}

/// Model for currency/meter unit setting
class CurrencySettingsModel extends BaseSettingsModel {
  final String value;

  CurrencySettingsModel(this.value);

  @override
  bool isValid() => value.isNotEmpty;

  @override
  String? getValidationError() {
    if (!isValid()) {
      return 'Currency symbol cannot be empty';
    }
    return null;
  }
}

/// Model for date format setting
class DateFormatSettingsModel extends BaseSettingsModel {
  final String value;
  static const List<String> validFormats = [
    'DD-MM-YYYY',
    'MM-DD-YYYY',
    'YYYY-MM-DD',
  ];

  DateFormatSettingsModel(this.value);

  @override
  bool isValid() => validFormats.contains(value);

  @override
  String? getValidationError() {
    if (!isValid()) {
      return 'Invalid date format';
    }
    return null;
  }
}

/// Model for date info display setting
class DateInfoSettingsModel extends BaseSettingsModel {
  final String value;
  static const List<String> validOptions = ['Date only', 'Date and Time'];

  DateInfoSettingsModel(this.value);

  @override
  bool isValid() => validOptions.contains(value);

  @override
  String? getValidationError() {
    if (!isValid()) {
      return 'Invalid date info option';
    }
    return null;
  }
}

/// Model for alert threshold setting
class AlertThresholdSettingsModel extends BaseSettingsModel {
  final double value;

  AlertThresholdSettingsModel(this.value);

  @override
  bool isValid() => value >= 0;

  @override
  String? getValidationError() {
    if (!isValid()) {
      return 'Alert threshold cannot be negative';
    }
    return null;
  }
}

/// Model for days in advance setting
class DaysInAdvanceSettingsModel extends BaseSettingsModel {
  final int value;

  DaysInAdvanceSettingsModel(this.value);

  @override
  bool isValid() => value > 0;

  @override
  String? getValidationError() {
    if (!isValid()) {
      return 'Days in advance must be greater than 0';
    }
    return null;
  }
}

/// Model for appearance/theme setting
class AppearanceSettingsModel extends BaseSettingsModel {
  final ThemeMode value;

  AppearanceSettingsModel(this.value);

  @override
  bool isValid() => true; // All theme modes are valid

  @override
  String? getValidationError() => null;
}

/// Model for notification settings
class NotificationSettingsModel extends BaseSettingsModel {
  final bool enabled;
  final bool lowBalanceAlerts;
  final bool daysInAdvanceAlerts;

  NotificationSettingsModel({
    required this.enabled,
    this.lowBalanceAlerts = true,
    this.daysInAdvanceAlerts = true,
  });

  @override
  bool isValid() => true; // All combinations are valid

  @override
  String? getValidationError() => null;
}

/// Model for language setting
class LanguageSettingsModel extends BaseSettingsModel {
  final String value;
  static final List<String> validLanguages = [
    'en',
    'es',
    'fr',
    'de',
    'it',
    'pt',
    'nl',
    'pl',
    'ru',
    'zh',
    'ja',
    'ko',
    'ar',
    'hi'
  ];

  LanguageSettingsModel(this.value);

  @override
  bool isValid() => validLanguages.contains(value);

  @override
  String? getValidationError() {
    if (!isValid()) {
      return 'Invalid language code';
    }
    return null;
  }
}
