// File: lib/core/settings/widgets/days_in_advance_selector_adapter.dart
import 'package:flutter/material.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/shared_modules/settings_model.dart';
import 'days_in_advance_dialog.dart';

/// An adapter component that replaces the radio button selector for days in advance
/// with a direct dialog access
class DaysInAdvanceSelectorAdapter extends StatelessWidget {
  final int currentValue;
  final ValueChanged<int> onChanged;
  final bool hasTotalAverage;
  final SettingsDisplayMode displayMode;
  final bool showHelperText;
  final bool showTitle;

  const DaysInAdvanceSelectorAdapter({
    Key? key,
    required this.currentValue,
    required this.onChanged,
    this.hasTotalAverage = true,
    this.displayMode = SettingsDisplayMode.expanded,
    this.showHelperText = true,
    this.showTitle = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final primaryColor = isDarkMode ? AppColors.primaryDark : AppColors.primary;
    final textColor = isDarkMode ? Colors.white : Colors.black87;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (showTitle) ...[
          Text(
            'Days in Advance',
            style: AppTextStyles.titleMedium.copyWith(
              color: primaryColor,
            ),
          ),
          const SizedBox(height: 4),
        ],

        if (showHelperText) ...[
          Text(
            'How many days in advance would you like to be notified about topping up?',
            style: AppTextStyles.bodyMedium.copyWith(
              color: textColor.withOpacity(0.7),
            ),
          ),
          const SizedBox(height: 8),
        ],

        // Current value display and edit button
        InkWell(
          onTap: () => _showDaysInAdvanceDialog(context),
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
            decoration: BoxDecoration(
              border: Border.all(
                color: isDarkMode ? Colors.grey[700]! : Colors.grey[300]!,
              ),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '$currentValue ${currentValue == 1 ? 'day' : 'days'}',
                  style: AppTextStyles.bodyLarge.copyWith(
                    fontWeight: FontWeight.w500,
                    color: textColor,
                  ),
                ),
                Icon(
                  Icons.edit,
                  color: primaryColor,
                  size: 20,
                ),
              ],
            ),
          ),
        ),

        if (showHelperText && !hasTotalAverage) ...[
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.amber.withOpacity(0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(Icons.info_outline, color: Colors.amber[800], size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Days in advance alerts will be active after you enter at least two meter readings to calculate your average usage.',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.amber[800],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  void _showDaysInAdvanceDialog(BuildContext context) {
    DaysInAdvanceDialog.show(
      context,
      currentValue,
      onChanged,
    );
  }
}
