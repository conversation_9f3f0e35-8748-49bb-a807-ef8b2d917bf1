// File: lib/core/shared_modules/radio_alert_threshold_selector_adapter.dart
import 'package:flutter/material.dart';
import '../settings/widgets/radio_alert_threshold_selector.dart';
import 'settings_model.dart';

/// An adapter class that provides a radio-based alert threshold selector
/// This allows us to use the new radio-based selector with the existing code
class RadioAlertThresholdSelectorAdapter extends StatelessWidget {
  final double currentValue;
  final ValueChanged<double> onChanged;
  final String currencySymbol;
  final String? errorText;
  final bool hasMeterReadings;
  final SettingsDisplayMode displayMode;
  final bool showHelperText;
  final bool showTitle;

  const RadioAlertThresholdSelectorAdapter({
    Key? key,
    required this.currentValue,
    required this.onChanged,
    required this.currencySymbol,
    this.errorText,
    this.hasMeterReadings = false,
    this.displayMode = SettingsDisplayMode.compact,
    this.showHelperText = false,
    this.showTitle = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Use the radio-based selector with the same interface as the slider-based one
    return RadioAlertThresholdSelector(
      currentValue: currentValue,
      onChanged: onChanged,
      currencySymbol: currencySymbol,
      errorText: errorText,
      showHelperText:
          showHelperText || displayMode == SettingsDisplayMode.expanded,
      showTitle: showTitle,
    );
  }
}
