// File: lib/features/notifications/presentation/widgets/storage_migration_dialog.dart

import 'package:flutter/material.dart';
import '../../data/migration/storage_migration_service.dart';

/// Dialog for showing migration progress
class StorageMigrationDialog extends StatefulWidget {
  final StorageMigrationService migrationService;
  final VoidCallback? onMigrationCompleted;
  final VoidCallback? onMigrationFailed;

  const StorageMigrationDialog({
    Key? key,
    required this.migrationService,
    this.onMigrationCompleted,
    this.onMigrationFailed,
  }) : super(key: key);

  @override
  State<StorageMigrationDialog> createState() => _StorageMigrationDialogState();
}

class _StorageMigrationDialogState extends State<StorageMigrationDialog> {
  double _progress = 0.0;
  String _status = 'Preparing for migration...';
  bool _isCompleted = false;
  bool _isFailed = false;

  @override
  void initState() {
    super.initState();
    _startMigration();
  }

  /// Start the migration process
  Future<void> _startMigration() async {
    try {
      final result = await widget.migrationService.executeMigration(
        onProgress: (progress) {
          setState(() {
            _progress = progress;
          });
        },
        onStatusUpdate: (status) {
          setState(() {
            _status = status;
          });
        },
      );

      if (result) {
        setState(() {
          _isCompleted = true;
          _progress = 1.0;
          _status = 'Migration completed successfully';
        });

        // Notify parent
        widget.onMigrationCompleted?.call();
      } else {
        setState(() {
          _isFailed = true;
          _status = 'Migration failed';
        });

        // Notify parent
        widget.onMigrationFailed?.call();
      }
    } catch (e) {
      setState(() {
        _isFailed = true;
        _status = 'Error: $e';
      });

      // Notify parent
      widget.onMigrationFailed?.call();
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Storage Migration'),
      content: SizedBox(
        width: 300,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Migrating notification data from SharedPreferences to SQLite database for improved performance and reliability.',
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            LinearProgressIndicator(
              value: _progress,
              backgroundColor: Colors.grey[300],
              valueColor: AlwaysStoppedAnimation<Color>(
                _isFailed ? Colors.red : Colors.blue,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              _status,
              textAlign: TextAlign.center,
              style: TextStyle(
                color: _isFailed ? Colors.red : null,
                fontWeight: _isCompleted || _isFailed ? FontWeight.bold : null,
              ),
            ),
          ],
        ),
      ),
      actions: [
        if (_isCompleted || _isFailed)
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: Text(_isCompleted ? 'Done' : 'Close'),
          ),
      ],
    );
  }
}

/// Show the storage migration dialog
Future<bool?> showStorageMigrationDialog(
  BuildContext context,
  StorageMigrationService migrationService,
) {
  return showDialog<bool>(
    context: context,
    barrierDismissible: false,
    builder: (context) => StorageMigrationDialog(
      migrationService: migrationService,
      onMigrationCompleted: () {
        // Wait a moment before closing
        Future.delayed(const Duration(seconds: 1), () {
          if (context.mounted) {
            Navigator.of(context).pop(true);
          }
        });
      },
      onMigrationFailed: () {
        // Do not automatically close on failure
      },
    ),
  );
}
