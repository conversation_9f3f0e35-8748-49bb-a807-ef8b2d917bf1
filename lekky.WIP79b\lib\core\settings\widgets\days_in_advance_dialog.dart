// File: lib/core/settings/widgets/days_in_advance_dialog.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../core/widgets/app_text_field.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../core/theme/app_colors.dart';
import '../validators/settings_validator.dart';

/// A dialog for setting the days in advance
class DaysInAdvanceDialog extends StatefulWidget {
  final int currentValue;
  final Function(int) onChanged;

  const DaysInAdvanceDialog({
    Key? key,
    required this.currentValue,
    required this.onChanged,
  }) : super(key: key);

  /// Static method to show the dialog
  static Future<void> show(
    BuildContext context,
    int currentValue,
    Function(int) onChanged,
  ) async {
    return showDialog(
      context: context,
      builder: (context) => DaysInAdvanceDialog(
        currentValue: currentValue,
        onChanged: onChanged,
      ),
    );
  }

  @override
  State<DaysInAdvanceDialog> createState() => _DaysInAdvanceDialogState();
}

class _DaysInAdvanceDialogState extends State<DaysInAdvanceDialog> {
  late TextEditingController _controller;
  String? _errorText;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(
      text: widget.currentValue.toString(),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _validateAndSave() {
    final validation = SettingsValidator.validateDaysInAdvance(_controller.text);
    if (validation['isValid']) {
      final value = int.tryParse(_controller.text);
      if (value != null && value >= 1 && value <= 99) {
        widget.onChanged(value);
        Navigator.of(context).pop();
      } else {
        setState(() {
          _errorText = 'Please enter a value between 1 and 99';
        });
      }
    } else {
      setState(() {
        _errorText = validation['errorMessage'];
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return AlertDialog(
      title: Row(
        children: [
          Icon(
            Icons.calendar_today,
            color: isDarkMode ? AppColors.primaryDark : AppColors.primary,
          ),
          const SizedBox(width: 8),
          const Text('Days in Advance'),
        ],
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'How many days in advance would you like to be notified about topping up?',
            style: AppTextStyles.bodyMedium,
          ),
          const SizedBox(height: 16),
          AppTextField(
            controller: _controller,
            labelText: 'Days in Advance',
            hintText: 'Enter value (1-99)',
            errorText: _errorText,
            keyboardType: TextInputType.number,
            inputFormatters: [
              FilteringTextInputFormatter.digitsOnly,
            ],
            autofocus: true,
            selectAllOnFocus: true,
            onChanged: (value) {
              // Clear error when user types
              if (_errorText != null) {
                setState(() {
                  _errorText = null;
                });
              }
            },
          ),
          const SizedBox(height: 8),
          Text(
            'Recommended: 2 days',
            style: AppTextStyles.bodySmall.copyWith(
              fontStyle: FontStyle.italic,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _validateAndSave,
          style: ElevatedButton.styleFrom(
            backgroundColor: isDarkMode ? AppColors.primaryDark : AppColors.primary,
            foregroundColor: Colors.white,
          ),
          child: const Text('Save'),
        ),
      ],
    );
  }
}
