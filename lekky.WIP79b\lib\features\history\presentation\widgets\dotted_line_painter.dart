// File: lib/features/history/presentation/widgets/dotted_line_painter.dart
import 'dart:math' as math;
import 'package:flutter/material.dart';

/// Custom painter for drawing a dotted line
class DottedLinePainter extends CustomPainter {
  final Offset start;
  final Offset end;
  final Color color;
  
  DottedLinePainter({
    required this.start,
    required this.end,
    required this.color,
  });
  
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;
    
    const dashWidth = 4;
    const dashSpace = 4;
    
    // Calculate the distance and direction
    final dx = end.dx - start.dx;
    final dy = end.dy - start.dy;
    final distance = math.sqrt(dx * dx + dy * dy);
    
    // Calculate the number of dashes
    final dashCount = distance ~/ (dashWidth + dashSpace);
    
    // Draw the dashes
    for (int i = 0; i < dashCount; i++) {
      final startFraction = i * (dashWidth + dashSpace) / distance;
      final endFraction = (i * (dashWidth + dashSpace) + dashWidth) / distance;
      
      final dashStart = Offset(
        start.dx + dx * startFraction,
        start.dy + dy * startFraction,
      );
      
      final dashEnd = Offset(
        start.dx + dx * endFraction,
        start.dy + dy * endFraction,
      );
      
      canvas.drawLine(dashStart, dashEnd, paint);
    }
  }
  
  @override
  bool shouldRepaint(DottedLinePainter oldDelegate) {
    return start != oldDelegate.start || 
           end != oldDelegate.end || 
           color != oldDelegate.color;
  }
}
