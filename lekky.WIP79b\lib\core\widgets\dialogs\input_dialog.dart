// File: lib/core/widgets/dialogs/input_dialog.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../theme/app_colors.dart';
import '../../theme/app_text_styles.dart';
import '../../utils/dialog_button_styles.dart';
import '../app_dialog.dart';
import '../app_text_field.dart';

/// A specialized dialog for collecting specific data from users.
///
/// This dialog presents a clear title, appropriate input fields with validation,
/// and Save/Cancel buttons.
class InputDialog {
  /// Shows an input dialog with a single text field.
  ///
  /// Parameters:
  /// - [context]: The build context.
  /// - [title]: The title of the dialog, should clearly indicate the purpose.
  /// - [labelText]: The label for the text field.
  /// - [hintText]: The hint text for the text field.
  /// - [initialValue]: The initial value for the text field.
  /// - [validator]: A function that validates the input and returns an error message if invalid.
  /// - [keyboardType]: The keyboard type for the text field.
  /// - [inputFormatters]: Input formatters for the text field.
  /// - [saveText]: The text for the save button (default: "Save").
  /// - [cancelText]: The text for the cancel button (default: "Cancel").
  /// - [helperText]: Optional helper text to provide guidance.
  /// - [prefixText]: Optional prefix text for the text field (e.g., currency symbol).
  /// - [maxLength]: Optional maximum length for the input.
  static Future<String?> show({
    required BuildContext context,
    required String title,
    required String labelText,
    String? hintText,
    String? initialValue,
    String? Function(String?)? validator,
    TextInputType keyboardType = TextInputType.text,
    List<TextInputFormatter>? inputFormatters,
    String saveText = 'Save',
    String cancelText = 'Cancel',
    String? helperText,
    String? prefixText,
    int? maxLength,
  }) async {
    final TextEditingController controller =
        TextEditingController(text: initialValue);
    String? errorText;

    return showDialog<String>(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AppDialog(
              title: title,
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (helperText != null) ...[
                    Text(
                      helperText,
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: Theme.of(context).brightness == Brightness.dark
                            ? AppColors.onSurfaceDark.withOpacity(0.8)
                            : AppColors.onSurface.withOpacity(0.8),
                      ),
                    ),
                    const SizedBox(height: 16),
                  ],
                  AppTextField(
                    controller: controller,
                    labelText: labelText,
                    hintText: hintText,
                    errorText: errorText,
                    keyboardType: keyboardType,
                    inputFormatters: inputFormatters,
                    prefixText: prefixText,
                    maxLength: maxLength,
                    autofocus: true,
                    onChanged: (value) {
                      if (errorText != null) {
                        setState(() {
                          errorText = validator?.call(value);
                        });
                      }
                    },
                  ),
                ],
              ),
              actions: [
                DialogButtonStyles.createCancelButton(
                  context: context,
                  onPressed: () => Navigator.of(context).pop(),
                  text: cancelText,
                ),
                DialogButtonStyles.createSaveButton(
                  context: context,
                  onPressed: () {
                    final value = controller.text;
                    final error = validator?.call(value);

                    if (error != null) {
                      setState(() {
                        errorText = error;
                      });
                    } else {
                      Navigator.of(context).pop(value);
                    }
                  },
                  text: saveText,
                ),
              ],
            );
          },
        );
      },
    );
  }

  /// Shows a numeric input dialog for collecting numeric values.
  ///
  /// This is a specialized version of the input dialog for numeric values,
  /// with appropriate keyboard type and input formatters.
  static Future<double?> showNumeric({
    required BuildContext context,
    required String title,
    required String labelText,
    String? hintText,
    double? initialValue,
    String? Function(String?)? validator,
    bool allowDecimal = true,
    String saveText = 'Save',
    String cancelText = 'Cancel',
    String? helperText,
    String? prefixText,
    int? maxLength,
  }) async {
    final String? result = await show(
      context: context,
      title: title,
      labelText: labelText,
      hintText: hintText,
      initialValue: initialValue?.toString(),
      validator: validator,
      keyboardType: TextInputType.numberWithOptions(decimal: allowDecimal),
      inputFormatters: [
        if (allowDecimal)
          FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*$'))
        else
          FilteringTextInputFormatter.digitsOnly,
      ],
      saveText: saveText,
      cancelText: cancelText,
      helperText: helperText,
      prefixText: prefixText,
      maxLength: maxLength,
    );

    if (result == null) {
      return null;
    }

    return double.tryParse(result);
  }

  /// Shows a custom form dialog with a provided widget as content.
  ///
  /// This is a specialized version of the input dialog for complex forms,
  /// allowing you to provide a custom widget as the content.
  ///
  /// Parameters:
  /// - [context]: The build context.
  /// - [title]: The title of the dialog, should clearly indicate the purpose.
  /// - [widget]: The custom widget to display in the dialog.
  /// - [barrierDismissible]: Whether the dialog can be dismissed by tapping outside (default: true).
  /// - [icon]: An optional icon to display in the dialog.
  /// - [iconColor]: The color of the icon (if provided).
  static Future<void> showCustomForm({
    required BuildContext context,
    required String title,
    required Widget widget,
    bool barrierDismissible = true,
    IconData? icon,
    Color? iconColor,
  }) async {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    // Determine the icon color if not specified
    final Color effectiveIconColor =
        iconColor ?? (isDarkMode ? AppColors.primaryDark : AppColors.primary);

    // Use the widget directly as content (icon is now in the title)
    Widget content = widget;

    // Use a simple dialog with fixed size
    return showDialog(
      context: context,
      barrierDismissible: barrierDismissible,
      builder: (BuildContext context) {
        // Create a dialog with custom positioning and size
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          insetPadding: const EdgeInsets.only(
            left: 16,
            right: 16,
            top: 0,
            bottom: 120,
          ),
          alignment: const Alignment(0, -0.3),
          child: SizedBox(
            width: 350,
            height: 460,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Padding(
                  padding: const EdgeInsets.fromLTRB(24, 16, 24, 8),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      if (icon != null) ...[
                        Icon(
                          icon,
                          size: 24, // Same size as text
                          color: effectiveIconColor,
                        ),
                        const SizedBox(width: 8),
                      ],
                      Text(
                        title,
                        style: AppTextStyles.titleLarge.copyWith(
                          color: isDarkMode
                              ? AppColors.onSurfaceDark
                              : AppColors.onSurface,
                        ),
                      ),
                    ],
                  ),
                ),
                Flexible(
                  child: Builder(builder: (context) {
                    // Create a scroll controller that we can access
                    final ScrollController scrollController =
                        ScrollController();

                    // Create a more robust keyboard listener
                    void handleKeyboard() {
                      // Get keyboard height
                      final keyboardHeight =
                          MediaQuery.of(context).viewInsets.bottom;

                      // Only proceed if keyboard is visible and scroll controller is attached
                      if (keyboardHeight > 0 && scrollController.hasClients) {
                        // Force scroll to the bottom to ensure input fields are visible
                        // Use a slight delay to ensure the keyboard is fully visible
                        Future.delayed(const Duration(milliseconds: 100), () {
                          if (scrollController.hasClients) {
                            scrollController.animateTo(
                              scrollController.position.maxScrollExtent,
                              duration: const Duration(milliseconds: 300),
                              curve: Curves.easeOut,
                            );
                          }
                        });
                      }
                    }

                    // Add a post-frame callback to handle keyboard visibility
                    WidgetsBinding.instance
                        .addPostFrameCallback((_) => handleKeyboard());

                    // We'll use a listener on the scroll controller instead of MediaQuery
                    scrollController.addListener(() {
                      final keyboardHeight =
                          MediaQuery.of(context).viewInsets.bottom;
                      if (keyboardHeight > 0) {
                        // If keyboard is visible, ensure content is scrolled
                        handleKeyboard();
                      }
                    });

                    return NotificationListener<ScrollNotification>(
                      // Add another listener for scroll events
                      onNotification: (notification) {
                        // When user scrolls, check if keyboard is visible
                        final keyboardHeight =
                            MediaQuery.of(context).viewInsets.bottom;
                        if (keyboardHeight > 0) {
                          handleKeyboard();
                        }
                        return false;
                      },
                      child: SingleChildScrollView(
                        // Use the controller we created
                        controller: scrollController,
                        physics: const AlwaysScrollableScrollPhysics(),
                        padding: const EdgeInsets.fromLTRB(24, 16, 24, 16),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            content,
                            // Add extra space at the bottom to ensure content is visible above keyboard
                            const SizedBox(
                                height:
                                    120), // Increased bottom padding for keyboard
                          ],
                        ),
                      ),
                    );
                  }),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
