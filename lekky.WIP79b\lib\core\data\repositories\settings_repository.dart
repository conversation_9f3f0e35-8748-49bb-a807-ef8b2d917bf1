// File: lib/core/data/repositories/settings_repository.dart
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../constants/app_constants.dart';
import '../database/db_optimizer.dart';

/// Repository for app settings
class SettingsRepository {
  /// Get a boolean setting
  Future<bool> getBoolSetting(String key, {bool defaultValue = false}) async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(key) ?? defaultValue;
  }

  /// Set a boolean setting
  Future<void> setBoolSetting(String key, bool value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(key, value);
  }

  /// Get a string setting
  Future<String> getStringSetting(String key,
      {String defaultValue = ''}) async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(key) ?? defaultValue;
  }

  /// Set a string setting
  Future<void> setStringSetting(String key, String value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(key, value);
  }

  /// Get an integer setting
  Future<int> getIntSetting(String key, {int defaultValue = 0}) async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getInt(key) ?? defaultValue;
  }

  /// Set an integer setting
  Future<void> setIntSetting(String key, int value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(key, value);
  }

  /// Get a double setting
  Future<double> getDoubleSetting(String key,
      {double defaultValue = 0.0}) async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getDouble(key) ?? defaultValue;
  }

  /// Set a double setting
  Future<void> setDoubleSetting(String key, double value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setDouble(key, value);
  }

  /// Get multiple settings at once
  Future<Map<String, dynamic>> getMultipleSettings(List<String> keys) async {
    return await DBOptimizer.batchReadPreferences(keys);
  }

  /// Set multiple settings at once
  Future<void> setMultipleSettings(Map<String, dynamic> settings) async {
    await DBOptimizer.batchWritePreferences(settings);
  }

  /// Check if setup is completed
  Future<bool> isSetupCompleted() async {
    try {
      final result = await getBoolSetting(AppConstants.keySetupCompleted,
          defaultValue: false);
      debugPrint('SettingsRepository: isSetupCompleted = $result');
      return result;
    } catch (e) {
      debugPrint('SettingsRepository: Error checking setup status: $e');
      return false; // Default to false (show welcome screen) in case of error
    }
  }

  /// Set setup completed
  Future<void> setSetupCompleted(bool value) async {
    try {
      await setBoolSetting(AppConstants.keySetupCompleted, value);
      debugPrint('SettingsRepository: setSetupCompleted = $value');
    } catch (e) {
      debugPrint('SettingsRepository: Error setting setup status: $e');
      rethrow;
    }
  }

  /// Check if it's the first launch
  Future<bool> isFirstLaunch() async {
    return await getBoolSetting(AppConstants.keyIsFirstLaunch,
        defaultValue: true);
  }

  /// Set first launch
  Future<void> setFirstLaunch(bool value) async {
    await setBoolSetting(AppConstants.keyIsFirstLaunch, value);
  }

  /// Get meter unit
  Future<String> getMeterUnit() async {
    return await getStringSetting(AppConstants.keyMeterUnit, defaultValue: '£');
  }

  /// Set meter unit
  Future<void> setMeterUnit(String value) async {
    await setStringSetting(AppConstants.keyMeterUnit, value);
  }

  /// Get alert threshold
  Future<double> getAlertThreshold() async {
    return await getDoubleSetting(AppConstants.keyAlertThreshold,
        defaultValue: 5.0);
  }

  /// Set alert threshold
  Future<void> setAlertThreshold(double value) async {
    await setDoubleSetting(AppConstants.keyAlertThreshold, value);
  }

  /// Get days in advance
  Future<int> getDaysInAdvance() async {
    return await getIntSetting(AppConstants.keyDaysInAdvance, defaultValue: 2);
  }

  /// Set days in advance
  Future<void> setDaysInAdvance(int value) async {
    await setIntSetting(AppConstants.keyDaysInAdvance, value);
  }

  /// Get date info
  Future<String> getDateInfo() async {
    return await getStringSetting(AppConstants.keyDateInfo,
        defaultValue: 'Date only');
  }

  /// Set date info
  Future<void> setDateInfo(String value) async {
    await setStringSetting(AppConstants.keyDateInfo, value);
  }

  /// Get date format
  Future<String> getDateFormat() async {
    return await getStringSetting(AppConstants.keyDateFormat,
        defaultValue: 'DD-MM-YYYY');
  }

  /// Set date format
  Future<void> setDateFormat(String value) async {
    await setStringSetting(AppConstants.keyDateFormat, value);
  }

  /// Get theme mode
  Future<String> getThemeMode() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      if (!prefs.containsKey(AppConstants.keyThemeMode)) {
        return 'system';
      }
      return prefs.getString(AppConstants.keyThemeMode) ?? 'system';
    } catch (e) {
      // If there's an error (like type mismatch), reset the value
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(AppConstants.keyThemeMode);
      return 'system';
    }
  }

  /// Set theme mode
  Future<void> setThemeMode(String value) async {
    await setStringSetting(AppConstants.keyThemeMode, value);
  }

  /// Check if notifications are enabled
  Future<bool> areNotificationsEnabled() async {
    return await getBoolSetting(AppConstants.keyNotificationsEnabled,
        defaultValue: true);
  }

  /// Set notifications enabled
  Future<void> setNotificationsEnabled(bool value) async {
    await setBoolSetting(AppConstants.keyNotificationsEnabled, value);
  }

  /// Get initial credit (when moving in)
  Future<double?> getInitialCredit() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.containsKey(AppConstants.keyInitialCredit)
        ? prefs.getDouble(AppConstants.keyInitialCredit)
        : null;
  }

  /// Set initial credit (when moving in)
  Future<void> setInitialCredit(double value) async {
    await setDoubleSetting(AppConstants.keyInitialCredit, value);
  }

  /// Clear initial credit
  Future<void> clearInitialCredit() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(AppConstants.keyInitialCredit);
  }

  /// Clear all settings
  Future<void> clearAllSettings() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.clear();
  }

  /// Get language
  Future<String> getLanguage() async {
    return await getStringSetting(AppConstants.keyLanguage, defaultValue: 'en');
  }

  /// Set language
  Future<void> setLanguage(String value) async {
    await setStringSetting(AppConstants.keyLanguage, value);
  }
}
