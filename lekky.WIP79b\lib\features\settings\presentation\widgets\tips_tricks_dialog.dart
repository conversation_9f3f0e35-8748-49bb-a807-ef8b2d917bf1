// File: lib/features/settings/presentation/widgets/tips_tricks_dialog.dart
import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';

/// A dialog that displays tips and tricks for using the app
class TipsTricksDialog extends StatelessWidget {
  const TipsTricksDialog({super.key});

  /// Static method to show the dialog
  static Future<void> show({
    required BuildContext context,
  }) async {
    await showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        insetPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
        child: Container(
          constraints: BoxConstraints(
            maxWidth: MediaQuery.of(context).size.width * 0.9,
            maxHeight: MediaQuery.of(context).size.height * 0.8,
          ),
          child: const TipsTricksDialog(),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final primaryColor = Theme.of(context).colorScheme.primary;

    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // Header
        Padding(
          padding: const EdgeInsets.fromLTRB(24, 16, 24, 0),
          child: Row(
            children: [
              Icon(
                Icons.lightbulb_outline,
                color: Theme.of(context).brightness == Brightness.dark
                    ? Colors.white
                    : primaryColor,
                size: 24,
              ),
              const SizedBox(width: 10),
              Text(
                'Tips & Tricks',
                style: AppTextStyles.titleMedium.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).brightness == Brightness.dark
                      ? Colors.white
                      : AppColors.primary,
                ),
              ),
              const Spacer(),
              IconButton(
                icon: Icon(
                  Icons.close,
                  color: Theme.of(context).brightness == Brightness.dark
                      ? Colors.white70
                      : AppColors.onSurface,
                ),
                onPressed: () => Navigator.of(context).pop(),
                tooltip: 'Close',
                constraints: const BoxConstraints(),
                padding: EdgeInsets.zero,
                iconSize: 20,
              ),
            ],
          ),
        ),

        // Content
        Flexible(
          child: Padding(
            padding: const EdgeInsets.fromLTRB(24, 8, 24, 0),
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Best Ways to Use the Lekky App
                  _buildInfoSection(
                    context,
                    'Best Ways to Use Lekky',
                    'These tips help you stay in control of your electricity costs:',
                    [
                      _buildBulletPoint(
                        context,
                        'Take meter readings frequently for the most accurate tracking and predictions. Use alerts and notifications to help remind you.',
                      ),
                      _buildBulletPoint(
                        context,
                        'Use the + button to instantly record readings or top-ups as they happen - don\'t wait until you forget :)',
                      ),
                      _buildBulletPoint(
                        context,
                        'It only takes seconds to check your remaining balance - it helps you manage and control your electrical usage.',
                      ),
                      _buildBulletPoint(
                        context,
                        'You can take readings at any time of day - the app calculates usage with minute-level precision for accurate results.',
                      ),
                    ],
                    icon: Icons.lightbulb_outline,
                  ),

                  const SizedBox(height: 16),

                  // Cost Calculations
                  _buildInfoSection(
                    context,
                    'Cost Calculations',
                    'Understanding your electricity costs:',
                    [
                      _buildBulletPoint(
                        context,
                        'Switch between day/week/month/year views to spot patterns in your usage you might otherwise miss.',
                      ),
                      _buildBulletPoint(
                        context,
                        'Use custom date ranges to analyze specific periods like holidays or when you\'ve been away from home.',
                      ),
                      _buildBulletPoint(
                        context,
                        'The calculation method shown below each cost figure tells you how the estimate is calculated.',
                      ),
                      _buildBulletPoint(
                        context,
                        'Use the projections to set a monthly electricity budget that works with your overall finances.',
                      ),
                      _buildBulletPoint(
                        context,
                        'Compare costs across seasons to prepare for higher-usage months before they arrive.',
                      ),
                    ],
                    icon: Icons.calculate,
                  ),

                  const SizedBox(height: 16),

                  // History Page
                  _buildInfoSection(
                    context,
                    'History Page',
                    'The benefits of keeping a record:',
                    [
                      _buildBulletPoint(
                        context,
                        'Your history reveals patterns - morning spikes might indicate heating, evening spikes could be cooking habits.',
                      ),
                      _buildBulletPoint(
                        context,
                        'Yellow warning triangles highlight entries that might need attention - fixing these improves your predictions.',
                      ),
                      _buildBulletPoint(
                        context,
                        'The short-average shows recent trends, while total-average gives the big picture - both matter!',
                      ),
                      _buildBulletPoint(
                        context,
                        'Averages are calculated with minute-level precision, so even readings taken hours apart on the same day will give accurate results.',
                      ),
                      _buildBulletPoint(
                        context,
                        'Tap any entry to see full details or make corrections - accuracy builds better predictions.',
                      ),
                      _buildBulletPoint(
                        context,
                        'Watch how your usage changes over time - improvements in efficiency will show as downward trends.',
                      ),
                    ],
                    icon: Icons.history,
                  ),

                  const SizedBox(height: 16),

                  // Notifications & Alerts
                  _buildInfoSection(
                    context,
                    'Notifications & Alerts',
                    'Help to keep a consistent record with helpful reminders:',
                    [
                      _buildBulletPoint(
                        context,
                        'Set alerts at thresholds that suit you - try to get notified when you have days of usage remaining, not when you\'re empty!',
                      ),
                      _buildBulletPoint(
                        context,
                        'Schedule reminders for consistent days/times to build a solid reading habit.',
                      ),
                      _buildBulletPoint(
                        context,
                        'Set reminder times when you\'re typically at home and can easily check your meter.',
                      ),
                      _buildBulletPoint(
                        context,
                        'Start with weekly reminders, then adjust frequency based on your usage patterns.',
                      ),
                      _buildBulletPoint(
                        context,
                        'Treat notifications as reminders to read the meter - it\'s something that needs done anyway!',
                      ),
                    ],
                    icon: Icons.notifications_active,
                  ),

                  const SizedBox(height: 16),

                  // Backup & Restore
                  _buildInfoSection(
                    context,
                    'Backup & Restore',
                    'Data management tips:',
                    [
                      _buildBulletPoint(
                        context,
                        'Export your data monthly - it takes seconds but protects months of valuable tracking.',
                      ),
                      _buildBulletPoint(
                        context,
                        'Backup files are saved to your Downloads folder - consider moving them to cloud storage for extra safety.',
                      ),
                      _buildBulletPoint(
                        context,
                        'Planning a new phone? Export your data first so you can import it on your new device.',
                      ),
                      _buildBulletPoint(
                        context,
                        'To transfer data between devices: 1) Export data on old device, 2) Copy the backup file to new device, 3) Install Lekky on new device, 4) Use "Restore Previous Data" on welcome screen or Settings > Data Backup to import.',
                      ),
                      _buildBulletPoint(
                        context,
                        'When importing, \'Append\' adds new entries while \'Replace\' starts fresh - choose based on your needs.',
                      ),
                      _buildBulletPoint(
                        context,
                        'In a spreadsheet meter readings have type 0 and top ups are type 1.',
                      ),
                      _buildBulletPoint(
                        context,
                        'Lekky can store up to 630 entries - if you reach this limit, consider exporting and then clearing older data.',
                      ),
                    ],
                    icon: Icons.backup,
                  ),
                ],
              ),
            ),
          ),
        ),

        // Footer with "Got it" button
        Padding(
          padding: const EdgeInsets.all(16),
          child: TextButton(
            onPressed: () => Navigator.of(context).pop(),
            style: TextButton.styleFrom(
              foregroundColor: primaryColor,
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
            child: const Text('Got it'),
          ),
        ),
      ],
    );
  }

  Widget _buildInfoSection(
    BuildContext context,
    String title,
    String description,
    List<Widget> items, {
    IconData? icon,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            if (icon != null) ...[
              Icon(
                icon,
                color: Theme.of(context).brightness == Brightness.dark
                    ? Colors.white
                    : Colors.black,
                size: 18,
              ),
              const SizedBox(width: 8),
            ],
            Text(
              title,
              style: AppTextStyles.titleSmall.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).brightness == Brightness.dark
                    ? Colors.white
                    : Colors.black,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Text(
          description,
          style: AppTextStyles.bodyMedium.copyWith(
            color: Theme.of(context).brightness == Brightness.dark
                ? Colors
                    .white70 // Lighter color for better readability in dark mode
                : AppColors.onSurface,
          ),
        ),
        const SizedBox(height: 8),
        ...items,
      ],
    );
  }

  Widget _buildBulletPoint(BuildContext context, String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0, left: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '•',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).brightness == Brightness.dark
                  ? Colors.white70
                  : Colors.black,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              text,
              style: TextStyle(
                fontSize: 14,
                color: Theme.of(context).brightness == Brightness.dark
                    ? Colors.white70
                    : AppColors.onSurface,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
