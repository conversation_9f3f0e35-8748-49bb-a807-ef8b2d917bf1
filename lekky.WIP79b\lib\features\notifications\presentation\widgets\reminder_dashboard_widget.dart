// File: lib/features/notifications/presentation/widgets/reminder_dashboard_widget.dart

import 'package:flutter/material.dart';
import 'dart:async';
import 'package:intl/intl.dart';
import '../../domain/models/notification_model.dart';

/// A model representing a scheduled reminder
class ScheduledReminder {
  final String id;
  final String title;
  final String description;
  final DateTime nextOccurrence;
  final String frequency; // 'daily', 'weekly', 'monthly', 'custom'
  final int frequencyDays;
  final NotificationPriority priority;
  final bool isActive;

  const ScheduledReminder({
    required this.id,
    required this.title,
    required this.description,
    required this.nextOccurrence,
    required this.frequency,
    this.frequencyDays = 0,
    required this.priority,
    this.isActive = true,
  });
}

/// A widget that displays a dashboard of active reminders
class ReminderDashboardWidget extends StatefulWidget {
  final List<ScheduledReminder> reminders;
  final Function(ScheduledReminder)? onReminderTap;
  final VoidCallback? onAddReminderTap;

  const ReminderDashboardWidget({
    Key? key,
    required this.reminders,
    this.onReminderTap,
    this.onAddReminderTap,
  }) : super(key: key);

  @override
  State<ReminderDashboardWidget> createState() =>
      _ReminderDashboardWidgetState();
}

class _ReminderDashboardWidgetState extends State<ReminderDashboardWidget> {
  Timer? _countdownTimer;
  Map<String, String> _countdowns = {};

  @override
  void initState() {
    super.initState();
    _updateCountdowns();
    _startCountdownTimer();
  }

  @override
  void didUpdateWidget(ReminderDashboardWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    _updateCountdowns();
  }

  @override
  void dispose() {
    _countdownTimer?.cancel();
    super.dispose();
  }

  void _startCountdownTimer() {
    // Update every minute
    _countdownTimer = Timer.periodic(const Duration(minutes: 1), (_) {
      if (mounted) {
        _updateCountdowns();
      }
    });
  }

  void _updateCountdowns() {
    final now = DateTime.now();
    final Map<String, String> newCountdowns = {};

    for (final reminder in widget.reminders) {
      if (reminder.isActive) {
        final difference = reminder.nextOccurrence.difference(now);

        if (difference.isNegative) {
          newCountdowns[reminder.id] = 'Overdue';
        } else {
          final days = difference.inDays;
          final hours = difference.inHours % 24;
          final minutes = difference.inMinutes % 60;

          if (days > 0) {
            newCountdowns[reminder.id] = '$days ${days == 1 ? 'day' : 'days'}';
          } else if (hours > 0) {
            newCountdowns[reminder.id] =
                '$hours ${hours == 1 ? 'hour' : 'hours'}';
          } else {
            newCountdowns[reminder.id] =
                '$minutes ${minutes == 1 ? 'minute' : 'minutes'}';
          }
        }
      }
    }

    if (mounted) {
      setState(() {
        _countdowns = newCountdowns;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // Filter active reminders and sort by next occurrence
    final activeReminders = widget.reminders
        .where((reminder) => reminder.isActive)
        .toList()
      ..sort((a, b) => a.nextOccurrence.compareTo(b.nextOccurrence));

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Active Reminders',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              if (widget.onAddReminderTap != null)
                IconButton(
                  icon: const Icon(Icons.add_circle_outline),
                  onPressed: widget.onAddReminderTap,
                  tooltip: 'Add Reminder',
                ),
            ],
          ),
        ),
        if (activeReminders.isEmpty)
          _buildEmptyState()
        else
          _buildReminderGrid(context, activeReminders),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.notifications_off_outlined,
              size: 48,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              'No active reminders',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Tap the + button to set up a reminder',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade600,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReminderGrid(
      BuildContext context, List<ScheduledReminder> reminders) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 1.0,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: reminders.length,
      itemBuilder: (context, index) {
        final reminder = reminders[index];
        return _buildReminderCard(context, reminder);
      },
    );
  }

  Widget _buildReminderCard(BuildContext context, ScheduledReminder reminder) {
    final Color priorityColor = _getPriorityColor(reminder.priority);
    final IconData frequencyIcon = _getFrequencyIcon(reminder.frequency);
    final String countdown = _countdowns[reminder.id] ?? 'Loading...';

    return InkWell(
      onTap: widget.onReminderTap != null
          ? () => widget.onReminderTap!(reminder)
          : null,
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: BorderSide(
            color: priorityColor.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Container(
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color: priorityColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.notifications_active,
                      color: priorityColor,
                      size: 20,
                    ),
                  ),
                  _buildFrequencyBadge(reminder.frequency, frequencyIcon),
                ],
              ),
              const Spacer(),
              Text(
                reminder.title,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 4),
              Text(
                reminder.description,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade700,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const Spacer(),
              _buildCountdownIndicator(countdown, reminder.nextOccurrence),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFrequencyBadge(String frequency, IconData icon) {
    String label;
    switch (frequency.toLowerCase()) {
      case 'daily':
        label = 'Daily';
        break;
      case 'weekly':
        label = 'Weekly';
        break;
      case 'monthly':
        label = 'Monthly';
        break;
      default:
        label = 'Custom';
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 12,
            color: Colors.blue.shade700,
          ),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.bold,
              color: Colors.blue.shade700,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCountdownIndicator(String countdown, DateTime nextOccurrence) {
    final now = DateTime.now();
    final difference = nextOccurrence.difference(now);

    Color indicatorColor;
    if (difference.isNegative) {
      indicatorColor = Colors.red;
    } else if (difference.inHours < 24) {
      indicatorColor = Colors.orange;
    } else {
      indicatorColor = Colors.green;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.timer,
              size: 14,
              color: indicatorColor,
            ),
            const SizedBox(width: 4),
            Text(
              countdown,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: indicatorColor,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Text(
          DateFormat('E, MMM d – h:mm a').format(nextOccurrence),
          style: TextStyle(
            fontSize: 10,
            color: Colors.grey.shade600,
          ),
        ),
      ],
    );
  }

  Color _getPriorityColor(NotificationPriority priority) {
    switch (priority) {
      case NotificationPriority.info:
        return Colors.blue;
      case NotificationPriority.warning:
        return Colors.orange;
      case NotificationPriority.alert:
        return Colors.red;
    }
  }

  IconData _getFrequencyIcon(String frequency) {
    switch (frequency.toLowerCase()) {
      case 'daily':
        return Icons.repeat;
      case 'weekly':
        return Icons.view_week;
      case 'monthly':
        return Icons.calendar_month;
      default:
        return Icons.calendar_today;
    }
  }
}
