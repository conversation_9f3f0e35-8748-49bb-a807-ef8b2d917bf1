// File: test/core/utils/average_manager_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:lekky/core/models/meter_entry.dart';
import 'package:lekky/core/utils/average_manager.dart';

void main() {
  group('AverageManager', () {
    late AverageManager averageManager;
    
    setUp(() {
      averageManager = AverageManager();
    });
    
    test('_ensureSorted should sort entries by timestamp', () {
      // Create entries with unsorted timestamps
      final entries = [
        MeterEntry(
          id: 1,
          reading: 10.0,
          amountToppedUp: 0.0,
          timestamp: DateTime(2023, 1, 3),
          shortAverageAfterTopUp: null,
          totalAverageUpToThisPoint: null,
        ),
        MeterEntry(
          id: 2,
          reading: 0.0,
          amountToppedUp: 20.0,
          timestamp: DateTime(2023, 1, 1),
          shortAverageAfterTopUp: null,
          totalAverageUpToThisPoint: null,
        ),
        MeterEntry(
          id: 3,
          reading: 5.0,
          amountToppedUp: 0.0,
          timestamp: DateTime(2023, 1, 2),
          shortAverageAfterTopUp: null,
          totalAverageUpToThisPoint: null,
        ),
      ];
      
      // Use reflection to access the private method
      final sortedEntries = averageManager.calculateAndUpdateAverages(entries);
      
      // Verify that entries are sorted by timestamp
      expect(sortedEntries[0].id, equals(2)); // 2023-01-01
      expect(sortedEntries[1].id, equals(3)); // 2023-01-02
      expect(sortedEntries[2].id, equals(1)); // 2023-01-03
    });
    
    test('calculateAndUpdateAverages should sort entries before calculating', () {
      // Create entries with unsorted timestamps
      final entries = [
        MeterEntry(
          id: 1,
          reading: 10.0,
          amountToppedUp: 0.0,
          timestamp: DateTime(2023, 1, 3),
          shortAverageAfterTopUp: null,
          totalAverageUpToThisPoint: null,
        ),
        MeterEntry(
          id: 2,
          reading: 0.0,
          amountToppedUp: 20.0,
          timestamp: DateTime(2023, 1, 1),
          shortAverageAfterTopUp: null,
          totalAverageUpToThisPoint: null,
        ),
        MeterEntry(
          id: 3,
          reading: 5.0,
          amountToppedUp: 0.0,
          timestamp: DateTime(2023, 1, 2),
          shortAverageAfterTopUp: null,
          totalAverageUpToThisPoint: null,
        ),
      ];
      
      // Calculate averages
      final updatedEntries = averageManager.calculateAndUpdateAverages(entries);
      
      // Verify that entries are sorted by timestamp
      expect(updatedEntries[0].id, equals(2)); // 2023-01-01
      expect(updatedEntries[1].id, equals(3)); // 2023-01-02
      expect(updatedEntries[2].id, equals(1)); // 2023-01-03
    });
    
    test('validateChronologicalOrder should sort entries', () {
      // Create entries with unsorted timestamps
      final entries = [
        MeterEntry(
          id: 1,
          reading: 10.0,
          amountToppedUp: 0.0,
          timestamp: DateTime(2023, 1, 3),
          shortAverageAfterTopUp: null,
          totalAverageUpToThisPoint: null,
        ),
        MeterEntry(
          id: 2,
          reading: 0.0,
          amountToppedUp: 20.0,
          timestamp: DateTime(2023, 1, 1),
          shortAverageAfterTopUp: null,
          totalAverageUpToThisPoint: null,
        ),
        MeterEntry(
          id: 3,
          reading: 5.0,
          amountToppedUp: 0.0,
          timestamp: DateTime(2023, 1, 2),
          shortAverageAfterTopUp: null,
          totalAverageUpToThisPoint: null,
        ),
      ];
      
      // Validate chronological order
      final validatedEntries = averageManager.validateChronologicalOrder(entries);
      
      // Verify that entries are sorted by timestamp
      expect(validatedEntries[0].id, equals(2)); // 2023-01-01
      expect(validatedEntries[1].id, equals(3)); // 2023-01-02
      expect(validatedEntries[2].id, equals(1)); // 2023-01-03
    });
  });
}
