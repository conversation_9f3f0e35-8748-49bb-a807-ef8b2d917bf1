// File: lib/core/shared_modules/currency_selector.dart
import 'package:flutter/material.dart';
import '../theme/app_colors.dart';
import 'base_settings_widget.dart';
import 'settings_model.dart';

class CurrencySelector extends BaseSettingsWidget {
  final String currentValue;
  final Function(String) onChanged;
  final String? errorText;

  const CurrencySelector({
    Key? key,
    required this.currentValue,
    required this.onChanged,
    this.errorText,
    SettingsDisplayMode displayMode = SettingsDisplayMode.compact,
    bool showHelperText = false,
    bool showTitle = true,
  }) : super(
            key: key,
            displayMode: displayMode,
            showHelperText: showHelperText,
            showTitle: showTitle);

  // Define currency options
  static final List<Map<String, String>> currencies = [
    {'code': 'USD', 'symbol': '\$', 'name': 'United States Dollar'},
    {'code': 'EUR', 'symbol': '€', 'name': 'Euro'},
    {'code': 'JPY', 'symbol': '¥', 'name': 'Japanese Yen'},
    {'code': 'GBP', 'symbol': '£', 'name': 'British Pound'},
    {'code': 'CNY', 'symbol': 'CN¥', 'name': 'Chinese Yuan'},
    {'code': 'AUD', 'symbol': 'A\$', 'name': 'Australian Dollar'},
    {'code': 'CAD', 'symbol': 'C\$', 'name': 'Canadian Dollar'},
    {'code': 'CHF', 'symbol': 'CHF', 'name': 'Swiss Franc'},
    {'code': 'INR', 'symbol': '₹', 'name': 'Indian Rupee'},
  ];

  @override
  Widget buildExpandedView(BuildContext context) {
    final primaryColor = Theme.of(context).colorScheme.primary;
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (showTitle) buildSectionTitle(context, 'Currency'),

        if (showHelperText)
          buildHelperText(context,
              'Select the currency symbol to use for your meter readings and costs.'),

        const SizedBox(height: 16),

        // Currency grid
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            ...currencies.map((currency) {
              return ChoiceChip(
                label: Text('${currency['symbol']} (${currency['code']})'),
                selected: currentValue == currency['symbol'],
                selectedColor: primaryColor.withOpacity(0.2),
                labelStyle: TextStyle(
                  color: currentValue == currency['symbol']
                      ? primaryColor
                      : isDarkMode
                          ? AppColors
                              .onBackgroundDark // Use semantic color for dark mode
                          : AppColors.onBackground,
                  fontWeight: currentValue == currency['symbol']
                      ? FontWeight.bold
                      : FontWeight.normal,
                ),
                onSelected: (selected) {
                  if (selected) {
                    onChanged(currency['symbol']!);
                  }
                },
              );
            }),
          ],
        ),

        buildErrorText(context, errorText),
      ],
    );
  }

  @override
  Widget buildCompactView(BuildContext context) {
    final primaryColor = Theme.of(context).colorScheme.primary;
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (showTitle) buildLabel(context, 'Currency'),

        if (showTitle)
          Text(
            'Current: $currentValue',
            style: const TextStyle(
              fontSize: 12,
              color: Colors.grey,
            ),
          ),

        if (showHelperText)
          buildHelperText(context, 'Select your preferred currency symbol'),

        const SizedBox(height: 8),

        // Currency grid in a more compact form
        Wrap(
          spacing: 6,
          runSpacing: 6,
          children: [
            ...currencies.map((currency) {
              return ChoiceChip(
                label: Text(currency['symbol']!),
                selected: currentValue == currency['symbol'],
                selectedColor: primaryColor.withOpacity(0.2),
                labelStyle: TextStyle(
                  color: currentValue == currency['symbol']
                      ? primaryColor
                      : isDarkMode
                          ? AppColors
                              .onBackgroundDark // Use semantic color for dark mode
                          : AppColors.onBackground,
                  fontWeight: currentValue == currency['symbol']
                      ? FontWeight.bold
                      : FontWeight.normal,
                ),
                onSelected: (selected) {
                  if (selected) {
                    onChanged(currency['symbol']!);
                  }
                },
              );
            }),
          ],
        ),

        buildErrorText(context, errorText),
      ],
    );
  }
}
