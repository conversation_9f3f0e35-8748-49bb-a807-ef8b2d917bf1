# Invalid History Entry Logic

## Overview

The Lekky app tracks prepaid electricity meter readings and top-ups, requiring a consistent and logical sequence of entries to accurately calculate usage and predict when the next top-up will be needed. An invalid history entry is one that violates the expected behavior of a prepaid electricity meter or creates logical inconsistencies in the usage data.

In a prepaid electricity system:
- Meter readings should generally decrease over time as electricity is consumed
- Readings can only increase when a top-up is added
- The amount of increase cannot exceed the total top-up amount
- All entries must maintain chronological integrity

This document outlines how the Lekky app identifies, prevents, and handles invalid entries to maintain data accuracy.

## Validation Rules

### Basic Validation Rules

1. **Non-negative Values**
   - Meter readings must be greater than 0
   - Top-up amounts must be greater than 0

2. **Chronological Integrity**
   - Entries are sorted by timestamp
   - Each entry must have a unique timestamp
   - Historical entries (backdated) must fit logically within the existing sequence

3. **Meter Reading Sequence Logic**
   - Meter readings must decrease over time (representing consumption)
   - Readings can only increase if there are top-ups between readings
   - The increase cannot exceed the total amount topped up

### Advanced Validation Rules

4. **Forward-Looking Validation**
   - A new reading cannot be lower than a future reading minus any top-ups between them
   - This prevents creating impossible consumption patterns

5. **Backward-Looking Validation**
   - A new reading cannot be higher than the previous reading plus any top-ups
   - This ensures consumption cannot be negative

6. **Top-Up Validation**
   - Top-ups are always considered valid (as long as the amount is positive)
   - This simplifies the user experience while maintaining logical integrity

## Detection Methods

### New Entry Insertion

When a user adds a new meter reading:

1. The app first performs basic validation (non-negative value)
2. If there are existing entries but no top-ups, the app suggests adding top-ups first
3. The `DBHelper.validateMeterReading()` method checks:
   - If it's the first reading (always valid if positive)
   - If there are readings before/after the selected date
   - If the reading fits logically between existing readings
   - If the reading respects top-up amounts

```dart
// Simplified validation logic
if (reading > previousReading && noTopUpsBetween) {
  return {
    'isValid': false,
    'errorMessage': 'Meter reading should decrease over time unless you add top-ups'
  };
}
```

### Entry Edits

When editing an existing entry through the History Entry Edit Dialog:

1. The entry being edited is temporarily removed from the validation set
2. The updated entry is validated against the remaining entries
3. The `validateMeterSequence()` function checks if the edit would create inconsistencies
4. The dialog shows validation errors in real-time as the user types

### Data Import/Bulk Operations

When importing data or performing bulk operations:

1. Each entry is validated individually using the same rules
2. The `validateMeterHistory()` function validates the entire history
3. Invalid entries are flagged but not automatically corrected
4. The user is shown which entries are problematic

## User Notification

The app employs several methods to alert users about invalid entries:

### During Entry Creation/Editing

1. **Real-time Validation**
   - Input fields show error messages as the user types
   - The "Confirm" or "Save" button is disabled when input is invalid

2. **Error Dialogs**
   - When validation fails, a SnackBar appears with a specific error message
   - Messages are descriptive, explaining why the entry is invalid

3. **Suggestion Dialogs**
   - If a user tries to add a meter reading without any top-ups, a dialog suggests adding top-ups first
   - This proactive approach prevents common user errors

### In the History View

1. **Visual Indicators**
   - Invalid entries are highlighted with a light red background
   - This makes it easy to spot problematic entries in the history list

2. **Error Messages**
   - Tapping on an invalid entry shows the specific validation error
   - The error message explains why the entry is considered invalid

3. **Testing Mode**
   - A hidden "Add Invalid Entry" button (for testing) can create invalid entries
   - This helps developers test the validation and display logic

## Developer Notes

### Code Organization

The validation logic is primarily implemented in three locations:

1. **`utils/validation_utils.dart`**
   - Contains `validateMeterSequence()` and `validateMeterHistory()`
   - Used for validating existing entries and edits

2. **`db_helper.dart`**
   - Contains `validateMeterReading()`
   - Used when adding new readings

3. **`utils/input_validator.dart`**
   - Contains basic input validation
   - Validates user input before deeper validation

### Integration with State Management

- The app uses a simple Provider pattern for state management
- Validation results are stored in a map keyed by entry ID
- The UI reactively updates based on validation state
- This approach allows for efficient re-validation when entries change

### Avoiding False Positives

Several techniques are used to avoid false positives:

1. **Context-Aware Validation**
   - Validation considers the entire history, not just adjacent entries
   - Top-ups between readings are properly accounted for

2. **Edit Mode Awareness**
   - When editing, the original entry is excluded from validation
   - This prevents self-comparison issues

3. **Timestamp Handling**
   - Entries with identical timestamps are handled properly
   - This prevents edge cases during bulk imports or edits

## Planned Enhancements

Future improvements to the validation system could include:

1. **Auto-correction Suggestions**
   - Suggest valid values when an invalid entry is detected
   - Offer to automatically fix common issues

2. **Batch Validation**
   - Validate multiple entries at once for better performance
   - Show a summary of all issues found

3. **Enhanced Visualization**
   - Graph view showing where invalid entries break the consumption pattern
   - Visual indicators of expected vs. actual readings

4. **Anomaly Detection**
   - Detect unusual but technically valid patterns
   - Alert users to potential meter issues or data entry errors

5. **Validation History**
   - Track changes to invalid entries over time
   - Allow reverting to previous valid states
