// File: lib/core/platform/timezone/timezone_adapter_factory.dart

import 'dart:io';
import 'package:flutter/foundation.dart';

import 'timezone_adapter.dart';
import 'android_timezone_adapter.dart';
import 'ios_timezone_adapter.dart';
import 'default_timezone_adapter.dart';

/// Factory for creating platform-specific timezone adapters
class TimezoneAdapterFactory {
  /// Create a timezone adapter for the current platform
  static TimezoneAdapter create() {
    if (kIsWeb) {
      // Web implementation (using default for now)
      return DefaultTimezoneAdapter();
    } else if (Platform.isAndroid) {
      // Android implementation
      return AndroidTimezoneAdapter();
    } else if (Platform.isIOS) {
      // iOS implementation
      return IOSTimezoneAdapter();
    } else {
      // Default implementation for other platforms
      return DefaultTimezoneAdapter();
    }
  }

  /// Create a timezone adapter for the current platform (alias for create())
  static TimezoneAdapter createAdapter() {
    return create();
  }
}
