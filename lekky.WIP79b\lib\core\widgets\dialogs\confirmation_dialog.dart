// File: lib/core/widgets/dialogs/confirmation_dialog.dart
import 'package:flutter/material.dart';
import '../../theme/app_colors.dart';
import '../../theme/app_text_styles.dart';

/// A specialized dialog for confirming user actions, especially irreversible ones.
///
/// This dialog presents a clear title, concise message, and two buttons:
/// - Confirm (primary action)
/// - Cancel (secondary action)
class ConfirmationDialog {
  /// Shows a confirmation dialog with the specified title and message.
  ///
  /// Parameters:
  /// - [context]: The build context.
  /// - [title]: The title of the dialog, should clearly indicate the action.
  /// - [message]: A concise message explaining the consequences of the action.
  /// - [confirmText]: The text for the confirm button (default: "Confirm").
  /// - [cancelText]: The text for the cancel button (default: "Cancel").
  /// - [isDestructive]: Whether the action is destructive (default: false).
  ///   If true, the confirm button will be styled with error colors.
  /// - [icon]: An optional icon to display in the dialog.
  /// - [barrierDismissible]: Whether the dialog can be dismissed by tapping outside (default: true).
  static Future<bool?> show({
    required BuildContext context,
    required String title,
    required String message,
    String confirmText = 'Confirm',
    String cancelText = 'Cancel',
    bool isDestructive = false,
    IconData? icon,
    bool barrierDismissible = true,
  }) async {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    // Create the content widget with an optional icon
    Widget content = Text(
      message,
      style: AppTextStyles.bodyMedium.copyWith(
        color: isDarkMode
            ? AppColors.onSurfaceDark.withOpacity(0.8)
            : AppColors.onSurface.withOpacity(0.8),
      ),
      textAlign: TextAlign.center,
    );

    if (icon != null) {
      content = Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 48,
            color: isDestructive
                ? (isDarkMode ? AppColors.errorDark : AppColors.error)
                : (isDarkMode ? AppColors.primaryDark : AppColors.primary),
          ),
          const SizedBox(height: 16),
          content,
        ],
      );
    }

    return showDialog<bool>(
      context: context,
      barrierDismissible: barrierDismissible,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          insetPadding:
              const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Header
              Padding(
                padding: const EdgeInsets.fromLTRB(24, 16, 24, 0),
                child: Row(
                  children: [
                    Icon(
                      icon ??
                          (isDestructive
                              ? Icons.delete_forever
                              : Icons.help_outline),
                      color: isDestructive
                          ? (isDarkMode ? AppColors.errorDark : AppColors.error)
                          : (isDarkMode
                              ? AppColors.primaryDark
                              : AppColors.primary),
                    ),
                    const SizedBox(width: 10),
                    Text(
                      title,
                      style: AppTextStyles.titleMedium.copyWith(
                        fontWeight: FontWeight.bold,
                        color: isDestructive
                            ? (isDarkMode
                                ? AppColors.errorDark
                                : AppColors.error)
                            : (isDarkMode
                                ? AppColors.primaryDark
                                : AppColors.primary),
                      ),
                    ),
                    const Spacer(),
                    IconButton(
                      icon: const Icon(Icons.close, color: AppColors.onSurface),
                      onPressed: () => Navigator.of(context).pop(false),
                      tooltip: 'Close',
                      constraints: const BoxConstraints(),
                      padding: EdgeInsets.zero,
                      iconSize: 20,
                    ),
                  ],
                ),
              ),

              // Content
              Padding(
                padding: const EdgeInsets.fromLTRB(24, 16, 24, 24),
                child: Text(
                  message,
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: isDarkMode
                        ? AppColors.onSurfaceDark.withOpacity(0.8)
                        : AppColors.onSurface.withOpacity(0.8),
                  ),
                ),
              ),

              // Actions
              Padding(
                padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    // Delete/Confirm button (positioned first)
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () => Navigator.of(context).pop(true),
                        style: ElevatedButton.styleFrom(
                          backgroundColor:
                              isDestructive ? Colors.red : AppColors.costTab,
                          foregroundColor: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          padding: const EdgeInsets.symmetric(vertical: 12),
                        ),
                        child: Text(confirmText),
                      ),
                    ),
                    const SizedBox(width: 8),
                    // Cancel button with blue outline (positioned second)
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () => Navigator.of(context).pop(false),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: AppColors.primary,
                          side: const BorderSide(color: AppColors.primary),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          padding: const EdgeInsets.symmetric(vertical: 12),
                        ),
                        child: Text(cancelText),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
