// File: lib/features/setup/presentation/widgets/threshold_input.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/extensions/context_extensions.dart';
import '../../../../core/utils/input_validator.dart';
import '../../../../core/widgets/app_card.dart';
import '../../../../core/widgets/app_text_field.dart';

/// A widget for inputting the alert threshold
class ThresholdInput extends StatefulWidget {
  final double threshold;
  final String meterUnit;
  final Function(double) onThresholdChanged;

  const ThresholdInput({
    Key? key,
    required this.threshold,
    required this.meterUnit,
    required this.onThresholdChanged,
  }) : super(key: key);

  @override
  State<ThresholdInput> createState() => _ThresholdInputState();
}

class _ThresholdInputState extends State<ThresholdInput> {
  late TextEditingController _controller;
  String? _errorText;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.threshold.toString());
  }

  @override
  void didUpdateWidget(ThresholdInput oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.threshold != widget.threshold) {
      _controller.text = widget.threshold.toString();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AppCard(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Alert Threshold',
            style: AppTextStyles.titleMedium.copyWith(
              color: context.textColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'You will be notified when your meter balance falls below this amount',
            style: AppTextStyles.bodyMedium.copyWith(
              color: context.textColor,
            ),
          ),
          const SizedBox(height: 16),
          AppTextField(
            controller: _controller,
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
            inputFormatters: [
              FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*$')),
            ],
            prefixText: widget.meterUnit,
            errorText: _errorText,
            onChanged: _validateAndUpdate,
          ),
          const SizedBox(height: 8),
          Text(
            'Recommended: ${widget.meterUnit}5.00',
            style: AppTextStyles.bodySmall.copyWith(
              fontStyle: FontStyle.italic,
              color: context.secondaryTextColor,
            ),
          ),
        ],
      ),
    );
  }

  void _validateAndUpdate(String value) {
    final validation = InputValidator.validateThreshold(value);

    setState(() {
      _errorText = validation['isValid'] ? null : validation['errorMessage'];
    });

    if (validation['isValid']) {
      final threshold = double.tryParse(value) ?? widget.threshold;
      widget.onThresholdChanged(threshold);
    }
  }
}
