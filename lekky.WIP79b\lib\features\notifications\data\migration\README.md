# Storage Solution Migration

This directory contains the implementation for migrating notification data from SharedPreferences to SQLite.

## Overview

The storage solution migration is part of Phase 4 of the RooLekky meter reading reminder enhancement plan. It replaces the SharedPreferences-based storage with a more robust SQLite database solution, which provides better performance, reliability, and data integrity for notification and reminder data.

## Components

The migration system consists of the following components:

### 1. Database Infrastructure

- **NotificationDBHelper**: A singleton class that manages the SQLite database connection, creates tables, and provides methods for CRUD operations.
- **SQLNotificationDataSource**: Implements the `LocalStorageDataSource` interface using SQLite storage.
- **LocalNotificationDataSource**: The legacy implementation using SharedPreferences (now deprecated).

### 2. Migration Framework

- **StorageFactory**: Creates the appropriate storage data source based on migration status.
- **StorageMigrationService**: Manages the migration process, including progress tracking and status updates.
- **AutoMigrationManager**: Handles automatic migration during app startup.

### 3. UI Components

- **StorageMigrationDialog**: A dialog that shows migration progress to the user.
- **StorageSettingsWidget**: A settings widget that displays the current storage type and allows the user to trigger migration manually.

## Database Schema

The SQLite database consists of the following tables:

1. **notifications**: Stores notification data
   - id (TEXT PRIMARY KEY)
   - title (TEXT)
   - message (TEXT)
   - timestamp (TEXT)
   - priority (INTEGER)
   - is_read (INTEGER)
   - action_type (TEXT)

2. **notification_settings**: Stores notification settings
   - key (TEXT PRIMARY KEY)
   - value (TEXT)

3. **reminder_settings**: Stores reminder settings with timestamps
   - key (TEXT PRIMARY KEY)
   - value (TEXT)
   - timestamp (TEXT)

4. **meter_reminder_history**: Tracks history of meter reading reminders
   - id (INTEGER PRIMARY KEY AUTOINCREMENT)
   - scheduled_date (TEXT)
   - actual_date (TEXT)
   - status (TEXT)
   - timezone (TEXT)
   - timezone_offset (INTEGER)
   - is_dst (INTEGER)

## Migration Process

The migration process follows these steps:

1. Check if migration is needed (not already completed)
2. Mark migration as attempted
3. Transfer notifications from SharedPreferences to SQLite
4. Transfer notification settings from SharedPreferences to SQLite
5. Transfer reminder settings from SharedPreferences to SQLite
6. Transfer timezone-aware settings from SharedPreferences to SQLite
7. Mark migration as completed

The migration can be triggered in three ways:

1. **Automatic**: During app startup via the `AutoMigrationManager`
2. **Manual**: By the user from the settings screen via the `StorageSettingsWidget`
3. **Programmatic**: By calling the `migrateToSQLite()` method on the `NotificationRepository`

## Performance Optimizations

The SQLite implementation includes several performance optimizations:

1. **Caching**: Frequently accessed settings are cached in memory
2. **Batch Operations**: Bulk operations use transactions for better performance
3. **Indexing**: Tables have appropriate indexes for efficient queries
4. **Cleanup**: Old reminder history entries are automatically cleaned up

## Data Integrity

The SQLite implementation includes several data integrity features:

1. **Transactions**: Critical operations use transactions to ensure atomicity
2. **Validation**: Data is validated before storage
3. **Error Handling**: Comprehensive error handling for database operations
4. **Backup Support**: The SQLite database can be backed up and restored

## Usage

### Initialization

The migration system is initialized as part of the notification system:

```dart
// Initialize notification service locator
await initNotificationServiceLocator();

// Initialize auto migration manager
await AutoMigrationManager.initialize();
```

### Manual Migration

To trigger migration manually:

```dart
final migrationService = notificationServiceLocator<StorageMigrationService>();
final result = await showStorageMigrationDialog(context, migrationService);
```

### Checking Migration Status

To check if migration has been completed:

```dart
final migrationService = notificationServiceLocator<StorageMigrationService>();
final isMigrated = await migrationService.getCurrentStorageType() == 'SQLite';
```

## Testing

The migration system includes several tests:

1. **Unit Tests**: Test individual components of the migration system
2. **Integration Tests**: Test the migration process end-to-end
3. **Performance Tests**: Test the performance of the SQLite implementation compared to SharedPreferences

## Future Improvements

Potential future improvements include:

1. **Encryption**: Add encryption for sensitive data
2. **Cloud Backup**: Add support for backing up to cloud storage
3. **Multi-Device Sync**: Add support for syncing data across devices
4. **Performance Tuning**: Further optimize database performance