// File: lib/core/widgets/app_dialog.dart
import 'package:flutter/material.dart';
import '../theme/app_colors.dart';
import '../theme/app_text_styles.dart';
import '../utils/responsive_layout.dart';
import '../utils/dialog_button_styles.dart';

/// A reusable dialog widget with consistent styling
class AppDialog extends StatelessWidget {
  final dynamic title; // Can be String or Widget
  final Widget? content;
  final String? message;
  final List<Widget>? actions;
  final bool barrierDismissible;
  final EdgeInsetsGeometry? contentPadding;
  final EdgeInsetsGeometry? titlePadding;
  final EdgeInsetsGeometry? actionsPadding;
  final Color? backgroundColor;
  final TextStyle? titleTextStyle;
  final TextStyle? messageTextStyle;
  final bool scrollable;
  final double? maxWidth;
  final double? maxHeight;
  final Color? confirmColor;
  final String? confirmText;
  final String? cancelText;

  const AppDialog({
    super.key,
    required this.title,
    this.content,
    this.message,
    this.actions,
    this.barrierDismissible = true,
    this.contentPadding,
    this.titlePadding,
    this.actionsPadding,
    this.backgroundColor,
    this.titleTextStyle,
    this.messageTextStyle,
    this.scrollable = false,
    this.maxWidth,
    this.maxHeight,
    this.confirmColor,
    this.confirmText,
    this.cancelText,
  }) : assert(content != null || message != null,
            'Either content or message must be provided');

  /// Shows the dialog
  static Future<T?> show<T>({
    required BuildContext context,
    required dynamic title, // Can be String or Widget
    Widget? content,
    String? message,
    List<Widget>? actions,
    bool barrierDismissible = true,
    EdgeInsetsGeometry? contentPadding,
    EdgeInsetsGeometry? titlePadding,
    EdgeInsetsGeometry? actionsPadding,
    Color? backgroundColor,
    TextStyle? titleTextStyle,
    TextStyle? messageTextStyle,
    bool scrollable = false,
    double? maxWidth,
    double? maxHeight,
    Color? confirmColor,
    String? confirmText,
    String? cancelText,
  }) {
    // If both confirmText and cancelText are provided, create default actions
    final List<Widget> dialogActions = actions ?? [];

    if (actions == null && (confirmText != null || cancelText != null)) {
      if (cancelText != null) {
        dialogActions.add(
          DialogButtonStyles.createCancelButton(
            context: context,
            onPressed: () => Navigator.of(context).pop(false),
            text: cancelText,
          ),
        );
      }

      if (confirmText != null) {
        dialogActions.add(
          DialogButtonStyles.createSaveButton(
            context: context,
            onPressed: () => Navigator.of(context).pop(true),
            text: confirmText,
          ),
        );
      }
    }

    return showDialog<T>(
      context: context,
      barrierDismissible: barrierDismissible,
      builder: (context) => AppDialog(
        title: title,
        content: content,
        message: message,
        actions: dialogActions.isEmpty ? null : dialogActions,
        barrierDismissible: barrierDismissible,
        contentPadding: contentPadding,
        titlePadding: titlePadding,
        actionsPadding: actionsPadding,
        backgroundColor: backgroundColor,
        titleTextStyle: titleTextStyle,
        messageTextStyle: messageTextStyle,
        scrollable: scrollable,
        maxWidth: maxWidth,
        maxHeight: maxHeight,
        confirmColor: confirmColor,
        confirmText: confirmText,
        cancelText: cancelText,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final defaultBackgroundColor =
        isDarkMode ? AppColors.surfaceDark : AppColors.surface;

    final responsiveTitleTextStyle = titleTextStyle ??
        AppTextStyles.titleLarge.copyWith(
          color: isDarkMode ? AppColors.onSurfaceDark : AppColors.onSurface,
        );

    final responsiveMessageTextStyle = messageTextStyle ??
        AppTextStyles.bodyMedium.copyWith(
          color: isDarkMode
              ? AppColors.onSurfaceDark.withOpacity(0.8)
              : AppColors.onSurface.withOpacity(0.8),
        );

    final responsiveContentPadding = contentPadding ??
        const EdgeInsets.symmetric(horizontal: 24, vertical: 16);
    final responsiveTitlePadding =
        titlePadding ?? const EdgeInsets.fromLTRB(24, 24, 24, 0);
    final responsiveActionsPadding =
        actionsPadding ?? const EdgeInsets.fromLTRB(16, 8, 16, 16);

    final double responsiveMaxWidth = maxWidth ??
        ResponsiveLayout.getValueForScreenType(
          context: context,
          mobile: 320.0,
          tablet: 400.0,
          desktop: 480.0,
          largeDesktop: 560.0,
        );

    final double responsiveMaxHeight =
        maxHeight ?? MediaQuery.of(context).size.height * 0.8;

    // Create content widget from message if content is null
    final Widget contentWidget = content ??
        Text(
          message!,
          style: responsiveMessageTextStyle,
          textAlign: TextAlign.center,
        );

    return Dialog(
      backgroundColor: backgroundColor ?? defaultBackgroundColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: ConstrainedBox(
        constraints: BoxConstraints(
          maxWidth: responsiveMaxWidth,
          maxHeight: responsiveMaxHeight,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Padding(
              padding: responsiveTitlePadding,
              child: title is String
                  ? Text(
                      title,
                      style: responsiveTitleTextStyle,
                      textAlign: TextAlign.center,
                    )
                  : title, // Use the Widget directly if it's not a String
            ),
            if (scrollable)
              Flexible(
                child: SingleChildScrollView(
                  padding: responsiveContentPadding,
                  child: contentWidget,
                ),
              )
            else
              Padding(
                padding: responsiveContentPadding,
                child: contentWidget,
              ),
            if (actions != null && actions!.isNotEmpty)
              Padding(
                padding: responsiveActionsPadding,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: actions!,
                ),
              ),
          ],
        ),
      ),
    );
  }
}
