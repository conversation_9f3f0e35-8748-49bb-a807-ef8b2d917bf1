// File: lib/core/constants/db_constants.dart
class DBConstants {
  // Database name and version
  static const String databaseName = 'lekky_meter.db';
  static const int databaseVersion = 1;

  // Table names
  static const String tableMeterEntries = 'meter_entries';
  static const String tableSettings = 'settings';
  static const String tableNotifications = 'notifications';

  // Column names for meter_entries table
  static const String columnId = 'id';
  static const String columnAmount = 'amount';
  static const String columnTimestamp = 'timestamp';
  static const String columnIsTopUp = 'is_top_up';
  static const String columnNotes = 'notes';

  // Column names for notifications table
  static const String columnNotificationId = 'notification_id';
  static const String columnTitle = 'title';
  static const String columnMessage = 'message';
  static const String columnNotificationTimestamp = 'timestamp';
  static const String columnIsRead = 'is_read';
  static const String columnIsUrgent = 'is_urgent';

  // SQL statements
  static const String createMeterEntriesTable = '''
    CREATE TABLE $tableMeterEntries (
      $columnId INTEGER PRIMARY KEY AUTOINCREMENT,
      $columnAmount REAL NOT NULL,
      $columnTimestamp TEXT NOT NULL,
      $columnIsTopUp INTEGER NOT NULL,
      $columnNotes TEXT
    )
  ''';

  static const String createNotificationsTable = '''
    CREATE TABLE $tableNotifications (
      $columnNotificationId INTEGER PRIMARY KEY AUTOINCREMENT,
      $columnTitle TEXT NOT NULL,
      $columnMessage TEXT NOT NULL,
      $columnNotificationTimestamp TEXT NOT NULL,
      $columnIsRead INTEGER NOT NULL,
      $columnIsUrgent INTEGER NOT NULL
    )
  ''';

  // Database optimization
  static const int optimizationThreshold = 100; // entries
  static const int vacuumIntervalDays = 30; // days
}
