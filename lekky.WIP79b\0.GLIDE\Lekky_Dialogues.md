# Lekky Dialogue Boxes

This document outlines the various dialog boxes used in the Lekky app, identifying the most suitable dialog box types for each use case. The goal is to enhance user experience while maintaining a consistent design approach that is fit for purpose.

## Core Principles

### Concise & Contextual
- Dialogs must be brief, clear, and directly tied to the user's current task
- Minimize confusion and abandonment by keeping content focused

### Accessibility & Intuition
- Support screen-readers, high contrast, and familiar layouts
- Reduce user frustration and errors through intuitive design

### Platform-Native Components
- Leverage iOS and Android built-in dialog patterns for consistency with user expectations
- Validate effectiveness through real-user testing

## Dialog Box Types and Use Cases

### 1. Confirmation Dialogs
**Purpose**: Confirm user actions, especially irreversible ones

**Best Suited For**:
- Deleting data (meter readings, top-ups, notifications)
- Resetting the app
- Clearing all data
- Emergency reset operations

**Implementation Guidelines**:
- Clear title indicating the action (e.g., "Delete Entry?")
- Concise message explaining consequences
- Two buttons: Confirm (primary action) and Cancel (secondary action)
- Use warning colors for destructive actions
- Keep text under 50 words

**Examples in Lekky**:
- Clear notifications confirmation
- Delete entry confirmation
- Reset app confirmation
- Emergency reset confirmation

### 2. Input Dialogs
**Purpose**: Collect specific data from users

**Best Suited For**:
- Adding meter readings
- Adding top-ups
- Setting alert thresholds
- Setting days in advance
- Currency selection

**Implementation Guidelines**:
- Clear title indicating the purpose
- Appropriate input fields with validation
- Helpful error messages
- Save and Cancel buttons
- Optional helper text for guidance

**Examples in Lekky**:
- Add Reading Dialog
- Add Top-Up Dialog
- Alert Threshold Dialog
- Days in Advance Dialog
- Meter Unit (Currency) Dialog

### 3. Information Dialogs
**Purpose**: Display important information to users

**Best Suited For**:
- Meter total details
- Average usage information
- Success confirmations
- Tips and help content

**Implementation Guidelines**:
- Descriptive title
- Concise information
- Single "OK" or "Close" button
- Use appropriate icons to enhance understanding
- Scrollable content for longer information (like tips)

**Examples in Lekky**:
- Meter Total Dialog
- Success confirmation after adding entries
- Tips & Tricks Dialog

### 4. Selection Dialogs
**Purpose**: Allow users to choose from multiple options

**Best Suited For**:
- Date format selection
- Date info selection
- Theme selection
- Currency selection

**Implementation Guidelines**:
- Clear title indicating what to select
- Visual distinction between options
- Radio buttons for single selection
- Checkboxes for multiple selection
- Save and Cancel buttons

**Examples in Lekky**:
- Date Format Dialog
- Date Info Dialog
- Theme Selection Dialog
- Currency Selection Dialog

### 5. Date Picker Dialogs
**Purpose**: Allow users to select dates

**Best Suited For**:
- Setting meter reading dates
- Setting top-up dates
- Selecting date ranges for cost calculations

**Implementation Guidelines**:
- Use platform-native date pickers
- Clear title indicating purpose
- Appropriate date constraints (e.g., can't select future dates for readings)
- Confirm and Cancel buttons
- Optional time selection when needed

**Examples in Lekky**:
- Date selection in Add Reading Dialog
- Date selection in Add Top-Up Dialog
- Date Range Selector in Cost Screen

### 6. Notification Dialogs
**Purpose**: Alert users about important events

**Best Suited For**:
- Low meter balance alerts
- Reminder notifications
- Error notifications

**Implementation Guidelines**:
- Clear title indicating the type of notification
- Concise message
- Appropriate icon and color based on severity
- Action button when applicable
- Dismiss button

**Examples in Lekky**:
- Low Balance Alert
- Reading Reminder Notification
- Error Notifications

### 7. Progress Dialogs
**Purpose**: Indicate ongoing operations

**Best Suited For**:
- Data import/export operations
- App reset process
- Initial setup

**Implementation Guidelines**:
- Simple title indicating the operation
- Progress indicator (determinate when possible)
- Cancel button when operation can be cancelled
- Auto-dismiss when operation completes

**Examples in Lekky**:
- Data Export Dialog
- Data Import Dialog

## Best Practices for Mobile App Dialog Boxes

### 1. Clear Purpose & Context
- Use only for critical, immediate decisions
- Give descriptive titles and concise messages explaining why

### 2. Concise Messaging
- Keep text under 50 words
- Use plain language (e.g., "Delete file?" instead of "Are you sure you want to permanently remove this item from your library?")
- Present one clear action when possible

### 3. Intuitive Layout
- Structure: Title (bold), short message, 1-2 action buttons
- Visual hierarchy: Primary action button stands out; secondary is neutral

### 4. Platform-Native Styling
- Follow platform conventions for button order and appearance
- iOS: Primary button on right, San Francisco font
- Android: Primary button on left, Roboto font

### 5. Accessibility
- Mark dialogs as alertdialog for screen readers
- Ensure contrast ratio ≥4.5:1 for text against background
- Honor system font-size settings
- Make touch targets ≥48×48 dp

### 6. Dismissal Options
- Allow tapping outside or a "Cancel" button for non-critical dialogs
- Require explicit choice for critical dialogs

### 7. Avoid Overuse
- Reserve for irreversible or important steps
- Use inline validation, banners, or toasts for routine feedback

### 8. Responsive Layout
- Dialog width ≤50% of screen with ≥16 px padding
- Test on small to large/foldable devices

### 9. User Testing & Analytics
- Track dismissal rates, CTA click-through, and error recovery
- Test wording and button placement

## Specific Dialog Implementations for Lekky

### Entry Edit Dialog
- **Purpose**: Add or edit meter readings and top-ups
- **Type**: Input Dialog
- **Key Features**:
  - Tabs for Meter Reading and Top-Up
  - Date picker with appropriate constraints
  - Numeric input with validation
  - Clear error messages
  - Save and Cancel buttons

### Meter Total Dialog
- **Purpose**: Display detailed meter information
- **Type**: Information Dialog
- **Key Features**:
  - Clear display of meter total
  - Separate display for top-up amounts
  - Average usage information
  - Close button

### Settings Dialogs
- **Purpose**: Configure app settings
- **Type**: Input/Selection Dialog
- **Key Features**:
  - Appropriate input type for each setting
  - Validation with helpful error messages
  - Default values and recommendations
  - Save and Cancel buttons

### Date Range Selector
- **Purpose**: Select date range for cost calculations
- **Type**: Date Picker Dialog
- **Key Features**:
  - From and To date fields
  - Appropriate date constraints
  - Clear error messages for invalid ranges
  - Apply and Cancel buttons

### Confirmation Dialogs
- **Purpose**: Confirm destructive actions
- **Type**: Confirmation Dialog
- **Key Features**:
  - Clear warning about consequences
  - Distinctive styling for destructive actions
  - Confirm and Cancel buttons
  - Appropriate icons

## Conclusion

Well-designed dialog boxes in the Lekky app should guide users through critical decisions without disruption. By following the principles outlined in this document—clear purpose, concise text, intuitive and accessible design, platform consistency, and rigorous user testing—dialog boxes will enhance rather than hinder the user experience.

The key is to use the right type of dialog for each purpose, maintain consistency in design while adapting to the specific needs of each use case, and always prioritize user experience by keeping dialogs simple, clear, and focused.
