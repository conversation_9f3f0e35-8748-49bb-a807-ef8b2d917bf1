// File: lib/features/notifications/presentation/screens/enhanced_reminder_settings_screen.dart

import 'package:flutter/material.dart';
import 'dart:async';
import '../../domain/models/notification_model.dart';
import '../../domain/models/reminder_time_model.dart';
import '../../domain/models/timezone_aware_reminder_time.dart';
import '../../domain/usecases/schedule_meter_reading_reminder.dart';
import '../widgets/reminder_confirmation_overlay.dart';
import '../widgets/reminder_timeline_view.dart';
import '../widgets/reminder_dashboard_widget.dart';
import '../widgets/notification_preview_widget.dart';
import '../widgets/reminder_management_widget.dart';
import '../widgets/reminder_error_state_widget.dart';
import '../widgets/permission_education_dialog.dart';
import '../widgets/permission_denied_dialog.dart';
import '../../../../core/platform/notification/notification_adapter.dart';
import '../../../../core/platform/permissions/permission_adapter.dart';
import '../../../../core/platform/timezone/timezone_adapter.dart';

/// A screen that showcases the enhanced UI feedback for reminder scheduling
class EnhancedReminderSettingsScreen extends StatefulWidget {
  final ScheduleMeterReadingReminder scheduleReminderUseCase;
  final NotificationAdapter notificationAdapter;
  final PermissionAdapter permissionAdapter;
  final TimezoneAdapter timezoneAdapter;

  const EnhancedReminderSettingsScreen({
    Key? key,
    required this.scheduleReminderUseCase,
    required this.notificationAdapter,
    required this.permissionAdapter,
    required this.timezoneAdapter,
  }) : super(key: key);

  @override
  State<EnhancedReminderSettingsScreen> createState() =>
      _EnhancedReminderSettingsScreenState();
}

class _EnhancedReminderSettingsScreenState
    extends State<EnhancedReminderSettingsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _isLoading = false;
  String _currentTimezone = '';
  ReminderTimeModel _selectedTime = const ReminderTimeModel(
    timeOfDay: TimeOfDay(hour: 19, minute: 0),
  );
  String _selectedFrequency = 'monthly';
  bool _hasPermission = false;
  List<ReminderEvent> _upcomingReminders = [];
  List<ScheduledReminder> _activeReminders = [];
  List<ManagedReminder> _managedReminders = [];
  List<ReminderTemplate> _templates = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _initialize();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _initialize() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Initialize timezone
      _currentTimezone = await widget.timezoneAdapter.getCurrentTimezone();

      // Check permissions
      final permissionStatus = await widget.permissionAdapter.checkPermission();
      _hasPermission = permissionStatus == PermissionStatus.granted;

      // Load sample data
      _loadSampleData();
    } catch (e) {
      // Handle initialization errors
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _loadSampleData() {
    // Sample upcoming reminders
    final now = DateTime.now();
    _upcomingReminders = [
      ReminderEvent(
        id: '1',
        title: 'Monthly Meter Reading',
        scheduledDate: now.add(const Duration(days: 3)),
        priority: NotificationPriority.info,
        status: 'scheduled',
      ),
      ReminderEvent(
        id: '2',
        title: 'Quarterly Usage Report',
        scheduledDate: now.add(const Duration(days: 15)),
        priority: NotificationPriority.warning,
        status: 'scheduled',
      ),
      ReminderEvent(
        id: '3',
        title: 'Low Balance Alert',
        scheduledDate: now.subtract(const Duration(days: 2)),
        priority: NotificationPriority.alert,
        status: 'triggered',
      ),
      ReminderEvent(
        id: '4',
        title: 'Annual Meter Check',
        scheduledDate: now.add(const Duration(days: 45)),
        priority: NotificationPriority.warning,
        status: 'scheduled',
      ),
    ];

    // Sample active reminders
    _activeReminders = [
      ScheduledReminder(
        id: '1',
        title: 'Monthly Meter Reading',
        description: 'Record your electricity meter reading',
        nextOccurrence: now.add(const Duration(days: 3)),
        frequency: 'monthly',
        frequencyDays: 30,
        priority: NotificationPriority.info,
      ),
      ScheduledReminder(
        id: '2',
        title: 'Weekly Balance Check',
        description: 'Check your remaining balance',
        nextOccurrence: now.add(const Duration(days: 1, hours: 5)),
        frequency: 'weekly',
        frequencyDays: 7,
        priority: NotificationPriority.warning,
      ),
    ];

    // Sample managed reminders
    _managedReminders = [
      ManagedReminder(
        id: '1',
        title: 'Monthly Meter Reading',
        message: 'Record your electricity meter reading',
        scheduledDate: now.add(const Duration(days: 3)),
        frequency: 'monthly',
        priority: NotificationPriority.info,
        category: 'Electricity',
        tag: 'Important',
      ),
      ManagedReminder(
        id: '2',
        title: 'Weekly Balance Check',
        message: 'Check your remaining balance',
        scheduledDate: now.add(const Duration(days: 1, hours: 5)),
        frequency: 'weekly',
        priority: NotificationPriority.warning,
        category: 'Balance',
      ),
      ManagedReminder(
        id: '3',
        title: 'Quarterly Usage Report',
        message: 'Review your quarterly usage statistics',
        scheduledDate: now.add(const Duration(days: 15)),
        frequency: 'custom',
        priority: NotificationPriority.info,
        category: 'Reports',
        tag: 'Analysis',
      ),
      ManagedReminder(
        id: '4',
        title: 'Annual Meter Check',
        message: 'Schedule a professional meter inspection',
        scheduledDate: now.add(const Duration(days: 45)),
        frequency: 'yearly',
        priority: NotificationPriority.warning,
        category: 'Maintenance',
        tag: 'Required',
        isActive: false,
      ),
    ];

    // Sample templates
    _templates = [
      ReminderTemplate(
        id: '1',
        name: 'Monthly Reading',
        title: 'Monthly Meter Reading',
        message: 'Time to record your meter reading',
        frequency: 'monthly',
        timeOfDay: const TimeOfDay(hour: 10, minute: 0),
        priority: NotificationPriority.info,
        icon: Icons.calendar_today,
      ),
      ReminderTemplate(
        id: '2',
        name: 'Weekly Balance',
        title: 'Check Balance',
        message: 'Time to check your remaining balance',
        frequency: 'weekly',
        timeOfDay: const TimeOfDay(hour: 18, minute: 0),
        priority: NotificationPriority.warning,
        icon: Icons.account_balance_wallet,
      ),
      ReminderTemplate(
        id: '3',
        name: 'Daily Usage',
        title: 'Daily Usage Check',
        message: 'Review your daily energy usage',
        frequency: 'daily',
        timeOfDay: const TimeOfDay(hour: 20, minute: 0),
        priority: NotificationPriority.info,
        icon: Icons.show_chart,
      ),
    ];
  }

  Future<void> _scheduleReminder() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Check permission first
      if (!_hasPermission) {
        final granted = await _requestPermission();
        if (!granted) {
          _showPermissionError();
          setState(() {
            _isLoading = false;
          });
          return;
        }
      }

      // Simulate scheduling success
      await Future.delayed(const Duration(seconds: 1));

      // Show confirmation
      if (mounted) {
        _showSchedulingSuccess();

        // Update the reminders list with the new reminder
        final now = DateTime.now();
        final newReminder = ReminderEvent(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          title: 'New Meter Reading Reminder',
          scheduledDate: now.add(const Duration(days: 30)),
          priority: NotificationPriority.info,
          status: 'scheduled',
        );

        setState(() {
          _upcomingReminders = [..._upcomingReminders, newReminder];
        });
      }
    } catch (e) {
      // Show error
      _showSchedulingError();
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<bool> _requestPermission() async {
    // Show educational UI first
    final proceedWithPermission = await showPermissionEducationDialog(context);
    if (!proceedWithPermission) return false;

    // Request actual permission
    final granted = await widget.permissionAdapter.requestPermission();

    if (granted) {
      setState(() {
        _hasPermission = true;
      });
      return true;
    }

    // Handle permission denial
    final isPermanentlyDenied =
        await widget.permissionAdapter.isPermanentlyDenied();
    final alternative = await showPermissionDeniedDialog(
      context,
      isPermanentlyDenied: isPermanentlyDenied,
    );

    if (alternative == PermissionAlternative.openSettings) {
      await widget.permissionAdapter.openAppSettings();
    }

    return false;
  }

  void _showSchedulingSuccess() {
    showReminderConfirmation(
      context,
      message:
          'Your reminder has been scheduled for ${_selectedTime.format(context)}',
    );
  }

  void _showSchedulingError() {
    showReminderErrorDialog(
      context,
      errorType: ReminderErrorType.scheduling,
      message: 'Unable to schedule your reminder. Please try again.',
      details: 'Error: Failed to schedule notification at ${DateTime.now()}',
      onRetry: _scheduleReminder,
      onTroubleshoot: () {
        // Navigate to troubleshooting screen or show help
      },
    );
  }

  void _showPermissionError() {
    showReminderErrorDialog(
      context,
      errorType: ReminderErrorType.permission,
      message: 'Notification permission is required to schedule reminders.',
      onRetry: _scheduleReminder,
    );
  }

  Future<void> _selectTime() async {
    final TimeOfDay? pickedTime = await showTimePicker(
      context: context,
      initialTime: _selectedTime.timeOfDay,
    );

    if (pickedTime != null) {
      setState(() {
        _selectedTime = _selectedTime.copyWith(timeOfDay: pickedTime);
      });
    }
  }

  Future<void> _selectDate() async {
    final DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: _selectedTime.selectedDate ?? DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (pickedDate != null) {
      setState(() {
        _selectedTime = _selectedTime.copyWith(selectedDate: pickedDate);
      });
    }
  }

  void _showNotificationPreview() {
    showNotificationPreviewDialog(
      context,
      title: 'Meter Reading Reminder',
      body:
          'It\'s time to record your meter reading. This helps track your usage accurately.',
      priority: NotificationPriority.info,
      onSoundTest: () {
        // Simulate playing notification sound
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Playing notification sound...')),
        );
      },
      onVibrationTest: () {
        // Simulate vibration
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Testing vibration pattern...')),
        );
      },
      onPermissionCheck: () async {
        final status = await widget.permissionAdapter.checkPermission();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Permission status: ${status == PermissionStatus.granted ? 'Granted' : 'Not granted'}',
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Reminder Settings'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Schedule'),
            Tab(text: 'Timeline'),
            Tab(text: 'Dashboard'),
            Tab(text: 'Manage'),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildScheduleTab(),
                _buildTimelineTab(),
                _buildDashboardTab(),
                _buildManageTab(),
              ],
            ),
    );
  }

  Widget _buildScheduleTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Card(
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Schedule Meter Reading Reminder',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Time picker
                  ListTile(
                    leading: const Icon(Icons.access_time),
                    title: const Text('Reminder Time'),
                    subtitle: Text(_selectedTime.format(context)),
                    trailing: const Icon(Icons.chevron_right),
                    onTap: _selectTime,
                  ),

                  // Date picker
                  ListTile(
                    leading: const Icon(Icons.calendar_today),
                    title: const Text('Start Date'),
                    subtitle: Text(
                      _selectedTime.selectedDate != null
                          ? '${_selectedTime.selectedDate!.day}/${_selectedTime.selectedDate!.month}/${_selectedTime.selectedDate!.year}'
                          : 'Today',
                    ),
                    trailing: const Icon(Icons.chevron_right),
                    onTap: _selectDate,
                  ),

                  // Frequency picker
                  ListTile(
                    leading: const Icon(Icons.repeat),
                    title: const Text('Frequency'),
                    subtitle: Text(
                        _selectedFrequency.substring(0, 1).toUpperCase() +
                            _selectedFrequency.substring(1)),
                    trailing: DropdownButton<String>(
                      value: _selectedFrequency,
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            _selectedFrequency = value;
                          });
                        }
                      },
                      items: const [
                        DropdownMenuItem(value: 'daily', child: Text('Daily')),
                        DropdownMenuItem(
                            value: 'weekly', child: Text('Weekly')),
                        DropdownMenuItem(
                            value: 'monthly', child: Text('Monthly')),
                      ],
                      underline: const SizedBox(),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Current timezone info
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.blue.shade50,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.public,
                          color: Colors.blue.shade700,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'Current timezone: $_currentTimezone',
                            style: TextStyle(
                              color: Colors.blue.shade700,
                              fontSize: 14,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Action buttons
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      OutlinedButton.icon(
                        onPressed: _showNotificationPreview,
                        icon: const Icon(Icons.visibility),
                        label: const Text('Preview'),
                      ),
                      ElevatedButton.icon(
                        onPressed: _scheduleReminder,
                        icon: const Icon(Icons.schedule),
                        label: const Text('Schedule Reminder'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 12,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Permission status card
          Card(
            elevation: 1,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Notification Permissions',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: _hasPermission
                              ? Colors.green.shade100
                              : Colors.red.shade100,
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          _hasPermission ? Icons.check_circle : Icons.error,
                          color: _hasPermission
                              ? Colors.green.shade700
                              : Colors.red.shade700,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              _hasPermission
                                  ? 'Notifications Enabled'
                                  : 'Notifications Disabled',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              _hasPermission
                                  ? 'You will receive reminders as scheduled'
                                  : 'Enable notifications to receive reminders',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey.shade600,
                              ),
                            ),
                          ],
                        ),
                      ),
                      if (!_hasPermission)
                        TextButton(
                          onPressed: _requestPermission,
                          child: const Text('Enable'),
                        ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTimelineTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          const Text(
            'Upcoming Reminders',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          ReminderTimelineView(
            reminders: _upcomingReminders,
            onReminderTap: (reminder) {
              // Handle reminder tap
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                    content: Text('Tapped on reminder: ${reminder.title}')),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildDashboardTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          ReminderDashboardWidget(
            reminders: _activeReminders,
            onReminderTap: (reminder) {
              // Handle reminder tap
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                    content: Text('Tapped on reminder: ${reminder.title}')),
              );
            },
            onAddReminderTap: () {
              // Navigate to add reminder screen
              _tabController.animateTo(0); // Go to schedule tab
            },
          ),
          const SizedBox(height: 24),

          // Next reminder countdown
          Card(
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  const Text(
                    'Next Scheduled Reminder',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  if (_activeReminders.isNotEmpty) ...[
                    Text(
                      _activeReminders.first.title,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 8),
                    // Countdown timer
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        _buildCountdownUnit(3, 'Days'),
                        _buildCountdownSeparator(),
                        _buildCountdownUnit(7, 'Hours'),
                        _buildCountdownSeparator(),
                        _buildCountdownUnit(22, 'Minutes'),
                      ],
                    ),
                  ] else
                    const Text(
                      'No upcoming reminders',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey,
                      ),
                    ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCountdownUnit(int value, String label) {
    return Container(
      width: 60,
      padding: const EdgeInsets.symmetric(vertical: 8),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Text(
            value.toString(),
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.blue.shade800,
            ),
          ),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: Colors.blue.shade800,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCountdownSeparator() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: Text(
        ':',
        style: TextStyle(
          fontSize: 24,
          fontWeight: FontWeight.bold,
          color: Colors.blue.shade800,
        ),
      ),
    );
  }

  Widget _buildManageTab() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          ReminderManagementWidget(
            reminders: _managedReminders,
            templates: _templates,
            categories: [
              'Electricity',
              'Gas',
              'Water',
              'Balance',
              'Reports',
              'Maintenance'
            ],
            tags: ['Important', 'Analysis', 'Required', 'Optional'],
            onReminderEdit: (reminder) {
              // Handle reminder edit
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Editing reminder: ${reminder.title}')),
              );
            },
            onReminderToggle: (reminder) {
              // Handle reminder toggle
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    'Toggled reminder: ${reminder.title} to ${!reminder.isActive ? 'active' : 'inactive'}',
                  ),
                ),
              );
            },
            onBatchDelete: (reminders) {
              // Handle batch delete
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Deleting ${reminders.length} reminders'),
                ),
              );
            },
            onBatchToggle: (reminders, activate) {
              // Handle batch toggle
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    '${activate ? 'Activating' : 'Deactivating'} ${reminders.length} reminders',
                  ),
                ),
              );
            },
            onTemplateSelected: (template) {
              // Handle template selection
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Selected template: ${template.name}')),
              );

              // Navigate to schedule tab with pre-filled values
              setState(() {
                _selectedTime = ReminderTimeModel(
                  timeOfDay: template.timeOfDay,
                );
                _selectedFrequency = template.frequency;
              });

              _tabController.animateTo(0); // Go to schedule tab
            },
            onCategoryFilter: (category) {
              // Handle category filter
            },
            onTagFilter: (tag) {
              // Handle tag filter
            },
          ),
        ],
      ),
    );
  }
}
