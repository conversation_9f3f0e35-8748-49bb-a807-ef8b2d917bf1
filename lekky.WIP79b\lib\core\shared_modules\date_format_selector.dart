// File: lib/core/shared_modules/date_format_selector.dart
import 'package:flutter/material.dart';
import 'base_settings_widget.dart';
import 'settings_model.dart';

class DateFormatSelector extends BaseSettingsWidget {
  final String currentValue;
  final Function(String) onChanged;
  final String? errorText;

  const DateFormatSelector({
    Key? key,
    required this.currentValue,
    required this.onChanged,
    this.errorText,
    SettingsDisplayMode displayMode = SettingsDisplayMode.compact,
    bool showHelperText = false,
    bool showTitle = true,
  }) : super(
            key: key,
            displayMode: displayMode,
            showHelperText: showHelperText,
            showTitle: showTitle);

  // Date format options
  static const List<String> dateFormats = [
    'DD-MM-YYYY',
    'MM-DD-YYYY',
    'YYYY-MM-DD',
  ];

  // Get a formatted example for a date format
  String getFormattedExample(String format) {
    final now = DateTime.now();
    final day = now.day.toString().padLeft(2, '0');
    final month = now.month.toString().padLeft(2, '0');
    final year = now.year.toString();

    switch (format) {
      case 'DD-MM-YYYY':
        return '$day-$month-$year';
      case 'MM-DD-YYYY':
        return '$month-$day-$year';
      case 'YYYY-MM-DD':
        return '$year-$month-$day';
      default:
        return '$day-$month-$year';
    }
  }

  @override
  Widget buildExpandedView(BuildContext context) {
    final primaryColor = Theme.of(context).colorScheme.primary;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (showTitle) buildSectionTitle(context, 'Date Format'),

        if (showHelperText)
          buildHelperText(context,
              'Choose how dates will be displayed throughout the app.'),

        const SizedBox(height: 16),

        // Date format options as radio buttons
        ...dateFormats.map((format) {
          return RadioListTile<String>(
            title: Text(format),
            subtitle: Text(
              'Example: ${getFormattedExample(format)}',
              style: const TextStyle(fontSize: 12),
            ),
            value: format,
            groupValue: currentValue,
            onChanged: (value) {
              if (value != null) {
                onChanged(value);
              }
            },
            dense: true,
            activeColor: primaryColor,
          );
        }),

        buildErrorText(context, errorText),
      ],
    );
  }

  @override
  Widget buildCompactView(BuildContext context) {
    final primaryColor = Theme.of(context).colorScheme.primary;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (showTitle) buildLabel(context, 'Date Format'),

        if (showTitle)
          Text(
            'Current: $currentValue (${getFormattedExample(currentValue)})',
            style: const TextStyle(
              fontSize: 12,
              color: Colors.grey,
            ),
          ),

        if (showHelperText)
          buildHelperText(context, 'Choose how dates will be displayed'),

        const SizedBox(height: 8),

        // Date format options as more compact radio buttons
        ...dateFormats.map((format) {
          return RadioListTile<String>(
            title: Text(
              '$format (${getFormattedExample(format)})',
              style: const TextStyle(fontSize: 13),
            ),
            value: format,
            groupValue: currentValue,
            onChanged: (value) {
              if (value != null) {
                onChanged(value);
              }
            },
            dense: true,
            contentPadding: const EdgeInsets.symmetric(horizontal: 0),
            activeColor: primaryColor,
          );
        }),

        buildErrorText(context, errorText),
      ],
    );
  }
}
