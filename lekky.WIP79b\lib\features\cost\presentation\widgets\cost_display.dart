// File: lib/features/cost/presentation/widgets/cost_display.dart
import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../domain/models/cost_period.dart';
import '../../domain/models/cost_mode.dart';

/// A widget for displaying the calculated cost
class CostDisplay extends StatelessWidget {
  /// The formatted cost value
  final String costValue;

  /// The selected period
  final CostPeriod selectedPeriod;

  /// The selected mode
  final CostMode selectedMode;

  /// Whether the calculation uses averages
  final bool usesAverages;

  /// Whether this is a custom date range
  final bool isCustomRange;

  /// The number of days in the period
  final int days;

  const CostDisplay({
    Key? key,
    required this.costValue,
    required this.selectedPeriod,
    required this.selectedMode,
    this.usesAverages = false,
    this.isCustomRange = false,
    this.days = 0,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Text(
          costValue,
          style: AppTextStyles.valueText.copyWith(
            color: AppColors.primary,
            fontWeight: FontWeight.bold,
            fontSize: 32,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          _getSubtitleText(),
          style: AppTextStyles.labelLarge.copyWith(
            color: Colors.black54,
            fontWeight: FontWeight.w500,
          ),
        ),
        if (usesAverages) ...[
          const SizedBox(height: 8),
          _buildCalculationMethodChip(),
        ],
      ],
    );
  }

  String _getSubtitleText() {
    if (isCustomRange) {
      return 'for selected period (${days} days)';
    } else {
      final periodText = selectedPeriod.name.toLowerCase();
      return selectedMode == CostMode.past
          ? 'for past $periodText'
          : 'for next $periodText';
    }
  }

  Widget _buildCalculationMethodChip() {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: 12,
        vertical: 6,
      ),
      decoration: BoxDecoration(
        color: Colors.amber[100],
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.info_outline,
            size: 16,
            color: Colors.amber[800],
          ),
          const SizedBox(width: 4),
          Text(
            'Based on average usage',
            style: AppTextStyles.bodySmall.copyWith(
              color: Colors.amber[800],
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
