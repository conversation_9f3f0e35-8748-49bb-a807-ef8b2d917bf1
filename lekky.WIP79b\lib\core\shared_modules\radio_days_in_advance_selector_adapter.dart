// File: lib/core/shared_modules/radio_days_in_advance_selector_adapter.dart
import 'package:flutter/material.dart';
import '../settings/widgets/radio_days_in_advance_selector.dart';
import 'settings_model.dart';

/// An adapter class that provides a radio-based days in advance selector
/// This allows us to use the new radio-based selector with the existing code
class RadioDaysInAdvanceSelectorAdapter extends StatelessWidget {
  final int currentValue;
  final ValueChanged<int> onChanged;
  final String? errorText;
  final bool hasTotalAverage;
  final SettingsDisplayMode displayMode;
  final bool showHelperText;
  final bool showTitle;

  const RadioDaysInAdvanceSelectorAdapter({
    Key? key,
    required this.currentValue,
    required this.onChanged,
    this.errorText,
    this.hasTotalAverage = false,
    this.displayMode = SettingsDisplayMode.compact,
    this.showHelperText = false,
    this.showTitle = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Use the radio-based selector with the same interface as the slider-based one
    return RadioDaysInAdvanceSelector(
      currentValue: currentValue,
      onChanged: onChanged,
      errorText: errorText,
      showHelperText:
          showHelperText || displayMode == SettingsDisplayMode.expanded,
      showTitle: showTitle,
    );
  }
}
