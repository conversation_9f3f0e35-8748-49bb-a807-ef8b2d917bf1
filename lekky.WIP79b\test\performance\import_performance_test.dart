// File: test/performance/import_performance_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:lekky/core/data/database/db_helper.dart';
import 'package:lekky/core/data/repositories/meter_entry_repository.dart';
import 'package:lekky/core/models/meter_entry.dart';
import 'package:lekky/features/history/presentation/controllers/history_controller.dart';
import 'package:shared_preferences/shared_preferences.dart';

void main() {
  group('Import Performance Tests', () {
    late DBHelper dbHelper;
    late MeterEntryRepository repository;
    late HistoryController controller;
    
    setUp(() async {
      // Set up SharedPreferences for testing
      SharedPreferences.setMockInitialValues({});
      
      // Create instances
      dbHelper = DBHelper();
      await dbHelper.init();
      repository = MeterEntryRepository(dbHelper: dbHelper);
      controller = HistoryController(repository: repository);
    });
    
    test('Performance comparison: old vs new implementation for 100 entries', () async {
      // Create test entries
      final entries = List.generate(100, (index) {
        final isTopUp = index % 3 == 0;
        return MeterEntry(
          reading: isTopUp ? 0.0 : 100.0 - index,
          amountToppedUp: isTopUp ? 50.0 : 0.0,
          timestamp: DateTime(2023, 1, 1).add(Duration(days: index)),
          shortAverageAfterTopUp: null,
          totalAverageUpToThisPoint: null,
        );
      });
      
      // Measure time for old implementation (one by one)
      final oldStartTime = DateTime.now();
      for (final entry in entries) {
        await repository.addEntry(entry);
      }
      final oldEndTime = DateTime.now();
      final oldDuration = oldEndTime.difference(oldStartTime);
      
      // Clear data
      await repository.deleteAllEntries();
      
      // Measure time for new implementation (bulk)
      final newStartTime = DateTime.now();
      await repository.bulkAddEntries(entries);
      final newEndTime = DateTime.now();
      final newDuration = newEndTime.difference(newStartTime);
      
      // Print results
      print('Old implementation (100 entries): ${oldDuration.inMilliseconds}ms');
      print('New implementation (100 entries): ${newDuration.inMilliseconds}ms');
      print('Improvement: ${(oldDuration.inMilliseconds - newDuration.inMilliseconds) / oldDuration.inMilliseconds * 100}%');
      
      // Verify that new implementation is faster
      expect(newDuration, lessThan(oldDuration));
    });
    
    test('Performance test: bulk import of 500 entries', () async {
      // Create test entries
      final entries = List.generate(500, (index) {
        final isTopUp = index % 3 == 0;
        return MeterEntry(
          reading: isTopUp ? 0.0 : 100.0 - (index % 100),
          amountToppedUp: isTopUp ? 50.0 : 0.0,
          timestamp: DateTime(2023, 1, 1).add(Duration(days: index)),
          shortAverageAfterTopUp: null,
          totalAverageUpToThisPoint: null,
        );
      });
      
      // Measure time for bulk import
      final startTime = DateTime.now();
      await repository.bulkAddEntries(entries);
      final endTime = DateTime.now();
      final duration = endTime.difference(startTime);
      
      // Print results
      print('Bulk import of 500 entries: ${duration.inMilliseconds}ms');
      print('Average time per entry: ${duration.inMilliseconds / 500}ms');
      
      // Verify that all entries were added
      final allEntries = await repository.getAllEntries();
      expect(allEntries.length, equals(500));
    });
    
    test('Performance test: controller bulk add of 500 entries', () async {
      // Create test entries
      final entries = List.generate(500, (index) {
        final isTopUp = index % 3 == 0;
        return MeterEntry(
          reading: isTopUp ? 0.0 : 100.0 - (index % 100),
          amountToppedUp: isTopUp ? 50.0 : 0.0,
          timestamp: DateTime(2023, 1, 1).add(Duration(days: index)),
          shortAverageAfterTopUp: null,
          totalAverageUpToThisPoint: null,
        );
      });
      
      // Measure time for controller bulk add
      final startTime = DateTime.now();
      await controller.bulkAddEntries(entries);
      final endTime = DateTime.now();
      final duration = endTime.difference(startTime);
      
      // Print results
      print('Controller bulk add of 500 entries: ${duration.inMilliseconds}ms');
      print('Average time per entry: ${duration.inMilliseconds / 500}ms');
      
      // Verify that all entries were added
      final allEntries = await repository.getAllEntries();
      expect(allEntries.length, equals(500));
    });
    
    test('Performance test: chunked import of 600 entries', () async {
      // Create test entries
      final entries = List.generate(600, (index) {
        final isTopUp = index % 3 == 0;
        return MeterEntry(
          reading: isTopUp ? 0.0 : 100.0 - (index % 100),
          amountToppedUp: isTopUp ? 50.0 : 0.0,
          timestamp: DateTime(2023, 1, 1).add(Duration(days: index)),
          shortAverageAfterTopUp: null,
          totalAverageUpToThisPoint: null,
        );
      });
      
      // Measure time for chunked import
      final startTime = DateTime.now();
      await repository.bulkAddEntries(entries);
      final endTime = DateTime.now();
      final duration = endTime.difference(startTime);
      
      // Print results
      print('Chunked import of 600 entries: ${duration.inMilliseconds}ms');
      print('Average time per entry: ${duration.inMilliseconds / 600}ms');
      
      // Verify that all entries were added
      final allEntries = await repository.getAllEntries();
      expect(allEntries.length, equals(600));
    });
  });
}
