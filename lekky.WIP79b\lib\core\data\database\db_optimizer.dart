// File: lib/core/data/database/db_optimizer.dart
import 'dart:async';
import 'package:shared_preferences/shared_preferences.dart';
import 'db_helper.dart';
import '../../models/meter_entry.dart';
import '../../utils/logger.dart';
import 'db_constants.dart';

/// A utility class for optimizing database operations
class DBOptimizer {
  // Private constructor to prevent instantiation
  DBOptimizer._();

  // Cache for meter entries
  static List<MeterEntry>? _cachedEntries;
  static DateTime? _lastCacheTime;
  static const int _cacheExpiryMinutes =
      1; // Reduced from 5 to ensure more frequent updates

  // Cache for meter total
  static double? _cachedMeterTotal;
  static DateTime? _lastMeterTotalCacheTime;

  /// Gets meter entries with caching
  static Future<List<MeterEntry>> getCachedMeterEntries(
      DBHelper dbHelper) async {
    final now = DateTime.now();

    // Check if cache is valid
    if (_cachedEntries != null &&
        _lastCacheTime != null &&
        now.difference(_lastCacheTime!).inMinutes < _cacheExpiryMinutes) {
      logger.d('DBOptimizer: Using cached entries from ${_lastCacheTime}');
      return _cachedEntries!;
    }

    // Cache miss, fetch from database
    logger.d('DBOptimizer: Cache miss for entries, fetching from database');
    final entries = await dbHelper.getMeterEntries();

    // Update cache
    _cachedEntries = entries;
    _lastCacheTime = now;
    logger.d(
        'DBOptimizer: Updated entries cache at $now with ${entries.length} entries');

    return entries;
  }

  /// Gets meter total with caching
  static Future<double> getCachedMeterTotal(DBHelper dbHelper) async {
    final now = DateTime.now();

    // Check if cache is valid
    if (_cachedMeterTotal != null &&
        _lastMeterTotalCacheTime != null &&
        now.difference(_lastMeterTotalCacheTime!).inMinutes <
            _cacheExpiryMinutes) {
      logger.d(
          'DBOptimizer: Using cached meter total from $_lastMeterTotalCacheTime');
      return _cachedMeterTotal!;
    }

    // Cache miss, calculate from database
    logger.d(
        'DBOptimizer: Cache miss for meter total, calculating from database');
    final total = await dbHelper.calculateMeterTotal();

    // Update cache
    _cachedMeterTotal = total;
    _lastMeterTotalCacheTime = now;
    logger
        .d('DBOptimizer: Updated meter total cache at $now with value $total');

    return total;
  }

  /// Invalidates the cache
  static void invalidateCache() {
    _cachedEntries = null;
    _lastCacheTime = null;
    _cachedMeterTotal = null;
    _lastMeterTotalCacheTime = null;

    // Add logging to track cache invalidation
    logger.d('DBOptimizer: Cache invalidated at ${DateTime.now()}');
  }

  /// Optimizes SharedPreferences access by batching writes
  static Future<void> batchWritePreferences(Map<String, dynamic> values) async {
    final prefs = await SharedPreferences.getInstance();

    for (final entry in values.entries) {
      final key = entry.key;
      final value = entry.value;

      if (value is String) {
        await prefs.setString(key, value);
      } else if (value is int) {
        await prefs.setInt(key, value);
      } else if (value is double) {
        await prefs.setDouble(key, value);
      } else if (value is bool) {
        await prefs.setBool(key, value);
      } else if (value is List<String>) {
        await prefs.setStringList(key, value);
      }
    }
  }

  /// Optimizes SharedPreferences access by batching reads
  static Future<Map<String, dynamic>> batchReadPreferences(
      List<String> keys) async {
    final prefs = await SharedPreferences.getInstance();
    final result = <String, dynamic>{};

    for (final key in keys) {
      if (prefs.containsKey(key)) {
        result[key] = prefs.get(key);
      }
    }

    return result;
  }

  /// Compacts the database by removing unnecessary entries
  static Future<void> compactDatabase(DBHelper dbHelper) async {
    // Get all entries
    final entries = await dbHelper.getMeterEntries();

    // Sort entries by timestamp
    entries.sort((a, b) => a.timestamp.compareTo(b.timestamp));

    // If there are more than the optimization threshold, keep only the last entries
    if (entries.length > DatabaseConstants.optimizationThreshold) {
      final entriesToRemove = entries.sublist(
          0, entries.length - DatabaseConstants.optimizationThreshold);

      // Remove old entries
      for (final entry in entriesToRemove) {
        if (entry.id != null) {
          await dbHelper.deleteMeterEntry(entry.id!);
        }
      }
    }

    // Invalidate cache after compacting
    invalidateCache();
  }

  /// Checks if the database needs optimization
  static Future<bool> needsOptimization(DBHelper dbHelper) async {
    final entries = await dbHelper.getMeterEntries();
    return entries.length > DatabaseConstants.optimizationThreshold;
  }
}
