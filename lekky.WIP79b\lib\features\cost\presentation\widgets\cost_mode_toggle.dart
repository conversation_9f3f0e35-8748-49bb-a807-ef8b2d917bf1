// File: lib/features/cost/presentation/widgets/cost_mode_toggle.dart
import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../domain/models/cost_mode.dart';

/// A toggle widget for switching between past and future cost modes
class CostModeToggle extends StatelessWidget {
  /// The current selected mode
  final CostMode selectedMode;

  /// Callback when the mode is changed
  final ValueChanged<CostMode> onModeChanged;

  const CostModeToggle({
    Key? key,
    required this.selectedMode,
    required this.onModeChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          _buildModeButton(
            context,
            CostMode.past,
            'Past',
          ),
          _buildModeButton(
            context,
            CostMode.future,
            'Future',
          ),
        ],
      ),
    );
  }

  Widget _buildModeButton(
    BuildContext context,
    CostMode mode,
    String label,
  ) {
    final isSelected = selectedMode == mode;
    
    return Expanded(
      child: GestureDetector(
        onTap: () => onModeChanged(mode),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12),
          decoration: BoxDecoration(
            color: isSelected ? AppColors.primary : Colors.transparent,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Center(
            child: Text(
              label,
              style: AppTextStyles.bodyMedium.copyWith(
                color: isSelected ? Colors.white : Colors.black87,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
