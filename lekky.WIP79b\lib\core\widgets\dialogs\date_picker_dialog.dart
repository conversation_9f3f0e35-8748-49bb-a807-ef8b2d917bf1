// File: lib/core/widgets/dialogs/date_picker_dialog.dart
import 'package:flutter/material.dart' hide DatePickerDialog;
import 'package:flutter/material.dart' as material;
import '../../theme/app_colors.dart';
import '../../theme/app_text_styles.dart';
import '../../utils/date_time_utils.dart';
import '../app_dialog.dart';

/// A specialized dialog for allowing users to select dates.
///
/// This dialog uses platform-native date pickers with appropriate date constraints.
class DatePickerDialog {
  /// Shows a date picker dialog.
  ///
  /// Parameters:
  /// - [context]: The build context.
  /// - [title]: The title of the dialog, should clearly indicate the purpose.
  /// - [initialDate]: The initially selected date.
  /// - [firstDate]: The earliest date that can be selected.
  /// - [lastDate]: The latest date that can be selected.
  /// - [confirmText]: The text for the confirm button (default: "Select").
  /// - [cancelText]: The text for the cancel button (default: "Cancel").
  /// - [helpText]: Optional help text to display in the date picker.
  /// - [errorFormatText]: Optional error message for invalid date format.
  /// - [errorInvalidText]: Optional error message for invalid date.
  /// - [fieldHintText]: Optional hint text for the date input field.
  /// - [fieldLabelText]: Optional label text for the date input field.
  /// - [currentDate]: Optional current date to highlight in the date picker.
  /// - [initialEntryMode]: The initial entry mode for the date picker (default: calendar).
  /// - [selectableDayPredicate]: Optional function to determine which days are selectable.
  static Future<DateTime?> showDatePicker({
    required BuildContext context,
    required String title,
    required DateTime initialDate,
    required DateTime firstDate,
    required DateTime lastDate,
    String confirmText = 'Select',
    String cancelText = 'Cancel',
    String? helpText,
    String? errorFormatText,
    String? errorInvalidText,
    String? fieldHintText,
    String? fieldLabelText,
    DateTime? currentDate,
    DatePickerEntryMode initialEntryMode = DatePickerEntryMode.calendar,
    bool Function(DateTime)? selectableDayPredicate,
  }) async {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    // Show the native date picker using Flutter's showDatePicker
    final DateTime? pickedDate = await material.showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: firstDate,
      lastDate: lastDate,
      helpText: helpText,
      cancelText: cancelText,
      confirmText: confirmText,
      errorFormatText: errorFormatText,
      errorInvalidText: errorInvalidText,
      fieldHintText: fieldHintText,
      fieldLabelText: fieldLabelText,
      initialEntryMode: initialEntryMode,
      selectableDayPredicate: selectableDayPredicate,
      currentDate: currentDate,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
                  primary:
                      isDarkMode ? AppColors.primaryDark : AppColors.primary,
                  onPrimary: isDarkMode
                      ? AppColors.onPrimaryDark
                      : AppColors.onPrimary,
                  surface:
                      isDarkMode ? AppColors.surfaceDark : AppColors.surface,
                  onSurface: isDarkMode
                      ? AppColors.onSurfaceDark
                      : AppColors.onSurface,
                ),
          ),
          child: child!,
        );
      },
    );

    return pickedDate;
  }

  /// Shows a date range picker dialog.
  ///
  /// Parameters:
  /// - [context]: The build context.
  /// - [title]: The title of the dialog, should clearly indicate the purpose.
  /// - [initialDateRange]: The initially selected date range.
  /// - [firstDate]: The earliest date that can be selected.
  /// - [lastDate]: The latest date that can be selected.
  /// - [confirmText]: The text for the confirm button (default: "Save").
  /// - [cancelText]: The text for the cancel button (default: "Cancel").
  /// - [helpText]: Optional help text to display in the date range picker.
  /// - [errorFormatText]: Optional error message for invalid date format.
  /// - [errorInvalidText]: Optional error message for invalid date.
  /// - [errorInvalidRangeText]: Optional error message for invalid date range.
  /// - [fieldStartHintText]: Optional hint text for the start date input field.
  /// - [fieldEndHintText]: Optional hint text for the end date input field.
  /// - [fieldStartLabelText]: Optional label text for the start date input field.
  /// - [fieldEndLabelText]: Optional label text for the end date input field.
  /// - [currentDate]: Optional current date to highlight in the date picker.
  /// - [initialEntryMode]: The initial entry mode for the date picker (default: calendar).
  /// - [saveText]: The text for the save button (default: "Save").
  static Future<DateTimeRange?> showDateRangePicker({
    required BuildContext context,
    required String title,
    DateTimeRange? initialDateRange,
    required DateTime firstDate,
    required DateTime lastDate,
    String confirmText = 'Save',
    String cancelText = 'Cancel',
    String? helpText,
    String? errorFormatText,
    String? errorInvalidText,
    String? errorInvalidRangeText,
    String? fieldStartHintText,
    String? fieldEndHintText,
    String? fieldStartLabelText,
    String? fieldEndLabelText,
    DateTime? currentDate,
    DatePickerEntryMode initialEntryMode = DatePickerEntryMode.calendar,
    String saveText = 'Save',
  }) async {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    // Show the native date range picker
    final DateTimeRange? pickedDateRange = await showDialog<DateTimeRange>(
      context: context,
      builder: (BuildContext context) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
                  primary:
                      isDarkMode ? AppColors.primaryDark : AppColors.primary,
                  onPrimary: isDarkMode
                      ? AppColors.onPrimaryDark
                      : AppColors.onPrimary,
                  surface:
                      isDarkMode ? AppColors.surfaceDark : AppColors.surface,
                  onSurface: isDarkMode
                      ? AppColors.onSurfaceDark
                      : AppColors.onSurface,
                ),
          ),
          child: DateRangePickerDialog(
            initialDateRange: initialDateRange,
            firstDate: firstDate,
            lastDate: lastDate,
            helpText: helpText,
            cancelText: cancelText,
            confirmText: confirmText,
            errorFormatText: errorFormatText,
            errorInvalidText: errorInvalidText,
            errorInvalidRangeText: errorInvalidRangeText,
            fieldStartHintText: fieldStartHintText,
            fieldEndHintText: fieldEndHintText,
            fieldStartLabelText: fieldStartLabelText,
            fieldEndLabelText: fieldEndLabelText,
            currentDate: currentDate,
            initialEntryMode: initialEntryMode,
          ),
        );
      },
    );

    return pickedDateRange;
  }

  /// Shows a custom date picker dialog with a preview of the selected date.
  ///
  /// This dialog shows a preview of the selected date in a specified format,
  /// which can be useful for showing users how their selected date will appear.
  ///
  /// Parameters:
  /// - [context]: The build context.
  /// - [title]: The title of the dialog, should clearly indicate the purpose.
  /// - [initialDate]: The initially selected date.
  /// - [firstDate]: The earliest date that can be selected.
  /// - [lastDate]: The latest date that can be selected.
  /// - [dateFormat]: The format to display the selected date in the preview.
  /// - [confirmText]: The text for the confirm button (default: "Select").
  /// - [cancelText]: The text for the cancel button (default: "Cancel").
  /// - [helpText]: Optional help text to display above the date picker.
  /// - [selectableDayPredicate]: Optional function to determine which days are selectable.
  static Future<DateTime?> showCustomDatePicker({
    required BuildContext context,
    required String title,
    required DateTime initialDate,
    required DateTime firstDate,
    required DateTime lastDate,
    required String dateFormat,
    String confirmText = 'Select',
    String cancelText = 'Cancel',
    String? helpText,
    bool Function(DateTime)? selectableDayPredicate,
  }) async {
    DateTime selectedDate = initialDate;

    return showDialog<DateTime>(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            final formattedDate =
                DateTimeUtils.formatDate(selectedDate, dateFormat);

            return AppDialog(
              title: title,
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (helpText != null) ...[
                    Text(
                      helpText,
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: Theme.of(context).brightness == Brightness.dark
                            ? AppColors.onSurfaceDark.withOpacity(0.8)
                            : AppColors.onSurface.withOpacity(0.8),
                      ),
                    ),
                    const SizedBox(height: 16),
                  ],
                  // Preview of the selected date
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Theme.of(context).brightness == Brightness.dark
                          ? AppColors.surfaceVariantDark
                          : AppColors.surfaceVariant,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Selected Date:',
                          style: AppTextStyles.bodySmall.copyWith(
                            color:
                                Theme.of(context).brightness == Brightness.dark
                                    ? AppColors.onSurfaceVariantDark
                                    : AppColors.onSurfaceVariant,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          formattedDate,
                          style: AppTextStyles.bodyLarge.copyWith(
                            fontWeight: FontWeight.bold,
                            color:
                                Theme.of(context).brightness == Brightness.dark
                                    ? AppColors.primaryDark
                                    : AppColors.primary,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 16),
                  // Button to open the date picker
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: () async {
                        final DateTime? pickedDate =
                            await DatePickerDialog.showDatePicker(
                          context: context,
                          title: title,
                          initialDate: selectedDate,
                          firstDate: firstDate,
                          lastDate: lastDate,
                          selectableDayPredicate: selectableDayPredicate,
                        );

                        if (pickedDate != null) {
                          setState(() {
                            selectedDate = pickedDate;
                          });
                        }
                      },
                      icon: const Icon(Icons.calendar_today),
                      label: const Text('Change Date'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor:
                            Theme.of(context).brightness == Brightness.dark
                                ? AppColors.primaryDark
                                : AppColors.primary,
                        foregroundColor:
                            Theme.of(context).brightness == Brightness.dark
                                ? AppColors.onPrimaryDark
                                : AppColors.onPrimary,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                    ),
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  style: TextButton.styleFrom(
                    foregroundColor: Colors.blue,
                    side: const BorderSide(color: Colors.blue),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: Text(cancelText),
                ),
                TextButton(
                  onPressed: () => Navigator.of(context).pop(selectedDate),
                  style: TextButton.styleFrom(
                    foregroundColor:
                        Theme.of(context).brightness == Brightness.dark
                            ? AppColors.primaryDark
                            : AppColors.primary,
                  ),
                  child: Text(confirmText),
                ),
              ],
            );
          },
        );
      },
    );
  }

  /// Shows a date and time picker dialog.
  ///
  /// This dialog allows users to select both a date and a time in sequence.
  ///
  /// Parameters:
  /// - [context]: The build context.
  /// - [title]: The title of the dialog, should clearly indicate the purpose.
  /// - [initialDate]: The initially selected date.
  /// - [firstDate]: The earliest date that can be selected.
  /// - [lastDate]: The latest date that can be selected.
  /// - [currentTime]: The initially selected time (default: current time).
  /// - [includeTime]: Whether to include time selection (default: true).
  /// - [confirmText]: The text for the confirm button (default: "Select").
  /// - [cancelText]: The text for the cancel button (default: "Cancel").
  /// - [helpText]: Optional help text to display in the date picker.
  /// - [addCurrentSecond]: Whether to add the current second to avoid duplicates (default: false).
  static Future<DateTime?> showDateTimePicker({
    required BuildContext context,
    required String title,
    required DateTime initialDate,
    required DateTime firstDate,
    required DateTime lastDate,
    TimeOfDay? currentTime,
    bool includeTime = true,
    String confirmText = 'Select',
    String cancelText = 'Cancel',
    String? helpText,
    bool addCurrentSecond = false,
  }) async {
    // Store the brightness for theming before any async operations
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    // First, pick a date
    final DateTime? pickedDate = await DatePickerDialog.showDatePicker(
      context: context,
      title: title,
      initialDate: initialDate,
      firstDate: firstDate,
      lastDate: lastDate,
      confirmText: confirmText,
      cancelText: cancelText,
      helpText: helpText,
    );

    if (pickedDate == null) {
      return null;
    }

    // If time selection is not needed, return just the date
    if (!includeTime) {
      return pickedDate;
    }

    // Then, pick a time
    final TimeOfDay initialTime =
        currentTime ?? TimeOfDay.fromDateTime(initialDate);

    // Check if the widget is still mounted before proceeding
    if (!context.mounted) return null;

    // Use a separate method to show the time picker to avoid BuildContext issues
    final TimeOfDay? pickedTime = await _showTimePicker(
      context: context,
      initialTime: initialTime,
      isDarkMode: isDarkMode,
    );

    if (pickedTime == null) {
      // If user cancels time selection, return null
      return null;
    }

    // Combine date and time
    final DateTime result = DateTime(
      pickedDate.year,
      pickedDate.month,
      pickedDate.day,
      pickedTime.hour,
      pickedTime.minute,
      addCurrentSecond
          ? DateTime.now().second
          : 0, // Add current second if requested
    );

    return result;
  }

  /// Helper method to show a time picker dialog
  static Future<TimeOfDay?> _showTimePicker({
    required BuildContext context,
    required TimeOfDay initialTime,
    required bool isDarkMode,
  }) async {
    return showTimePicker(
      context: context,
      initialTime: initialTime,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
                  primary:
                      isDarkMode ? AppColors.primaryDark : AppColors.primary,
                  onPrimary: isDarkMode
                      ? AppColors.onPrimaryDark
                      : AppColors.onPrimary,
                  surface:
                      isDarkMode ? AppColors.surfaceDark : AppColors.surface,
                  onSurface: isDarkMode
                      ? AppColors.onSurfaceDark
                      : AppColors.onSurface,
                ),
          ),
          child: child!,
        );
      },
    );
  }
}
