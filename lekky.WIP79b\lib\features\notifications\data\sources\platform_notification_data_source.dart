// File: lib/features/notifications/data/sources/platform_notification_data_source.dart

import 'package:flutter/material.dart';
import '../../../../core/platform/notification/notification_adapter.dart';
import '../../../../core/platform/permissions/permission_adapter.dart';
import '../../../../core/platform/timezone/timezone_adapter.dart';
import '../../domain/models/notification_model.dart';

/// Data source for platform-specific notification functionality
class PlatformNotificationDataSource {
  final NotificationAdapter _notificationAdapter;
  final PermissionAdapter _permissionAdapter;
  final TimezoneAdapter _timezoneAdapter;
  bool _isInitialized = false;

  // Notification IDs
  static const int lowBalanceNotificationId = 1;
  static const int topUpReminderNotificationId = 2;
  static const int invalidRecordNotificationId = 3;
  static const int meterReadingReminderNotificationId = 4;
  static const int welcomeNotificationId = 5;
  static const int firstMeterReadingReminderNotificationId = 6;
  static const int notificationSystemActivationId = 7;
  static const int checkMeterNowNotificationId = 8;

  PlatformNotificationDataSource({
    required NotificationAdapter notificationAdapter,
    required PermissionAdapter permissionAdapter,
    required TimezoneAdapter timezoneAdapter,
  })  : _notificationAdapter = notificationAdapter,
        _permissionAdapter = permissionAdapter,
        _timezoneAdapter = timezoneAdapter;

  /// Initialize the data source
  Future<void> initialize() async {
    if (_isInitialized) return;

    await _notificationAdapter.initialize();
    await _permissionAdapter.initialize();
    await _timezoneAdapter.initialize();

    // Create notification channels
    await _notificationAdapter.createNotificationChannels();

    _isInitialized = true;
  }

  /// Check if notification permissions are granted
  Future<bool> checkNotificationPermissions() async {
    final status = await _permissionAdapter.checkPermission();
    return status == PermissionStatus.granted;
  }

  /// Request notification permissions
  Future<bool> requestNotificationPermissions() async {
    return await _permissionAdapter.requestPermission();
  }

  /// Check if notification permission is permanently denied
  Future<bool> isNotificationPermissionPermanentlyDenied() async {
    return await _permissionAdapter.isPermanentlyDenied();
  }

  /// Open app notification settings
  Future<bool> openNotificationSettings() async {
    return await _permissionAdapter.openAppSettings();
  }

  /// Show a notification immediately
  Future<void> showNotification({
    required int id,
    required String title,
    required String body,
    String? payload,
  }) async {
    await _notificationAdapter.showNotification(
      id: id,
      title: title,
      body: body,
      payload: payload,
    );
  }

  /// Schedule a notification for a future date
  Future<bool> scheduleNotification({
    required int id,
    required String title,
    required String body,
    required DateTime scheduledDate,
    String? payload,
  }) async {
    return await _notificationAdapter.scheduleNotification(
      id: id,
      title: title,
      body: body,
      scheduledDate: scheduledDate,
      payload: payload,
    );
  }

  /// Reschedule a notification by ID
  Future<bool> rescheduleNotification(int id) async {
    return await _notificationAdapter.rescheduleNotification(id);
  }

  /// Reschedule all pending notifications
  Future<int> rescheduleAllNotifications() async {
    return await _notificationAdapter.rescheduleAllNotifications();
  }

  /// Cancel a notification
  Future<void> cancelNotification(int id) async {
    await _notificationAdapter.cancelNotification(id);
  }

  /// Cancel all notifications
  Future<void> cancelAllNotifications() async {
    await _notificationAdapter.cancelAllNotifications();
  }

  /// Check if the time zone has changed
  Future<bool> hasTimeZoneChanged() async {
    return await _timezoneAdapter.hasTimezoneChanged();
  }

  /// Show a low balance notification
  Future<void> showLowBalanceNotification({
    required String meterUnit,
    required double balance,
    required double threshold,
  }) async {
    final title = 'Low Balance Alert';
    final message =
        'Your balance is low: $meterUnit${balance.toStringAsFixed(2)}. '
        'You should top up soon as you are below your alert threshold of $meterUnit${threshold.toStringAsFixed(2)}.';

    await showNotification(
      id: lowBalanceNotificationId,
      title: title,
      body: message,
      payload: 'low_balance:$balance:$threshold',
    );
  }

  /// Show a time to top up notification
  Future<void> showTimeToTopUpNotification({
    required String meterUnit,
    required double balance,
    required int daysRemaining,
  }) async {
    final title = 'Time to Top Up';
    final message =
        'Based on your usage, you should top up in $daysRemaining ${daysRemaining == 1 ? 'day' : 'days'}. '
        'Your estimated balance is $meterUnit${balance.toStringAsFixed(2)}.';

    await showNotification(
      id: topUpReminderNotificationId,
      title: title,
      body: message,
      payload: 'top_up:$balance:$daysRemaining',
    );
  }

  /// Show an invalid record notification
  Future<void> showInvalidRecordNotification({
    required String message,
  }) async {
    final title = 'Invalid Entries Detected';

    await showNotification(
      id: invalidRecordNotificationId,
      title: title,
      body: message,
      payload: 'invalid_record',
    );
  }

  /// Schedule a meter reading reminder
  Future<bool> scheduleMeterReadingReminder(DateTime scheduledDate) async {
    return await scheduleNotification(
      id: meterReadingReminderNotificationId,
      title: 'Meter Reading Reminder',
      body:
          'It\'s time to record your meter reading. This helps track your usage accurately.',
      scheduledDate: scheduledDate,
      payload: 'meter_reading_reminder',
    );
  }

  /// Show a meter reading reminder notification immediately
  Future<void> showMeterReadingReminderNotification() async {
    await showNotification(
      id: meterReadingReminderNotificationId,
      title: 'Meter Reading Reminder',
      body:
          'It\'s time to record your meter reading. This helps track your usage accurately.',
      payload: 'meter_reading_reminder',
    );
  }

  /// Get the next scheduled meter reading reminder date
  Future<DateTime?> getNextMeterReadingReminderDate() async {
    // This would be implemented by checking the platform's scheduled notifications
    // For now, we'll rely on the stored value in the local data source
    return null;
  }

  /// Show welcome notification for first-time users
  Future<void> showWelcomeNotification() async {
    await showNotification(
      id: welcomeNotificationId,
      title: 'Welcome to Lekky',
      body:
          'Hi, thanks for using Lekky. Your notification messages can be found here for Top Up reminders etc.',
      payload: 'welcome',
    );
  }

  /// Show first meter reading reminder for new users
  Future<void> showFirstMeterReadingReminder() async {
    await showNotification(
      id: firstMeterReadingReminderNotificationId,
      title: 'Enter Your First Meter Reading',
      body:
          'Enter your first meter reading to start tracking your usage and receive helpful alerts.',
      payload: 'first_meter_reading',
    );
  }

  /// Schedule first meter reading reminder for 24 hours after setup
  Future<void> scheduleFirstMeterReadingReminder() async {
    // Schedule for 24 hours from now
    final scheduledDate = DateTime.now().add(const Duration(hours: 24));

    await scheduleNotification(
      id: firstMeterReadingReminderNotificationId,
      title: 'Enter Your First Meter Reading',
      body:
          'Enter your first meter reading to start tracking your usage and receive helpful alerts.',
      scheduledDate: scheduledDate,
      payload: 'first_meter_reading',
    );
  }
}
