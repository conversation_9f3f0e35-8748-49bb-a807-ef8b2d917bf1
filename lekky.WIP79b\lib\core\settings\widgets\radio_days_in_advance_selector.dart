// File: lib/core/settings/widgets/radio_days_in_advance_selector.dart
import 'package:flutter/material.dart';
import '../../theme/app_colors.dart';
import '../../theme/app_text_styles.dart';
import '../../settings/validators/settings_validator.dart';

/// A widget that allows selecting days in advance using radio buttons
class RadioDaysInAdvanceSelector extends StatefulWidget {
  final int currentValue;
  final ValueChanged<int> onChanged;
  final String? errorText;
  final bool showTitle;
  final bool showHelperText;

  // Common day options
  static const List<int> dayOptions = [1, 2, 3, 5, 7, 14];
  // Special value to indicate custom option
  static const int customOptionValue = -1;

  const RadioDaysInAdvanceSelector({
    Key? key,
    required this.currentValue,
    required this.onChanged,
    this.errorText,
    this.showTitle = true,
    this.showHelperText = true,
  }) : super(key: key);

  @override
  State<RadioDaysInAdvanceSelector> createState() =>
      _RadioDaysInAdvanceSelectorState();
}

class _RadioDaysInAdvanceSelectorState
    extends State<RadioDaysInAdvanceSelector> {
  /// Check if the current value is one of the standard options
  bool _isStandardOption(int value) {
    return RadioDaysInAdvanceSelector.dayOptions.contains(value);
  }

  /// Show a dialog to enter a custom days value
  void _showCustomDaysDialog() {
    showDialog(
      context: context,
      builder: (context) {
        final controller = TextEditingController(
          text: _isStandardOption(widget.currentValue)
              ? ''
              : widget.currentValue.toString(),
        );
        String? errorText;

        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('Custom Days in Advance'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'Enter a custom number of days between 1 and 99',
                    style: TextStyle(
                      fontSize: 14,
                      color: Theme.of(context).brightness == Brightness.dark
                          ? Colors.grey[300]
                          : Colors.grey[700],
                    ),
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: controller,
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      labelText: 'Days in Advance',
                      errorText: errorText,
                      border: const OutlineInputBorder(),
                    ),
                    autofocus: true,
                    onChanged: (value) {
                      final validation =
                          SettingsValidator.validateDaysInAdvance(value);
                      setState(() {
                        errorText = validation['isValid']
                            ? null
                            : validation['errorMessage'];
                      });
                    },
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Cancel'),
                ),
                TextButton(
                  onPressed: () {
                    final value = controller.text;
                    final validation =
                        SettingsValidator.validateDaysInAdvance(value);
                    if (validation['isValid']) {
                      final parsedValue = int.parse(value);
                      widget.onChanged(parsedValue);
                      Navigator.of(context).pop();
                    } else {
                      setState(() {
                        errorText = validation['errorMessage'];
                      });
                    }
                  },
                  child: const Text('Save'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final primaryColor = Theme.of(context).colorScheme.primary;
    final textColor = isDarkMode ? Colors.white : Colors.black87;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.showTitle) ...[
          Row(
            children: [
              const Icon(Icons.calendar_today, size: 20),
              const SizedBox(width: 8),
              Text(
                'Days in Advance',
                style: AppTextStyles.titleMedium.copyWith(
                  color: isDarkMode
                      ? AppColors.primaryTextDark
                      : AppColors.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
        ],

        if (widget.showHelperText) ...[
          Text(
            'Get notified this many days before your meter balance is predicted to reach the alert threshold',
            style: TextStyle(
              fontSize: 14,
              color: isDarkMode ? Colors.grey[300] : Colors.grey[700],
            ),
          ),
          const SizedBox(height: 16),
        ],

        // Two-column grid of radio buttons
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            for (final days in RadioDaysInAdvanceSelector.dayOptions)
              SizedBox(
                width: MediaQuery.of(context).size.width *
                    0.4, // Approximately half the width
                child: RadioListTile<int>(
                  title: Text(
                    days == 1 ? '1 day' : '$days days',
                    style: TextStyle(
                      color: textColor,
                      fontWeight: widget.currentValue == days
                          ? FontWeight.bold
                          : FontWeight.normal,
                    ),
                  ),
                  value: days,
                  groupValue: _isStandardOption(widget.currentValue)
                      ? widget.currentValue
                      : null,
                  activeColor: primaryColor,
                  onChanged: (value) {
                    if (value != null) {
                      widget.onChanged(value);
                    }
                  },
                  dense: true,
                  contentPadding: const EdgeInsets.symmetric(horizontal: 8),
                ),
              ),
            // Custom option
            SizedBox(
              width: MediaQuery.of(context).size.width *
                  0.4, // Approximately half the width
              child: RadioListTile<int>(
                title: Text(
                  'Custom...',
                  style: TextStyle(
                    color: textColor,
                    fontWeight: !_isStandardOption(widget.currentValue)
                        ? FontWeight.bold
                        : FontWeight.normal,
                  ),
                ),
                value: RadioDaysInAdvanceSelector.customOptionValue,
                groupValue: _isStandardOption(widget.currentValue)
                    ? null
                    : RadioDaysInAdvanceSelector.customOptionValue,
                activeColor: primaryColor,
                onChanged: (_) {
                  _showCustomDaysDialog();
                },
                dense: true,
                contentPadding: const EdgeInsets.symmetric(horizontal: 8),
              ),
            ),
          ],
        ),

        if (widget.showHelperText) ...[
          const SizedBox(height: 8),
          Text(
            'Tip: Consider your usage patterns when setting this value. If you use electricity quickly, choose fewer days.',
            style: TextStyle(
              fontSize: 12,
              fontStyle: FontStyle.italic,
              color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
            ),
          ),
        ],

        if (widget.errorText != null) ...[
          const SizedBox(height: 8),
          Text(
            widget.errorText!,
            style: TextStyle(color: Theme.of(context).colorScheme.error),
          ),
        ],
      ],
    );
  }
}
