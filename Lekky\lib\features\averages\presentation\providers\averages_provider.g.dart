// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'averages_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$averagesHash() => r'bf8c8e8c8e8c8e8c8e8c8e8c8e8c8e8c8e8c8e8c';

/// Reactive provider for averages that updates when data changes
///
/// Copied from [Averages].
@ProviderFor(Averages)
final averagesProvider =
    AutoDisposeAsyncNotifierProvider<Averages, AverageState>.internal(
  Averages.new,
  name: r'averagesProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$averagesHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$Averages = AutoDisposeAsyncNotifier<AverageState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
