import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/utils/helpful_messages.dart';
import '../../../../core/widgets/gradient_button.dart';
import '../../../../core/widgets/message_banner.dart';
import '../../../../core/widgets/home_notification_button.dart';
import '../../../../core/widgets/home_settings_button.dart';
import '../../../../core/models/meter_entry.dart';
import '../../../history/presentation/controllers/history_controller.dart';
import '../../../../core/widgets/dialogs/entry_edit_dialog.dart';
import '../controllers/home_controller.dart';
import '../widgets/combined_info_card.dart';
import '../widgets/meter_value_card.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({Key? key}) : super(key: key);

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  // Index for the current message to display
  int _currentMessageIndex = 0;

  @override
  void initState() {
    super.initState();

    // Initialize the controller
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<HomeController>().init();
    });

    // Rotate messages every 10 seconds
    _startMessageRotation();
  }

  void _startMessageRotation() {
    // Rotate messages every 10 seconds
    Future.delayed(const Duration(seconds: 10), () {
      if (mounted) {
        setState(() {
          _currentMessageIndex =
              (_currentMessageIndex + 1) % HelpfulMessages.allMessages.length;
        });
        _startMessageRotation(); // Schedule the next rotation
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _buildBody(),
      // Removed FloatingActionButton as it's redundant with the green Add Entry button
    );
  }

  Widget _buildBody() {
    return Consumer<HomeController>(
      builder: (context, controller, _) {
        return RefreshIndicator(
          onRefresh: controller.refresh,
          child: CustomScrollView(
            slivers: [
              _buildAppBar(),
              SliverPadding(
                padding: const EdgeInsets.symmetric(
                    horizontal: 8), // Match Cost screen padding
                sliver: SliverList(
                  delegate: SliverChildListDelegate([
                    // Center and set fixed width for all components
                    Padding(
                      padding: const EdgeInsets.only(top: 8),
                      child: Center(
                        child: SizedBox(
                          width: 350, // Match Cost dialog width
                          child: MeterValueCard(
                            meterTotal: controller.formattedMeterTotal,
                            averageUsage: controller.formattedAverageUsage,
                            shortTermAverageUsage:
                                controller.formattedShortTermAverageUsage,
                            isLoading: controller.isLoading,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 0),
                    // Combined Meter Info and Notifications Card
                    Center(
                      child: SizedBox(
                        width: 350, // Match Cost dialog width
                        child: CombinedInfoCard(
                          lastReadingDate: controller.formattedLastReadingDate,
                          dateToTopUp: controller.formattedDateToTopUp,
                          isLoading: controller.isLoading,
                        ),
                      ),
                    ),
                    const SizedBox(height: 4),
                    // Add Entry button with width reduced by 24px
                    Center(
                      child: SizedBox(
                        width: 326, // Reduced by 24px from dialog width
                        child: Padding(
                          padding: const EdgeInsets.only(top: 4),
                          child: GradientButton(
                            text: 'Add Entry', // Text on one line
                            icon: const Icon(
                              Icons.add,
                              color: Colors.white,
                              size: 24, // Slightly larger icon
                            ),
                            height:
                                60, // Reduced height to accommodate new card
                            gradientColors: const [
                              Color(0xFF43E97B),
                              Color(0xFF38F9D7)
                            ], // Green gradient
                            onPressed: () => _showEntryEditDialog(null),
                            textStyle: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 24, // Increased text size
                              letterSpacing: 0.5, // Better letter spacing
                            ),
                          ),
                        ),
                      ),
                    ),
                    // Error message if applicable
                    if (controller.error.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 16),
                        child: Text(
                          controller.error,
                          style: AppTextStyles.bodyMedium.copyWith(
                            color: AppColors.error,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                  ]),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  void _showEntryEditDialog(MeterEntry? entry) {
    final historyController = context.read<HistoryController>();
    EntryEditDialog.show(
      context: context,
      controller: historyController,
      entry: entry,
      onSave: (meterEntry) {
        historyController.addEntry(meterEntry);
      },
    );
  }

  // Add method to navigate to History screen with invalid entries filtered
  void _navigateToInvalidEntries(BuildContext context) {
    // Navigate to History screen
    Navigator.pushNamed(
      context,
      '/history',
      arguments: {'filterInvalidEntries': true},
    );
  }

  // Build the message banner with rotating helpful messages
  Widget _buildMessageBanner() {
    return MessageBanner(
      message: HelpfulMessages.allMessages[_currentMessageIndex],
    );
  }

  // Build the app bar with the message bar positioned directly below it
  Widget _buildAppBar() {
    return SliverAppBar(
      toolbarHeight: 96, // Fixed height of 96px for the banner
      pinned: true,
      automaticallyImplyLeading: false, // Remove back arrow
      // Use a Stack for the banner content
      flexibleSpace: SizedBox(
        height: 96, // Fixed height of 96px
        child: Stack(
          children: [
            // Background gradient
            Positioned.fill(
              child: Container(
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Color(0xFF0D47A1),
                      Color(0xFF1976D2)
                    ], // Lekky Meter banner blue
                  ),
                ),
              ),
            ),
            // Custom positioned title - aligned with Meter Total dialog box
            const Positioned(
              top: 20, // Moved up by 4px as requested
              left:
                  20, // Exactly 20px from left edge to match Meter Total dialog box
              child: Text(
                'Lekky',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 40, // Doubled font size
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            // App bar actions positioned exactly like History screen
            Positioned(
              top: 10, // Same top position as History icons
              right: 12 +
                  MediaQuery.of(context).padding.right, // Adjust for safe area
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Invalid entry indicator
                  Consumer<HomeController>(
                    builder: (context, controller, _) {
                      return controller.hasInvalidEntries
                          ? Transform.translate(
                              offset: const Offset(0,
                                  4), // Move down by 4px to align with other icons
                              child: Container(
                                margin: const EdgeInsets.only(right: 8),
                                decoration: const BoxDecoration(
                                  color: AppColors.error,
                                  shape: BoxShape.circle,
                                ),
                                child: GestureDetector(
                                  onTap: () =>
                                      _navigateToInvalidEntries(context),
                                  child: Padding(
                                    padding: const EdgeInsets.all(8.0),
                                    child: Text(
                                      '${controller.invalidEntryCount}',
                                      style: const TextStyle(
                                        color: Colors.white,
                                        fontWeight: FontWeight.bold,
                                        fontSize: 14,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            )
                          : const SizedBox.shrink();
                    },
                  ),
                  // Settings and notification buttons with consistent spacing
                  Transform.translate(
                    offset: const Offset(
                        0, 4), // Move down by 4px to align with other icons
                    child: const HomeSettingsButton(),
                  ),
                  Transform.translate(
                    offset: const Offset(
                        0, 4), // Move down by 4px to align with other icons
                    child: const HomeNotificationButton(),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      // No actions - notification button is now in the Stack
      // Position the message bar directly below the banner with no spacing
      bottom: PreferredSize(
        // Use zero height to eliminate any spacing
        preferredSize: const Size.fromHeight(0),
        child: _buildMessageBanner(),
      ),
    );
  }
}
