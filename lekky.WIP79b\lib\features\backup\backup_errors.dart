// File: lib/features/backup/backup_errors.dart
import '../../core/utils/error_handler.dart';

/// Types of errors that can occur during backup operations
enum BackupErrorType {
  /// Error reading or writing files
  fileIOError,
  
  /// Error parsing backup data
  parseError,
  
  /// Backup version mismatch
  versionMismatch,
  
  /// No data found in backup
  emptyData,
  
  /// Unknown error
  unknown,
}

/// Extension to convert BackupErrorType to ErrorType
extension BackupErrorTypeExtension on BackupErrorType {
  /// Convert to ErrorType
  ErrorType toErrorType() {
    switch (this) {
      case BackupErrorType.fileIOError:
        return ErrorType.permission;
      case BackupErrorType.parseError:
        return ErrorType.validation;
      case BackupErrorType.versionMismatch:
        return ErrorType.validation;
      case BackupErrorType.emptyData:
        return ErrorType.validation;
      case BackupErrorType.unknown:
        return ErrorType.unknown;
    }
  }
  
  /// Get error severity
  ErrorSeverity toErrorSeverity() {
    switch (this) {
      case BackupErrorType.fileIOError:
        return ErrorSeverity.medium;
      case BackupErrorType.parseError:
        return ErrorSeverity.medium;
      case BackupErrorType.versionMismatch:
        return ErrorSeverity.low;
      case BackupErrorType.emptyData:
        return ErrorSeverity.low;
      case BackupErrorType.unknown:
        return ErrorSeverity.medium;
    }
  }
}

/// Create an AppError from a BackupErrorType
AppError createBackupError(
  BackupErrorType type,
  String message, {
  dynamic details,
}) {
  return AppError(
    message: message,
    severity: type.toErrorSeverity(),
    type: type.toErrorType(),
    details: details,
  );
}
