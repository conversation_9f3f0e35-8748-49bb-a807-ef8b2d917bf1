// File: test/core/platform/timezone/timezone_adapter_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:lekky/core/platform/timezone/timezone_adapter.dart';

// Test implementation of TimezoneAdapter for testing
class TestTimezoneAdapter implements TimezoneAdapter {
  bool _initialized = false;
  String _currentTimezone = 'Europe/London';
  String? _lastKnownTimezone;
  bool _hasTimezoneChanged = false;
  bool _isInDST = false;
  double _timezoneOffset = 1.0; // +1 hour
  String _timezoneAbbreviation = 'BST';
  bool _isScheduledTimeValid = true;

  int _initializeCallCount = 0;
  int _getCurrentTimezoneCallCount = 0;
  int _getLastKnownTimezoneCallCount = 0;
  int _saveCurrentTimezoneCallCount = 0;
  int _hasTimezoneChangedCallCount = 0;
  int _convertToUtcCallCount = 0;
  int _convertToLocalCallCount = 0;
  int _isInDSTCallCount = 0;
  int _getTimezoneOffsetCallCount = 0;
  int _getIanaTimezoneNameCallCount = 0;
  int _getTimezoneAbbreviationCallCount = 0;
  int _getAdjustedDateTimeCallCount = 0;
  int _isScheduledTimeValidCallCount = 0;
  int _rescheduleAfterTimezoneChangeCallCount = 0;

  final List<Function()> _timezoneChangeCallbacks = [];

  @override
  Future<void> initialize() async {
    _initialized = true;
    _initializeCallCount++;
  }

  @override
  String getCurrentTimezone() {
    _getCurrentTimezoneCallCount++;
    return _currentTimezone;
  }

  @override
  Future<String?> getLastKnownTimezone() async {
    _getLastKnownTimezoneCallCount++;
    return _lastKnownTimezone;
  }

  @override
  Future<void> saveCurrentTimezone(String timezone) async {
    _saveCurrentTimezoneCallCount++;
    _lastKnownTimezone = _currentTimezone;
    _currentTimezone = timezone;
  }

  @override
  Future<bool> hasTimezoneChanged() async {
    _hasTimezoneChangedCallCount++;
    return _hasTimezoneChanged;
  }

  @override
  DateTime convertToUtc(DateTime localDateTime) {
    _convertToUtcCallCount++;
    // Simulate conversion to UTC by subtracting the offset
    return localDateTime.subtract(Duration(hours: _timezoneOffset.round()));
  }

  @override
  DateTime convertToLocal(DateTime utcDateTime) {
    _convertToLocalCallCount++;
    // Simulate conversion to local by adding the offset
    return utcDateTime.add(Duration(hours: _timezoneOffset.round()));
  }

  @override
  bool isInDaylightSavingTime(DateTime dateTime) {
    _isInDSTCallCount++;
    return _isInDST;
  }

  @override
  double getTimezoneOffset(DateTime dateTime) {
    _getTimezoneOffsetCallCount++;
    return _timezoneOffset;
  }

  @override
  String getIanaTimezoneName() {
    _getIanaTimezoneNameCallCount++;
    return _currentTimezone;
  }

  @override
  String getTimezoneAbbreviation(DateTime dateTime) {
    _getTimezoneAbbreviationCallCount++;
    return _timezoneAbbreviation;
  }

  @override
  void registerTimezoneChangeCallback(void Function() callback) {
    _timezoneChangeCallbacks.add(callback);
  }

  @override
  void unregisterTimezoneChangeCallback(void Function() callback) {
    _timezoneChangeCallbacks.remove(callback);
  }

  @override
  DateTime getAdjustedDateTime(DateTime original) {
    _getAdjustedDateTimeCallCount++;
    // Simulate timezone adjustment
    return original.add(const Duration(minutes: 30));
  }

  @override
  bool isScheduledTimeValid(DateTime scheduledTime) {
    _isScheduledTimeValidCallCount++;
    return _isScheduledTimeValid;
  }

  @override
  DateTime rescheduleAfterTimezoneChange(DateTime original) {
    _rescheduleAfterTimezoneChangeCallCount++;
    // Simulate rescheduling by adding 1 hour
    return original.add(const Duration(hours: 1));
  }

  // Helper methods for testing
  void setCurrentTimezone(String timezone) {
    final oldTimezone = _currentTimezone;
    _currentTimezone = timezone;

    if (oldTimezone != timezone) {
      _hasTimezoneChanged = true;
      _notifyTimezoneChangeCallbacks();
    }
  }

  void setLastKnownTimezone(String? timezone) {
    _lastKnownTimezone = timezone;
  }

  void setHasTimezoneChanged(bool changed) {
    _hasTimezoneChanged = changed;
  }

  void setIsInDST(bool isInDST) {
    _isInDST = isInDST;
  }

  void setTimezoneOffset(double offset) {
    _timezoneOffset = offset;
  }

  void setTimezoneAbbreviation(String abbreviation) {
    _timezoneAbbreviation = abbreviation;
  }

  void setIsScheduledTimeValid(bool isValid) {
    _isScheduledTimeValid = isValid;
  }

  void _notifyTimezoneChangeCallbacks() {
    for (final callback in _timezoneChangeCallbacks) {
      callback();
    }
  }

  bool isInitialized() {
    return _initialized;
  }

  int getInitializeCallCount() {
    return _initializeCallCount;
  }

  int getGetCurrentTimezoneCallCount() {
    return _getCurrentTimezoneCallCount;
  }

  int getSaveCurrentTimezoneCallCount() {
    return _saveCurrentTimezoneCallCount;
  }

  int getHasTimezoneChangedCallCount() {
    return _hasTimezoneChangedCallCount;
  }

  List<Function()> getTimezoneChangeCallbacks() {
    return _timezoneChangeCallbacks;
  }
}

void main() {
  late TestTimezoneAdapter adapter;

  setUp(() {
    adapter = TestTimezoneAdapter();
  });

  group('TimezoneAdapter', () {
    test('should initialize successfully', () async {
      // Act
      await adapter.initialize();

      // Assert
      expect(adapter.isInitialized(), true);
      expect(adapter.getInitializeCallCount(), 1);
    });

    test('should get current timezone', () {
      // Arrange
      adapter.setCurrentTimezone('America/New_York');

      // Act
      final result = adapter.getCurrentTimezone();

      // Assert
      expect(result, 'America/New_York');
      expect(adapter.getGetCurrentTimezoneCallCount(), 1);
    });

    test('should get last known timezone', () async {
      // Arrange
      adapter.setLastKnownTimezone('Europe/Paris');

      // Act
      final result = await adapter.getLastKnownTimezone();

      // Assert
      expect(result, 'Europe/Paris');
    });

    test('should save current timezone', () async {
      // Arrange
      adapter.setCurrentTimezone('Europe/London');

      // Act
      await adapter.saveCurrentTimezone('Asia/Tokyo');

      // Assert
      expect(adapter.getCurrentTimezone(), 'Asia/Tokyo');
      expect(adapter.getSaveCurrentTimezoneCallCount(), 1);
    });

    test('should check if timezone has changed', () async {
      // Arrange
      adapter.setHasTimezoneChanged(true);

      // Act
      final result = await adapter.hasTimezoneChanged();

      // Assert
      expect(result, true);
      expect(adapter.getHasTimezoneChangedCallCount(), 1);
    });

    test('should convert local time to UTC', () {
      // Arrange
      adapter.setTimezoneOffset(2.0); // +2 hours
      final localTime = DateTime(2025, 5, 5, 14, 0); // 2:00 PM local time

      // Act
      final utcTime = adapter.convertToUtc(localTime);

      // Assert
      expect(utcTime.hour, 12); // 12:00 PM UTC (2:00 PM - 2 hours)
    });

    test('should convert UTC time to local', () {
      // Arrange
      adapter.setTimezoneOffset(2.0); // +2 hours
      final utcTime = DateTime.utc(2025, 5, 5, 12, 0); // 12:00 PM UTC

      // Act
      final localTime = adapter.convertToLocal(utcTime);

      // Assert
      expect(localTime.hour, 14); // 2:00 PM local time (12:00 PM + 2 hours)
    });

    test('should check if date is in DST', () {
      // Arrange
      adapter.setIsInDST(true);
      final date = DateTime(2025, 7, 1); // Summer, likely DST

      // Act
      final result = adapter.isInDaylightSavingTime(date);

      // Assert
      expect(result, true);
    });

    test('should get timezone offset', () {
      // Arrange
      adapter.setTimezoneOffset(1.5); // +1.5 hours
      final date = DateTime(2025, 5, 5);

      // Act
      final result = adapter.getTimezoneOffset(date);

      // Assert
      expect(result, 1.5);
    });

    test('should get IANA timezone name', () {
      // Arrange
      adapter.setCurrentTimezone('Europe/Berlin');

      // Act
      final result = adapter.getIanaTimezoneName();

      // Assert
      expect(result, 'Europe/Berlin');
    });

    test('should get timezone abbreviation', () {
      // Arrange
      adapter.setTimezoneAbbreviation('CEST');
      final date = DateTime(2025, 7, 1);

      // Act
      final result = adapter.getTimezoneAbbreviation(date);

      // Assert
      expect(result, 'CEST');
    });

    test('should register and notify timezone change callbacks', () {
      // Arrange
      bool callbackCalled = false;
      adapter.registerTimezoneChangeCallback(() {
        callbackCalled = true;
      });

      // Act
      adapter.setCurrentTimezone('Asia/Singapore'); // This triggers callbacks

      // Assert
      expect(callbackCalled, true);
      expect(adapter.getTimezoneChangeCallbacks().length, 1);
    });

    test('should unregister timezone change callbacks', () {
      // Arrange
      final callback = () {};
      adapter.registerTimezoneChangeCallback(callback);
      expect(adapter.getTimezoneChangeCallbacks().length, 1);

      // Act
      adapter.unregisterTimezoneChangeCallback(callback);

      // Assert
      expect(adapter.getTimezoneChangeCallbacks().length, 0);
    });

    test('should adjust datetime for timezone changes', () {
      // Arrange
      final originalTime = DateTime(2025, 5, 5, 10, 0);

      // Act
      final adjustedTime = adapter.getAdjustedDateTime(originalTime);

      // Assert
      expect(adjustedTime, isNot(equals(originalTime)));
      expect(adjustedTime.difference(originalTime).inMinutes, 30);
    });

    test('should validate scheduled time', () {
      // Arrange
      adapter.setIsScheduledTimeValid(true);
      final scheduledTime = DateTime(2025, 5, 5, 10, 0);

      // Act
      final result = adapter.isScheduledTimeValid(scheduledTime);

      // Assert
      expect(result, true);
    });

    test('should reschedule after timezone change', () {
      // Arrange
      final originalTime = DateTime(2025, 5, 5, 10, 0);

      // Act
      final rescheduledTime =
          adapter.rescheduleAfterTimezoneChange(originalTime);

      // Assert
      expect(rescheduledTime.difference(originalTime).inHours, 1);
    });
  });
}
