// File: test/core/data/database/db_helper_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:lekky/core/data/database/db_helper.dart';
import 'package:lekky/core/models/meter_entry.dart';
import 'package:shared_preferences/shared_preferences.dart';

void main() {
  group('DBHelper', () {
    late DBHelper dbHelper;
    
    setUp(() async {
      // Set up SharedPreferences for testing
      SharedPreferences.setMockInitialValues({});
      
      // Create a new instance of DBHelper
      dbHelper = DBHelper();
      await dbHelper.init();
    });
    
    test('bulkImportEntries should add entries and calculate averages', () async {
      // Create test entries
      final entries = [
        MeterEntry(
          reading: 100.0,
          amountToppedUp: 0.0,
          timestamp: DateTime(2023, 1, 1),
          shortAverageAfterTopUp: null,
          totalAverageUpToThisPoint: null,
        ),
        MeterEntry(
          reading: 0.0,
          amountToppedUp: 50.0,
          timestamp: DateTime(2023, 1, 2),
          shortAverageAfterTopUp: null,
          totalAverageUpToThisPoint: null,
        ),
        MeterEntry(
          reading: 80.0,
          amountToppedUp: 0.0,
          timestamp: DateTime(2023, 1, 3),
          shortAverageAfterTopUp: null,
          totalAverageUpToThisPoint: null,
        ),
      ];
      
      // Import entries
      await dbHelper.bulkImportEntries(entries);
      
      // Get all entries
      final allEntries = await dbHelper.getAllEntries();
      
      // Verify that entries were added
      expect(allEntries.length, equals(3));
      
      // Verify that entries are sorted by timestamp
      expect(allEntries[0].timestamp, equals(DateTime(2023, 1, 1)));
      expect(allEntries[1].timestamp, equals(DateTime(2023, 1, 2)));
      expect(allEntries[2].timestamp, equals(DateTime(2023, 1, 3)));
      
      // Verify that averages were calculated
      expect(allEntries[2].shortAverageAfterTopUp, isNotNull);
      expect(allEntries[2].totalAverageUpToThisPoint, isNotNull);
    });
    
    test('bulkImportEntries should replace existing entries when replace is true', () async {
      // Add initial entries
      await dbHelper.insertMeterEntry(
        MeterEntry(
          reading: 100.0,
          amountToppedUp: 0.0,
          timestamp: DateTime(2023, 1, 1),
          shortAverageAfterTopUp: null,
          totalAverageUpToThisPoint: null,
        ),
      );
      
      // Create new entries
      final entries = [
        MeterEntry(
          reading: 80.0,
          amountToppedUp: 0.0,
          timestamp: DateTime(2023, 2, 1),
          shortAverageAfterTopUp: null,
          totalAverageUpToThisPoint: null,
        ),
        MeterEntry(
          reading: 0.0,
          amountToppedUp: 50.0,
          timestamp: DateTime(2023, 2, 2),
          shortAverageAfterTopUp: null,
          totalAverageUpToThisPoint: null,
        ),
      ];
      
      // Import entries with replace=true
      await dbHelper.bulkImportEntries(entries, replace: true);
      
      // Get all entries
      final allEntries = await dbHelper.getAllEntries();
      
      // Verify that old entries were replaced
      expect(allEntries.length, equals(2));
      
      // Verify that only new entries exist
      expect(allEntries[0].timestamp, equals(DateTime(2023, 2, 1)));
      expect(allEntries[1].timestamp, equals(DateTime(2023, 2, 2)));
    });
    
    test('bulkImportEntries should append to existing entries when replace is false', () async {
      // Add initial entries
      await dbHelper.insertMeterEntry(
        MeterEntry(
          reading: 100.0,
          amountToppedUp: 0.0,
          timestamp: DateTime(2023, 1, 1),
          shortAverageAfterTopUp: null,
          totalAverageUpToThisPoint: null,
        ),
      );
      
      // Create new entries
      final entries = [
        MeterEntry(
          reading: 80.0,
          amountToppedUp: 0.0,
          timestamp: DateTime(2023, 2, 1),
          shortAverageAfterTopUp: null,
          totalAverageUpToThisPoint: null,
        ),
        MeterEntry(
          reading: 0.0,
          amountToppedUp: 50.0,
          timestamp: DateTime(2023, 2, 2),
          shortAverageAfterTopUp: null,
          totalAverageUpToThisPoint: null,
        ),
      ];
      
      // Import entries with replace=false
      await dbHelper.bulkImportEntries(entries, replace: false);
      
      // Get all entries
      final allEntries = await dbHelper.getAllEntries();
      
      // Verify that old entries were kept
      expect(allEntries.length, equals(3));
      
      // Verify that entries are sorted by timestamp
      expect(allEntries[0].timestamp, equals(DateTime(2023, 1, 1)));
      expect(allEntries[1].timestamp, equals(DateTime(2023, 2, 1)));
      expect(allEntries[2].timestamp, equals(DateTime(2023, 2, 2)));
    });
    
    test('bulkImportEntries should handle progress updates', () async {
      // Create test entries
      final entries = [
        MeterEntry(
          reading: 100.0,
          amountToppedUp: 0.0,
          timestamp: DateTime(2023, 1, 1),
          shortAverageAfterTopUp: null,
          totalAverageUpToThisPoint: null,
        ),
        MeterEntry(
          reading: 0.0,
          amountToppedUp: 50.0,
          timestamp: DateTime(2023, 1, 2),
          shortAverageAfterTopUp: null,
          totalAverageUpToThisPoint: null,
        ),
        MeterEntry(
          reading: 80.0,
          amountToppedUp: 0.0,
          timestamp: DateTime(2023, 1, 3),
          shortAverageAfterTopUp: null,
          totalAverageUpToThisPoint: null,
        ),
      ];
      
      // Track progress updates
      final progressUpdates = <double>[];
      
      // Import entries with progress tracking
      await dbHelper.bulkImportEntries(
        entries,
        onProgress: (progress) {
          progressUpdates.add(progress);
        },
      );
      
      // Verify that progress updates were received
      expect(progressUpdates, isNotEmpty);
      
      // Verify that progress updates start at 0.0 and end at 1.0
      expect(progressUpdates.first, equals(0.0));
      expect(progressUpdates.last, equals(1.0));
    });
  });
}
