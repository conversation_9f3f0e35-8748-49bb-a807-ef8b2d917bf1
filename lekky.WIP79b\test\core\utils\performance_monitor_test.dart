// File: test/core/utils/performance_monitor_test.dart

import 'dart:async';
import 'package:flutter_test/flutter_test.dart';
import 'package:lekky/core/utils/performance_monitor.dart';

void main() {
  setUp(() {
    // Reset performance monitor before each test
    PerformanceMonitor.clearMetrics();
  });

  group('PerformanceMonitor', () {
    test('should initialize with default settings', () {
      // Act
      PerformanceMonitor.initialize();

      // Assert - should not throw
    });

    test('should track metrics', () {
      // Arrange
      PerformanceMonitor.initialize();

      // Act
      PerformanceMonitor.trackMetric(
        name: 'test_metric',
        type: MetricType.custom,
        value: 42.0,
        unit: 'units',
      );

      // Assert
      final metrics = PerformanceMonitor.getMetricsByName('test_metric');
      expect(metrics.length, 1);
      expect(metrics[0].name, 'test_metric');
      expect(metrics[0].type, MetricType.custom);
      expect(metrics[0].value, 42.0);
      expect(metrics[0].unit, 'units');
    });

    test('should track metrics with tags', () {
      // Arrange
      PerformanceMonitor.initialize();

      // Act
      PerformanceMonitor.trackMetric(
        name: 'test_metric',
        type: MetricType.custom,
        value: 42.0,
        unit: 'units',
        tags: {'tag1': 'value1', 'tag2': 'value2'},
      );

      // Assert
      final metrics = PerformanceMonitor.getMetricsByName('test_metric');
      expect(metrics.length, 1);
      expect(metrics[0].tags!['tag1'], 'value1');
      expect(metrics[0].tags!['tag2'], 'value2');
    });

    test('should get metrics by type', () {
      // Arrange
      PerformanceMonitor.initialize();

      // Act
      PerformanceMonitor.trackMetric(
        name: 'test_metric1',
        type: MetricType.latency,
        value: 100.0,
        unit: 'ms',
      );

      PerformanceMonitor.trackMetric(
        name: 'test_metric2',
        type: MetricType.memory,
        value: 50.0,
        unit: 'MB',
      );

      PerformanceMonitor.trackMetric(
        name: 'test_metric3',
        type: MetricType.latency,
        value: 200.0,
        unit: 'ms',
      );

      // Assert
      final latencyMetrics =
          PerformanceMonitor.getMetricsByType(MetricType.latency);
      expect(latencyMetrics.length, 2);
      expect(latencyMetrics[0].name, 'test_metric3'); // Newest first
      expect(latencyMetrics[1].name, 'test_metric1');

      final memoryMetrics =
          PerformanceMonitor.getMetricsByType(MetricType.memory);
      expect(memoryMetrics.length, 1);
      expect(memoryMetrics[0].name, 'test_metric2');
    });

    test('should track operation duration', () async {
      // Arrange
      PerformanceMonitor.initialize();

      // Act
      PerformanceMonitor.startOperation('test_operation');
      await Future.delayed(Duration(milliseconds: 50));
      PerformanceMonitor.endOperation('test_operation');

      // Assert
      final metrics = PerformanceMonitor.getMetricsByName('test_operation');
      expect(metrics.length, 1);
      expect(metrics[0].type, MetricType.latency);
      expect(metrics[0].unit, 'ms');
      expect(metrics[0].value, greaterThanOrEqualTo(50.0));
    });

    test('should track operation with tags', () async {
      // Arrange
      PerformanceMonitor.initialize();
      final tags = {'component': 'test', 'priority': 'high'};

      // Act
      PerformanceMonitor.startOperation('test_operation', tags: tags);
      await Future.delayed(Duration(milliseconds: 50));
      PerformanceMonitor.endOperation('test_operation', tags: tags);

      // Assert
      final metrics = PerformanceMonitor.getMetricsByName('test_operation');
      expect(metrics.length, 1);
      expect(metrics[0].tags!['component'], 'test');
      expect(metrics[0].tags!['priority'], 'high');
    });

    test('should track async function duration', () async {
      // Arrange
      PerformanceMonitor.initialize();

      // Act
      await PerformanceMonitor.trackFunction('async_function', () async {
        await Future.delayed(Duration(milliseconds: 50));
        return 'result';
      });

      // Assert
      final metrics = PerformanceMonitor.getMetricsByName('async_function');
      expect(metrics.length, 1);
      expect(metrics[0].type, MetricType.latency);
      expect(metrics[0].value, greaterThanOrEqualTo(50.0));
    });

    test('should track sync function duration', () {
      // Arrange
      PerformanceMonitor.initialize();

      // Act
      PerformanceMonitor.trackSyncFunction('sync_function', () {
        // Simulate work
        int sum = 0;
        for (int i = 0; i < 1000000; i++) {
          sum += i;
        }
        return sum;
      });

      // Assert
      final metrics = PerformanceMonitor.getMetricsByName('sync_function');
      expect(metrics.length, 1);
      expect(metrics[0].type, MetricType.latency);
      expect(metrics[0].value, greaterThan(0.0));
    });

    test('should calculate statistics for metrics', () {
      // Arrange
      PerformanceMonitor.initialize();

      // Act - add several metrics with the same name
      for (int i = 1; i <= 10; i++) {
        PerformanceMonitor.trackMetric(
          name: 'stats_test',
          type: MetricType.latency,
          value: i * 10.0, // 10, 20, 30, ..., 100
          unit: 'ms',
        );
      }

      // Assert
      final stats = PerformanceMonitor.getMetricStatistics('stats_test');
      expect(stats, isNotNull);
      expect(stats!.name, 'stats_test');
      expect(stats.type, MetricType.latency);
      expect(stats.unit, 'ms');
      expect(stats.min, 10.0);
      expect(stats.max, 100.0);
      expect(stats.average, 55.0);
      expect(stats.median, 55.0);
      expect(stats.p95, 95.0);
      expect(stats.p99, 99.0);
      expect(stats.count, 10);
    });

    test('should get all metrics', () {
      // Arrange
      PerformanceMonitor.initialize();

      // Act
      PerformanceMonitor.trackMetric(
        name: 'metric1',
        type: MetricType.latency,
        value: 10.0,
        unit: 'ms',
      );

      PerformanceMonitor.trackMetric(
        name: 'metric2',
        type: MetricType.memory,
        value: 20.0,
        unit: 'MB',
      );

      // Assert
      final allMetrics = PerformanceMonitor.getAllMetrics();
      expect(allMetrics.length, 2);
    });

    test('should clear metrics', () {
      // Arrange
      PerformanceMonitor.initialize();

      PerformanceMonitor.trackMetric(
        name: 'metric1',
        type: MetricType.latency,
        value: 10.0,
        unit: 'ms',
      );

      PerformanceMonitor.trackMetric(
        name: 'metric2',
        type: MetricType.memory,
        value: 20.0,
        unit: 'MB',
      );

      // Act
      PerformanceMonitor.clearMetrics();

      // Assert
      final allMetrics = PerformanceMonitor.getAllMetrics();
      expect(allMetrics.length, 0);
    });

    test('should clear metrics of specific type', () {
      // Arrange
      PerformanceMonitor.initialize();

      PerformanceMonitor.trackMetric(
        name: 'metric1',
        type: MetricType.latency,
        value: 10.0,
        unit: 'ms',
      );

      PerformanceMonitor.trackMetric(
        name: 'metric2',
        type: MetricType.memory,
        value: 20.0,
        unit: 'MB',
      );

      // Act
      PerformanceMonitor.clearMetricsOfType(MetricType.latency);

      // Assert
      final latencyMetrics =
          PerformanceMonitor.getMetricsByType(MetricType.latency);
      expect(latencyMetrics.length, 0);

      final memoryMetrics =
          PerformanceMonitor.getMetricsByType(MetricType.memory);
      expect(memoryMetrics.length, 1);
    });

    test('should generate performance report', () {
      // Arrange
      PerformanceMonitor.initialize();

      PerformanceMonitor.trackMetric(
        name: 'metric1',
        type: MetricType.latency,
        value: 10.0,
        unit: 'ms',
      );

      PerformanceMonitor.trackMetric(
        name: 'metric2',
        type: MetricType.memory,
        value: 20.0,
        unit: 'MB',
      );

      // Act
      final report = PerformanceMonitor.getReport();

      // Assert
      expect(report, isNotNull);
      expect(report['metrics_count'], 2);
      expect(report['metrics_by_type'], isNotNull);
      expect(report['statistics'], isNotNull);
    });

    test('should stream metrics as they are tracked', () async {
      // Arrange
      PerformanceMonitor.initialize();
      final streamedMetrics = <PerformanceMetric>[];

      // Subscribe to the metric stream
      final subscription = PerformanceMonitor.metricStream.listen((metric) {
        streamedMetrics.add(metric);
      });

      // Act
      PerformanceMonitor.trackMetric(
        name: 'stream_test',
        type: MetricType.custom,
        value: 42.0,
        unit: 'units',
      );

      // Wait for stream to process
      await Future.delayed(Duration(milliseconds: 50));

      // Assert
      expect(streamedMetrics.length, 1);
      expect(streamedMetrics[0].name, 'stream_test');

      // Clean up
      await subscription.cancel();
    });

    test('should track active operations', () async {
      // Arrange
      PerformanceMonitor.initialize();

      // Act
      PerformanceMonitor.startOperation('operation1');
      PerformanceMonitor.startOperation('operation2');

      // Assert
      final activeOperations = PerformanceMonitor.getActiveOperations();
      expect(activeOperations.length, 2);
      expect(activeOperations.containsKey('operation1'), true);
      expect(activeOperations.containsKey('operation2'), true);

      // Clean up
      PerformanceMonitor.endOperation('operation1');
      PerformanceMonitor.endOperation('operation2');
    });

    test('should use Future extension for tracking', () async {
      // Arrange
      PerformanceMonitor.initialize();

      // Act
      await Future.delayed(Duration(milliseconds: 50))
          .trackPerformance('future_extension');

      // Assert
      final metrics = PerformanceMonitor.getMetricsByName('future_extension');
      expect(metrics.length, 1);
      expect(metrics[0].type, MetricType.latency);
      expect(metrics[0].value, greaterThanOrEqualTo(50.0));
    });
  });
}
