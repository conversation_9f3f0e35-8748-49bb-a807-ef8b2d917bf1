// AG working out of ShortAverages for HIstory Table in Lekky

//Database
//	Date	EntryType	Amount	ShortAverage	TotalAverage
//	1		MR			7.35
//	2		TU			150
//	3		MR			155.09
//	4		MR			144.6
//	5		MR			136.11
//	6		TU			150
//	7		MR			180.5
//	8		MR			160.23
//	9		MR			142.02


// MR = Meter Reading
// TU = Top Up


Sort Rows by Datestamp

ShortAveragesFunction

// Process Entries from oldest timestamp to newest timestamp

TotalEntryRows = Number of entries in Table (excluding Header)

Row = 1
TotalTopUpCount = 0
LastMeterReadingValue = 0

If TotalEntryRows = 0
	"No Entries in History Table"
	End ShortAveragesFunction
	
If TotalEntryRows = 1
	ShortAverage(Row) = "N/A"
	End ShortAveragesFunction

For Row = 2 to TotalEntryRows

	LastEntryType = EntryType(Row-1)
	If LastEntryType = MR then LastMeterReadingValue Amount(Row-1)
		TotalTopUpCount = 0
	If LastEntryType = TU then TotalTopUpCount = TotalTopUpCount + Amount(Row-1)
	
	NewEntryType = EntryType(Row)
	
	If NewEntryType = TU then 
		ShortAverage(Row) = "N/A"
		next Row
	
	If NewEntryType = MR and LastMeterReadingValue = 0
		LastMeterReadingValue = Amount(Row)
		ShortAverage(Row) = "N/A"
		next Row

	NewMeterReadingValue = Amount(Row)
	
	DaysBetweenReadings = Date(Row) - Date(Row-1)
	
	ShortAverage(Row) = ((LastMeterReadingValue + TotalTopUpCount)-NewMeterReadingValue) / DaysBetweenReadings
	
Next Row

End ShortAveragesFunction