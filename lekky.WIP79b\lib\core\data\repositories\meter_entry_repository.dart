// File: lib/core/data/repositories/meter_entry_repository.dart
import '../../models/meter_entry.dart';
import '../../providers/notification_provider.dart';
import '../../utils/error_handler.dart';
import '../../utils/logger.dart';
import '../database/db_helper.dart';
import '../database/db_optimizer.dart';
import '../../../features/history/domain/models/related_validation_result.dart';

/// Repository for meter entries
class MeterEntryRepository {
  final DBHelper _dbHelper;

  MeterEntryRepository({DBHelper? dbHelper})
      : _dbHelper = dbHelper ?? DBHelper() {
    // Initialize validation results - this needs to be done immediately
    _initializeValidationResults();
  }

  /// Initialize validation results
  Future<void> _initializeValidationResults() async {
    try {
      logger.i('MeterEntryRepository: Initializing validation results');
      _validationResults = await validateAllEntries();
      logger.i(
          'MeterEntryRepository: Validation results initialized with ${_validationResults.length} entries');
    } catch (e) {
      logger.e('Failed to initialize validation results',
          details: e.toString());
      // Initialize with empty map to avoid null errors
      _validationResults = {};
    }
  }

  /// Get all meter entries
  Future<List<MeterEntry>> getAllEntries() async {
    try {
      return await _dbHelper.getMeterEntries();
    } catch (e) {
      ErrorHandler.addError(AppError.database(
        'Failed to get meter entries',
        details: e.toString(),
      ));
      return [];
    }
  }

  /// Get all meter entries with caching
  Future<List<MeterEntry>> getAllEntriesWithCache() async {
    return await DBOptimizer.getCachedMeterEntries(_dbHelper);
  }

  /// Add a new meter entry
  Future<bool> addEntry(MeterEntry entry) async {
    try {
      await _dbHelper.insertMeterEntry(entry);
      // Ensure cache is invalidated after adding an entry
      DBOptimizer.invalidateCache();
      // Update validation results
      await updateValidationResults();

      // Check if this is a meter reading (not a top-up)
      if (entry.reading > 0 && entry.amountToppedUp == 0) {
        // Update notification system to indicate user has meter readings
        final notificationProvider = NotificationProvider();
        await notificationProvider.setHasMeterReadings(true);

        // Get count of meter readings
        final meterReadingCount = await getMeterReadingCount();

        // If this is the first meter reading, show notification system activation
        if (meterReadingCount == 1) {
          await notificationProvider.showNotificationSystemActivation();
        }
      }

      logger.i('MeterEntryRepository: Added entry and invalidated cache');
      return true;
    } catch (e) {
      ErrorHandler.addError(AppError.database(
        'Failed to add meter entry',
        details: e.toString(),
      ));
      return false;
    }
  }

  /// Add multiple meter entries in bulk with optimized processing
  ///
  /// This method is optimized for adding large numbers of entries at once.
  /// It uses the bulkImportEntries method of DBHelper for efficient processing.
  ///
  /// @param entries The list of MeterEntry objects to add
  /// @param replace Whether to replace existing entries (true) or append (false)
  /// @param onProgress Optional callback for progress updates (0.0 to 1.0)
  Future<bool> bulkAddEntries(
    List<MeterEntry> entries, {
    bool replace = false,
    Function(double progress)? onProgress,
  }) async {
    try {
      logger.i(
          'MeterEntryRepository: Starting bulk add of ${entries.length} entries (replace: $replace)');

      // Report initial progress
      if (onProgress != null) {
        onProgress(0.0);
      }

      // Use the optimized bulk import method
      await _dbHelper.bulkImportEntries(
        entries,
        replace: replace,
        onProgress: (progress) {
          // Forward progress updates
          if (onProgress != null) {
            onProgress(progress * 0.8); // Scale to 80% for database operations
          }
        },
      );

      // Report progress
      if (onProgress != null) {
        onProgress(0.8); // 80% progress after database operations
      }

      // Ensure cache is invalidated after adding entries
      DBOptimizer.invalidateCache();
      logger.d('MeterEntryRepository: Invalidated cache after bulk add');

      // Report progress
      if (onProgress != null) {
        onProgress(0.9); // 90% progress after invalidating cache
      }

      // Update validation results
      await updateValidationResults();
      logger
          .d('MeterEntryRepository: Updated validation results after bulk add');

      // Check if there are any meter readings (not top-ups)
      bool hasMeterReadings =
          entries.any((e) => e.reading > 0 && e.amountToppedUp == 0);

      if (hasMeterReadings) {
        // Update notification system to indicate user has meter readings
        final notificationProvider = NotificationProvider();
        await notificationProvider.setHasMeterReadings(true);

        // Get count of meter readings
        final meterReadingCount = await getMeterReadingCount();

        // If this is the first meter reading, show notification system activation
        if (meterReadingCount ==
            entries
                .where((e) => e.reading > 0 && e.amountToppedUp == 0)
                .length) {
          await notificationProvider.showNotificationSystemActivation();
        }
      }

      // Report completion
      if (onProgress != null) {
        onProgress(1.0); // 100% progress after completion
      }

      logger.i(
          'MeterEntryRepository: Completed bulk add of ${entries.length} entries');
      return true;
    } catch (e) {
      ErrorHandler.addError(AppError.database(
        'Failed to bulk add meter entries',
        details: e.toString(),
      ));
      return false;
    }
  }

  /// Delete a meter entry
  Future<bool> deleteEntry(int id) async {
    try {
      await _dbHelper.deleteMeterEntry(id);
      // Ensure cache is invalidated after deleting an entry
      DBOptimizer.invalidateCache();
      // Update validation results
      await updateValidationResults();
      logger.i('MeterEntryRepository: Deleted entry and invalidated cache');
      return true;
    } catch (e) {
      logger.e('Failed to delete meter entry', details: e.toString());
      return false;
    }
  }

  /// Delete all meter entries
  Future<bool> deleteAllEntries() async {
    try {
      await _dbHelper.deleteAllMeterEntries();
      // Ensure cache is invalidated after deleting all entries
      DBOptimizer.invalidateCache();
      // Update validation results
      await updateValidationResults();
      logger
          .i('MeterEntryRepository: Deleted all entries and invalidated cache');
      return true;
    } catch (e) {
      logger.e('Failed to delete all meter entries', details: e.toString());
      return false;
    }
  }

  /// Calculate the current meter total
  Future<double> calculateMeterTotal() async {
    try {
      return await _dbHelper.calculateMeterTotal();
    } catch (e) {
      ErrorHandler.addError(AppError.database(
        'Failed to calculate meter total',
        details: e.toString(),
      ));
      return 0.0;
    }
  }

  /// Calculate the current meter total with caching
  Future<double> calculateMeterTotalWithCache() async {
    try {
      return await DBOptimizer.getCachedMeterTotal(_dbHelper);
    } catch (e) {
      ErrorHandler.addError(AppError.database(
        'Failed to calculate meter total with cache',
        details: e.toString(),
      ));
      return 0.0;
    }
  }

  /// Validate a meter reading
  Future<Map<String, dynamic>> validateMeterReading(
      double reading, DateTime date,
      {int? entryId}) async {
    try {
      return await _dbHelper.validateMeterReading(reading, date,
          entryId: entryId);
    } catch (e) {
      ErrorHandler.addError(AppError.validation(
        'Failed to validate meter reading',
        details: e.toString(),
      ));
      return const RelatedValidationResult(
        isValid: false,
        errorMessage: 'An error occurred while validating the meter reading',
        severity: 'error',
      ).toMap();
    }
  }

  /// Validate a meter entry against all other entries
  /// Returns a map of entry IDs to validation results
  Future<Map<int, Map<String, dynamic>>> validateAllEntries() async {
    try {
      final entries = await getAllEntries();
      final Map<int, Map<String, dynamic>> results = {};

      // First pass: validate each entry individually
      for (final entry in entries) {
        if (entry.id != null && entry.amountToppedUp == 0) {
          results[entry.id!] = await validateMeterReading(
            entry.reading,
            entry.timestamp,
            entryId: entry.id,
          );
        } else if (entry.id != null && entry.amountToppedUp > 0) {
          // Top-ups are always valid
          results[entry.id!] = RelatedValidationResult.valid().toMap();
        }
      }

      return results;
    } catch (e) {
      ErrorHandler.addError(AppError.validation(
        'Failed to validate all entries',
        details: e.toString(),
      ));
      return {};
    }
  }

  /// Optimize the database
  Future<bool> optimizeDatabase() async {
    try {
      if (await DBOptimizer.needsOptimization(_dbHelper)) {
        await DBOptimizer.compactDatabase(_dbHelper);
      }
      return true;
    } catch (e) {
      ErrorHandler.addError(AppError.database(
        'Failed to optimize database',
        details: e.toString(),
      ));
      return false;
    }
  }

  // Cache for validation results to avoid recalculating
  Map<int, Map<String, dynamic>> _validationResults = {};

  /// Check if there are any invalid entries
  bool hasInvalidEntries() {
    // Check the validation results
    final validationResults = _validationResults.values;
    return validationResults.any((result) =>
        result['isValid'] == false ||
        (result['relatedEntryId'] != null && result['isValid'] == false));
  }

  /// Get the count of invalid entries
  int getInvalidEntryCount() {
    // Create a set of all entry IDs that are either directly invalid or related to invalid entries
    final Set<int> allInvalidEntryIds = {};

    // Add directly invalid entries
    for (final entry in _validationResults.entries) {
      if (entry.value['isValid'] == false) {
        allInvalidEntryIds.add(entry.key);
      }
    }

    // Add related entries
    for (final result in _validationResults.values) {
      if (result['isValid'] == false && result['relatedEntryId'] != null) {
        allInvalidEntryIds.add(result['relatedEntryId'] as int);
      }
    }

    return allInvalidEntryIds.length;
  }

  /// Update validation results cache
  Future<void> updateValidationResults() async {
    _validationResults = await validateAllEntries();
  }

  /// Get the latest meter reading
  Future<MeterEntry?> getLatestMeterReading() async {
    try {
      final entries = await getAllEntries();
      if (entries.isEmpty) {
        return null;
      }

      // Filter to only include entries with readings (not top-ups)
      final readingEntries = entries.where((e) => e.reading > 0).toList();
      if (readingEntries.isEmpty) {
        return null;
      }

      // Sort by date, newest first
      readingEntries.sort((a, b) => b.timestamp.compareTo(a.timestamp));
      return readingEntries.first;
    } catch (e) {
      ErrorHandler.addError(AppError.database(
        'Failed to get latest meter reading',
        details: e.toString(),
      ));
      return null;
    }
  }

  /// Calculate the average usage per day
  Future<double> getAverageUsage() async {
    try {
      final entries = await getAllEntries();
      if (entries.isEmpty) {
        return 0.0;
      }

      // Filter to only include entries with readings (not top-ups)
      final readingEntries = entries.where((e) => e.reading > 0).toList();
      if (readingEntries.length < 2) {
        return 0.0;
      }

      // Sort by date, oldest first
      readingEntries.sort((a, b) => a.timestamp.compareTo(b.timestamp));

      // Calculate total usage
      double totalUsage = 0.0;
      for (int i = 0; i < readingEntries.length - 1; i++) {
        final current = readingEntries[i];
        final next = readingEntries[i + 1];
        final usage = current.reading - next.reading;
        if (usage > 0) {
          totalUsage += usage;
        }
      }

      // Calculate total days
      final firstDate = readingEntries.first.timestamp;
      final lastDate = readingEntries.last.timestamp;
      final days = lastDate.difference(firstDate).inDays;

      if (days <= 0) {
        return 0.0;
      }

      return totalUsage / days;
    } catch (e) {
      ErrorHandler.addError(AppError.database(
        'Failed to calculate average usage',
        details: e.toString(),
      ));
      return 0.0;
    }
  }

  /// Get the count of meter readings (not top-ups)
  Future<int> getMeterReadingCount() async {
    try {
      final entries = await getAllEntries();
      if (entries.isEmpty) {
        return 0;
      }

      // Filter to only include entries with readings (not top-ups)
      final readingEntries =
          entries.where((e) => e.reading > 0 && e.amountToppedUp == 0).toList();
      return readingEntries.length;
    } catch (e) {
      ErrorHandler.addError(AppError.database(
        'Failed to get meter reading count',
        details: e.toString(),
      ));
      return 0;
    }
  }
}
