// File: test/core/platform/notification/notification_adapter_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:lekky/core/platform/notification/notification_adapter.dart';

// Test implementation of NotificationAdapter for testing
class TestNotificationAdapter implements NotificationAdapter {
  bool _initialized = false;
  String _currentTimeZone = 'Europe/London';
  bool _permissionsGranted = true;
  bool _permissionPermanentlyDenied = false;
  bool _canScheduleExactAlarms = true;
  bool _timeZoneChanged = false;
  final Map<String, dynamic> _channelSettings = {'importance': 'high'};
  final List<int> _canceledNotificationIds = [];
  bool _allNotificationsCanceled = false;
  final List<Map<String, dynamic>> _scheduledNotifications = [];
  int _rescheduleAllCount = 0;
  int _rescheduleCount = 0;

  @override
  Future<void> initialize() async {
    _initialized = true;
  }

  @override
  Future<bool> checkNotificationPermissions() async {
    return _permissionsGranted;
  }

  @override
  Future<bool> requestNotificationPermissions() async {
    return _permissionsGranted;
  }

  @override
  Future<bool> isNotificationPermissionPermanentlyDenied() async {
    return _permissionPermanentlyDenied;
  }

  @override
  Future<bool> openNotificationSettings() async {
    return true;
  }

  @override
  Future<void> showNotification({
    required int id,
    required String title,
    required String body,
    String? payload,
  }) async {
    _scheduledNotifications.add({
      'id': id,
      'title': title,
      'body': body,
      'payload': payload,
      'immediate': true,
    });
  }

  @override
  Future<bool> scheduleNotification({
    required int id,
    required String title,
    required String body,
    required DateTime scheduledDate,
    String? payload,
    int maxRetries = 3,
  }) async {
    _scheduledNotifications.add({
      'id': id,
      'title': title,
      'body': body,
      'scheduledDate': scheduledDate,
      'payload': payload,
      'maxRetries': maxRetries,
      'immediate': false,
    });
    return true;
  }

  @override
  Future<bool> rescheduleNotification(int id) async {
    _rescheduleCount++;
    return true;
  }

  @override
  Future<int> rescheduleAllNotifications() async {
    _rescheduleAllCount++;
    return _scheduledNotifications.length;
  }

  @override
  Future<void> cancelNotification(int id) async {
    _canceledNotificationIds.add(id);
  }

  @override
  Future<void> cancelAllNotifications() async {
    _allNotificationsCanceled = true;
    _canceledNotificationIds.clear();
  }

  @override
  Future<void> createNotificationChannels() async {
    // No-op for test
  }

  @override
  Future<bool> hasTimeZoneChanged() async {
    return _timeZoneChanged;
  }

  @override
  String getCurrentTimeZone() {
    return _currentTimeZone;
  }

  @override
  Future<Map<String, dynamic>> getChannelSettings(String channelId) async {
    return _channelSettings;
  }

  @override
  Future<bool> canScheduleExactAlarms() async {
    return _canScheduleExactAlarms;
  }

  @override
  Future<void> requestExactAlarmPermission() async {
    _canScheduleExactAlarms = true;
  }

  // Helper methods for testing
  void setPermissionsGranted(bool granted) {
    _permissionsGranted = granted;
  }

  void setPermissionPermanentlyDenied(bool denied) {
    _permissionPermanentlyDenied = denied;
  }

  void setTimeZone(String timeZone) {
    _currentTimeZone = timeZone;
  }

  void setTimeZoneChanged(bool changed) {
    _timeZoneChanged = changed;
  }

  void setCanScheduleExactAlarms(bool canSchedule) {
    _canScheduleExactAlarms = canSchedule;
  }

  List<int> getCanceledNotificationIds() {
    return _canceledNotificationIds;
  }

  bool getAllNotificationsCanceled() {
    return _allNotificationsCanceled;
  }

  List<Map<String, dynamic>> getScheduledNotifications() {
    return _scheduledNotifications;
  }

  int getRescheduleAllCount() {
    return _rescheduleAllCount;
  }

  int getRescheduleCount() {
    return _rescheduleCount;
  }

  bool isInitialized() {
    return _initialized;
  }
}

void main() {
  late TestNotificationAdapter adapter;

  setUp(() {
    adapter = TestNotificationAdapter();
  });

  group('NotificationAdapter', () {
    test('should initialize successfully', () async {
      // Act
      await adapter.initialize();

      // Assert
      expect(adapter.isInitialized(), true);
    });

    test('should check notification permissions', () async {
      // Arrange
      adapter.setPermissionsGranted(true);

      // Act
      final result = await adapter.checkNotificationPermissions();

      // Assert
      expect(result, true);
    });

    test('should handle denied permissions', () async {
      // Arrange
      adapter.setPermissionsGranted(false);

      // Act
      final result = await adapter.checkNotificationPermissions();

      // Assert
      expect(result, false);
    });

    test('should check if permissions are permanently denied', () async {
      // Arrange
      adapter.setPermissionPermanentlyDenied(true);

      // Act
      final result = await adapter.isNotificationPermissionPermanentlyDenied();

      // Assert
      expect(result, true);
    });

    test('should cancel a notification', () async {
      // Arrange
      const notificationId = 123;

      // Act
      await adapter.cancelNotification(notificationId);

      // Assert
      expect(adapter.getCanceledNotificationIds(), contains(notificationId));
    });

    test('should cancel all notifications', () async {
      // Act
      await adapter.cancelAllNotifications();

      // Assert
      expect(adapter.getAllNotificationsCanceled(), true);
      expect(adapter.getCanceledNotificationIds(), isEmpty);
    });

    test('should schedule a notification', () async {
      // Arrange
      const notificationId = 456;
      const title = 'Test Title';
      const body = 'Test Body';
      final scheduledDate = DateTime.now().add(Duration(hours: 1));

      // Act
      final result = await adapter.scheduleNotification(
        id: notificationId,
        title: title,
        body: body,
        scheduledDate: scheduledDate,
      );

      // Assert
      expect(result, true);
      final notifications = adapter.getScheduledNotifications();
      expect(notifications.length, 1);
      expect(notifications[0]['id'], notificationId);
      expect(notifications[0]['title'], title);
      expect(notifications[0]['body'], body);
      expect(notifications[0]['scheduledDate'], scheduledDate);
      expect(notifications[0]['immediate'], false);
    });

    test('should show an immediate notification', () async {
      // Arrange
      const notificationId = 789;
      const title = 'Immediate Title';
      const body = 'Immediate Body';

      // Act
      await adapter.showNotification(
        id: notificationId,
        title: title,
        body: body,
      );

      // Assert
      final notifications = adapter.getScheduledNotifications();
      expect(notifications.length, 1);
      expect(notifications[0]['id'], notificationId);
      expect(notifications[0]['title'], title);
      expect(notifications[0]['body'], body);
      expect(notifications[0]['immediate'], true);
    });

    test('should reschedule a notification', () async {
      // Arrange
      const notificationId = 101;

      // Act
      final result = await adapter.rescheduleNotification(notificationId);

      // Assert
      expect(result, true);
      expect(adapter.getRescheduleCount(), 1);
    });

    test('should reschedule all notifications', () async {
      // Act
      final result = await adapter.rescheduleAllNotifications();

      // Assert
      expect(result, 0); // No notifications scheduled yet
      expect(adapter.getRescheduleAllCount(), 1);
    });

    test('should get current timezone', () {
      // Arrange
      adapter.setTimeZone('America/New_York');

      // Act
      final result = adapter.getCurrentTimeZone();

      // Assert
      expect(result, 'America/New_York');
    });

    test('should check if timezone has changed', () async {
      // Arrange
      adapter.setTimeZoneChanged(true);

      // Act
      final result = await adapter.hasTimeZoneChanged();

      // Assert
      expect(result, true);
    });

    test('should check if exact alarms can be scheduled', () async {
      // Arrange
      adapter.setCanScheduleExactAlarms(true);

      // Act
      final result = await adapter.canScheduleExactAlarms();

      // Assert
      expect(result, true);
    });

    test('should get channel settings', () async {
      // Act
      final result = await adapter.getChannelSettings('default_channel');

      // Assert
      expect(result, isA<Map<String, dynamic>>());
      expect(result['importance'], 'high');
    });
  });
}
