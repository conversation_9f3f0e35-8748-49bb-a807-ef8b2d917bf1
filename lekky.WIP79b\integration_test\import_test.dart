// File: integration_test/import_test.dart
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:lekky/core/data/repositories/meter_entry_repository.dart';
import 'package:lekky/core/models/meter_entry.dart';
import 'package:lekky/features/backup/backup_service.dart';
import 'package:lekky/features/history/presentation/controllers/history_controller.dart';
import 'package:lekky/main.dart' as app;
import 'package:path_provider/path_provider.dart';
import 'package:provider/provider.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Import Flow Test', () {
    testWidgets('Import data with bulk add method', (WidgetTester tester) async {
      // Start the app
      app.main();
      await tester.pumpAndSettle();

      // Create test entries
      final entries = List.generate(100, (index) {
        final isTopUp = index % 3 == 0;
        return MeterEntry(
          reading: isTopUp ? 0.0 : 100.0 - index,
          amountToppedUp: isTopUp ? 50.0 : 0.0,
          timestamp: DateTime(2023, 1, 1).add(Duration(days: index)),
          shortAverageAfterTopUp: null,
          totalAverageUpToThisPoint: null,
        );
      });

      // Create a temporary CSV file
      final tempDir = await getTemporaryDirectory();
      final csvFile = File('${tempDir.path}/test_import.csv');
      
      // Export entries to CSV
      final backupService = BackupService();
      await backupService.exportMeterEntries(
        entries: entries,
        outputFile: csvFile,
      );

      // Get the HistoryController
      final historyController = tester.element(find.byType(MaterialApp))
          .findAncestorWidgetOfExactType<Provider<HistoryController>>()
          ?.value;
      
      expect(historyController, isNotNull);

      // Import entries using bulkAddEntries
      final result = await historyController!.bulkAddEntries(entries);
      
      expect(result, isTrue);

      // Wait for UI to update
      await tester.pumpAndSettle();

      // Verify that entries were added
      final meterEntryRepository = MeterEntryRepository();
      final allEntries = await meterEntryRepository.getAllEntries();
      
      expect(allEntries.length, equals(100));

      // Clean up
      await csvFile.delete();
    });

    testWidgets('Import large dataset with progress indicator', (WidgetTester tester) async {
      // Start the app
      app.main();
      await tester.pumpAndSettle();

      // Create a large dataset (500 entries)
      final entries = List.generate(500, (index) {
        final isTopUp = index % 3 == 0;
        return MeterEntry(
          reading: isTopUp ? 0.0 : 100.0 - (index % 100),
          amountToppedUp: isTopUp ? 50.0 : 0.0,
          timestamp: DateTime(2023, 1, 1).add(Duration(days: index)),
          shortAverageAfterTopUp: null,
          totalAverageUpToThisPoint: null,
        );
      });

      // Get the HistoryController
      final historyController = tester.element(find.byType(MaterialApp))
          .findAncestorWidgetOfExactType<Provider<HistoryController>>()
          ?.value;
      
      expect(historyController, isNotNull);

      // Track progress updates
      final progressUpdates = <double>[];

      // Import entries using bulkAddEntries with progress tracking
      final result = await historyController!.bulkAddEntries(
        entries,
        onProgress: (progress) {
          progressUpdates.add(progress);
        },
      );
      
      expect(result, isTrue);

      // Verify that progress updates were received
      expect(progressUpdates, isNotEmpty);
      
      // Verify that progress updates start at 0.0 and end at 1.0
      expect(progressUpdates.first, equals(0.0));
      expect(progressUpdates.last, equals(1.0));

      // Wait for UI to update
      await tester.pumpAndSettle();

      // Verify that entries were added
      final meterEntryRepository = MeterEntryRepository();
      final allEntries = await meterEntryRepository.getAllEntries();
      
      expect(allEntries.length, equals(500));
    });
  });
}
