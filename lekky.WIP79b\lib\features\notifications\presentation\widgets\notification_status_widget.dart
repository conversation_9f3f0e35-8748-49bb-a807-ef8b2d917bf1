// File: lib/features/notifications/presentation/widgets/notification_status_widget.dart

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../core/platform/permissions/permission_adapter.dart';
import '../../domain/usecases/get_reminder_status.dart';
import '../providers/notification_provider.dart';

/// Widget for displaying notification status and controls
class NotificationStatusWidget extends StatelessWidget {
  const NotificationStatusWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer<NotificationProvider>(
      builder: (context, provider, _) {
        final reminderStatus = provider.reminderStatus;

        if (reminderStatus == null) {
          return const Center(child: CircularProgressIndicator());
        }

        return Card(
          margin: const EdgeInsets.all(8.0),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildStatusHeader(context, reminderStatus, provider),
                const SizedBox(height: 16),
                _buildStatusDetails(context, reminderStatus),
                const SizedBox(height: 16),
                _buildTimezoneStatus(context, reminderStatus),
                const SizedBox(height: 16),
                _buildPermissionStatus(context, provider),
                const SizedBox(height: 16),
                _buildControls(context, provider, reminderStatus),
              ],
            ),
          ),
        );
      },
    );
  }

  /// Build the status header with icon and title
  Widget _buildStatusHeader(BuildContext context, ReminderStatus status,
      NotificationProvider provider) {
    final isActive = status.isFullyFunctional;
    final permissionStatus = provider.permissionStatus;

    return Row(
      children: [
        Stack(
          children: [
            Icon(
              isActive ? Icons.notifications_active : Icons.notifications_off,
              color: isActive ? Colors.green : Colors.red,
              size: 24,
            ),
            if (permissionStatus == PermissionStatus.permanentlyDenied)
              Positioned(
                right: 0,
                bottom: 0,
                child: Container(
                  padding: const EdgeInsets.all(2),
                  decoration: BoxDecoration(
                    color: Colors.red,
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.block,
                    color: Colors.white,
                    size: 10,
                  ),
                ),
              ),
          ],
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            status.getStatusDescription(),
            style: Theme.of(context).textTheme.titleMedium,
          ),
        ),
      ],
    );
  }

  /// Build the permission status section
  Widget _buildPermissionStatus(
    BuildContext context,
    NotificationProvider provider,
  ) {
    final permissionStatus = provider.permissionStatus;
    final isPermissionGranted = permissionStatus == PermissionStatus.granted;

    return Card(
      color: isPermissionGranted ? Colors.green.shade50 : Colors.red.shade50,
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  isPermissionGranted ? Icons.check_circle : Icons.error,
                  color: isPermissionGranted ? Colors.green : Colors.red,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Permission Status: ${provider.getPermissionStatusDescription()}',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
              ],
            ),
            if (!isPermissionGranted) ...[
              const SizedBox(height: 8),
              const Text(
                'Notifications require permission to work properly. This allows us to send you reminders for meter readings even when the app is closed.',
                style: TextStyle(fontSize: 12),
              ),
              const SizedBox(height: 8),
              if (permissionStatus == PermissionStatus.permanentlyDenied) ...[
                const Text(
                  'You\'ve permanently denied notification permissions. Please enable them in your device settings.',
                  style: TextStyle(color: Colors.red, fontSize: 12),
                ),
                const SizedBox(height: 8),
                ElevatedButton.icon(
                  icon: const Icon(Icons.settings),
                  label: const Text('Open Settings'),
                  onPressed: () async {
                    await provider.openAppSettings();
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                  ),
                ),
              ] else ...[
                ElevatedButton.icon(
                  icon: const Icon(Icons.notifications_active),
                  label: const Text('Enable Notifications'),
                  onPressed: () async {
                    await provider.requestPermissionWithEducation(context);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ] else ...[
              const SizedBox(height: 8),
              const Text(
                'Notifications are properly set up. You\'ll receive reminders for your meter readings.',
                style: TextStyle(color: Colors.green, fontSize: 12),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// Build the status details section
  Widget _buildStatusDetails(BuildContext context, ReminderStatus status) {
    if (!status.isEnabled) {
      return const Text('Enable reminders to see more details.');
    }

    final frequencyText = _getFrequencyText(status.frequency);

    // Format next reminder date with timezone info if needed
    String nextReminderText;
    if (status.nextReminderDate != null) {
      nextReminderText = _formatDateTime(status.nextReminderDate!);

      // Add timezone indicator if in a different timezone
      if (status.isInDifferentTimezone) {
        final timezoneAbbr = status.currentTimezone.split('/').last;
        nextReminderText += ' ($timezoneAbbr)';
      }
    } else {
      nextReminderText = 'Not scheduled';
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Frequency: $frequencyText'),
        const SizedBox(height: 4),
        Text('Next reminder: $nextReminderText'),
        const SizedBox(height: 4),
        if (status.lastReminderDate != null)
          Text('Last reminder: ${_formatDateTime(status.lastReminderDate!)}'),
      ],
    );
  }

  /// Build the controls section
  Widget _buildControls(
    BuildContext context,
    NotificationProvider provider,
    ReminderStatus status,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // Toggle for enabling/disabling reminders
        SwitchListTile(
          title: const Text('Enable Reminders'),
          value: status.isEnabled,
          onChanged: (value) async {
            await provider.setMeterReadingRemindersEnabled(value);
          },
          contentPadding: EdgeInsets.zero,
        ),

        // Only show these controls if reminders are enabled
        if (status.isEnabled) ...[
          const Divider(),

          // Frequency selector
          DropdownButtonFormField<int>(
            decoration: const InputDecoration(
              labelText: 'Reminder Frequency',
            ),
            value: status.frequency,
            items: [
              DropdownMenuItem(value: 1, child: Text(_getFrequencyText(1))),
              DropdownMenuItem(value: 7, child: Text(_getFrequencyText(7))),
              DropdownMenuItem(value: 14, child: Text(_getFrequencyText(14))),
              DropdownMenuItem(value: 30, child: Text(_getFrequencyText(30))),
            ],
            onChanged: (value) async {
              if (value != null) {
                await provider.setMeterReadingReminderFrequency(value);
              }
            },
          ),

          const SizedBox(height: 16),

          // Time picker button
          OutlinedButton.icon(
            icon: const Icon(Icons.access_time),
            label: Text(
                'Set Reminder Time: ${_formatTimeOfDay(status.reminderTime.timeOfDay)}${status.isInDifferentTimezone ? ' *' : ''}'),
            onPressed: () => _showTimePicker(context, provider, status),
          ),

          const SizedBox(height: 8),

          // Test notification button
          ElevatedButton.icon(
            icon: const Icon(Icons.notifications),
            label: const Text('Test Notification'),
            onPressed: () async {
              await provider.showMeterReadingReminderNotification();
              // Show confirmation
              if (context.mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Test notification sent')),
                );
              }
            },
          ),
        ],

        // Only show educational content about permissions if reminders are enabled but permissions are denied
        if (status.isEnabled && !status.arePermissionsGranted) ...[
          const Divider(),
          Card(
            color: Colors.blue.shade50,
            child: Padding(
              padding: const EdgeInsets.all(12.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Why are notifications important?',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    '• Regular meter readings help you track your energy usage\n'
                    '• Avoid estimated bills by submitting accurate readings\n'
                    '• Get reminders even when the app is closed\n'
                    '• Never miss a reading date again',
                    style: TextStyle(fontSize: 12),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      TextButton.icon(
                        icon: const Icon(Icons.settings),
                        label: const Text('Open Settings'),
                        onPressed: () async {
                          await provider.openAppSettings();
                        },
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ],
    );
  }

  /// Show time picker dialog
  void _showTimePicker(
    BuildContext context,
    NotificationProvider provider,
    ReminderStatus status,
  ) async {
    final TimeOfDay? pickedTime = await showTimePicker(
      context: context,
      initialTime: status.reminderTime.timeOfDay,
    );

    if (pickedTime != null && context.mounted) {
      final newReminderTime = status.reminderTime.copyWith(
        timeOfDay: pickedTime,
      );

      await provider.setMeterReadingReminderTime(newReminderTime);
    }
  }

  /// Build the timezone status section
  Widget _buildTimezoneStatus(BuildContext context, ReminderStatus status) {
    // Only show timezone info if reminders are enabled
    if (!status.isEnabled) {
      return const SizedBox.shrink();
    }

    // If not in a different timezone and not in DST, show minimal info
    if (!status.isInDifferentTimezone && !status.isDaylightSavingTime) {
      return const SizedBox.shrink();
    }

    return Card(
      color: status.isInDifferentTimezone
          ? Colors.amber.shade50
          : Colors.blue.shade50,
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.public,
                  color: status.isInDifferentTimezone
                      ? Colors.amber.shade800
                      : Colors.blue,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    status.getTimezoneStatusDescription(),
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            if (status.isInDifferentTimezone) ...[
              const Text(
                'Your device timezone has changed since the reminder was set. '
                'The reminder time has been adjusted to maintain the same local time.',
                style: TextStyle(fontSize: 12),
              ),
            ],
            if (status.isDaylightSavingTime) ...[
              const Text(
                'Daylight Saving Time is currently active. Reminder times will be adjusted automatically.',
                style: TextStyle(fontSize: 12),
              ),
            ],
            const SizedBox(height: 8),
            if (status.timezoneAwareReminderTime != null) ...[
              SwitchListTile(
                title: const Text('Adjust for timezone changes',
                    style: TextStyle(fontSize: 14)),
                subtitle: const Text(
                    'Keep reminder at same local time when timezone changes',
                    style: TextStyle(fontSize: 12)),
                value:
                    status.timezoneAwareReminderTime!.adjustForTimezoneChanges,
                onChanged: (value) async {
                  // This would be implemented by updating the timezone-aware reminder time
                  // For now, just show a message
                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                          content: Text('This feature will be available soon')),
                    );
                  }
                },
                contentPadding: EdgeInsets.zero,
                dense: true,
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// Format a DateTime object for display
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} at '
        '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  /// Format a TimeOfDay object for display
  String _formatTimeOfDay(TimeOfDay timeOfDay) {
    final hour = timeOfDay.hour.toString().padLeft(2, '0');
    final minute = timeOfDay.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }

  /// Get the text description for a frequency value
  String _getFrequencyText(int days) {
    switch (days) {
      case 1:
        return 'Daily';
      case 7:
        return 'Weekly';
      case 14:
        return 'Bi-weekly';
      case 30:
        return 'Monthly';
      default:
        return 'Every $days days';
    }
  }
}
