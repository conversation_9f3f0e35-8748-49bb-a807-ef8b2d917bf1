// File: lib/core/utils/crash_reporter.dart

import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:lekky/core/utils/enhanced_logger.dart';
import 'package:lekky/core/utils/error_handling.dart';

/// Crash report data structure
class CrashReport {
  final String id;
  final DateTime timestamp;
  final String type;
  final String message;
  final String? stackTrace;
  final Map<String, dynamic> deviceInfo;
  final Map<String, dynamic> appInfo;
  final Map<String, dynamic>? additionalInfo;

  CrashReport({
    required this.id,
    required this.timestamp,
    required this.type,
    required this.message,
    this.stackTrace,
    required this.deviceInfo,
    required this.appInfo,
    this.additionalInfo,
  });

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'timestamp': timestamp.toIso8601String(),
      'type': type,
      'message': message,
      'stackTrace': stackTrace,
      'deviceInfo': deviceInfo,
      'appInfo': appInfo,
      'additionalInfo': additionalInfo,
    };
  }

  /// Create from JSON
  factory CrashReport.fromJson(Map<String, dynamic> json) {
    return CrashReport(
      id: json['id'],
      timestamp: DateTime.parse(json['timestamp']),
      type: json['type'],
      message: json['message'],
      stackTrace: json['stackTrace'],
      deviceInfo: json['deviceInfo'],
      appInfo: json['appInfo'],
      additionalInfo: json['additionalInfo'],
    );
  }
}

/// Crash reporter service for collecting and reporting crashes
class CrashReporter {
  static bool _isInitialized = false;
  static final List<CrashReport> _reports = [];
  static int _maxStoredReports = 20;
  static bool _crashReportingEnabled = true;
  static bool _autoSendReports = false;

  static Map<String, dynamic> _deviceInfo = {};
  static Map<String, dynamic> _appInfo = {};

  static final StreamController<CrashReport> _reportStreamController =
      StreamController<CrashReport>.broadcast();

  /// Initialize the crash reporter
  static Future<void> initialize({
    bool crashReportingEnabled = true,
    bool autoSendReports = false,
    int maxStoredReports = 20,
  }) async {
    if (_isInitialized) return;

    _crashReportingEnabled = crashReportingEnabled;
    _autoSendReports = autoSendReports;
    _maxStoredReports = maxStoredReports;

    // Initialize basic device and app info
    _initializeBasicDeviceInfo();
    _initializeBasicAppInfo();

    // Load any existing reports
    await _loadReports();

    // Set up global error handlers
    _setupErrorHandlers();

    _isInitialized = true;

    EnhancedLogger.info(
      'Crash reporter initialized',
      details: {
        'crashReportingEnabled': crashReportingEnabled,
        'autoSendReports': autoSendReports,
        'maxStoredReports': maxStoredReports,
      },
      component: 'crash_reporter',
    );
  }

  /// Initialize basic device info without external packages
  static void _initializeBasicDeviceInfo() {
    try {
      _deviceInfo = {
        'platform': Platform.operatingSystem,
        'version': Platform.operatingSystemVersion,
        'locale': Platform.localeName,
        'numberOfProcessors': Platform.numberOfProcessors,
      };
    } catch (e, stackTrace) {
      EnhancedLogger.error(
        'Failed to initialize device info',
        exception: e,
        stackTrace: stackTrace,
        component: 'crash_reporter',
      );

      _deviceInfo = {'platform': 'unknown'};
    }
  }

  /// Initialize basic app info without external packages
  static void _initializeBasicAppInfo() {
    try {
      _appInfo = {
        'flutter': kIsWeb ? 'web' : 'native',
        'debug': kDebugMode,
        'profile': kProfileMode,
        'release': kReleaseMode,
      };
    } catch (e, stackTrace) {
      EnhancedLogger.error(
        'Failed to initialize app info',
        exception: e,
        stackTrace: stackTrace,
        component: 'crash_reporter',
      );

      _appInfo = {'unknown': true};
    }
  }

  /// Set up global error handlers
  static void _setupErrorHandlers() {
    // Flutter error handler
    FlutterError.onError = (FlutterErrorDetails details) {
      // Report to crash reporter
      reportCrash(
        type: 'flutter_error',
        message: details.exception.toString(),
        stackTrace: details.stack.toString(),
        additionalInfo: {
          'library': details.library ?? 'unknown',
          'context': details.context?.toString() ?? 'unknown',
        },
      );

      // Also print to console in debug mode
      if (kDebugMode) {
        FlutterError.dumpErrorToConsole(details);
      }
    };

    // Register with error handler
    ErrorHandler.registerErrorHandler((error) {
      // Only report critical and high severity errors
      if (error.severity == ErrorSeverity.critical ||
          error.severity == ErrorSeverity.high) {
        reportCrash(
          type: 'app_error',
          message: error.message,
          stackTrace: error.stackTrace.toString(),
          additionalInfo: {
            'type': error.type.toString(),
            'severity': error.severity.toString(),
            'details': error.details,
            'component': error.component,
            'operationId': error.operationId,
          },
        );
      }
    });
  }

  /// Report a crash
  static Future<CrashReport> reportCrash({
    required String type,
    required String message,
    String? stackTrace,
    Map<String, dynamic>? additionalInfo,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }

    if (!_crashReportingEnabled) {
      EnhancedLogger.info(
        'Crash reporting is disabled, ignoring crash',
        details: {'type': type, 'message': message},
        component: 'crash_reporter',
      );

      // Return a dummy report
      return CrashReport(
        id: 'disabled',
        timestamp: DateTime.now(),
        type: type,
        message: message,
        stackTrace: stackTrace,
        deviceInfo: _deviceInfo,
        appInfo: _appInfo,
        additionalInfo: additionalInfo,
      );
    }

    // Create crash report
    final report = CrashReport(
      id: _generateReportId(),
      timestamp: DateTime.now(),
      type: type,
      message: message,
      stackTrace: stackTrace,
      deviceInfo: _deviceInfo,
      appInfo: _appInfo,
      additionalInfo: additionalInfo,
    );

    // Add to reports list
    _reports.add(report);

    // Trim if exceeding max capacity
    if (_reports.length > _maxStoredReports) {
      _reports.removeAt(0);
    }

    // Save reports
    await _saveReports();

    // Log the crash
    EnhancedLogger.critical(
      'Crash detected: $message',
      details: {
        'type': type,
        'reportId': report.id,
        'additionalInfo': additionalInfo,
      },
      component: 'crash_reporter',
      exception: message,
      stackTrace: stackTrace != null ? StackTrace.fromString(stackTrace) : null,
    );

    // Add to stream
    _reportStreamController.add(report);

    // Auto-send if enabled
    if (_autoSendReports) {
      sendReport(report);
    }

    return report;
  }

  /// Generate a unique report ID
  static String _generateReportId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = DateTime.now().microsecond;
    return 'cr_${timestamp}_$random';
  }

  /// Load reports from storage
  static Future<void> _loadReports() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/crash_reports.json');

      if (await file.exists()) {
        final content = await file.readAsString();
        final List<dynamic> jsonList = json.decode(content);

        _reports.clear();
        for (final item in jsonList) {
          _reports.add(CrashReport.fromJson(item));
        }

        EnhancedLogger.info(
          'Loaded ${_reports.length} crash reports',
          component: 'crash_reporter',
        );
      }
    } catch (e, stackTrace) {
      EnhancedLogger.error(
        'Failed to load crash reports',
        exception: e,
        stackTrace: stackTrace,
        component: 'crash_reporter',
      );
    }
  }

  /// Save reports to storage
  static Future<void> _saveReports() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/crash_reports.json');

      final jsonList = _reports.map((report) => report.toJson()).toList();
      await file.writeAsString(json.encode(jsonList));
    } catch (e, stackTrace) {
      EnhancedLogger.error(
        'Failed to save crash reports',
        exception: e,
        stackTrace: stackTrace,
        component: 'crash_reporter',
      );
    }
  }

  /// Send a crash report to the backend
  static Future<bool> sendReport(CrashReport report) async {
    try {
      // In a real app, this would send the report to a backend service
      // For now, we'll just log it
      EnhancedLogger.info(
        'Sending crash report',
        details: {'reportId': report.id},
        component: 'crash_reporter',
      );

      // Simulate network request
      await Future.delayed(Duration(milliseconds: 500));

      // Mark as sent
      return true;
    } catch (e, stackTrace) {
      EnhancedLogger.error(
        'Failed to send crash report',
        exception: e,
        stackTrace: stackTrace,
        component: 'crash_reporter',
      );
      return false;
    }
  }

  /// Send all unsent reports
  static Future<int> sendAllReports() async {
    if (!_isInitialized) {
      await initialize();
    }

    int sentCount = 0;

    for (final report in _reports) {
      final success = await sendReport(report);
      if (success) {
        sentCount++;
      }
    }

    return sentCount;
  }

  /// Get all crash reports
  static List<CrashReport> getAllReports() {
    return List.unmodifiable(_reports);
  }

  /// Get crash reports by type
  static List<CrashReport> getReportsByType(String type) {
    return _reports.where((report) => report.type == type).toList();
  }

  /// Clear all crash reports
  static Future<void> clearReports() async {
    _reports.clear();
    await _saveReports();
  }

  /// Enable or disable crash reporting
  static void setCrashReportingEnabled(bool enabled) {
    _crashReportingEnabled = enabled;
  }

  /// Set auto-send reports
  static void setAutoSendReports(bool autoSend) {
    _autoSendReports = autoSend;
  }

  /// Get a stream of crash reports as they are reported
  static Stream<CrashReport> get reportStream => _reportStreamController.stream;

  /// Get the total number of crash reports
  static int get reportCount => _reports.length;

  /// Dispose the crash reporter
  static void dispose() {
    _reportStreamController.close();
  }

  /// Show a crash reporting dialog
  static Future<bool> showCrashReportingDialog(BuildContext context) async {
    return await showDialog<bool>(
          context: context,
          barrierDismissible: false,
          builder: (context) => AlertDialog(
            title: Text('Crash Detected'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'The app encountered an unexpected error. Would you like to send a crash report to help us improve the app?',
                ),
                SizedBox(height: 16),
                Text(
                  'No personal information will be sent with the report.',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: Text('Don\'t Send'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: Text('Send Report'),
              ),
            ],
          ),
        ) ??
        false;
  }
}

/// Extension for handling errors in Flutter widgets
extension CrashReporterWidgetExtension on Widget {
  /// Wrap a widget with error handling
  Widget withErrorHandling({String? componentName}) {
    return ErrorBoundary(
      child: this,
      componentName: componentName,
    );
  }
}

/// Widget that catches errors in its child widget
class ErrorBoundary extends StatefulWidget {
  final Widget child;
  final String? componentName;

  const ErrorBoundary({
    Key? key,
    required this.child,
    this.componentName,
  }) : super(key: key);

  @override
  ErrorBoundaryState createState() => ErrorBoundaryState();
}

class ErrorBoundaryState extends State<ErrorBoundary> {
  bool _hasError = false;
  String _errorMessage = '';

  @override
  Widget build(BuildContext context) {
    if (_hasError) {
      return Material(
        color: Colors.red.shade100,
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.error_outline, color: Colors.red),
              SizedBox(height: 8),
              Text(
                'Widget Error',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              if (kDebugMode)
                Text(
                  _errorMessage,
                  style: TextStyle(fontSize: 12),
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                ),
            ],
          ),
        ),
      );
    }

    return widget.child;
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // Reset error state when dependencies change
    _hasError = false;
  }

  @override
  void didUpdateWidget(ErrorBoundary oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Reset error state when widget updates
    _hasError = false;
  }

  // This method is called when an error occurs in the widget tree
  static Iterable<DiagnosticsNode> _buildDiagnostics(
      Object exception, StackTrace? stack) {
    final builder = DiagnosticPropertiesBuilder()
      ..add(StringProperty('exception', exception.toString()))
      ..add(StringProperty('stack', stack?.toString() ?? ''));
    return builder.properties;
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties.add(
        StringProperty('componentName', widget.componentName ?? 'unknown'));
  }

  // Called when an error occurs in the widget subtree
  void reportError(Object exception, StackTrace? stack) {
    setState(() {
      _hasError = true;
      _errorMessage = exception.toString();
    });

    // Report to crash reporter
    CrashReporter.reportCrash(
      type: 'widget_error',
      message: exception.toString(),
      stackTrace: stack?.toString(),
      additionalInfo: {
        'component': widget.componentName ?? 'unknown',
      },
    );

    FlutterError.reportError(FlutterErrorDetails(
      exception: exception,
      stack: stack,
      library: 'error boundary',
      context:
          ErrorDescription('in ${widget.componentName ?? 'unknown'} widget'),
      informationCollector: () => _buildDiagnostics(exception, stack),
    ));
  }
}
