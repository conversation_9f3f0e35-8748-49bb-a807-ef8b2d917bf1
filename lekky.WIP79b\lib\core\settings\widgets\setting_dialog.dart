// File: lib/core/settings/widgets/setting_dialog.dart
import 'package:flutter/material.dart';

/// A reusable dialog widget for settings that can be used by both screens
class SettingDialog extends StatelessWidget {
  final String title;
  final String? subtitle;
  final Widget content;
  final VoidCallback? onCancel;
  final VoidCallback? onSave;
  final String cancelText;
  final String saveText;

  const SettingDialog({
    Key? key,
    required this.title,
    this.subtitle,
    required this.content,
    this.onCancel,
    this.onSave,
    this.cancelText = 'Cancel',
    this.saveText = 'Save',
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(title,
          style: const TextStyle(fontSize: 18)), // Reduced from default 20
      titlePadding:
          const EdgeInsets.fromLTRB(24, 20, 24, 8), // Reduced bottom padding
      contentPadding: const EdgeInsets.fromLTRB(
          24, 8, 24, 16), // Reduced top and bottom padding
      insetPadding: const EdgeInsets.symmetric(
          horizontal: 40, vertical: 24), // Reduced from default
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (subtitle != null) ...[
            Text(
              subtitle!,
              style: const TextStyle(fontSize: 13), // Reduced from 14
            ),
            const SizedBox(height: 12), // Reduced from 16
          ],
          content,
        ],
      ),
      actionsPadding:
          const EdgeInsets.fromLTRB(16, 0, 16, 8), // Reduced padding
      actions: [
        if (onCancel != null)
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              onCancel?.call();
            },
            style: TextButton.styleFrom(
              padding: const EdgeInsets.symmetric(
                  horizontal: 12, vertical: 6), // Reduced padding
              visualDensity: VisualDensity.compact,
            ),
            child: Text(cancelText),
          ),
        if (onSave != null)
          TextButton(
            onPressed: onSave,
            style: TextButton.styleFrom(
              padding: const EdgeInsets.symmetric(
                  horizontal: 12, vertical: 6), // Reduced padding
              visualDensity: VisualDensity.compact,
            ),
            child: Text(saveText),
          ),
      ],
    );
  }
}
