// File: lib/features/cost/data/cost_repository.dart
import '../../../core/data/repositories/meter_entry_repository.dart';
import '../../../core/data/repositories/settings_repository.dart';
import '../../../core/utils/average_calculator.dart';
import '../../../core/utils/cost_calculator.dart';
import '../domain/models/cost_period.dart';
import '../domain/models/cost_result.dart';
import '../domain/usecases/calculate_cost.dart';

/// Repository for the cost feature
class CostRepository {
  final MeterEntryRepository _meterEntryRepository;
  final SettingsRepository _settingsRepository;

  DateTime? _earliestDate;
  DateTime? _latestDate;

  DateTime? get earliestDate => _earliestDate;
  DateTime? get latestDate => _latestDate;

  CostRepository({
    required MeterEntryRepository meterEntryRepository,
    required SettingsRepository settingsRepository,
  })  : _meterEntryRepository = meterEntryRepository,
        _settingsRepository = settingsRepository;

  /// Fetch the earliest and latest date from meter entries
  Future<void> fetchDateRange() async {
    final entries = await _meterEntryRepository.getAllEntriesWithCache();
    if (entries.isNotEmpty) {
      entries.sort((a, b) => a.timestamp.compareTo(b.timestamp));
      _earliestDate = entries.first.timestamp;
      _latestDate = entries.last.timestamp;
    } else {
      _earliestDate = null;
      _latestDate = null;
    }
  }

  /// Calculate the cost for a specific period
  Future<CostResult> calculateCostForPeriod(
      CostPeriod period, DateTime? fromDate, DateTime? toDate) async {
    // Get all entries
    var entries = await _meterEntryRepository.getAllEntriesWithCache();
    double averageUsage = 0.0; // Define averageUsage at the top level
    double? topUpAmount; // Track top-ups during the period
    DateTime? topUpDate; // Track the date of the top-up

    DateTime? firstEntry;
    DateTime? lastEntry;
    if (entries.isNotEmpty) {
      entries.sort((a, b) => a.timestamp.compareTo(b.timestamp));
      firstEntry = entries.first.timestamp;
      lastEntry = entries.last.timestamp;
    }

    // Validate date range
    if (fromDate != null && toDate != null) {
      if (firstEntry != null && fromDate.isBefore(firstEntry)) {
        // TODO: Implement logic for dates before the earliest reading using short average
        // Debug information - would use proper logging in production code
      }
      if (lastEntry != null && toDate.isAfter(lastEntry)) {
        // TODO: Implement logic for dates after the latest reading using short average
        // Debug information - would use proper logging in production code
      }

      // Filter entries within the date range
      entries = entries
          .where((entry) =>
              entry.timestamp
                  .isAfter(fromDate.subtract(const Duration(days: 1))) &&
              entry.timestamp.isBefore(toDate.add(const Duration(days: 1))))
          .toList();

      // Check for top-ups in the date range
      final topUps =
          entries.where((entry) => entry.amountToppedUp > 0).toList();
      if (topUps.isNotEmpty) {
        // For simplicity, we'll just track the first top-up in the range
        // In a real app, you might want to handle multiple top-ups
        topUpAmount = topUps.first.amountToppedUp;
        topUpDate = topUps.first.timestamp;
      }

      // Implement the logic to use the known Short Average for days before and/or days after known meter readings
      double totalUsage = 0;

      // Calculate usage based on short average
      if (fromDate.isBefore(firstEntry ?? fromDate)) {
        // Use fromDate if firstEntry is null
        // TODO Implement short average logic
        final daysBefore = (firstEntry ?? fromDate)
            .difference(fromDate)
            .inDays; // Use fromDate if firstEntry is null
        //TODO Get short average from database
      }

      // Calculate the average usage for the custom date range
      if (entries.length > 1) {
        // Filter to only include meter readings (not top-ups)
        final meterReadings =
            entries.where((e) => e.amountToppedUp == 0).toList();

        if (meterReadings.length > 1) {
          for (int i = 0; i < meterReadings.length - 1; i++) {
            final entry1 = meterReadings[i];
            final entry2 = meterReadings[i + 1];
            final days = entry2.timestamp.difference(entry1.timestamp).inDays;
            if (days > 0) {
              // Avoid division by zero
              // Calculate usage: Previous Amount Left - Current Amount Left
              // Note: This doesn't account for top-ups between readings
              final usage = entry1.amountLeft - entry2.amountLeft;
              if (usage > 0) {
                // Ensure usage is positive
                totalUsage += usage;
              }
            }
          }

          // Calculate average daily usage
          final totalDays = meterReadings.last.timestamp
              .difference(meterReadings.first.timestamp)
              .inDays;
          if (totalDays > 0) {
            averageUsage = totalUsage / totalDays;
          }
        } else {
          // If we don't have enough meter readings in the range, use the total average
          entries = await _meterEntryRepository.getAllEntriesWithCache();
          if (entries.isNotEmpty) {
            // Sort entries by timestamp
            entries.sort((a, b) => a.timestamp.compareTo(b.timestamp));
            averageUsage = AverageCalculator.calculateTotalAverage(
                entries, entries.length - 1);
          }
        }
      } else {
        // If we don't have enough entries in the range, use the total average
        entries = await _meterEntryRepository.getAllEntriesWithCache();
        if (entries.isNotEmpty) {
          // Sort entries by timestamp
          entries.sort((a, b) => a.timestamp.compareTo(b.timestamp));
          averageUsage = AverageCalculator.calculateTotalAverage(
              entries, entries.length - 1);
        }
      }
    } else {
      // If no entries in the date range, use the total average
      entries = await _meterEntryRepository.getAllEntriesWithCache();
      if (entries.isNotEmpty) {
        // Sort entries by timestamp
        entries.sort((a, b) => a.timestamp.compareTo(b.timestamp));
        averageUsage = AverageCalculator.calculateTotalAverage(
            entries, entries.length - 1);
      }
    }

    // Get the meter unit
    final meterUnit = await _settingsRepository.getMeterUnit();

    // Calculate the cost for the period
    double costPerPeriod;
    if (period == CostPeriod.custom && fromDate != null && toDate != null) {
      // For custom periods, use our new CostCalculator

      // Get all entries sorted by date
      final allEntries = await _meterEntryRepository.getAllEntriesWithCache();

      // Use the CostCalculator to calculate the cost
      costPerPeriod =
          CostCalculator.calculateCost(allEntries, fromDate, toDate);

      // If we couldn't calculate a cost (returned 0), fall back to average-based calculation
      if (costPerPeriod <= 0) {
        // Calculate time difference in minutes and convert to days with minute precision
        final minutes = toDate.difference(fromDate).inMinutes;
        final days =
            minutes / (24 * 60); // Convert minutes to days with precision
        costPerPeriod = averageUsage * days;
      }
    } else {
      // For standard periods, use the period's days value
      costPerPeriod = averageUsage * period.days;
    }

    // Create the cost result
    final costResult = CostResult(
      averageUsage: averageUsage,
      costPerPeriod: costPerPeriod,
      period: period,
      meterUnit: meterUnit,
    );

    // Get initial credit if available
    double? initialCredit = await _settingsRepository.getInitialCredit();

    // Add top-up and initial credit information if available
    return CostResult(
      averageUsage: costResult.averageUsage,
      costPerPeriod: costResult.costPerPeriod,
      period: costResult.period,
      meterUnit: costResult.meterUnit,
      topUpAmount: topUpAmount,
      topUpDate: topUpDate,
      initialCredit: initialCredit,
    );
  }

  /// Get the meter unit
  Future<String> getMeterUnit() async {
    return await _settingsRepository.getMeterUnit();
  }

  /// Get all meter entries
  Future<List<dynamic>> getMeterEntries() async {
    return await _meterEntryRepository.getAllEntriesWithCache();
  }

  /// Get cached meter entries (synchronous version)
  List<dynamic> getCachedMeterEntries() {
    // In a real implementation, we would have a proper caching mechanism
    // For now, we'll return an empty list to avoid errors
    // The actual data will be fetched asynchronously in the calculateCostForPeriod method
    return [];
  }

  /// Get the total average usage
  Future<double> getTotalAverageUsage() async {
    final entries = await _meterEntryRepository.getAllEntriesWithCache();
    if (entries.isEmpty) {
      return 0.0;
    }

    // Sort entries by timestamp
    entries.sort((a, b) => a.timestamp.compareTo(b.timestamp));

    // Calculate the total average usage
    return AverageCalculator.calculateTotalAverage(entries, entries.length - 1);
  }

  /// Check if there are any invalid history records
  bool hasInvalidHistoryRecords() {
    // Access the meter entry repository to check for invalid entries
    return _meterEntryRepository.hasInvalidEntries();
  }

  /// Get the count of invalid entries
  int getInvalidEntryCount() {
    // Get the count from the meter entry repository
    return _meterEntryRepository.getInvalidEntryCount();
  }

  /// Get the date info setting
  Future<String> getDateInfo() async {
    return await _settingsRepository.getDateInfo();
  }
}
