// File: lib/core/utils/date_time_utils.dart
import 'package:intl/intl.dart';

/// Utility class for date and time operations
class DateTimeUtils {
  // Private constructor to prevent instantiation
  DateTimeUtils._();

  /// Formats a date according to the specified format
  static String formatDate(DateTime date, String format) {
    return DateFormat(format).format(date);
  }

  /// Formats a date using the default format (DD-MM-YYYY)
  static String formatDateDefault(DateTime date) {
    return DateFormat('dd-MM-yyyy').format(date);
  }

  /// Formats a date with time (DD-MM-YYYY HH:MM)
  static String formatDateWithTime(DateTime date) {
    return DateFormat('dd-MM-yyyy HH:mm').format(date);
  }

  /// Formats a date with day name (DDD, DD-MM-YYYY)
  static String formatDateWithDayName(DateTime date) {
    return DateFormat('EEE, dd-MM-yyyy').format(date);
  }

  /// Formats a date with full day name (DDDD, DD-MM-YYYY)
  static String formatDateWithFullDayName(DateTime date) {
    return DateFormat('EEEE, dd-MM-yyyy').format(date);
  }

  /// Formats a date with month name (DD MMM YYYY)
  static String formatDateWithMonthName(DateTime date) {
    return DateFormat('dd MMM yyyy').format(date);
  }

  /// Formats a date with full month name (DD MMMM YYYY)
  static String formatDateWithFullMonthName(DateTime date) {
    return DateFormat('dd MMMM yyyy').format(date);
  }

  /// Formats a date in a relative way (Today, Yesterday, etc.)
  static String formatDateRelative(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final tomorrow = today.add(const Duration(days: 1));
    final dateOnly = DateTime(date.year, date.month, date.day);

    if (dateOnly == today) {
      return 'Today';
    } else if (dateOnly == yesterday) {
      return 'Yesterday';
    } else if (dateOnly == tomorrow) {
      return 'Tomorrow';
    } else {
      return formatDateDefault(date);
    }
  }

  /// Formats a date in a relative way with time (Today at HH:MM, etc.)
  static String formatDateRelativeWithTime(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final tomorrow = today.add(const Duration(days: 1));
    final dateOnly = DateTime(date.year, date.month, date.day);

    final time = DateFormat('HH:mm').format(date);

    if (dateOnly == today) {
      return 'Today at $time';
    } else if (dateOnly == yesterday) {
      return 'Yesterday at $time';
    } else if (dateOnly == tomorrow) {
      return 'Tomorrow at $time';
    } else {
      return '${formatDateDefault(date)} at $time';
    }
  }

  /// Calculates the difference in days between two dates
  static int daysBetween(DateTime from, DateTime to) {
    final fromDate = DateTime(from.year, from.month, from.day);
    final toDate = DateTime(to.year, to.month, to.day);
    return toDate.difference(fromDate).inDays;
  }

  /// Checks if a date is today
  static bool isToday(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year &&
        date.month == now.month &&
        date.day == now.day;
  }

  /// Checks if a date is yesterday
  static bool isYesterday(DateTime date) {
    final now = DateTime.now();
    final yesterday = DateTime(now.year, now.month, now.day - 1);
    return date.year == yesterday.year &&
        date.month == yesterday.month &&
        date.day == yesterday.day;
  }

  /// Checks if a date is tomorrow
  static bool isTomorrow(DateTime date) {
    final now = DateTime.now();
    final tomorrow = DateTime(now.year, now.month, now.day + 1);
    return date.year == tomorrow.year &&
        date.month == tomorrow.month &&
        date.day == tomorrow.day;
  }

  /// Checks if a date is in the past
  static bool isPast(DateTime date) {
    return date.isBefore(DateTime.now());
  }

  /// Checks if a date is in the future
  static bool isFuture(DateTime date) {
    return date.isAfter(DateTime.now());
  }

  /// Gets the start of the day for a date
  static DateTime startOfDay(DateTime date) {
    return DateTime(date.year, date.month, date.day);
  }

  /// Gets the end of the day for a date
  static DateTime endOfDay(DateTime date) {
    return DateTime(date.year, date.month, date.day, 23, 59, 59, 999);
  }

  /// Gets the start of the week for a date (Monday)
  static DateTime startOfWeek(DateTime date) {
    final day = date.weekday;
    return DateTime(date.year, date.month, date.day - (day - 1));
  }

  /// Gets the end of the week for a date (Sunday)
  static DateTime endOfWeek(DateTime date) {
    final day = date.weekday;
    return DateTime(
        date.year, date.month, date.day + (7 - day), 23, 59, 59, 999);
  }

  /// Gets the start of the month for a date
  static DateTime startOfMonth(DateTime date) {
    return DateTime(date.year, date.month, 1);
  }

  /// Gets the end of the month for a date
  static DateTime endOfMonth(DateTime date) {
    return DateTime(date.year, date.month + 1, 0, 23, 59, 59, 999);
  }

  /// Gets the start of the year for a date
  static DateTime startOfYear(DateTime date) {
    return DateTime(date.year, 1, 1);
  }

  /// Gets the end of the year for a date
  static DateTime endOfYear(DateTime date) {
    return DateTime(date.year, 12, 31, 23, 59, 59, 999);
  }

  /// Formats a duration between two dates in a human-readable format
  /// Shows days, hours, and minutes as appropriate
  static String formatDuration(DateTime from, DateTime to) {
    // Handle special case for a full day (00:00 to 23:59)
    if ((from.hour == 0 && from.minute == 0) &&
        (to.hour == 23 && to.minute == 59) &&
        from.day == to.day &&
        from.month == to.month &&
        from.year == to.year) {
      return '1 day';
    }

    // Handle special case for same day with different times
    if (from.day == to.day && from.month == to.month && from.year == to.year) {
      // If it's the same day but different times, calculate hours and minutes
      final hours = to.hour - from.hour;
      final minutes = to.minute - from.minute;

      // Adjust for negative minutes
      int adjustedHours = hours;
      int adjustedMinutes = minutes;
      if (minutes < 0) {
        adjustedHours -= 1;
        adjustedMinutes = 60 + minutes;
      }

      // If it's close to a full day (23+ hours), just show "1 day"
      if (adjustedHours >= 23) {
        return '1 day';
      }

      // Build the parts of the duration string for same-day
      final List<String> parts = [];

      // Add hours if > 0
      if (adjustedHours > 0) {
        parts.add('$adjustedHours ${adjustedHours == 1 ? 'hour' : 'hours'}');
      }

      // Add minutes if > 0 or if no other parts
      if (adjustedMinutes > 0 || parts.isEmpty) {
        parts.add(
            '$adjustedMinutes ${adjustedMinutes == 1 ? 'minute' : 'minutes'}');
      }

      // Join parts with commas
      return parts.join(', ');
    }

    // Calculate the duration
    final duration = to.difference(from);

    // Calculate days, hours, and minutes
    final days = duration.inDays;
    final hours = duration.inHours % 24;
    final minutes = duration.inMinutes % 60;

    // Special case for exactly 24 hours (or multiples)
    if (duration.inHours > 0 && duration.inHours % 24 == 0 && minutes == 0) {
      final exactDays = duration.inHours ~/ 24;
      return exactDays == 1 ? '1 day' : '$exactDays days';
    }

    // For multi-day periods, if it's close to a whole number of days (within 1 hour),
    // just show the days
    if (days > 0 && hours < 1 && minutes < 30) {
      return '$days ${days == 1 ? 'day' : 'days'}';
    }

    // For multi-day periods with significant hours, round up to the next day
    if (days > 0 && (hours > 12 || (hours > 0 && minutes > 30))) {
      final roundedDays = days + 1;
      return '$roundedDays ${roundedDays == 1 ? 'day' : 'days'}';
    }

    // Build the parts of the duration string
    final List<String> parts = [];

    // Add days if > 0
    if (days > 0) {
      parts.add('$days ${days == 1 ? 'day' : 'days'}');
    }

    // Add hours if > 0
    if (hours > 0) {
      parts.add('$hours ${hours == 1 ? 'hour' : 'hours'}');
    }

    // Add minutes if > 0 or if no other parts
    if (minutes > 0 || parts.isEmpty) {
      parts.add('$minutes ${minutes == 1 ? 'minute' : 'minutes'}');
    }

    // Join parts with commas
    return parts.join(', ');
  }
}
