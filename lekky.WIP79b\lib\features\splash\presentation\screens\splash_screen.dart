// File: lib/features/splash/presentation/screens/splash_screen.dart
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/data/repositories/settings_repository.dart';
import '../../../../core/providers/localization_provider.dart';
import '../../../../core/providers/notification_provider.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/utils/logger.dart';
import '../../../../core/utils/permission_helper.dart';
import '../widgets/splash_animation.dart';

/// The splash screen of the app
class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  // Create an instance of the PermissionHelper
  final PermissionHelper _permissionHelper = PermissionHelper();
  String _statusMessage = 'Initializing...';

  @override
  void initState() {
    super.initState();
    _checkSetupStatus();
  }

  void _updateStatus(String message) {
    if (mounted) {
      setState(() {
        _statusMessage = message;
      });
    }
  }

  Future<void> _checkSetupStatus() async {
    try {
      logger.i('SplashScreen: Checking setup status...');
      // Simulate a delay for the splash screen
      await Future.delayed(const Duration(seconds: 2));

      if (!mounted) {
        logger.i('SplashScreen: Widget not mounted, returning');
        return;
      }

      // Request storage permissions before proceeding
      _updateStatus('Checking permissions...');
      final hasPermission =
          await _permissionHelper.checkAndRequestStoragePermission();

      if (!mounted) return;

      if (!hasPermission) {
        logger.w('SplashScreen: Storage permission denied');
        _updateStatus('Storage permission required');
        // Show permission explanation dialog
        await _showPermissionExplanationDialog();
        if (!mounted) return;
      }

      // Initialize notification provider
      final notificationProvider = NotificationProvider();
      await notificationProvider.initialize();

      // Initialize localization provider
      _updateStatus('Initializing language settings...');
      if (mounted) {
        final localizationProvider =
            Provider.of<LocalizationProvider>(context, listen: false);
        await localizationProvider.detectAndSetDeviceLocale();
      }

      // First try to check directly from SharedPreferences
      bool isSetupCompleted = false;
      try {
        logger.i('SplashScreen: Checking SharedPreferences directly');
        final prefs = await SharedPreferences.getInstance();
        isSetupCompleted =
            prefs.getBool(AppConstants.keySetupCompleted) ?? false;
        logger.i(
            'SplashScreen: Direct SharedPreferences check: isSetupCompleted = $isSetupCompleted');
      } catch (e) {
        logger.e('SplashScreen: Error checking SharedPreferences directly: $e');
        // Fall back to repository method
      }

      // If direct check failed, use the repository
      if (!isSetupCompleted) {
        logger.i('SplashScreen: Falling back to repository check');
        final settingsRepository = SettingsRepository();
        isSetupCompleted = await settingsRepository.isSetupCompleted();
        logger.i(
            'SplashScreen: Repository check: isSetupCompleted = $isSetupCompleted');
      }

      if (!mounted) {
        logger.i('SplashScreen: Widget not mounted after check, returning');
        return;
      }

      // Navigate to the appropriate screen
      if (isSetupCompleted) {
        logger.i('SplashScreen: Setup is completed, navigating to Home');

        // Show welcome notification if this is the first time after setup
        await notificationProvider.showWelcomeNotification();

        // Navigate to home screen
        if (mounted) {
          Navigator.of(context).pushReplacementNamed(AppConstants.routeHome);
        }
      } else {
        logger.i('SplashScreen: Setup not completed, navigating to Welcome');
        if (mounted) {
          Navigator.of(context).pushReplacementNamed(AppConstants.routeWelcome);
        }
      }
    } catch (e) {
      logger.e('SplashScreen: Error checking setup status: $e');
      // Fallback to Welcome screen in case of error
      if (mounted) {
        logger.i('SplashScreen: Navigating to Welcome due to error');
        Navigator.of(context).pushReplacementNamed(AppConstants.routeWelcome);
      }
    }
  }

  /// Show a dialog explaining why storage permission is needed
  Future<void> _showPermissionExplanationDialog() async {
    if (!mounted) return;

    bool shouldTryAgain = false;

    // Show the initial dialog
    await showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: const Text('Storage Permission Required'),
          content: const SingleChildScrollView(
            child: ListBody(
              children: <Widget>[
                Text(
                  'Lekky needs access to your device storage to find and restore backup files.',
                  style: TextStyle(fontSize: 16),
                ),
                SizedBox(height: 12),
                Text(
                  'Without this permission, you won\'t be able to restore your previous data.',
                  style: TextStyle(fontSize: 16),
                ),
              ],
            ),
          ),
          actions: <Widget>[
            TextButton(
              child: const Text('Continue Anyway'),
              onPressed: () {
                Navigator.of(dialogContext).pop();
                shouldTryAgain = false;
              },
            ),
            TextButton(
              child: const Text('Try Again'),
              onPressed: () {
                Navigator.of(dialogContext).pop();
                shouldTryAgain = true;
              },
            ),
          ],
        );
      },
    );

    // If user wants to try again and the widget is still mounted
    if (shouldTryAgain && mounted) {
      final hasPermission =
          await _permissionHelper.checkAndRequestStoragePermission();

      // Update status based on permission result
      if (!mounted) return;

      if (!hasPermission) {
        _updateStatus('Permission denied. Continuing anyway...');
      } else {
        _updateStatus('Permission granted. Checking for backup files...');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    debugPrint('SplashScreen: Building UI');
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: AppColors.primaryGradient,
          ),
        ),
        child: Stack(
          children: [
            const Center(
              child: SplashAnimation(),
            ),
            // Status message
            Positioned(
              bottom: 60,
              left: 0,
              right: 0,
              child: Center(
                child: Text(
                  _statusMessage,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
            // Debug overlay in debug mode only
            if (kDebugMode)
              Positioned(
                bottom: 10,
                left: 10,
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.7),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Text(
                    'Debug Build',
                    style: TextStyle(color: Colors.white, fontSize: 12),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
