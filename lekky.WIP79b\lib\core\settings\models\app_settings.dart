// File: lib/core/settings/models/app_settings.dart
import 'package:flutter/material.dart';

/// A unified settings model that can be used by both Setup and Settings screens
class AppSettings {
  final String meterUnit;
  final double alertThreshold;
  final int daysInAdvance;
  final String dateFormat;
  final String dateInfo;
  final bool notificationsEnabled;
  final double? initialMeterCredit;
  final ThemeMode themeMode;
  final String language;

  const AppSettings({
    required this.meterUnit,
    required this.alertThreshold,
    required this.daysInAdvance,
    required this.dateFormat,
    required this.dateInfo,
    required this.notificationsEnabled,
    this.initialMeterCredit,
    this.themeMode = ThemeMode.system,
    this.language = 'en',
  });

  /// Creates a copy of this AppSettings with the given fields replaced with the new values
  AppSettings copyWith({
    String? meterUnit,
    double? alertThreshold,
    int? daysInAdvance,
    String? dateFormat,
    String? dateInfo,
    bool? notificationsEnabled,
    double? initialMeterCredit,
    ThemeMode? themeMode,
    String? language,
  }) {
    return AppSettings(
      meterUnit: meterUnit ?? this.meterUnit,
      alertThreshold: alertThreshold ?? this.alertThreshold,
      daysInAdvance: daysInAdvance ?? this.daysInAdvance,
      dateFormat: dateFormat ?? this.dateFormat,
      dateInfo: dateInfo ?? this.dateInfo,
      notificationsEnabled: notificationsEnabled ?? this.notificationsEnabled,
      initialMeterCredit: initialMeterCredit ?? this.initialMeterCredit,
      themeMode: themeMode ?? this.themeMode,
      language: language ?? this.language,
    );
  }

  /// Default configuration
  factory AppSettings.defaultSettings() {
    return const AppSettings(
      meterUnit: '£',
      alertThreshold: 5.0,
      daysInAdvance: 2,
      dateFormat: 'DD-MM-YYYY',
      dateInfo: 'Date only',
      notificationsEnabled: true,
      initialMeterCredit: null,
      themeMode: ThemeMode.system,
      language: 'en',
    );
  }

  /// Creates an AppSettings from a map
  factory AppSettings.fromMap(Map<String, dynamic> map) {
    return AppSettings(
      meterUnit: map['meterUnit'] ?? '£',
      alertThreshold: map['alertThreshold']?.toDouble() ?? 5.0,
      daysInAdvance: map['daysInAdvance'] ?? 2,
      dateFormat: map['dateFormat'] ?? 'DD-MM-YYYY',
      dateInfo: map['dateInfo'] ?? 'Date only',
      notificationsEnabled: map['notificationsEnabled'] ?? true,
      initialMeterCredit: map['initialMeterCredit']?.toDouble(),
      themeMode: ThemeMode.values[map['themeMode'] ?? 0],
      language: map['language'] ?? 'en',
    );
  }

  /// Converts this AppSettings to a map
  Map<String, dynamic> toMap() {
    return {
      'meterUnit': meterUnit,
      'alertThreshold': alertThreshold,
      'daysInAdvance': daysInAdvance,
      'dateFormat': dateFormat,
      'dateInfo': dateInfo,
      'notificationsEnabled': notificationsEnabled,
      'initialMeterCredit': initialMeterCredit,
      'themeMode': themeMode.index,
      'language': language,
    };
  }
}
