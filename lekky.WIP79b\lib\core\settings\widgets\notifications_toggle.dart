// File: lib/core/settings/widgets/notifications_toggle.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/notification_provider.dart';
import '../../theme/app_text_styles.dart';
import '../../widgets/app_card.dart';

/// A shared widget for toggling notifications
/// Can be used in both Setup and Settings screens
class NotificationsToggle extends StatelessWidget {
  final bool notificationsEnabled;
  final Function(bool) onToggle;
  final bool useDialog;
  final bool showCard;
  final bool showDetailedSettings;

  const NotificationsToggle({
    Key? key,
    required this.notificationsEnabled,
    required this.onToggle,
    this.useDialog = false,
    this.showCard = true,
    this.showDetailedSettings = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (useDialog) {
      return SwitchListTile(
        title: const Text('Enable Notifications'),
        subtitle: const Text(
          'Receive alerts when your balance is low or when it\'s time to top up',
        ),
        value: notificationsEnabled,
        onChanged: onToggle,
        secondary: const Icon(Icons.notifications),
      );
    }

    final content = Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Notifications',
          style: AppTextStyles.titleMedium,
        ),
        const SizedBox(height: 8),
        Text(
          'Enable notifications to receive alerts when your meter balance is low',
          style: AppTextStyles.bodyMedium,
        ),
        const SizedBox(height: 16),
        SwitchListTile(
          title: const Text('Enable Notifications'),
          subtitle: Text(
            notificationsEnabled
                ? 'You will receive alerts when your meter balance is low'
                : 'You will not receive any alerts',
          ),
          value: notificationsEnabled,
          onChanged: onToggle,
          dense: true,
        ),
        if (showDetailedSettings && notificationsEnabled) ...[
          const Divider(),
          _buildDetailedSettings(context),
        ],
      ],
    );

    if (showCard) {
      return AppCard(
        padding: const EdgeInsets.all(16),
        child: content,
      );
    }

    return content;
  }

  Widget _buildDetailedSettings(BuildContext context) {
    final notificationProvider = Provider.of<NotificationProvider>(context);

    return FutureBuilder<bool>(
      future: notificationProvider.areLowBalanceAlertsEnabled(),
      builder: (context, snapshot) {
        final lowBalanceAlertsEnabled = snapshot.data ?? true;

        return Column(
          children: [
            SwitchListTile(
              title: const Text('Low Balance Alerts'),
              subtitle: const Text(
                'Get notified when your meter balance falls below your alert threshold',
              ),
              value: lowBalanceAlertsEnabled,
              onChanged: (value) async {
                await notificationProvider.setLowBalanceAlertsEnabled(value);
              },
              dense: true,
            ),
            FutureBuilder<bool>(
              future: notificationProvider.areTopUpAlertsEnabled(),
              builder: (context, snapshot) {
                final topUpAlertsEnabled = snapshot.data ?? true;

                return SwitchListTile(
                  title: const Text('Time to Top Up Alerts'),
                  subtitle: const Text(
                    'Get notified when it\'s time to top up based on your usage patterns',
                  ),
                  value: topUpAlertsEnabled,
                  onChanged: (value) async {
                    await notificationProvider.setTopUpAlertsEnabled(value);
                  },
                  dense: true,
                );
              },
            ),
            FutureBuilder<bool>(
              future: notificationProvider.areInvalidRecordAlertsEnabled(),
              builder: (context, snapshot) {
                final invalidRecordAlertsEnabled = snapshot.data ?? true;

                return SwitchListTile(
                  title: const Text('Invalid Record Alerts'),
                  subtitle: const Text(
                    'Get notified when an invalid meter reading or top-up is detected',
                  ),
                  value: invalidRecordAlertsEnabled,
                  onChanged: (value) async {
                    await notificationProvider.setInvalidRecordAlertsEnabled(value);
                  },
                  dense: true,
                );
              },
            ),
            const Padding(
              padding: EdgeInsets.only(top: 8),
              child: Text(
                'Note: You may need to grant permission for notifications in your device settings',
                style: TextStyle(
                  fontStyle: FontStyle.italic,
                  fontSize: 12,
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
