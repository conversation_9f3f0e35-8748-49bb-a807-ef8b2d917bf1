import 'package:shared_preferences/shared_preferences.dart';
import '../utils/logger.dart';
import '../utils/performance_monitor.dart';

/// Analytics service for tracking permission request patterns and success rates
class PermissionAnalyticsService {
  static final PermissionAnalyticsService _instance = 
      PermissionAnalyticsService._internal();
  
  factory PermissionAnalyticsService() => _instance;
  PermissionAnalyticsService._internal();

  // SharedPreferences keys
  static const String _requestCountPrefix = 'analytics_request_count_';
  static const String _successCountPrefix = 'analytics_success_count_';
  static const String _denialCountPrefix = 'analytics_denial_count_';
  static const String _firstRequestTimePrefix = 'analytics_first_request_';
  static const String _lastSuccessTimePrefix = 'analytics_last_success_';
  static const String _sessionStartTime = 'analytics_session_start';

  /// Track permission request attempt
  Future<void> trackPermissionRequest(String notificationType) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Increment request count
      final currentCount = prefs.getInt('$_requestCountPrefix$notificationType') ?? 0;
      await prefs.setInt('$_requestCountPrefix$notificationType', currentCount + 1);
      
      // Set first request time if not already set
      if (!prefs.containsKey('$_firstRequestTimePrefix$notificationType')) {
        await prefs.setInt('$_firstRequestTimePrefix$notificationType', 
            DateTime.now().millisecondsSinceEpoch);
      }
      
      Logger.info('PermissionAnalyticsService: Tracked request for $notificationType (count: ${currentCount + 1})');
    } catch (e) {
      Logger.error('Error tracking permission request for $notificationType: $e');
    }
  }

  /// Track permission request success
  Future<void> trackPermissionSuccess(String notificationType) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Increment success count
      final currentCount = prefs.getInt('$_successCountPrefix$notificationType') ?? 0;
      await prefs.setInt('$_successCountPrefix$notificationType', currentCount + 1);
      
      // Update last success time
      await prefs.setInt('$_lastSuccessTimePrefix$notificationType', 
          DateTime.now().millisecondsSinceEpoch);
      
      Logger.info('PermissionAnalyticsService: Tracked success for $notificationType (count: ${currentCount + 1})');
    } catch (e) {
      Logger.error('Error tracking permission success for $notificationType: $e');
    }
  }

  /// Track permission request denial
  Future<void> trackPermissionDenial(String notificationType, String reason) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Increment denial count
      final currentCount = prefs.getInt('$_denialCountPrefix$notificationType') ?? 0;
      await prefs.setInt('$_denialCountPrefix$notificationType', currentCount + 1);
      
      Logger.info('PermissionAnalyticsService: Tracked denial for $notificationType (reason: $reason, count: ${currentCount + 1})');
    } catch (e) {
      Logger.error('Error tracking permission denial for $notificationType: $e');
    }
  }

  /// Get success rate for specific notification type
  Future<double> getSuccessRate(String notificationType) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final requestCount = prefs.getInt('$_requestCountPrefix$notificationType') ?? 0;
      final successCount = prefs.getInt('$_successCountPrefix$notificationType') ?? 0;
      
      if (requestCount == 0) return 0.0;
      return (successCount / requestCount) * 100;
    } catch (e) {
      Logger.error('Error calculating success rate for $notificationType: $e');
      return 0.0;
    }
  }

  /// Get analytics summary for specific notification type
  Future<Map<String, dynamic>> getAnalyticsSummary(String notificationType) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      final requestCount = prefs.getInt('$_requestCountPrefix$notificationType') ?? 0;
      final successCount = prefs.getInt('$_successCountPrefix$notificationType') ?? 0;
      final denialCount = prefs.getInt('$_denialCountPrefix$notificationType') ?? 0;
      final firstRequestTime = prefs.getInt('$_firstRequestTimePrefix$notificationType');
      final lastSuccessTime = prefs.getInt('$_lastSuccessTimePrefix$notificationType');
      
      final successRate = requestCount > 0 ? (successCount / requestCount) * 100 : 0.0;
      
      return {
        'notification_type': notificationType,
        'request_count': requestCount,
        'success_count': successCount,
        'denial_count': denialCount,
        'success_rate': successRate,
        'first_request_time': firstRequestTime != null 
            ? DateTime.fromMillisecondsSinceEpoch(firstRequestTime).toIso8601String()
            : null,
        'last_success_time': lastSuccessTime != null 
            ? DateTime.fromMillisecondsSinceEpoch(lastSuccessTime).toIso8601String()
            : null,
        'has_reached_limit': requestCount >= 3,
      };
    } catch (e) {
      Logger.error('Error getting analytics summary for $notificationType: $e');
      return {};
    }
  }

  /// Get overall analytics summary for all notification types
  Future<Map<String, dynamic>> getOverallAnalytics() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys();
      
      final notificationTypes = <String>{};
      
      // Extract notification types from keys
      for (final key in keys) {
        if (key.startsWith(_requestCountPrefix)) {
          final type = key.substring(_requestCountPrefix.length);
          notificationTypes.add(type);
        }
      }
      
      final typeAnalytics = <String, Map<String, dynamic>>{};
      int totalRequests = 0;
      int totalSuccesses = 0;
      
      for (final type in notificationTypes) {
        final summary = await getAnalyticsSummary(type);
        typeAnalytics[type] = summary;
        totalRequests += (summary['request_count'] as int? ?? 0);
        totalSuccesses += (summary['success_count'] as int? ?? 0);
      }
      
      final overallSuccessRate = totalRequests > 0 ? (totalSuccesses / totalRequests) * 100 : 0.0;
      
      return {
        'overall_success_rate': overallSuccessRate,
        'total_requests': totalRequests,
        'total_successes': totalSuccesses,
        'notification_types': typeAnalytics,
        'session_start_time': prefs.getInt(_sessionStartTime),
        'generated_at': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      Logger.error('Error getting overall analytics: $e');
      return {};
    }
  }

  /// Start analytics session
  Future<void> startSession() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_sessionStartTime, DateTime.now().millisecondsSinceEpoch);
      Logger.info('PermissionAnalyticsService: Started analytics session');
    } catch (e) {
      Logger.error('Error starting analytics session: $e');
    }
  }

  /// Clear analytics data for specific notification type
  Future<void> clearAnalytics(String notificationType) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('$_requestCountPrefix$notificationType');
      await prefs.remove('$_successCountPrefix$notificationType');
      await prefs.remove('$_denialCountPrefix$notificationType');
      await prefs.remove('$_firstRequestTimePrefix$notificationType');
      await prefs.remove('$_lastSuccessTimePrefix$notificationType');
      
      Logger.info('PermissionAnalyticsService: Cleared analytics for $notificationType');
    } catch (e) {
      Logger.error('Error clearing analytics for $notificationType: $e');
    }
  }

  /// Clear all analytics data
  Future<void> clearAllAnalytics() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys();
      
      for (final key in keys) {
        if (key.startsWith(_requestCountPrefix) || 
            key.startsWith(_successCountPrefix) || 
            key.startsWith(_denialCountPrefix) || 
            key.startsWith(_firstRequestTimePrefix) || 
            key.startsWith(_lastSuccessTimePrefix) ||
            key == _sessionStartTime) {
          await prefs.remove(key);
        }
      }
      
      Logger.info('PermissionAnalyticsService: Cleared all analytics data');
    } catch (e) {
      Logger.error('Error clearing all analytics: $e');
    }
  }

  /// Track permission request with performance monitoring
  Future<void> trackRequestWithTiming(String notificationType, Future<bool> Function() requestFunction) async {
    await PerformanceMonitor.timeOperation('permission_request_$notificationType', () async {
      await trackPermissionRequest(notificationType);
      
      try {
        final success = await requestFunction();
        if (success) {
          await trackPermissionSuccess(notificationType);
        } else {
          await trackPermissionDenial(notificationType, 'user_denied');
        }
      } catch (e) {
        await trackPermissionDenial(notificationType, 'error: $e');
        rethrow;
      }
    });
  }
}
