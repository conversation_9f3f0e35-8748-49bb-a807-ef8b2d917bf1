// File: lib/core/widgets/themed_choice_chip.dart
import 'package:flutter/material.dart';
import '../theme/app_colors.dart';
import '../extensions/context_extensions.dart';

/// A themed choice chip that adapts to light and dark mode
class ThemedChoiceChip extends StatelessWidget {
  /// The label text to display
  final String label;
  
  /// Whether the chip is selected
  final bool selected;
  
  /// Callback when the chip is selected
  final Function(bool) onSelected;
  
  /// Optional custom selected color
  final Color? selectedColor;

  /// Creates a themed choice chip
  const ThemedChoiceChip({
    Key? key,
    required this.label,
    required this.selected,
    required this.onSelected,
    this.selectedColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isDarkMode = context.isDarkMode;
    final primaryColor = isDarkMode ? AppColors.primaryDark : AppColors.primary;

    return ChoiceChip(
      label: Text(label),
      selected: selected,
      selectedColor: (selectedColor ?? primaryColor).withOpacity(0.2),
      labelStyle: TextStyle(
        color: selected
            ? selectedColor ?? primaryColor
            : context.colorScheme.onSurface,
        fontWeight: selected ? FontWeight.bold : FontWeight.normal,
      ),
      onSelected: onSelected,
    );
  }
}
