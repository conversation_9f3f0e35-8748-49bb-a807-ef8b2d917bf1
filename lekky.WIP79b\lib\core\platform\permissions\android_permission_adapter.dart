// File: lib/core/platform/permissions/android_permission_adapter.dart

import 'dart:io';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/services.dart';
import 'package:permission_handler/permission_handler.dart' as ph;
import 'permission_adapter.dart';
import 'permission_strategy.dart' as strategy;

/// Android implementation of the PermissionAdapter
class AndroidPermissionAdapter implements PermissionAdapter {
  final MethodChannel _channel =
      const MethodChannel('com.roolekky/permissions');
  final List<void Function(PermissionStatus)> _listeners = [];
  SharedPreferences? _prefs;

  static const String _lastPermissionCheckKey = 'last_permission_check_time';
  static const String _permissionDeniedCountKey = 'permission_denied_count';
  strategy.PermissionStrategy? _strategy;

  @override
  Future<void> initialize() async {
    _prefs = await SharedPreferences.getInstance();

    // Select the appropriate strategy based on Android version
    final androidVersion = await _getAndroidVersion();
    if (androidVersion >= 33) {
      // Android 13+
      _strategy = strategy.Android13PermissionStrategy(_channel, _prefs!);
    } else if (androidVersion >= 31) {
      // Android 12
      _strategy = strategy.Android12PermissionStrategy(_channel, _prefs!);
    } else {
      // Older Android versions
      _strategy = strategy.AndroidLegacyPermissionStrategy(_channel, _prefs!);
    }

    // Setup platform callback handler
    _channel.setMethodCallHandler((call) async {
      if (call.method == 'onPermissionStatusChanged') {
        final statusValue = call.arguments['status'] as int;
        final status = _intToPermissionStatus(statusValue);
        _notifyListeners(status);
      }
      return null;
    });
  }

  @override
  Future<PermissionStatus> checkPermission() async {
    if (_strategy == null) {
      await initialize();
    }

    return await _strategy!.checkPermission();
  }

  @override
  Future<bool> requestPermission({PermissionCallback? callback}) async {
    if (_strategy == null) {
      await initialize();
    }

    // Increment request count
    final deniedCount = _prefs?.getInt(_permissionDeniedCountKey) ?? 0;

    final result = await _strategy!.requestPermission();

    // Handle result
    if (result) {
      // Reset denied count on success
      await _prefs?.setInt(_permissionDeniedCountKey, 0);
      await savePermissionCheckTime();
    } else {
      // Increment denied count
      await _prefs?.setInt(_permissionDeniedCountKey, deniedCount + 1);
    }

    if (callback != null) {
      callback(result);
    }

    return result;
  }

  @override
  Future<bool> isPermanentlyDenied() async {
    if (_strategy == null) {
      await initialize();
    }

    // Check if permission is permanently denied via permission handler
    final status = await ph.Permission.notification.status;
    return status == ph.PermissionStatus.permanentlyDenied;
  }

  @override
  Future<bool> openAppSettings() async {
    return await ph.openAppSettings();
  }

  @override
  Future<DateTime?> getLastPermissionCheckTime() async {
    final timestamp = _prefs?.getInt(_lastPermissionCheckKey);
    if (timestamp == null) {
      return null;
    }
    return DateTime.fromMillisecondsSinceEpoch(timestamp);
  }

  @override
  Future<void> savePermissionCheckTime() async {
    final now = DateTime.now().millisecondsSinceEpoch;
    await _prefs?.setInt(_lastPermissionCheckKey, now);
  }

  @override
  String getPermissionStatusString(PermissionStatus status) {
    switch (status) {
      case PermissionStatus.granted:
        return 'Granted';
      case PermissionStatus.denied:
        return 'Denied';
      case PermissionStatus.permanentlyDenied:
        return 'Permanently Denied';
      case PermissionStatus.restricted:
        return 'Restricted';
      case PermissionStatus.limited:
        return 'Limited';
      case PermissionStatus.unknown:
        return 'Unknown';
    }
  }

  @override
  Future<bool> showPermissionEducationUI() async {
    // This would be implemented in the UI layer
    // For now, we'll just return true to indicate the user wants to proceed
    // In a real implementation, this would show a dialog explaining why the permission is needed
    return true;
  }

  @override
  Future<void> showPermissionDeniedAlternativesUI() async {
    // This would be implemented in the UI layer
    // For now, we'll just do nothing
    // In a real implementation, this would show a dialog with alternative options
  }

  @override
  void registerPermissionStatusListener(
      void Function(PermissionStatus) listener) {
    _listeners.add(listener);
  }

  @override
  void unregisterPermissionStatusListener(
      void Function(PermissionStatus) listener) {
    _listeners.remove(listener);
  }

  @override
  Future<bool> shouldShowRequestPermissionRationale() async {
    // Get the number of times the permission has been denied
    final deniedCount = _prefs?.getInt(_permissionDeniedCountKey) ?? 0;

    // If denied more than once, we should show the rationale
    return deniedCount > 0;
  }

  @override
  Future<PermissionStatus> getSystemPermissionStatus() async {
    if (_strategy == null) {
      await initialize();
    }

    return await _strategy!.checkPermission();
  }

  // Helper method to get Android version
  Future<int> _getAndroidVersion() async {
    try {
      final result = await _channel.invokeMethod<int>('getAndroidVersion');
      return result ?? 0;
    } catch (e) {
      debugPrint('Error getting Android version: $e');
      return 0;
    }
  }

  // Helper method to convert int to PermissionStatus
  PermissionStatus _intToPermissionStatus(int value) {
    switch (value) {
      case 0:
        return PermissionStatus.granted;
      case 1:
        return PermissionStatus.denied;
      case 2:
        return PermissionStatus.permanentlyDenied;
      case 3:
        return PermissionStatus.restricted;
      case 4:
        return PermissionStatus.limited;
      default:
        return PermissionStatus.unknown;
    }
  }

  // Helper method to notify listeners of permission status changes
  void _notifyListeners(PermissionStatus status) {
    for (final listener in _listeners) {
      listener(status);
    }
  }
}
