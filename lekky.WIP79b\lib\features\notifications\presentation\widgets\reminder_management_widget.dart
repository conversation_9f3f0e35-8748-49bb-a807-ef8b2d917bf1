// File: lib/features/notifications/presentation/widgets/reminder_management_widget.dart

import 'package:flutter/material.dart';
import '../../domain/models/notification_model.dart';

/// A model representing a reminder template
class ReminderTemplate {
  final String id;
  final String name;
  final String title;
  final String message;
  final String frequency;
  final TimeOfDay timeOfDay;
  final NotificationPriority priority;
  final IconData icon;

  const ReminderTemplate({
    required this.id,
    required this.name,
    required this.title,
    required this.message,
    required this.frequency,
    required this.timeOfDay,
    required this.priority,
    required this.icon,
  });
}

/// A model representing a managed reminder
class ManagedReminder {
  final String id;
  final String title;
  final String message;
  final DateTime scheduledDate;
  final String frequency;
  final NotificationPriority priority;
  final bool isActive;
  final String? category;
  final String? tag;
  bool isSelected;

  ManagedReminder({
    required this.id,
    required this.title,
    required this.message,
    required this.scheduledDate,
    required this.frequency,
    required this.priority,
    this.isActive = true,
    this.category,
    this.tag,
    this.isSelected = false,
  });

  ManagedReminder copyWith({
    String? id,
    String? title,
    String? message,
    DateTime? scheduledDate,
    String? frequency,
    NotificationPriority? priority,
    bool? isActive,
    String? category,
    String? tag,
    bool? isSelected,
  }) {
    return ManagedReminder(
      id: id ?? this.id,
      title: title ?? this.title,
      message: message ?? this.message,
      scheduledDate: scheduledDate ?? this.scheduledDate,
      frequency: frequency ?? this.frequency,
      priority: priority ?? this.priority,
      isActive: isActive ?? this.isActive,
      category: category ?? this.category,
      tag: tag ?? this.tag,
      isSelected: isSelected ?? this.isSelected,
    );
  }
}

/// A widget that provides enhanced reminder management functionality
class ReminderManagementWidget extends StatefulWidget {
  final List<ManagedReminder> reminders;
  final List<ReminderTemplate> templates;
  final List<String> categories;
  final List<String> tags;
  final Function(ManagedReminder)? onReminderEdit;
  final Function(ManagedReminder)? onReminderToggle;
  final Function(List<ManagedReminder>)? onBatchDelete;
  final Function(List<ManagedReminder>, bool)? onBatchToggle;
  final Function(ReminderTemplate)? onTemplateSelected;
  final Function(String)? onCategoryFilter;
  final Function(String)? onTagFilter;

  const ReminderManagementWidget({
    Key? key,
    required this.reminders,
    this.templates = const [],
    this.categories = const [],
    this.tags = const [],
    this.onReminderEdit,
    this.onReminderToggle,
    this.onBatchDelete,
    this.onBatchToggle,
    this.onTemplateSelected,
    this.onCategoryFilter,
    this.onTagFilter,
  }) : super(key: key);

  @override
  State<ReminderManagementWidget> createState() =>
      _ReminderManagementWidgetState();
}

class _ReminderManagementWidgetState extends State<ReminderManagementWidget> {
  bool _isSelectionMode = false;
  String? _selectedCategory;
  String? _selectedTag;
  List<ManagedReminder> _filteredReminders = [];

  @override
  void initState() {
    super.initState();
    _updateFilteredReminders();
  }

  @override
  void didUpdateWidget(ReminderManagementWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    _updateFilteredReminders();
  }

  void _updateFilteredReminders() {
    _filteredReminders = widget.reminders.where((reminder) {
      // Apply category filter
      if (_selectedCategory != null && reminder.category != _selectedCategory) {
        return false;
      }

      // Apply tag filter
      if (_selectedTag != null && reminder.tag != _selectedTag) {
        return false;
      }

      return true;
    }).toList();
  }

  void _toggleSelectionMode() {
    setState(() {
      _isSelectionMode = !_isSelectionMode;

      // Clear selections when exiting selection mode
      if (!_isSelectionMode) {
        _filteredReminders = _filteredReminders.map((reminder) {
          return reminder.copyWith(isSelected: false);
        }).toList();
      }
    });
  }

  void _toggleReminderSelection(int index) {
    setState(() {
      _filteredReminders[index] = _filteredReminders[index].copyWith(
        isSelected: !_filteredReminders[index].isSelected,
      );
    });
  }

  void _selectAll() {
    setState(() {
      _filteredReminders = _filteredReminders.map((reminder) {
        return reminder.copyWith(isSelected: true);
      }).toList();
    });
  }

  void _deselectAll() {
    setState(() {
      _filteredReminders = _filteredReminders.map((reminder) {
        return reminder.copyWith(isSelected: false);
      }).toList();
    });
  }

  void _performBatchDelete() {
    final selectedReminders =
        _filteredReminders.where((r) => r.isSelected).toList();
    if (selectedReminders.isNotEmpty && widget.onBatchDelete != null) {
      widget.onBatchDelete!(selectedReminders);
    }
  }

  void _performBatchToggle(bool activate) {
    final selectedReminders =
        _filteredReminders.where((r) => r.isSelected).toList();
    if (selectedReminders.isNotEmpty && widget.onBatchToggle != null) {
      widget.onBatchToggle!(selectedReminders, activate);
    }
  }

  void _applyFilter(String? category, String? tag) {
    setState(() {
      _selectedCategory = category;
      _selectedTag = tag;
      _updateFilteredReminders();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildHeader(),
        if (widget.templates.isNotEmpty) _buildTemplatesSection(),
        if (widget.categories.isNotEmpty || widget.tags.isNotEmpty)
          _buildFilterBar(),
        _buildRemindersList(),
      ],
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            _isSelectionMode ? 'Select Reminders' : 'Manage Reminders',
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          _isSelectionMode
              ? Row(
                  children: [
                    IconButton(
                      icon: const Icon(Icons.select_all),
                      tooltip: 'Select All',
                      onPressed: _selectAll,
                    ),
                    IconButton(
                      icon: const Icon(Icons.deselect),
                      tooltip: 'Deselect All',
                      onPressed: _deselectAll,
                    ),
                    IconButton(
                      icon: const Icon(Icons.close),
                      tooltip: 'Exit Selection Mode',
                      onPressed: _toggleSelectionMode,
                    ),
                  ],
                )
              : IconButton(
                  icon: const Icon(Icons.select_all),
                  tooltip: 'Selection Mode',
                  onPressed: _toggleSelectionMode,
                ),
        ],
      ),
    );
  }

  Widget _buildTemplatesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Padding(
          padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Text(
            'Quick Templates',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        SizedBox(
          height: 100,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 8),
            itemCount: widget.templates.length,
            itemBuilder: (context, index) {
              final template = widget.templates[index];
              return _buildTemplateCard(template);
            },
          ),
        ),
        const Divider(),
      ],
    );
  }

  Widget _buildTemplateCard(ReminderTemplate template) {
    final Color priorityColor = _getPriorityColor(template.priority);

    return InkWell(
      onTap: widget.onTemplateSelected != null
          ? () => widget.onTemplateSelected!(template)
          : null,
      child: Container(
        width: 120,
        margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey.shade300),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: priorityColor.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                template.icon,
                color: priorityColor,
                size: 24,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              template.name,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 12,
              ),
              textAlign: TextAlign.center,
            ),
            Text(
              template.frequency,
              style: TextStyle(
                fontSize: 10,
                color: Colors.grey.shade600,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterBar() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          if (widget.categories.isNotEmpty)
            Expanded(
              child: _buildFilterDropdown(
                'Category',
                _selectedCategory,
                ['All', ...widget.categories],
                (value) {
                  final category = value == 'All' ? null : value;
                  _applyFilter(category, _selectedTag);
                  if (widget.onCategoryFilter != null) {
                    widget.onCategoryFilter!(category ?? 'All');
                  }
                },
              ),
            ),
          const SizedBox(width: 8),
          if (widget.tags.isNotEmpty)
            Expanded(
              child: _buildFilterDropdown(
                'Tag',
                _selectedTag,
                ['All', ...widget.tags],
                (value) {
                  final tag = value == 'All' ? null : value;
                  _applyFilter(_selectedCategory, tag);
                  if (widget.onTagFilter != null) {
                    widget.onTagFilter!(tag ?? 'All');
                  }
                },
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildFilterDropdown(
    String label,
    String? selectedValue,
    List<String> items,
    Function(String) onChanged,
  ) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          value: selectedValue ?? 'All',
          isExpanded: true,
          icon: const Icon(Icons.arrow_drop_down),
          hint: Text(label),
          style: const TextStyle(
            color: Colors.black,
            fontSize: 14,
          ),
          onChanged: (value) {
            if (value != null) {
              onChanged(value);
            }
          },
          items: items.map<DropdownMenuItem<String>>((String value) {
            return DropdownMenuItem<String>(
              value: value,
              child: Text(value),
            );
          }).toList(),
        ),
      ),
    );
  }

  Widget _buildRemindersList() {
    if (_filteredReminders.isEmpty) {
      return _buildEmptyState();
    }

    return Column(
      children: [
        if (_isSelectionMode) _buildBatchActions(),
        ListView.separated(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: _filteredReminders.length,
          separatorBuilder: (context, index) => const Divider(height: 1),
          itemBuilder: (context, index) {
            final reminder = _filteredReminders[index];
            return _buildReminderItem(reminder, index);
          },
        ),
      ],
    );
  }

  Widget _buildBatchActions() {
    final selectedCount = _filteredReminders.where((r) => r.isSelected).length;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      color: Colors.blue.shade50,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '$selectedCount ${selectedCount == 1 ? 'item' : 'items'} selected',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildBatchActionButton(
                icon: Icons.delete,
                label: 'Delete',
                onTap: selectedCount > 0 ? _performBatchDelete : null,
              ),
              _buildBatchActionButton(
                icon: Icons.check_circle,
                label: 'Enable',
                onTap:
                    selectedCount > 0 ? () => _performBatchToggle(true) : null,
              ),
              _buildBatchActionButton(
                icon: Icons.cancel,
                label: 'Disable',
                onTap:
                    selectedCount > 0 ? () => _performBatchToggle(false) : null,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildBatchActionButton({
    required IconData icon,
    required String label,
    required VoidCallback? onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          children: [
            Icon(
              icon,
              color: onTap != null ? Colors.blue : Colors.grey,
              size: 20,
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: onTap != null ? Colors.blue : Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReminderItem(ManagedReminder reminder, int index) {
    final Color priorityColor = _getPriorityColor(reminder.priority);

    return ListTile(
      leading: _isSelectionMode
          ? Checkbox(
              value: reminder.isSelected,
              onChanged: (value) => _toggleReminderSelection(index),
            )
          : Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: priorityColor.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                reminder.isActive
                    ? Icons.notifications_active
                    : Icons.notifications_off,
                color: reminder.isActive ? priorityColor : Colors.grey,
                size: 20,
              ),
            ),
      title: Text(
        reminder.title,
        style: TextStyle(
          fontWeight: FontWeight.bold,
          color: reminder.isActive ? Colors.black : Colors.grey,
        ),
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '${_formatDateTime(reminder.scheduledDate)} · ${reminder.frequency}',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade600,
            ),
          ),
          if (reminder.category != null || reminder.tag != null)
            Padding(
              padding: const EdgeInsets.only(top: 4),
              child: Wrap(
                spacing: 4,
                children: [
                  if (reminder.category != null)
                    _buildChip(reminder.category!, Colors.blue.shade100),
                  if (reminder.tag != null)
                    _buildChip(reminder.tag!, Colors.green.shade100),
                ],
              ),
            ),
        ],
      ),
      trailing: _isSelectionMode
          ? null
          : Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Switch(
                  value: reminder.isActive,
                  onChanged: widget.onReminderToggle != null
                      ? (value) => widget.onReminderToggle!(reminder)
                      : null,
                ),
                IconButton(
                  icon: const Icon(Icons.edit),
                  onPressed: widget.onReminderEdit != null
                      ? () => widget.onReminderEdit!(reminder)
                      : null,
                ),
              ],
            ),
      onTap: _isSelectionMode
          ? () => _toggleReminderSelection(index)
          : widget.onReminderEdit != null
              ? () => widget.onReminderEdit!(reminder)
              : null,
    );
  }

  Widget _buildChip(String label, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        label,
        style: const TextStyle(
          fontSize: 10,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    String message;

    if (_selectedCategory != null || _selectedTag != null) {
      message = 'No reminders match the current filters';
    } else {
      message = 'No reminders have been set up yet';
    }

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.notifications_off_outlined,
              size: 64,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              message,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.grey.shade600,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  Color _getPriorityColor(NotificationPriority priority) {
    switch (priority) {
      case NotificationPriority.info:
        return Colors.blue;
      case NotificationPriority.warning:
        return Colors.orange;
      case NotificationPriority.alert:
        return Colors.red;
    }
  }
}
