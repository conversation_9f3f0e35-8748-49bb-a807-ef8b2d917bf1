// File: lib/core/settings/widgets/radio_region_selector.dart
import 'package:flutter/material.dart';
import '../../../core/theme/app_text_styles.dart';

/// A shared widget for selecting region using radio buttons
/// Can be used in both Setup and Settings screens
class RadioRegionSelector extends StatelessWidget {
  final String currentValue;
  final Function(String) onChanged;
  final bool useDialog;
  final bool showCard;

  const RadioRegionSelector({
    Key? key,
    required this.currentValue,
    required this.onChanged,
    this.useDialog = false,
    this.showCard = true,
  }) : super(key: key);

  // Define region options
  static const List<Map<String, String>> regions = [
    {'code': 'us', 'name': 'United States'},
    {'code': 'uk', 'name': 'United Kingdom'},
    {'code': 'eu', 'name': 'European Union'},
    {'code': 'ca', 'name': 'Canada'},
    {'code': 'au', 'name': 'Australia'},
    {'code': 'in', 'name': 'India'},
    {'code': 'cn', 'name': 'China'},
    {'code': 'jp', 'name': 'Japan'},
    {'code': 'br', 'name': 'Brazil'},
    {'code': 'ru', 'name': 'Russia'},
  ];

  @override
  Widget build(BuildContext context) {
    if (useDialog) {
      return ListTile(
        title: const Text('Region'),
        subtitle: Text(_getRegionName(currentValue)),
        leading: const Icon(Icons.public),
        trailing: const Icon(Icons.chevron_right),
        onTap: () => _showRegionDialog(context),
      );
    }

    final content = Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Region',
          style: AppTextStyles.titleMedium,
        ),
        const SizedBox(height: 8),
        const Text(
          'Select your region for localized settings',
          style: AppTextStyles.bodyMedium,
        ),
        const SizedBox(height: 16),
        _buildRegionRadioList(context),
      ],
    );

    // We're no longer using the card to match the language selector styling
    return content;
  }

  Widget _buildRegionRadioList(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final primaryColor = isDarkMode ? Colors.blue[300] : Colors.blue[700];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: regions.map((region) {
        return RadioListTile<String>(
          title: Text(region['name']!),
          value: region['code']!,
          groupValue: currentValue,
          onChanged: (value) {
            if (value != null) {
              onChanged(value);
            }
          },
          activeColor: primaryColor,
          dense: true,
          contentPadding: const EdgeInsets.symmetric(horizontal: 8),
        );
      }).toList(),
    );
  }

  String _getRegionName(String code) {
    final region = regions.firstWhere(
      (r) => r['code'] == code,
      orElse: () => {'code': code, 'name': code},
    );
    return region['name']!;
  }

  void _showRegionDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) {
          String selectedValue = currentValue;

          return AlertDialog(
            title: const Text('Select Region'),
            content: SizedBox(
              width: double.maxFinite,
              child: SingleChildScrollView(
                child: RadioRegionSelector(
                  currentValue: selectedValue,
                  onChanged: (value) {
                    setState(() {
                      selectedValue = value;
                    });
                  },
                  useDialog: false,
                  showCard: false,
                ),
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () {
                  onChanged(selectedValue);
                  Navigator.of(context).pop();
                },
                child: const Text('Save'),
              ),
            ],
          );
        },
      ),
    );
  }

  /// Static method to show a region selection dialog
  static Future<void> showRegionDialog(
    BuildContext context,
    String currentValue,
    Function(String) onChanged,
  ) async {
    String selectedValue = currentValue;

    return showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Region'),
        content: SizedBox(
          width: double.maxFinite,
          child: SingleChildScrollView(
            child: RadioRegionSelector(
              currentValue: selectedValue,
              onChanged: (value) {
                selectedValue = value;
              },
              useDialog: false,
              showCard: false,
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              onChanged(selectedValue);
              Navigator.of(context).pop();
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }
}
