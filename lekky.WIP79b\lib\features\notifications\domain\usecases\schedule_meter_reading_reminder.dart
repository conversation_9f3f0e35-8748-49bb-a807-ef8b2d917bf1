// File: lib/features/notifications/domain/usecases/schedule_meter_reading_reminder.dart

import '../repositories/notification_repository.dart';

/// Use case for scheduling meter reading reminders
class ScheduleMeterReadingReminder {
  final NotificationRepository _repository;

  ScheduleMeterReadingReminder(this._repository);

  /// Execute the use case
  ///
  /// Returns true if the reminder was successfully scheduled, false otherwise.
  Future<bool> execute() async {
    return await _repository.scheduleMeterReadingReminder();
  }

  /// Execute with force reschedule
  ///
  /// This is useful when the time zone has changed or when the app is starting up.
  /// Returns true if the reminder was successfully rescheduled, false otherwise.
  Future<bool> executeWithReschedule({bool forceReschedule = true}) async {
    return await _repository.rescheduleMeterReadingReminders(
      forceReschedule: forceReschedule,
    );
  }
}
