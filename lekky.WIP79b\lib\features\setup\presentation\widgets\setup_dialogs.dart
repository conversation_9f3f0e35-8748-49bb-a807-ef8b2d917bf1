// File: lib/features/setup/presentation/widgets/setup_dialogs.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/extensions/context_extensions.dart';
import '../../../../core/widgets/app_text_field.dart';
import '../../../../core/widgets/info_dialog.dart';
import '../../../../core/widgets/setting_dialog.dart';
import '../../../../core/utils/dialog_button_styles.dart';
import '../../../../core/settings/validators/settings_validator.dart';
import '../../../../core/settings/widgets/currency_selector.dart';
import '../../../../core/settings/widgets/radio_currency_selector.dart';
import '../../../../core/shared_modules/radio_selectors_adapter.dart';
import '../../../../core/settings/widgets/date_format_selector.dart';
import '../../../../core/settings/widgets/date_info_selector.dart';
import '../../../../core/settings/widgets/alert_threshold_input.dart';
import '../../../../core/settings/widgets/days_advance_input.dart';
import '../../../../core/settings/widgets/radio_alert_threshold_selector.dart';
import '../../../../core/settings/widgets/radio_days_in_advance_selector.dart';
import '../../../../core/settings/widgets/notifications_toggle.dart';
import '../controllers/setup_controller.dart';
import 'initial_meter_credit_input.dart';

/// A utility class for showing setup dialogs
class SetupDialogs {
  /// Shows a dialog for selecting the currency
  static Future<void> showCurrencyDialog(
    BuildContext context,
    String currentValue,
    Function(String) onChanged,
  ) async {
    return showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) {
          String selectedValue = currentValue;
          final FocusNode focusNode = FocusNode();

          return Dialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header
                  Row(
                    children: [
                      Icon(
                        Icons.attach_money,
                        color: context.isDarkMode
                            ? AppColors.primaryDark
                            : AppColors.primary,
                        size: 24,
                      ),
                      const SizedBox(width: 10),
                      Text(
                        'Currency',
                        style: AppTextStyles.titleLarge.copyWith(
                          color: context.isDarkMode
                              ? AppColors.primaryDark
                              : AppColors.primary,
                        ),
                      ),
                      const Spacer(),
                      IconButton(
                        icon: const Icon(Icons.close),
                        onPressed: () => Navigator.of(context).pop(),
                        tooltip: 'Close',
                        constraints: const BoxConstraints(),
                        padding: EdgeInsets.zero,
                        iconSize: 20,
                      ),
                    ],
                  ),

                  // Subtitle
                  Padding(
                    padding: const EdgeInsets.only(
                        left: 34), // Align with title text
                    child: Text(
                      'Select the currency for your meter readings',
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: context.textColor,
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Content
                  RadioCurrencySelector(
                    currentValue: selectedValue,
                    onChanged: (value) {
                      setState(() {
                        selectedValue = value;
                      });
                    },
                    useDialog: false,
                    showCard: false,
                    focusNode: focusNode,
                  ),
                  const SizedBox(height: 16),

                  // "Got it" button
                  Center(
                    child: TextButton(
                      onPressed: () {
                        onChanged(selectedValue);
                        Navigator.of(context).pop();
                      },
                      style: TextButton.styleFrom(
                        foregroundColor: context.isDarkMode
                            ? AppColors.primaryDark
                            : AppColors.primary,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 24, vertical: 12),
                      ),
                      child: Text(
                        'Got it',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                          color: context.isDarkMode
                              ? AppColors.primaryDark
                              : AppColors.primary,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  /// Shows a dialog for setting alert threshold and days in advance
  static Future<void> showAlertSettingsDialog(
    BuildContext context,
    double currentThreshold,
    int currentDaysInAdvance,
    String meterUnit,
    Function(double) onThresholdChanged,
    Function(int) onDaysInAdvanceChanged,
  ) async {
    return showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) {
          double threshold = currentThreshold;
          int daysInAdvance = currentDaysInAdvance;

          return SettingDialog(
            title: 'Alert Settings',
            subtitle:
                'Configure when you want to be notified about low meter balance',
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Alert Threshold
                Text(
                  'Alert Threshold',
                  style: AppTextStyles.titleSmall.copyWith(
                    color: context.textColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'You will be notified when your meter balance falls below this amount',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: context.secondaryTextColor,
                  ),
                ),
                const SizedBox(height: 8),
                RadioAlertThresholdSelector(
                  currentValue: threshold,
                  onChanged: (value) {
                    setState(() {
                      threshold = value;
                    });
                  },
                  currencySymbol: meterUnit,
                  showTitle: false,
                ),

                const SizedBox(height: 16),
                const Divider(),
                const SizedBox(height: 16),

                // Days in Advance Alert
                Text(
                  'Days in Advance Alert',
                  style: AppTextStyles.titleSmall.copyWith(
                    color: context.textColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'How many days before you run out of credit should we notify you',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: context.secondaryTextColor,
                  ),
                ),
                const SizedBox(height: 8),
                RadioDaysInAdvanceSelector(
                  currentValue: daysInAdvance,
                  onChanged: (value) {
                    setState(() {
                      daysInAdvance = value;
                    });
                  },
                  showTitle: false,
                ),
              ],
            ),
            onCancel: () {},
            onSave: () {
              // Save values
              onThresholdChanged(threshold);
              onDaysInAdvanceChanged(daysInAdvance);
              Navigator.of(context).pop();
            },
          );
        },
      ),
    );
  }

  /// Shows a dialog for setting the first meter reading
  static Future<void> showFirstMeterReadingDialog(
    BuildContext context,
    double? currentInitialCredit,
    String meterUnit,
    Function(double?) onInitialCreditChanged,
  ) async {
    return showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) {
          double? initialCredit = currentInitialCredit;
          String? errorText;

          final controller = TextEditingController(
            text: initialCredit?.toString() ?? '',
          );

          return Dialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header
                  Row(
                    children: [
                      Icon(
                        Icons.speed,
                        color: context.isDarkMode
                            ? AppColors.primaryDark
                            : AppColors.primary,
                        size: 24,
                      ),
                      const SizedBox(width: 10),
                      Text(
                        'First Meter Reading',
                        style: AppTextStyles.titleLarge.copyWith(
                          color: context.isDarkMode
                              ? AppColors.primaryDark
                              : AppColors.primary,
                        ),
                      ),
                      const Spacer(),
                      IconButton(
                        icon: const Icon(Icons.close),
                        onPressed: () => Navigator.of(context).pop(),
                        tooltip: 'Close',
                        constraints: const BoxConstraints(),
                        padding: EdgeInsets.zero,
                        iconSize: 20,
                      ),
                    ],
                  ),

                  // Subtitle
                  Padding(
                    padding: const EdgeInsets.only(
                        left: 34), // Align with title text
                    child: Text(
                      'Enter your initial meter credit (optional)',
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: context.textColor,
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Content
                  AppTextField(
                    controller: controller,
                    keyboardType:
                        const TextInputType.numberWithOptions(decimal: true),
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*$')),
                    ],
                    prefixText: meterUnit,
                    errorText: errorText,
                    selectAllOnFocus: true,
                    onChanged: (value) {
                      if (value.isEmpty) {
                        setState(() {
                          initialCredit = null;
                          errorText = null;
                        });
                        return;
                      }

                      final validation =
                          SettingsValidator.validateInitialCredit(value);
                      setState(() {
                        errorText = validation['isValid']
                            ? null
                            : validation['errorMessage'];
                        if (validation['isValid']) {
                          initialCredit = double.tryParse(value);
                        }
                      });
                    },
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Leave blank if you don\'t want to track your initial credit',
                    style: AppTextStyles.bodySmall.copyWith(
                      fontStyle: FontStyle.italic,
                      color: context.secondaryTextColor,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // "Got it" button
                  Center(
                    child: TextButton(
                      onPressed: () {
                        // Validate if not empty
                        if (controller.text.isNotEmpty) {
                          final validation =
                              SettingsValidator.validateInitialCredit(
                                  controller.text);
                          if (!validation['isValid']) {
                            setState(() {
                              errorText = validation['errorMessage'];
                            });
                            return;
                          }
                        }

                        // Save value
                        onInitialCreditChanged(initialCredit);
                        Navigator.of(context).pop();
                      },
                      style: TextButton.styleFrom(
                        foregroundColor: context.isDarkMode
                            ? AppColors.primaryDark
                            : AppColors.primary,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 24, vertical: 12),
                      ),
                      child: Text(
                        'Got it',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                          color: context.isDarkMode
                              ? AppColors.primaryDark
                              : AppColors.primary,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  /// Shows a dialog for setting display settings (date format and date info)
  static Future<void> showDisplaySettingsDialog(
    BuildContext context,
    String currentDateFormat,
    String currentDateInfo,
    Function(String) onDateFormatChanged,
    Function(String) onDateInfoChanged,
  ) async {
    return showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) {
          String dateFormat = currentDateFormat;
          String dateInfo = currentDateInfo;

          return Dialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header
                  Row(
                    children: [
                      Icon(
                        Icons.palette_outlined,
                        color: context.isDarkMode
                            ? AppColors.primaryDark
                            : AppColors.primary,
                        size: 24,
                      ),
                      const SizedBox(width: 10),
                      Text(
                        'Display Settings',
                        style: AppTextStyles.titleLarge.copyWith(
                          color: context.isDarkMode
                              ? AppColors.primaryDark
                              : AppColors.primary,
                        ),
                      ),
                      const Spacer(),
                      IconButton(
                        icon: const Icon(Icons.close),
                        onPressed: () => Navigator.of(context).pop(),
                        tooltip: 'Close',
                        constraints: const BoxConstraints(),
                        padding: EdgeInsets.zero,
                        iconSize: 20,
                      ),
                    ],
                  ),

                  // Subtitle
                  Padding(
                    padding: const EdgeInsets.only(
                        left: 34), // Align with title text
                    child: Text(
                      'Configure how dates are displayed in the app',
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: context.textColor,
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Content
                  Flexible(
                    child: SingleChildScrollView(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Date Format
                          Text(
                            'Date Format',
                            style: AppTextStyles.titleSmall.copyWith(
                              color: context.textColor,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'Choose how dates are formatted',
                            style: AppTextStyles.bodySmall.copyWith(
                              color: context.secondaryTextColor,
                            ),
                          ),
                          const SizedBox(height: 8),
                          DateFormatSelector(
                            currentValue: dateFormat,
                            onChanged: (value) {
                              setState(() {
                                dateFormat = value;
                              });
                            },
                            useDialog: false,
                            showCard: false,
                          ),

                          const SizedBox(height: 16),
                          const Divider(),
                          const SizedBox(height: 16),

                          // Date Info
                          Text(
                            'Date Information',
                            style: AppTextStyles.titleSmall.copyWith(
                              color: context.textColor,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'Choose what date information to display',
                            style: AppTextStyles.bodySmall.copyWith(
                              color: context.secondaryTextColor,
                            ),
                          ),
                          const SizedBox(height: 8),
                          DateInfoSelector(
                            currentValue: dateInfo,
                            onChanged: (value) {
                              setState(() {
                                dateInfo = value;
                              });
                            },
                            useDialog: false,
                            showCard: false,
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),

                  // "Got it" button
                  Center(
                    child: TextButton(
                      onPressed: () {
                        onDateFormatChanged(dateFormat);
                        onDateInfoChanged(dateInfo);
                        Navigator.of(context).pop();
                      },
                      style: TextButton.styleFrom(
                        foregroundColor: context.isDarkMode
                            ? AppColors.primaryDark
                            : AppColors.primary,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 24, vertical: 12),
                      ),
                      child: Text(
                        'Got it',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                          color: context.isDarkMode
                              ? AppColors.primaryDark
                              : AppColors.primary,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  /// Shows a dialog for setting notification preferences
  static Future<void> showNotificationsDialog(
    BuildContext context,
    bool currentNotificationsEnabled,
    Function(bool) onNotificationsEnabledChanged,
  ) async {
    return showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) {
          bool notificationsEnabled = currentNotificationsEnabled;

          return Dialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header
                  Row(
                    children: [
                      Icon(
                        Icons.notifications,
                        color: context.isDarkMode
                            ? AppColors.primaryDark
                            : AppColors.primary,
                        size: 24,
                      ),
                      const SizedBox(width: 10),
                      Text(
                        'Notifications',
                        style: AppTextStyles.titleLarge.copyWith(
                          color: context.isDarkMode
                              ? AppColors.primaryDark
                              : AppColors.primary,
                        ),
                      ),
                      const Spacer(),
                      IconButton(
                        icon: const Icon(Icons.close),
                        onPressed: () => Navigator.of(context).pop(),
                        tooltip: 'Close',
                        constraints: const BoxConstraints(),
                        padding: EdgeInsets.zero,
                        iconSize: 20,
                      ),
                    ],
                  ),

                  // Subtitle
                  Padding(
                    padding: const EdgeInsets.only(
                        left: 34), // Align with title text
                    child: Text(
                      'Configure notification preferences',
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: context.textColor,
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Content
                  SwitchListTile(
                    title: Text(
                      'Enable Notifications',
                      style: TextStyle(
                        color: context.textColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    subtitle: Text(
                      notificationsEnabled
                          ? 'You will receive notifications about low meter balance'
                          : 'You will not receive any notifications',
                      style: TextStyle(
                        color: context.secondaryTextColor,
                      ),
                    ),
                    value: notificationsEnabled,
                    onChanged: (value) {
                      setState(() {
                        notificationsEnabled = value;
                      });
                    },
                    activeColor: context.isDarkMode
                        ? AppColors.primaryDark
                        : AppColors.primary,
                    dense: true,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Notifications will be sent when your meter balance falls below the alert threshold',
                    style: AppTextStyles.bodySmall.copyWith(
                      fontStyle: FontStyle.italic,
                      color: context.secondaryTextColor,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // "Got it" button
                  Center(
                    child: TextButton(
                      onPressed: () {
                        onNotificationsEnabledChanged(notificationsEnabled);
                        Navigator.of(context).pop();
                      },
                      style: TextButton.styleFrom(
                        foregroundColor: context.isDarkMode
                            ? AppColors.primaryDark
                            : AppColors.primary,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 24, vertical: 12),
                      ),
                      child: Text(
                        'Got it',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                          color: context.isDarkMode
                              ? AppColors.primaryDark
                              : AppColors.primary,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
