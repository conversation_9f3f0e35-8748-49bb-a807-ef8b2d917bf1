# Lekky App Color Palette

This document defines the complete color palette for the Lekky app in both light and dark modes. The colors are organized by their semantic roles to ensure consistent usage throughout the application.

## Tab and Screen Colors

| Screen | Primary Color | Gradient | Description |
|--------|--------------|----------|-------------|
| Home | `#49D941` (Green) | `[#49D941, #36D1DC]` | Vibrant green for the home screen |
| Meter | `#36D1DC` (Blue) | `[#36D1DC, #5B86E5]` | Blue for meter readings |
| Top-Up | `#FF9800` (Orange) | `[#FF9800, #FFB74D]` | Orange for top-ups |
| History | `#9C27B0` (Purple) | `[#9C27B0, #BA68C8]` | Darker purple for history, matching the blue theme |
| Cost | `#E65100` (Dark Orange) | `[#E65100, #FF8A65]` | Darker orange for cost, matching the blue theme |
| Settings | `#D8DEDB` (Light Grey) | `[#424242, #616161]` | Grey for settings |
| Setup | N/A | `[#424242, #616161]` | Dark grey for setup |
| Welcome | N/A | `[#003087, #0057B8]` | Primary blue for welcome screen |

## Light Mode

### Text

| Role | Hex Code | Description |
|------|----------|-------------|
| `--color-text-primary` | `#212121` | Primary text color for headings and body text. Used for most text content with a contrast ratio of 16:1 against white backgrounds. |
| `--color-text-secondary` | `#757575` | Secondary text color for less important information, subtitles, and hints. Provides a contrast ratio of 4.6:1 against white backgrounds. |
| `--color-text-disabled` | `#BDBDBD` | Used for disabled text elements. Should only be applied to non-interactive or disabled elements. |
| `--color-text-link` | `#1976D2` | Used for links and interactive text elements. Provides a contrast ratio of 4.5:1 against white backgrounds. |
| `--color-text-error` | `#D32F2F` | Used for error messages and validation text. Provides a contrast ratio of 4.6:1 against white backgrounds. |
| `--color-text-success` | `#388E3C` | Used for success messages and confirmations. Provides a contrast ratio of 4.8:1 against white backgrounds. |
| `--color-text-on-primary` | `#FFFFFF` | Text color used on primary colored backgrounds. Provides a contrast ratio of 4.5:1 against the primary color. |
| `--color-text-on-accent` | `#FFFFFF` | Text color used on accent colored backgrounds. Ensures readability with a contrast ratio of at least 4.5:1. |

### Backgrounds

| Role | Hex Code | Description |
|------|----------|-------------|
| `--color-bg-app` | `#F5F5F5` | Main application background color. Used for the overall app background to provide a neutral, light base. |
| `--color-bg-card` | `#FFFFFF` | Used for card backgrounds, providing subtle elevation against the app background. |
| `--color-bg-surface` | `#FFFFFF` | Used for surface elements like dialogs, sheets, and menus. |
| `--color-bg-input` | `#FFFFFF` | Background color for input fields and form elements. |
| `--color-bg-overlay` | `rgba(0, 0, 0, 0.5)` | Semi-transparent overlay for modals and dialogs. |
| `--color-bg-disabled` | `#F5F5F5` | Background color for disabled elements and controls. |
| `--color-bg-hover` | `rgba(0, 0, 0, 0.04)` | Subtle hover state background for interactive elements. |
| `--color-bg-selected` | `rgba(25, 118, 210, 0.08)` | Background color for selected items or active states. |

### Other

| Role | Hex Code | Description |
|------|----------|-------------|
| `--color-primary` | `#1976D2` | Primary brand color used for main actions, buttons, and key UI elements. |
| `--color-primary-light` | `#42A5F5` | Lighter variant of the primary color for hover states and secondary elements. |
| `--color-primary-dark` | `#0D47A1` | Darker variant of the primary color for active states and emphasis. |
| `--color-accent` | `#FF9800` | Accent color used for floating action buttons, highlights, and to draw attention to specific elements. |
| `--color-accent-light` | `#FFB74D` | Lighter variant of the accent color for hover states and secondary accents. |
| `--color-accent-dark` | `#F57C00` | Darker variant of the accent color for active states and emphasis. |
| `--color-border` | `#E0E0E0` | Used for borders, dividers, and separators throughout the interface. |
| `--color-success` | `#4CAF50` | Used for success states, confirmations, and positive actions. |
| `--color-warning` | `#FFC107` | Used for warnings and cautionary messages. |
| `--color-error` | `#F44336` | Used for error states, destructive actions, and critical alerts. |
| `--color-info` | `#2196F3` | Used for informational messages and neutral notifications. |
| `--color-shadow` | `rgba(0, 0, 0, 0.1)` | Used for shadows to create elevation and depth in the interface. |

## Dark Mode

### Text

| Role | Hex Code | Description |
|------|----------|-------------|
| `--color-text-primary-dark` | `#FFFFFF` | Primary text color for headings and body text in dark mode. Provides a contrast ratio of 16:1 against dark backgrounds. |
| `--color-text-secondary-dark` | `#B0B0B0` | Secondary text color for less important information in dark mode. Provides a contrast ratio of 4.5:1 against dark backgrounds. |
| `--color-text-disabled-dark` | `#757575` | Used for disabled text elements in dark mode. Should only be applied to non-interactive elements. |
| `--color-text-link-dark` | `#90CAF9` | Used for links and interactive text elements in dark mode. Provides a contrast ratio of 4.5:1 against dark backgrounds. |
| `--color-text-error-dark` | `#EF9A9A` | Used for error messages in dark mode. Provides a contrast ratio of 4.5:1 against dark backgrounds. |
| `--color-text-success-dark` | `#A5D6A7` | Used for success messages in dark mode. Provides a contrast ratio of 4.5:1 against dark backgrounds. |
| `--color-text-on-primary-dark` | `#FFFFFF` | Text color used on primary colored backgrounds in dark mode. |
| `--color-text-on-accent-dark` | `#000000` | Text color used on accent colored backgrounds in dark mode. |

### Backgrounds

| Role | Hex Code | Description |
|------|----------|-------------|
| `--color-bg-app-dark` | `#121212` | Main application background color in dark mode. Used for the overall app background. |
| `--color-bg-card-dark` | `#1E1E1E` | Used for card backgrounds in dark mode, providing subtle elevation against the app background. |
| `--color-bg-surface-dark` | `#242424` | Used for surface elements like dialogs, sheets, and menus in dark mode. |
| `--color-bg-input-dark` | `#2A2A2A` | Background color for input fields and form elements in dark mode. |
| `--color-bg-overlay-dark` | `rgba(0, 0, 0, 0.7)` | Semi-transparent overlay for modals and dialogs in dark mode. |
| `--color-bg-disabled-dark` | `#2A2A2A` | Background color for disabled elements and controls in dark mode. |
| `--color-bg-hover-dark` | `rgba(255, 255, 255, 0.08)` | Subtle hover state background for interactive elements in dark mode. |
| `--color-bg-selected-dark` | `rgba(64, 196, 255, 0.16)` | Background color for selected items or active states in dark mode. |

### Other

| Role | Hex Code | Description |
|------|----------|-------------|
| `--color-primary-dark` | `#42A5F5` | Primary brand color in dark mode, brighter than light mode to ensure visibility. |
| `--color-primary-light-dark` | `#90CAF9` | Lighter variant of the primary color in dark mode. |
| `--color-primary-dark-dark` | `#1976D2` | Darker variant of the primary color in dark mode. |
| `--color-accent-dark` | `#FFB74D` | Accent color in dark mode, brighter than light mode to ensure visibility. |
| `--color-accent-light-dark` | `#FFCC80` | Lighter variant of the accent color in dark mode. |
| `--color-accent-dark-dark` | `#FF9800` | Darker variant of the accent color in dark mode. |
| `--color-border-dark` | `#424242` | Used for borders, dividers, and separators in dark mode. |
| `--color-success-dark` | `#66BB6A` | Used for success states in dark mode. |
| `--color-warning-dark` | `#FFD54F` | Used for warnings in dark mode. |
| `--color-error-dark` | `#E57373` | Used for error states in dark mode. |
| `--color-info-dark` | `#64B5F6` | Used for informational messages in dark mode. |
| `--color-shadow-dark` | `rgba(0, 0, 0, 0.3)` | Used for shadows in dark mode to create elevation and depth. |

## Usage Guidelines

1. Always use the semantic color roles rather than hardcoded hex values.
2. Ensure sufficient contrast between text and background colors (minimum 4.5:1 for normal text, 3:1 for large text).
3. Use primary colors for main actions and key UI elements.
4. Use accent colors sparingly to highlight important elements or calls to action.
5. For dark mode, automatically switch to the corresponding dark mode color roles.
6. Maintain consistent use of colors for similar UI elements and states throughout the app.
7. Use success, warning, and error colors consistently for their respective states.
