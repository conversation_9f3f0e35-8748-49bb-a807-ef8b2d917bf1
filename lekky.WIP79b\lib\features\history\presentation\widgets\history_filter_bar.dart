// File: lib/features/history/presentation/widgets/history_filter_bar.dart
import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/utils/date_time_utils.dart';
import '../../../../core/widgets/dialogs/date_picker_dialog.dart'
    as custom_date_picker;
import '../../domain/models/history_filter.dart';
import '../controllers/history_controller.dart';

/// A filter bar for the history screen
class HistoryFilterBar extends StatefulWidget {
  final HistoryFilter filter;
  final Future<void> Function(HistoryFilter) onFilterChanged;
  final Future<void> Function() onResetFilter;
  final HistoryController controller;

  const HistoryFilterBar({
    super.key,
    required this.filter,
    required this.onFilterChanged,
    required this.onResetFilter,
    required this.controller,
  });

  @override
  State<HistoryFilterBar> createState() => _HistoryFilterBarState();
}

class _HistoryFilterBarState extends State<HistoryFilterBar> {
  late HistoryFilter _filter;
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    _filter = widget.filter;
  }

  @override
  void didUpdateWidget(HistoryFilterBar oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.filter != widget.filter) {
      _filter = widget.filter;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ListTile(
              title: Text(
                'Filter Options',
                style: AppTextStyles.titleMedium.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.primary,
                ),
              ),
              trailing: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextButton(
                    onPressed: widget.onResetFilter,
                    child: Text(
                      'Reset',
                      style: AppTextStyles.labelLarge.copyWith(
                        color: AppColors.primary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  IconButton(
                    icon: Icon(
                      _isExpanded ? Icons.expand_less : Icons.expand_more,
                      color: AppColors.primary,
                    ),
                    onPressed: () {
                      setState(() {
                        _isExpanded = !_isExpanded;
                      });
                    },
                  ),
                ],
              ),
            ),
            if (_isExpanded) ...[
              const Divider(),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildDateRangeFilter(),
                    const SizedBox(height: 16),
                    _buildEntryTypeFilter(),
                    const SizedBox(height: 16),
                    _buildSortingOptions(),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildDateRangeFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Date Range',
          style: AppTextStyles.titleSmall.copyWith(
            color: AppColors.tertiary,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: InkWell(
                onTap: () => _selectStartDate(context),
                child: InputDecorator(
                  decoration: InputDecoration(
                    labelText: 'Start Date',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                  ),
                  child: Text(
                    _filter.startDate != null
                        ? DateTimeUtils.formatDateDefault(_filter.startDate!)
                        : 'Select Start Date',
                  ),
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: InkWell(
                onTap: () => _selectEndDate(context),
                child: InputDecorator(
                  decoration: InputDecoration(
                    labelText: 'End Date',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                  ),
                  child: Text(
                    _filter.endDate != null
                        ? DateTimeUtils.formatDateDefault(_filter.endDate!)
                        : 'Select End Date',
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildEntryTypeFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Entry Type',
          style: AppTextStyles.titleSmall.copyWith(
            color: AppColors.tertiary,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: CheckboxListTile(
                title: Text(
                  'Meter Readings',
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: AppColors.primary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                value: _filter.showMeterReadings,
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _filter = _filter.copyWith(showMeterReadings: value);
                    });
                    widget.onFilterChanged(_filter);
                  }
                },
                activeColor: AppColors.primary,
                controlAffinity: ListTileControlAffinity.leading,
                dense: true,
              ),
            ),
            Expanded(
              child: CheckboxListTile(
                title: Text(
                  'Top-ups',
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: AppColors.tertiary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                value: _filter.showTopUps,
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _filter = _filter.copyWith(showTopUps: value);
                    });
                    widget.onFilterChanged(_filter);
                  }
                },
                activeColor: AppColors.tertiary,
                controlAffinity: ListTileControlAffinity.leading,
                dense: true,
              ),
            ),
          ],
        ),
        CheckboxListTile(
          title: Text(
            'Show Invalid Entries',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.error,
              fontWeight: FontWeight.w500,
            ),
          ),
          value: _filter.showInvalidEntries,
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _filter = _filter.copyWith(showInvalidEntries: value);
              });
              widget.onFilterChanged(_filter);
            }
          },
          activeColor: AppColors.error,
          controlAffinity: ListTileControlAffinity.leading,
          dense: true,
        ),
      ],
    );
  }

  Widget _buildSortingOptions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Sorting',
          style: AppTextStyles.titleSmall.copyWith(
            color: AppColors.tertiary,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: DropdownButtonFormField<String>(
                decoration: InputDecoration(
                  labelText: 'Sort By',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 8,
                  ),
                ),
                value: _filter.sortBy,
                items: const [
                  DropdownMenuItem(
                    value: 'date',
                    child: Text('Date'),
                  ),
                  DropdownMenuItem(
                    value: 'amount',
                    child: Text('Amount'),
                  ),
                ],
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _filter = _filter.copyWith(sortBy: value);
                    });
                    widget.onFilterChanged(_filter);
                  }
                },
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: DropdownButtonFormField<bool>(
                decoration: InputDecoration(
                  labelText: 'Order',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 8,
                  ),
                ),
                value: _filter.sortAscending,
                items: const [
                  DropdownMenuItem(
                    value: false,
                    child: Text('Descending'),
                  ),
                  DropdownMenuItem(
                    value: true,
                    child: Text('Ascending'),
                  ),
                ],
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _filter = _filter.copyWith(sortAscending: value);
                    });
                    widget.onFilterChanged(_filter);
                  }
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Future<void> _selectStartDate(BuildContext context) async {
    final initialDate = _filter.startDate ?? DateTime.now();
    final firstDate = DateTime(2000);
    final lastDate = _filter.endDate ?? DateTime.now();

    // Check the Date Info setting
    final dateInfo = await widget.controller.getDateInfo();
    final includeTime = dateInfo == 'Date and time';

    // Store context in local variable to avoid BuildContext issues
    if (!mounted) return;
    final localContext = context;

    DateTime? selectedDateTime;

    try {
      if (includeTime) {
        // Show date and time picker if Date Info is set to "Date and time"
        if (!mounted) return;

        // Use a synchronous function to avoid BuildContext issues
        selectedDateTime = await Future<DateTime?>.sync(() async {
          if (!mounted) return null;
          return await custom_date_picker.DatePickerDialog.showDateTimePicker(
            context: localContext,
            title: 'Select Start Date and Time',
            initialDate: initialDate,
            firstDate: firstDate,
            lastDate: lastDate,
            currentTime: TimeOfDay.fromDateTime(initialDate),
            includeTime: true,
            addCurrentSecond: true, // Add current second to avoid duplicates
            confirmText: 'Select',
            cancelText: 'Cancel',
            helpText: 'Choose a start date and time for filtering',
          );
        });
      } else {
        // Show only date picker if Date Info is set to "Date only"
        if (!mounted) return;

        // Use a synchronous function to avoid BuildContext issues
        final pickedDate = await Future<DateTime?>.sync(() async {
          if (!mounted) return null;
          return await custom_date_picker.DatePickerDialog.showDatePicker(
            context: localContext,
            title: 'Select Start Date',
            initialDate: initialDate,
            firstDate: firstDate,
            lastDate: lastDate,
            helpText: 'Choose a start date for filtering',
          );
        });

        if (pickedDate != null) {
          // Use current time when Date Info is set to "Date only"
          final now = DateTime.now();
          selectedDateTime = DateTime(
            pickedDate.year,
            pickedDate.month,
            pickedDate.day,
            now.hour,
            now.minute,
            now.second,
          );
        }
      }

      if (selectedDateTime != null && mounted) {
        setState(() {
          _filter = _filter.copyWith(startDate: selectedDateTime);
        });
        await widget.onFilterChanged(_filter);
      }
    } catch (e) {
      // Handle any errors that might occur during date picking
      if (mounted) {
        // Use a safer approach to show error
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('Error selecting date: $e')),
            );
          }
        });
      }
    }
  }

  Future<void> _selectEndDate(BuildContext context) async {
    final initialDate = _filter.endDate ?? DateTime.now();
    final firstDate = _filter.startDate ?? DateTime(2000);
    final lastDate = DateTime.now();

    // Check the Date Info setting
    final dateInfo = await widget.controller.getDateInfo();
    final includeTime = dateInfo == 'Date and time';

    // Store context in local variable to avoid BuildContext issues
    if (!mounted) return;
    final localContext = context;

    DateTime? selectedDateTime;

    try {
      if (includeTime) {
        // Show date and time picker if Date Info is set to "Date and time"
        if (!mounted) return;

        // Use a synchronous function to avoid BuildContext issues
        selectedDateTime = await Future<DateTime?>.sync(() async {
          if (!mounted) return null;
          return await custom_date_picker.DatePickerDialog.showDateTimePicker(
            context: localContext,
            title: 'Select End Date and Time',
            initialDate: initialDate,
            firstDate: firstDate,
            lastDate: lastDate,
            currentTime: TimeOfDay.fromDateTime(initialDate),
            includeTime: true,
            addCurrentSecond: true, // Add current second to avoid duplicates
            confirmText: 'Select',
            cancelText: 'Cancel',
            helpText: 'Choose an end date and time for filtering',
          );
        });
      } else {
        // Show only date picker if Date Info is set to "Date only"
        if (!mounted) return;

        // Use a synchronous function to avoid BuildContext issues
        final pickedDate = await Future<DateTime?>.sync(() async {
          if (!mounted) return null;
          return await custom_date_picker.DatePickerDialog.showDatePicker(
            context: localContext,
            title: 'Select End Date',
            initialDate: initialDate,
            firstDate: firstDate,
            lastDate: lastDate,
            helpText: 'Choose an end date for filtering',
          );
        });

        if (pickedDate != null) {
          // Use current time when Date Info is set to "Date only"
          final now = DateTime.now();
          selectedDateTime = DateTime(
            pickedDate.year,
            pickedDate.month,
            pickedDate.day,
            now.hour,
            now.minute,
            now.second,
          );
        }
      }

      if (selectedDateTime != null && mounted) {
        setState(() {
          _filter = _filter.copyWith(endDate: selectedDateTime);
        });
        await widget.onFilterChanged(_filter);
      }
    } catch (e) {
      // Handle any errors that might occur during date picking
      if (mounted) {
        // Use a safer approach to show error
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('Error selecting date: $e')),
            );
          }
        });
      }
    }
  }
}
