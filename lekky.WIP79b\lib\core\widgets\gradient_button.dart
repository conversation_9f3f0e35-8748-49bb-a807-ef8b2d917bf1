// File: lib/core/widgets/gradient_button.dart
import 'package:flutter/material.dart';
import '../theme/app_colors.dart';
import '../theme/app_text_styles.dart';
import '../utils/responsive_layout.dart';

/// A button with a gradient background
class GradientButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final List<Color>? gradientColors;
  final Alignment? gradientBegin;
  final Alignment? gradientEnd;
  final EdgeInsetsGeometry? padding;
  final double? height;
  final double? width;
  final BorderRadius? borderRadius;
  final Widget? icon;
  final double? elevation;
  final TextStyle? textStyle;
  final bool isLoading;
  final Color? loadingColor;
  final double? loadingSize;
  final bool isOutlined;
  final Color? outlineColor;
  final double? outlineWidth;

  const GradientButton({
    Key? key,
    required this.text,
    required this.onPressed,
    this.gradientColors,
    this.gradientBegin,
    this.gradientEnd,
    this.padding,
    this.height,
    this.width,
    this.borderRadius,
    this.icon,
    this.elevation,
    this.textStyle,
    this.isLoading = false,
    this.loadingColor,
    this.loadingSize,
    this.isOutlined = false,
    this.outlineColor,
    this.outlineWidth,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final defaultGradientColors =
        isDarkMode ? AppColors.primaryGradient : AppColors.primaryGradient;
    final defaultLoadingColor =
        isDarkMode ? AppColors.onPrimaryDark : AppColors.onPrimary;
    final defaultOutlineColor =
        isDarkMode ? AppColors.primaryDark : AppColors.primary;

    final responsiveHeight =
        height ?? ResponsiveLayout.getButtonHeightForScreenType(context);
    final responsiveBorderRadius = borderRadius ??
        BorderRadius.circular(
            ResponsiveLayout.getBorderRadiusForScreenType(context));
    final responsivePadding =
        padding ?? const EdgeInsets.symmetric(horizontal: 24, vertical: 12);
    final responsiveElevation = elevation ?? 2.0;
    final responsiveLoadingSize = loadingSize ?? 24.0;

    return Material(
      elevation: responsiveElevation,
      borderRadius: responsiveBorderRadius,
      color: AppColors.transparent,
      child: InkWell(
        onTap: isLoading ? null : onPressed,
        borderRadius: responsiveBorderRadius,
        child: Container(
          height: responsiveHeight,
          width: width,
          padding: responsivePadding,
          decoration: BoxDecoration(
            gradient: isOutlined
                ? null
                : LinearGradient(
                    begin: gradientBegin ?? Alignment.centerLeft,
                    end: gradientEnd ?? Alignment.centerRight,
                    colors: gradientColors ?? defaultGradientColors,
                  ),
            borderRadius: responsiveBorderRadius,
            border: isOutlined
                ? Border.all(
                    color: outlineColor ?? defaultOutlineColor,
                    width: outlineWidth ?? 2.0,
                  )
                : null,
          ),
          child: Center(
            child: isLoading
                ? SizedBox(
                    width: responsiveLoadingSize,
                    height: responsiveLoadingSize,
                    child: CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(
                        loadingColor ?? defaultLoadingColor,
                      ),
                      strokeWidth: 3.0,
                    ),
                  )
                : Row(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      if (icon != null) ...[
                        icon!,
                        const SizedBox(width: 8),
                      ],
                      Text(
                        text,
                        style: textStyle ??
                            AppTextStyles.labelLarge.copyWith(
                              color: isOutlined
                                  ? (outlineColor ?? defaultOutlineColor)
                                  : (isDarkMode
                                      ? AppColors.onPrimaryDark
                                      : AppColors.onPrimary),
                              fontWeight: FontWeight.bold,
                            ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
          ),
        ),
      ),
    );
  }
}
