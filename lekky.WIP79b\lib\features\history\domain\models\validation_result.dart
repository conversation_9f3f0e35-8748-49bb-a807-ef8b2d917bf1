// File: lib/features/history/domain/models/validation_result.dart

/// Result of validating a meter entry
class ValidationResult {
  final bool isValid;
  final String? errorMessage;
  final String severity;

  const ValidationResult({
    required this.isValid,
    this.errorMessage,
    this.severity = 'none',
  });

  /// Creates a valid result
  factory ValidationResult.valid() {
    return const ValidationResult(
      isValid: true,
      severity: 'none',
    );
  }

  /// Creates an error result
  factory ValidationResult.error(String message) {
    return ValidationResult(
      isValid: false,
      errorMessage: message,
      severity: 'error',
    );
  }

  /// Creates a warning result
  factory ValidationResult.warning(String message) {
    return ValidationResult(
      isValid: false,
      errorMessage: message,
      severity: 'warning',
    );
  }

  /// Creates a result from a map
  factory ValidationResult.fromMap(Map<String, dynamic> map) {
    return ValidationResult(
      isValid: map['isValid'] ?? false,
      errorMessage: map['errorMessage'],
      severity: map['severity'] ?? 'none',
    );
  }

  /// Converts this result to a map
  Map<String, dynamic> toMap() {
    return {
      'isValid': isValid,
      'errorMessage': errorMessage,
      'severity': severity,
    };
  }
}
