// File: lib/features/notifications/presentation/widgets/permission_denied_dialog.dart

import 'package:flutter/material.dart';

/// Alternative options for when notification permissions are denied
enum PermissionAlternative {
  openSettings,
  calendarReminders,
  inAppReminders,
  skip,
}

/// A dialog that shows alternative options when notification permissions are denied
class PermissionDeniedDialog extends StatelessWidget {
  final bool isPermanentlyDenied;
  final Function(PermissionAlternative) onOptionSelected;

  const PermissionDeniedDialog({
    Key? key,
    required this.isPermanentlyDenied,
    required this.onOptionSelected,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  Icons.notification_important,
                  color: Colors.orange,
                  size: 24,
                ),
                SizedBox(width: 8),
                Expanded(
                  child: Text(
                    isPermanentlyDenied
                        ? 'Notifications Disabled'
                        : 'Notifications Denied',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 16),

            // Explanation
            Text(
              isPermanentlyDenied
                  ? 'Notifications are permanently disabled. Without notifications, you won\'t receive reminders when it\'s time to submit your meter readings.'
                  : 'Without notification permissions, we can\'t send you reminders when it\'s time to submit your meter readings.',
              style: TextStyle(fontSize: 14),
            ),
            SizedBox(height: 20),

            // Options
            Text(
              'What would you like to do?',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
            SizedBox(height: 12),

            // Option 1: Open Settings
            if (isPermanentlyDenied)
              _buildOption(
                context,
                icon: Icons.settings,
                title: 'Open Settings',
                description:
                    'Go to your device settings to enable notifications',
                onTap: () =>
                    onOptionSelected(PermissionAlternative.openSettings),
              ),

            // Option 2: Try Again (only if not permanently denied)
            if (!isPermanentlyDenied)
              _buildOption(
                context,
                icon: Icons.refresh,
                title: 'Try Again',
                description: 'Request notification permission again',
                onTap: () =>
                    onOptionSelected(PermissionAlternative.openSettings),
              ),

            // Option 3: Calendar Reminders
            _buildOption(
              context,
              icon: Icons.calendar_today,
              title: 'Use Calendar Reminders',
              description: 'Add reminders to your device calendar instead',
              onTap: () =>
                  onOptionSelected(PermissionAlternative.calendarReminders),
            ),

            // Option 4: In-App Reminders
            _buildOption(
              context,
              icon: Icons.app_shortcut,
              title: 'Use In-App Reminders Only',
              description: 'Only show reminders when you open the app',
              onTap: () =>
                  onOptionSelected(PermissionAlternative.inAppReminders),
            ),

            // Option 5: Skip Reminders
            _buildOption(
              context,
              icon: Icons.not_interested,
              title: 'Skip Reminders',
              description: 'Don\'t use any reminders for now',
              onTap: () => onOptionSelected(PermissionAlternative.skip),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOption(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String description,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8.0),
        child: Row(
          children: [
            Container(
              padding: EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.blue.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: Colors.blue,
                size: 24,
              ),
            ),
            SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                  ),
                  SizedBox(height: 2),
                  Text(
                    description,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade700,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.chevron_right,
              color: Colors.grey,
              size: 20,
            ),
          ],
        ),
      ),
    );
  }
}

/// Show the permission denied alternatives dialog
Future<PermissionAlternative?> showPermissionDeniedDialog(
  BuildContext context, {
  required bool isPermanentlyDenied,
}) async {
  return await showDialog<PermissionAlternative>(
    context: context,
    barrierDismissible: false,
    builder: (context) => PermissionDeniedDialog(
      isPermanentlyDenied: isPermanentlyDenied,
      onOptionSelected: (option) {
        Navigator.of(context).pop(option);
      },
    ),
  );
}
