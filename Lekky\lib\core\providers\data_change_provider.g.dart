// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'data_change_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$dataChangeNotifierHash() => r'8f5c5e5c5e5c5e5c5e5c5e5c5e5c5e5c5e5c5e5c';

/// Provider that tracks data changes to trigger reactive updates
///
/// Copied from [DataChangeNotifier].
@ProviderFor(DataChangeNotifier)
final dataChangeNotifierProvider =
    AutoDisposeNotifierProvider<DataChangeNotifier, int>.internal(
  DataChangeNotifier.new,
  name: r'dataChangeNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$dataChangeNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$DataChangeNotifier = AutoDisposeNotifier<int>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
