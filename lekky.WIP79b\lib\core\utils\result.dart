// File: lib/core/utils/result.dart
import 'error_handler.dart';

/// A class that represents a result of an operation that can either succeed or fail
class Result<T> {
  /// The value of the result if it's successful
  final T? _value;

  /// The error of the result if it's a failure
  final AppError? _error;

  /// Whether the result is a success
  final bool _isSuccess;

  /// Private constructor
  const Result._({
    T? value,
    AppError? error,
    required bool isSuccess,
  })  : _value = value,
        _error = error,
        _isSuccess = isSuccess;

  /// Create a success result
  factory Result.success(T value) {
    return Result._(value: value, isSuccess: true);
  }

  /// Create a failure result
  factory Result.failure(AppError error) {
    ErrorHandler.addError(error);
    return Result._(error: error, isSuccess: false);
  }

  /// Whether the result is a success
  bool get isSuccess => _isSuccess;

  /// Whether the result is a failure
  bool get isFailure => !_isSuccess;

  /// Get the value of the result
  T get value {
    if (isFailure) {
      throw Exception('Cannot get value of a failure result');
    }
    return _value as T;
  }

  /// Get the error of the result
  AppError get error {
    if (isSuccess) {
      throw Exception('Cannot get error of a success result');
    }
    return _error!;
  }

  /// Map the result to a new result
  Result<R> map<R>(R Function(T) mapper) {
    if (isSuccess) {
      return Result.success(mapper(value));
    } else {
      return Result.failure(error);
    }
  }

  /// Flat map the result to a new result
  Result<R> flatMap<R>(Result<R> Function(T) mapper) {
    if (isSuccess) {
      return mapper(value);
    } else {
      return Result.failure(error);
    }
  }

  /// Handle both success and failure cases
  R fold<R>({
    required R Function(T) onSuccess,
    required R Function(AppError) onFailure,
  }) {
    if (isSuccess) {
      return onSuccess(value);
    } else {
      return onFailure(error);
    }
  }

  /// Handle the success case
  void onSuccess(void Function(T) action) {
    if (isSuccess) {
      action(value);
    }
  }

  /// Handle the failure case
  void onFailure(void Function(AppError) action) {
    if (isFailure) {
      action(error);
    }
  }
}
