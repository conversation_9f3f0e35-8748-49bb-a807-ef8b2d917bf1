// File: lib/features/setup/presentation/widgets/setup_actions.dart
import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/extensions/context_extensions.dart';
import '../../../../core/widgets/gradient_button.dart';

/// A widget for setup actions (save, reset, and load)
class SetupActions extends StatelessWidget {
  final VoidCallback onSave;
  final VoidCallback onReset;
  final VoidCallback? onLoad;
  final bool isSaving;
  final bool isInitialSetup;

  const SetupActions({
    super.key,
    required this.onSave,
    required this.onReset,
    this.onLoad,
    this.isSaving = false,
    this.isInitialSetup = true,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16),
      child: Column(
        children: [
          GradientButton(
            text: isSaving ? 'Saving...' : 'Continue',
            onPressed: isSaving ? null : onSave,
            isLoading: isSaving,
            gradientColors: context.isDarkMode
                ? [
                    AppColors.primaryDark,
                    AppColors.primaryDark.withOpacity(0.7)
                  ]
                : [AppColors.primary, AppColors.primary.withOpacity(0.7)],
            icon: const Icon(
              Icons.arrow_forward,
              color: AppColors.onPrimary,
            ),
            width: double.infinity,
          ),
          const SizedBox(height: 16),
          // Reset button now takes full width
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: isSaving ? null : onReset,
              icon: Icon(
                Icons.refresh,
                size: 18,
                color: context.isDarkMode
                    ? AppColors.primaryDark
                    : AppColors.primary,
              ),
              label: Text(
                'Reset to Default',
                style: TextStyle(
                  color: context.isDarkMode
                      ? AppColors.primaryDark
                      : AppColors.primary,
                ),
              ),
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 12),
                side: BorderSide(
                  color: context.isDarkMode
                      ? AppColors.primaryDark
                      : AppColors.primary,
                ),
              ),
            ),
          ),
          // Restore from backup feature removed
        ],
      ),
    );
  }
}
