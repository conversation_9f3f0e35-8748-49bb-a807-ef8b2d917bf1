// File: test/core/utils/enhanced_logger_test.dart

import 'dart:async';
import 'package:flutter_test/flutter_test.dart';
import 'package:lekky/core/utils/enhanced_logger.dart';

void main() {
  setUp(() {
    // Reset logger for each test
    EnhancedLogger.dispose();
  });

  group('EnhancedLogger', () {
    test('should initialize with default settings', () async {
      // Act & Assert - this should not throw
      await EnhancedLogger.initialize(
        consoleLoggingEnabled: true,
        fileLoggingEnabled: false,
      );

      // Log a test message
      EnhancedLogger.info('Test message');
    });

    test('should log messages with different levels', () async {
      // Arrange
      await EnhancedLogger.initialize(
        consoleLoggingEnabled: true,
        fileLoggingEnabled: false,
        minimumLogLevel: LogLevel.debug,
      );

      // Act & Assert - these should not throw
      EnhancedLogger.debug('Debug message');
      EnhancedLogger.info('Info message');
      EnhancedLogger.warning('Warning message');
      EnhancedLogger.error('Error message');
      EnhancedLogger.critical('Critical message');
    });

    test('should respect minimum log level', () async {
      // Arrange
      List<Map<String, dynamic>> loggedMessages = [];
      await EnhancedLogger.initialize(
        consoleLoggingEnabled: true,
        fileLoggingEnabled: false,
        minimumLogLevel: LogLevel.warning,
      );

      // Listen to log stream
      final subscription = EnhancedLogger.logStream.listen((logEntry) {
        loggedMessages.add(logEntry);
      });

      // Act
      EnhancedLogger.debug('Debug message'); // Should be filtered out
      EnhancedLogger.info('Info message'); // Should be filtered out
      EnhancedLogger.warning('Warning message');
      EnhancedLogger.error('Error message');

      // Wait for logs to be processed
      await Future.delayed(const Duration(milliseconds: 100));

      // Assert
      expect(loggedMessages.length, 2);
      expect(loggedMessages[0]['level'], 'WARNING');
      expect(loggedMessages[1]['level'], 'ERROR');

      // Clean up
      await subscription.cancel();
    });

    test('should include details in log entries', () async {
      // Arrange
      List<Map<String, dynamic>> loggedMessages = [];
      await EnhancedLogger.initialize(
        consoleLoggingEnabled: true,
        fileLoggingEnabled: false,
      );

      // Listen to log stream
      final subscription = EnhancedLogger.logStream.listen((logEntry) {
        loggedMessages.add(logEntry);
      });

      // Act
      EnhancedLogger.info('Message with details', details: {
        'key1': 'value1',
        'key2': 42,
      });

      // Wait for logs to be processed
      await Future.delayed(const Duration(milliseconds: 100));

      // Assert
      expect(loggedMessages.length, 1);
      expect(loggedMessages[0]['message'], 'Message with details');
      expect(loggedMessages[0]['details']['key1'], 'value1');
      expect(loggedMessages[0]['details']['key2'], 42);

      // Clean up
      await subscription.cancel();
    });

    test('should include component in log entries', () async {
      // Arrange
      List<Map<String, dynamic>> loggedMessages = [];
      await EnhancedLogger.initialize(
        consoleLoggingEnabled: true,
        fileLoggingEnabled: false,
      );

      // Listen to log stream
      final subscription = EnhancedLogger.logStream.listen((logEntry) {
        loggedMessages.add(logEntry);
      });

      // Act
      EnhancedLogger.info('Component message', component: 'notifications');

      // Wait for logs to be processed
      await Future.delayed(const Duration(milliseconds: 100));

      // Assert
      expect(loggedMessages.length, 1);
      expect(loggedMessages[0]['component'], 'notifications');

      // Clean up
      await subscription.cancel();
    });

    test('should include exception and stack trace in error logs', () async {
      // Arrange
      List<Map<String, dynamic>> loggedMessages = [];
      await EnhancedLogger.initialize(
        consoleLoggingEnabled: true,
        fileLoggingEnabled: false,
      );

      // Listen to log stream
      final subscription = EnhancedLogger.logStream.listen((logEntry) {
        loggedMessages.add(logEntry);
      });

      // Act
      try {
        throw Exception('Test exception');
      } catch (e, stackTrace) {
        EnhancedLogger.error(
          'Error occurred',
          exception: e,
          stackTrace: stackTrace,
        );
      }

      // Wait for logs to be processed
      await Future.delayed(const Duration(milliseconds: 100));

      // Assert
      expect(loggedMessages.length, 1);
      expect(loggedMessages[0]['message'], 'Error occurred');
      expect(loggedMessages[0]['details']['exception'],
          contains('Test exception'));
      expect(loggedMessages[0]['details']['stackTrace'], isNotNull);

      // Clean up
      await subscription.cancel();
    });

    test('should manage context data', () async {
      // Arrange
      List<Map<String, dynamic>> loggedMessages = [];
      await EnhancedLogger.initialize(
        consoleLoggingEnabled: true,
        fileLoggingEnabled: false,
        initialContext: {'app_version': '1.0.0'},
      );

      // Listen to log stream
      final subscription = EnhancedLogger.logStream.listen((logEntry) {
        loggedMessages.add(logEntry);
      });

      // Act
      EnhancedLogger.info('Initial context message');

      EnhancedLogger.addContext('user_id', 'user123');
      EnhancedLogger.info('Added context message');

      EnhancedLogger.removeContext('app_version');
      EnhancedLogger.info('Removed context message');

      EnhancedLogger.clearContext();
      EnhancedLogger.info('Cleared context message');

      // Wait for logs to be processed
      await Future.delayed(const Duration(milliseconds: 100));

      // Assert
      expect(loggedMessages.length, 4);
      expect(loggedMessages[0]['context']['app_version'], '1.0.0');

      expect(loggedMessages[1]['context']['app_version'], '1.0.0');
      expect(loggedMessages[1]['context']['user_id'], 'user123');

      expect(loggedMessages[2]['context']['app_version'], isNull);
      expect(loggedMessages[2]['context']['user_id'], 'user123');

      expect(loggedMessages[3]['context'], isEmpty);

      // Clean up
      await subscription.cancel();
    });

    test('should set minimum log level', () async {
      // Arrange
      List<Map<String, dynamic>> loggedMessages = [];
      await EnhancedLogger.initialize(
        consoleLoggingEnabled: true,
        fileLoggingEnabled: false,
        minimumLogLevel: LogLevel.info,
      );

      // Listen to log stream
      final subscription = EnhancedLogger.logStream.listen((logEntry) {
        loggedMessages.add(logEntry);
      });

      // Act - log a debug message (should be filtered)
      EnhancedLogger.debug('Debug message');

      // Change minimum level
      EnhancedLogger.setMinimumLogLevel(LogLevel.debug);

      // Log another debug message (should be included)
      EnhancedLogger.debug('Debug message after level change');

      // Wait for logs to be processed
      await Future.delayed(const Duration(milliseconds: 100));

      // Assert
      expect(loggedMessages.length, 1);
      expect(loggedMessages[0]['message'], 'Debug message after level change');

      // Clean up
      await subscription.cancel();
    });

    test('should enable and disable console logging', () async {
      // Arrange
      await EnhancedLogger.initialize(
        consoleLoggingEnabled: true,
        fileLoggingEnabled: false,
      );

      // Act & Assert - these should not throw
      EnhancedLogger.setConsoleLogging(false);
      EnhancedLogger.info('Console logging disabled');

      EnhancedLogger.setConsoleLogging(true);
      EnhancedLogger.info('Console logging enabled');
    });
  });
}
