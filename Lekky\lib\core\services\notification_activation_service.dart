import 'dart:async';
import 'package:flutter/material.dart';
import '../utils/logger.dart';
import 'notification_settings_helper.dart';
import 'permission_request_state_manager.dart';
import 'permission_analytics_service.dart';
import 'notification_permission_manager.dart';

/// Result of notification activation attempt
class NotificationActivationResult {
  final bool success;
  final String? errorMessage;
  final bool permissionDenied;
  final bool shouldShowDontAskAgain;
  final List<String> batchedTypes;

  const NotificationActivationResult({
    required this.success,
    this.errorMessage,
    this.permissionDenied = false,
    this.shouldShowDontAskAgain = false,
    this.batchedTypes = const [],
  });

  factory NotificationActivationResult.success({List<String> batchedTypes = const []}) {
    return NotificationActivationResult(
      success: true,
      batchedTypes: batchedTypes,
    );
  }

  factory NotificationActivationResult.failure({
    required String errorMessage,
    bool permissionDenied = false,
    bool shouldShowDontAskAgain = false,
  }) {
    return NotificationActivationResult(
      success: false,
      errorMessage: errorMessage,
      permissionDenied: permissionDenied,
      shouldShowDontAskAgain: shouldShowDontAskAgain,
    );
  }
}

/// Centralized service for handling notification activation with permission management
class NotificationActivationService {
  static final NotificationActivationService _instance = 
      NotificationActivationService._internal();
  
  factory NotificationActivationService() => _instance;
  NotificationActivationService._internal();

  final _stateManager = PermissionRequestStateManager();
  final _analytics = PermissionAnalyticsService();
  final _settingsHelper = NotificationSettingsHelper();
  final _permissionManager = NotificationPermissionManager();

  // Batching configuration
  static const Duration _batchTimeout = Duration(seconds: 2);
  Timer? _batchTimer;
  final List<String> _batchedRequests = [];

  /// Handle notification activation for specific type
  Future<NotificationActivationResult> handleNotificationActivation(
    BuildContext context,
    String notificationType,
  ) async {
    try {
      Logger.info('NotificationActivationService: Handling activation for $notificationType');

      // Check if user has chosen "don't ask again"
      final dontAskAgain = await _stateManager.isDontAskAgain(notificationType);
      if (dontAskAgain) {
        Logger.info('NotificationActivationService: User chose dont ask again for $notificationType');
        return NotificationActivationResult.failure(
          errorMessage: 'Permission requests disabled for this notification type',
          permissionDenied: true,
        );
      }

      // Handle batching for non-reminder types
      if (notificationType != 'reminders') {
        final shouldBatch = await _handleBatching(notificationType);
        if (shouldBatch) {
          Logger.info('NotificationActivationService: Request batched for $notificationType');
          return NotificationActivationResult.success(batchedTypes: [notificationType]);
        }
      }

      // Process immediately for reminders or when no batching needed
      return await _processActivationRequest(context, [notificationType]);
    } catch (e) {
      Logger.error('NotificationActivationService: Error handling activation for $notificationType: $e');
      return NotificationActivationResult.failure(
        errorMessage: 'Failed to activate notifications: $e',
      );
    }
  }

  /// Handle batching logic for non-reminder notification types
  Future<bool> _handleBatching(String notificationType) async {
    // Check if dialog is currently showing
    if (_stateManager.tryQueueRequest(notificationType)) {
      return true; // Request was queued
    }

    // Add to batch and start/reset timer
    if (!_batchedRequests.contains(notificationType)) {
      _batchedRequests.add(notificationType);
    }

    _batchTimer?.cancel();
    _batchTimer = Timer(_batchTimeout, () {
      _processBatchedRequests();
    });

    // If this is the first request in batch, don't batch (process immediately)
    return _batchedRequests.length > 1;
  }

  /// Process batched requests
  Future<void> _processBatchedRequests() async {
    if (_batchedRequests.isEmpty) return;

    final batchedTypes = List<String>.from(_batchedRequests);
    _batchedRequests.clear();

    Logger.info('NotificationActivationService: Processing batched requests: $batchedTypes');

    // Find a valid context (this is a limitation - in real implementation,
    // we'd need to store context or use a different approach)
    // For now, we'll process each request individually when context becomes available
    for (final type in batchedTypes) {
      final queuedRequest = _stateManager.getNextQueuedRequest();
      if (queuedRequest != null) {
        Logger.info('NotificationActivationService: Processing queued request for $queuedRequest');
        // Context would need to be provided by the UI layer
      }
    }
  }

  /// Process activation request for given notification types
  Future<NotificationActivationResult> _processActivationRequest(
    BuildContext context,
    List<String> notificationTypes,
  ) async {
    try {
      // Check if this is first activation
      final isFirstActivation = await _settingsHelper.isFirstNotificationActivation();
      
      if (!isFirstActivation) {
        Logger.info('NotificationActivationService: Not first activation, skipping permission request');
        return NotificationActivationResult.success(batchedTypes: notificationTypes);
      }

      Logger.info('NotificationActivationService: First activation detected, requesting permissions');

      // Track analytics for each type
      for (final type in notificationTypes) {
        await _analytics.trackPermissionRequest(type);
      }

      // Mark dialog as showing
      final primaryType = notificationTypes.first;
      _stateManager.markDialogShowing(primaryType);

      try {
        // Check current permission status
        final hasPermission = await _permissionManager.hasPermission();
        
        if (hasPermission) {
          Logger.info('NotificationActivationService: Permission already granted');
          
          // Track success for all types
          for (final type in notificationTypes) {
            await _analytics.trackPermissionSuccess(type);
          }
          
          return NotificationActivationResult.success(batchedTypes: notificationTypes);
        }

        // Request permission
        final granted = await _permissionManager.requestPermission(context);
        
        if (granted) {
          Logger.info('NotificationActivationService: Permission granted');
          
          // Track success for all types
          for (final type in notificationTypes) {
            await _analytics.trackPermissionSuccess(type);
          }
          
          return NotificationActivationResult.success(batchedTypes: notificationTypes);
        } else {
          Logger.info('NotificationActivationService: Permission denied');
          
          // Track denial and increment retry count for all types
          for (final type in notificationTypes) {
            await _analytics.trackPermissionDenial(type, 'user_denied');
            await _stateManager.incrementRetryCount(type);
          }

          // Check if should show "don't ask again" option
          final shouldShowDontAskAgain = await _stateManager.shouldShowDontAskAgain(primaryType);
          
          return NotificationActivationResult.failure(
            errorMessage: 'Notification permission is required for alerts',
            permissionDenied: true,
            shouldShowDontAskAgain: shouldShowDontAskAgain,
          );
        }
      } finally {
        _stateManager.markDialogDismissed();
      }
    } catch (e) {
      Logger.error('NotificationActivationService: Error processing activation request: $e');
      
      // Track error for all types
      for (final type in notificationTypes) {
        await _analytics.trackPermissionDenial(type, 'error: $e');
      }
      
      return NotificationActivationResult.failure(
        errorMessage: 'Failed to request permission: $e',
      );
    }
  }

  /// Handle "don't ask again" selection
  Future<void> handleDontAskAgain(String notificationType) async {
    await _stateManager.setDontAskAgain(notificationType, true);
    await _analytics.trackPermissionDenial(notificationType, 'dont_ask_again');
    Logger.info('NotificationActivationService: Set dont ask again for $notificationType');
  }

  /// Reset permission data for specific notification type
  Future<void> resetPermissionData(String notificationType) async {
    await _stateManager.resetPermissionData(notificationType);
    await _analytics.clearAnalytics(notificationType);
    Logger.info('NotificationActivationService: Reset permission data for $notificationType');
  }

  /// Get activation summary for debugging
  Future<Map<String, dynamic>> getActivationSummary() async {
    final stateManagerSummary = await _stateManager.getPermissionSummary();
    final analyticsSummary = await _analytics.getOverallAnalytics();
    
    return {
      'state_manager': stateManagerSummary,
      'analytics': analyticsSummary,
      'batched_requests': List.from(_batchedRequests),
      'batch_timer_active': _batchTimer?.isActive ?? false,
    };
  }

  /// Dispose resources
  void dispose() {
    _batchTimer?.cancel();
    _batchedRequests.clear();
    _stateManager.clearQueue();
  }
}
