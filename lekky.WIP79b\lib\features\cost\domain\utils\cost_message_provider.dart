// File: lib/features/cost/domain/utils/cost_message_provider.dart

import '../../domain/models/cost_period.dart';

/// Utility class for providing appropriate messages for the Cost screen
class CostMessageProvider {
  /// Returns the appropriate message based on the type of calculation
  ///
  /// Uses the following rules:
  /// - For day, week, month, year: "Based on total average use."
  /// - For dates within meter records: "Based on your records." (uses short averages between readings)
  /// - For dates partially within records and extending beyond: "Based on your records and averages."
  /// - For dates completely outside records: "Based on average usage" (uses total average)
  static String getAverageUsageMessage({
    required CostPeriod selectedPeriod,
    required DateTime? fromDate,
    required DateTime? toDate,
    required DateTime? earliestDate,
    required DateTime? latestDate,
  }) {
    // For day, week, month, and year calculations, show "Based on total average use."
    if (selectedPeriod != CostPeriod.custom) {
      return 'Based on total average use.';
    }

    // For custom date ranges, check if it's within the recorded meter reading range
    if (fromDate != null &&
        toDate != null &&
        earliestDate != null &&
        latestDate != null) {
      // If the date range is within the recorded meter reading range
      // Uses short averages (between meter readings) for more accurate calculation
      // Compare dates only (ignoring time) for more accurate results
      final fromDateOnly =
          DateTime(fromDate.year, fromDate.month, fromDate.day);
      final earliestDateOnly =
          DateTime(earliestDate.year, earliestDate.month, earliestDate.day);
      final toDateOnly = DateTime(toDate.year, toDate.month, toDate.day);
      final latestDateOnly =
          DateTime(latestDate.year, latestDate.month, latestDate.day);

      if ((!fromDateOnly.isBefore(earliestDateOnly) ||
              fromDateOnly.isAtSameMomentAs(earliestDateOnly)) &&
          (!toDateOnly.isAfter(latestDateOnly) ||
              toDateOnly.isAtSameMomentAs(latestDateOnly))) {
        return 'Based on your records.';
      }

      // If the date range starts within the recorded meter reading range but extends into the future
      // Uses short averages for dates within records and total average for dates beyond
      if ((!fromDateOnly.isBefore(earliestDateOnly) ||
              fromDateOnly.isAtSameMomentAs(earliestDateOnly)) &&
          !fromDateOnly.isAfter(latestDateOnly) &&
          toDateOnly.isAfter(latestDateOnly)) {
        return 'Based on your records and averages.';
      }

      // If the date range is completely before the earliest record or starts before earliest record
      // Uses total average for the entire calculation
      if (toDateOnly.isBefore(earliestDateOnly) ||
          fromDateOnly.isBefore(earliestDateOnly)) {
        return 'Based on total average use.';
      }
    }

    // Default to "Based on average usage" for other cases
    return 'Based on average usage';
  }
}
