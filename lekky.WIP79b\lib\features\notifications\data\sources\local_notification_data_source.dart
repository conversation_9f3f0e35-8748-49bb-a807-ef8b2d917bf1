// File: lib/features/notifications/data/sources/local_notification_data_source.dart

import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../../core/utils/logger.dart';
import '../../domain/models/notification_model.dart';
import '../../domain/models/reminder_time_model.dart';
import '../../domain/models/timezone_aware_reminder_time.dart';
import 'local_storage_data_source.dart';

/// Data source for locally stored notification data using SharedPreferences
/// This implementation will be deprecated in favor of SQLNotificationDataSource
@Deprecated('Use SQLNotificationDataSource instead')
class LocalNotificationDataSource implements LocalStorageDataSource {
  bool _isInitialized = false;

  // Key constants for SharedPreferences
  static const String _notificationsKey = 'lekky_notifications';
  static const String _notificationsEnabledKey = 'notifications_enabled';
  static const String _meterReadingReminderEnabledKey =
      'meter_reading_reminder_enabled';
  static const String _meterReadingReminderFrequencyKey =
      'meter_reading_reminder_frequency';
  static const String _meterReadingReminderLastDateKey =
      'meter_reading_reminder_last_date';
  static const String _meterReadingReminderTimeKey =
      'meter_reading_reminder_time';
  static const String _meterReadingReminderTimezoneAwareKey =
      'meter_reading_reminder_timezone_aware';
  static const String _nextMeterReminderDateKey = 'next_meter_reminder_date';

  /// Initialize the data source
  Future<void> initialize() async {
    if (_isInitialized) return;
    _isInitialized = true;
  }

  /// Get all notifications
  Future<List<NotificationModel>> getNotifications() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final notificationsJson = prefs.getStringList(_notificationsKey) ?? [];

      final notifications = notificationsJson.map((json) {
        final Map<String, dynamic> data = jsonDecode(json);
        return NotificationModel.fromMap(data);
      }).toList();

      // Sort notifications by timestamp (newest first)
      notifications.sort((a, b) => b.timestamp.compareTo(a.timestamp));

      return notifications;
    } catch (e) {
      // Return empty list on error
      return [];
    }
  }

  /// Add a notification
  Future<void> addNotification(NotificationModel notification) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final notificationsJson = prefs.getStringList(_notificationsKey) ?? [];

      // Add new notification
      notificationsJson.add(jsonEncode(notification.toMap()));

      // Save updated list
      await prefs.setStringList(_notificationsKey, notificationsJson);
    } catch (e) {
      // Handle error
      rethrow;
    }
  }

  /// Mark a notification as read
  Future<void> markAsRead(String id) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final notificationsJson = prefs.getStringList(_notificationsKey) ?? [];

      // Convert to list of models
      final notifications = notificationsJson.map((json) {
        final Map<String, dynamic> data = jsonDecode(json);
        return NotificationModel.fromMap(data);
      }).toList();

      // Find and update the notification
      final index = notifications.indexWhere((n) => n.id == id);
      if (index != -1) {
        notifications[index] = notifications[index].copyWith(isRead: true);
      }

      // Convert back to JSON and save
      final updatedJson =
          notifications.map((n) => jsonEncode(n.toMap())).toList();
      await prefs.setStringList(_notificationsKey, updatedJson);
    } catch (e) {
      // Handle error
      rethrow;
    }
  }

  /// Mark all notifications as read
  Future<void> markAllAsRead() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final notificationsJson = prefs.getStringList(_notificationsKey) ?? [];

      // Convert to list of models
      final notifications = notificationsJson.map((json) {
        final Map<String, dynamic> data = jsonDecode(json);
        return NotificationModel.fromMap(data);
      }).toList();

      // Mark all as read
      final updatedNotifications =
          notifications.map((n) => n.copyWith(isRead: true)).toList();

      // Convert back to JSON and save
      final updatedJson =
          updatedNotifications.map((n) => jsonEncode(n.toMap())).toList();
      await prefs.setStringList(_notificationsKey, updatedJson);
    } catch (e) {
      // Handle error
      rethrow;
    }
  }

  /// Remove a notification
  Future<void> removeNotification(String id) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final notificationsJson = prefs.getStringList(_notificationsKey) ?? [];

      // Convert to list of models
      final notifications = notificationsJson.map((json) {
        final Map<String, dynamic> data = jsonDecode(json);
        return NotificationModel.fromMap(data);
      }).toList();

      // Remove the notification
      notifications.removeWhere((n) => n.id == id);

      // Convert back to JSON and save
      final updatedJson =
          notifications.map((n) => jsonEncode(n.toMap())).toList();
      await prefs.setStringList(_notificationsKey, updatedJson);
    } catch (e) {
      // Handle error
      rethrow;
    }
  }

  /// Clear all notifications
  Future<void> clearAll() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setStringList(_notificationsKey, []);
    } catch (e) {
      // Handle error
      rethrow;
    }
  }

  /// Check if notifications are enabled
  Future<bool> areNotificationsEnabled() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_notificationsEnabledKey) ?? true;
  }

  /// Enable or disable notifications
  Future<void> setNotificationsEnabled(bool enabled) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_notificationsEnabledKey, enabled);
  }

  /// Check if meter reading reminders are enabled
  Future<bool> areMeterReadingRemindersEnabled() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_meterReadingReminderEnabledKey) ?? false;
  }

  /// Enable or disable meter reading reminders
  Future<void> setMeterReadingRemindersEnabled(bool enabled) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_meterReadingReminderEnabledKey, enabled);
  }

  /// Get the meter reading reminder frequency in days
  Future<int> getMeterReadingReminderFrequency() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getInt(_meterReadingReminderFrequencyKey) ??
        7; // Default to weekly
  }

  /// Set the meter reading reminder frequency in days
  Future<void> setMeterReadingReminderFrequency(int days) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(_meterReadingReminderFrequencyKey, days);
  }

  /// Get the last date a meter reading reminder was shown
  Future<DateTime?> getLastMeterReadingReminderDate() async {
    final prefs = await SharedPreferences.getInstance();
    final dateString = prefs.getString(_meterReadingReminderLastDateKey);
    return dateString != null ? DateTime.parse(dateString) : null;
  }

  /// Set the last date a meter reading reminder was shown
  Future<void> setLastMeterReadingReminderDate(DateTime date) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(
        _meterReadingReminderLastDateKey, date.toIso8601String());
  }

  /// Get the time of day for meter reading reminders
  Future<ReminderTimeModel> getMeterReadingReminderTime() async {
    final prefs = await SharedPreferences.getInstance();
    final timeString = prefs.getString(_meterReadingReminderTimeKey);

    if (timeString != null) {
      return ReminderTimeModel.fromString(timeString);
    }

    // Default to 7:00 PM if not set
    return const ReminderTimeModel(
      timeOfDay: TimeOfDay(hour: 19, minute: 0),
    );
  }

  /// Set the time of day for meter reading reminders
  Future<void> setMeterReadingReminderTime(ReminderTimeModel time) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_meterReadingReminderTimeKey, time.toTimeString());
  }

  /// Get the timezone-aware reminder time for meter readings
  Future<TimezoneAwareReminderTime?> getTimezoneAwareReminderTime() async {
    final prefs = await SharedPreferences.getInstance();
    final jsonString = prefs.getString(_meterReadingReminderTimezoneAwareKey);

    if (jsonString != null) {
      try {
        final Map<String, dynamic> json = jsonDecode(jsonString);
        return TimezoneAwareReminderTime.fromJson(json);
      } catch (e) {
        print('Error parsing timezone-aware reminder time: $e');
      }
    }

    // If no timezone-aware reminder time is stored, try to convert from the legacy format
    final legacyTime = await getMeterReadingReminderTime();

    // Default to UTC if this is the first time
    const String defaultTimezone = 'UTC';

    return TimezoneAwareReminderTime.fromReminderTimeModel(
      model: legacyTime,
      currentTimezone: defaultTimezone,
    );
  }

  /// Set the timezone-aware reminder time for meter readings
  Future<void> setTimezoneAwareReminderTime(
      TimezoneAwareReminderTime time) async {
    final prefs = await SharedPreferences.getInstance();

    // Also update the legacy format for backward compatibility
    await setMeterReadingReminderTime(time.toReminderTimeModel());

    // Save the timezone-aware format
    final json = time.toJson();
    await prefs.setString(
        _meterReadingReminderTimezoneAwareKey, jsonEncode(json));
  }

  /// Save the next scheduled meter reading reminder date
  Future<void> saveNextMeterReadingReminderDate(DateTime date) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(_nextMeterReminderDateKey, date.millisecondsSinceEpoch);
  }

  /// Save the next scheduled meter reading reminder date with timezone info
  Future<void> saveNextMeterReadingReminderDateWithTimezone(
      DateTime date, String timezone, bool isDST) async {
    final prefs = await SharedPreferences.getInstance();

    // Save the basic timestamp for backward compatibility
    await prefs.setInt(_nextMeterReminderDateKey, date.millisecondsSinceEpoch);

    // Save additional timezone information
    final timezoneInfo = {
      'timestamp': date.millisecondsSinceEpoch,
      'timezone': timezone,
      'isDST': isDST,
      'utcTimestamp': date.toUtc().millisecondsSinceEpoch,
    };

    await prefs.setString(
        '${_nextMeterReminderDateKey}_tz_info', jsonEncode(timezoneInfo));
  }

  /// Get the next scheduled meter reading reminder date
  Future<DateTime?> getNextMeterReadingReminderDate() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final timestamp = prefs.getInt(_nextMeterReminderDateKey);

      if (timestamp != null) {
        return DateTime.fromMillisecondsSinceEpoch(timestamp);
      }

      return null;
    } catch (e) {
      return null;
    }
  }

  /// Get the timezone information for the next scheduled reminder
  Future<Map<String, dynamic>?>
      getNextMeterReadingReminderTimezoneInfo() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString =
          prefs.getString('${_nextMeterReminderDateKey}_tz_info');

      if (jsonString != null) {
        return jsonDecode(jsonString);
      }

      return null;
    } catch (e) {
      print('Error getting next reminder timezone info: $e');
      return null;
    }
  }
}
