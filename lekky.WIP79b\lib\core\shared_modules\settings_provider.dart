// File: lib/core/shared_modules/settings_provider.dart
import 'package:flutter/material.dart';
import '../utils/event_bus.dart';
import '../utils/logger.dart';
import 'settings_service.dart';
import 'settings_model.dart';

/// Provider for managing all app settings
class SettingsProvider extends ChangeNotifier {
  final SettingsService _service = SettingsService();

  // State variables
  String _currency = SettingsService.defaultCurrency;
  String _dateFormat = SettingsService.defaultDateFormat;
  String _dateInfo = SettingsService.defaultDateInfo;
  double _alertThreshold = SettingsService.defaultAlertThreshold;
  int _daysInAdvance = SettingsService.defaultDaysInAdvance;
  ThemeMode _themeMode = ThemeMode.system;
  bool _notificationsEnabled = SettingsService.defaultNotificationsEnabled;
  bool _lowBalanceAlerts = SettingsService.defaultLowBalanceAlerts;
  bool _daysInAdvanceAlerts = SettingsService.defaultDaysInAdvanceAlerts;
  bool _isLoading = true;
  String _error = '';

  // Getters
  String get currency => _currency;
  String get dateFormat => _dateFormat;
  String get dateInfo => _dateInfo;
  double get alertThreshold => _alertThreshold;
  int get daysInAdvance => _daysInAdvance;
  ThemeMode get themeMode => _themeMode;
  bool get notificationsEnabled => _notificationsEnabled;
  bool get lowBalanceAlerts => _lowBalanceAlerts;
  bool get daysInAdvanceAlerts => _daysInAdvanceAlerts;
  bool get isLoading => _isLoading;
  String get error => _error;

  // Constructor
  SettingsProvider() {
    loadSettings();
  }

  // Load all settings
  Future<void> loadSettings() async {
    _isLoading = true;
    _error = '';
    notifyListeners();

    try {
      _currency = await _service.getCurrency();
      _dateFormat = await _service.getDateFormat();
      _dateInfo = await _service.getDateInfo();
      _alertThreshold = await _service.getAlertThreshold();
      _daysInAdvance = await _service.getDaysInAdvance();
      _themeMode = await _service.getThemeMode();
      _notificationsEnabled = await _service.getNotificationsEnabled();
      _lowBalanceAlerts = await _service.getLowBalanceAlerts();
      _daysInAdvanceAlerts = await _service.getDaysInAdvanceAlerts();

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _error = 'Error loading settings: $e';
      _isLoading = false;
      notifyListeners();
    }
  }

  // Set currency
  Future<void> setCurrency(String value) async {
    try {
      await _service.setCurrency(value);
      _currency = value;
      notifyListeners();
    } catch (e) {
      _error = 'Error setting currency: $e';
      notifyListeners();
    }
  }

  // Set date format
  Future<void> setDateFormat(String value) async {
    try {
      await _service.setDateFormat(value);
      _dateFormat = value;
      notifyListeners();

      // We'll let the SettingsController handle the event firing
      // This prevents duplicate events and race conditions
      logger.i('SettingsProvider: Date format updated to $value');
    } catch (e) {
      _error = 'Error setting date format: $e';
      notifyListeners();
      logger.e('Error setting date format', details: e.toString());
    }
  }

  // Set date info
  Future<void> setDateInfo(String value) async {
    try {
      await _service.setDateInfo(value);
      _dateInfo = value;
      notifyListeners();

      // We'll let the SettingsController handle the event firing
      // This prevents duplicate events and race conditions
      logger.i('SettingsProvider: Date info updated to $value');
    } catch (e) {
      _error = 'Error setting date info: $e';
      notifyListeners();
      logger.e('Error setting date info', details: e.toString());
    }
  }

  // Set alert threshold
  Future<void> setAlertThreshold(double value) async {
    try {
      await _service.setAlertThreshold(value);
      _alertThreshold = value;
      notifyListeners();
      // Notify other parts of the app that alert settings have changed
      EventBus().fire(EventType.alertSettingsUpdated);
      logger.i('Alert threshold updated to $value');
    } catch (e) {
      _error = 'Error setting alert threshold: $e';
      notifyListeners();
      logger.e('Error setting alert threshold', details: e.toString());
    }
  }

  // Set days in advance
  Future<void> setDaysInAdvance(int value) async {
    try {
      await _service.setDaysInAdvance(value);
      _daysInAdvance = value;
      notifyListeners();
      // Notify other parts of the app that alert settings have changed
      EventBus().fire(EventType.alertSettingsUpdated);
      logger.i('Days in advance updated to $value');
    } catch (e) {
      _error = 'Error setting days in advance: $e';
      notifyListeners();
      logger.e('Error setting days in advance', details: e.toString());
    }
  }

  // Set theme mode
  Future<void> setThemeMode(ThemeMode mode) async {
    try {
      await _service.setThemeMode(mode);
      _themeMode = mode;
      notifyListeners();
    } catch (e) {
      _error = 'Error setting theme mode: $e';
      notifyListeners();
    }
  }

  // Set notifications enabled
  Future<void> setNotificationsEnabled(bool value) async {
    try {
      await _service.setNotificationsEnabled(value);
      _notificationsEnabled = value;
      notifyListeners();
      // Notify other parts of the app that alert settings have changed
      EventBus().fire(EventType.alertSettingsUpdated);
      logger.i('Notifications enabled set to $value');
    } catch (e) {
      _error = 'Error setting notifications enabled: $e';
      notifyListeners();
      logger.e('Error setting notifications enabled', details: e.toString());
    }
  }

  // Set low balance alerts
  Future<void> setLowBalanceAlerts(bool value) async {
    try {
      await _service.setLowBalanceAlerts(value);
      _lowBalanceAlerts = value;
      notifyListeners();
    } catch (e) {
      _error = 'Error setting low balance alerts: $e';
      notifyListeners();
    }
  }

  // Set days in advance alerts
  Future<void> setDaysInAdvanceAlerts(bool value) async {
    try {
      await _service.setDaysInAdvanceAlerts(value);
      _daysInAdvanceAlerts = value;
      notifyListeners();
    } catch (e) {
      _error = 'Error setting days in advance alerts: $e';
      notifyListeners();
    }
  }

  // Check if setup is completed
  Future<bool> isSetupCompleted() async {
    return await _service.isSetupCompleted();
  }

  // Set setup completed
  Future<void> setSetupCompleted(bool value) async {
    try {
      await _service.setSetupCompleted(value);
      notifyListeners();
    } catch (e) {
      _error = 'Error setting setup completed: $e';
      notifyListeners();
    }
  }

  // Save all settings at once (for setup page)
  Future<void> saveAllSettings({
    required String currency,
    required String dateFormat,
    required String dateInfo,
    required double alertThreshold,
    required int daysInAdvance,
    required ThemeMode themeMode,
    required bool notificationsEnabled,
    required bool lowBalanceAlerts,
    required bool daysInAdvanceAlerts,
  }) async {
    try {
      await _service.setCurrency(currency);
      await _service.setDateFormat(dateFormat);
      await _service.setDateInfo(dateInfo);
      await _service.setAlertThreshold(alertThreshold);
      await _service.setDaysInAdvance(daysInAdvance);
      await _service.setThemeMode(themeMode);
      await _service.setNotificationsEnabled(notificationsEnabled);
      await _service.setLowBalanceAlerts(lowBalanceAlerts);
      await _service.setDaysInAdvanceAlerts(daysInAdvanceAlerts);

      _currency = currency;
      _dateFormat = dateFormat;
      _dateInfo = dateInfo;
      _alertThreshold = alertThreshold;
      _daysInAdvance = daysInAdvance;
      _themeMode = themeMode;
      _notificationsEnabled = notificationsEnabled;
      _lowBalanceAlerts = lowBalanceAlerts;
      _daysInAdvanceAlerts = daysInAdvanceAlerts;

      notifyListeners();
    } catch (e) {
      _error = 'Error saving settings: $e';
      notifyListeners();
    }
  }

  // Clear error
  void clearError() {
    _error = '';
    notifyListeners();
  }
}
