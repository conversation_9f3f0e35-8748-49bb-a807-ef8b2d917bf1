// File: lib/core/models/meter_entry_with_averages.dart

/// MeterEntryWithAverages class for storing meter data with calculated averages
class MeterEntryWithAverages {
  final DateTime date;
  final String entryType; // "MR" for meter reading, "TU" for top-up
  final double amount;
  final String? shortAverage;
  final String? totalAverage;

  MeterEntryWithAverages({
    required this.date,
    required this.entryType,
    required this.amount,
    this.shortAverage,
    this.totalAverage,
  });

  /// Creates a copy of this MeterEntryWithAverages with the given fields replaced with the new values
  MeterEntryWithAverages copyWith({
    DateTime? date,
    String? entryType,
    double? amount,
    String? shortAverage,
    String? totalAverage,
  }) {
    return MeterEntryWithAverages(
      date: date ?? this.date,
      entryType: entryType ?? this.entryType,
      amount: amount ?? this.amount,
      shortAverage: shortAverage ?? this.shortAverage,
      totalAverage: totalAverage ?? this.totalAverage,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'date': date.toIso8601String(),
      'entryType': entryType,
      'amount': amount,
      'shortAverage': shortAverage,
      'totalAverage': totalAverage,
    };
  }

  factory MeterEntryWithAverages.fromMap(Map<String, dynamic> map) {
    return MeterEntryWithAverages(
      date: DateTime.parse(map['date']),
      entryType: map['entryType'],
      amount: map['amount']?.toDouble() ?? 0.0,
      shortAverage: map['shortAverage'],
      totalAverage: map['totalAverage'],
    );
  }
}
