# History Validation Proposals

## Introduction

This document outlines design considerations and proposed solutions to enhance the validation system in the Lekky app. The goal is to create a coherent, robust application that delivers a positive user experience while maintaining data integrity. These proposals address potential edge cases, clarify assumptions, and suggest improvements to the current validation logic.

## Current System Analysis

The Lekky app currently implements validation for prepaid electricity meter readings and top-ups with a focus on:
- Ensuring readings decrease over time (consumption)
- Validating that readings only increase when top-ups are added
- Maintaining chronological integrity of entries
- Providing visual feedback for invalid entries

While the current system works well for typical usage patterns, several areas could benefit from refinement to handle edge cases and improve user experience.

## Design Considerations & Proposed Solutions

### 1. Meter Reading Format Clarification

#### Current Situation
The validation system assumes a monetary-based meter system but doesn't explicitly document this, which could lead to confusion for new developers or users familiar with kWh-based systems.

#### Proposed Solution
- **Documentation Update**: Explicitly state in both code comments and user-facing help: "Meter readings are represented in monetary units (e.g., £), not kWh."
- **UI Enhancement**: Add unit indicators in input fields and displays (e.g., "£" prefix or suffix)
- **Onboarding Clarification**: Include a brief explanation during app setup that clarifies the meter reading format

#### Implementation Details
```dart
// Example UI enhancement for input fields
TextField(
  decoration: InputDecoration(
    labelText: 'Meter Reading',
    prefixText: currencySymbol, // Dynamically set based on user preference
    helperText: 'Enter the monetary value shown on your meter',
  ),
  keyboardType: TextInputType.numberWithOptions(decimal: true),
  // ...
)
```

### 2. Top-Up Granularity

#### Current Situation
The validation logic handles multiple top-ups between readings, but this isn't explicitly documented, potentially causing confusion about how the system calculates available balance.

#### Proposed Solution
- **Documentation Enhancement**: Clearly state that multiple top-ups between readings are supported and aggregated
- **Visualization Improvement**: In the history view, visually group top-ups that occur between the same two readings
- **Validation Logic Refinement**: Ensure validation messages specifically mention "total top-ups" rather than just "top-up" when multiple top-ups exist

#### Implementation Details
```dart
// Example of refined validation message
if (reading > maxPossibleReading) {
  final topUpCount = topUpsBetween.length;
  final message = topUpCount > 1
      ? 'This reading is too high based on previous readings and ${topUpCount} top-ups totaling ${totalTopUps.toStringAsFixed(2)} ${currencySymbol}'
      : 'This reading is too high based on previous readings and top-up';
  
  return {
    'isValid': false,
    'errorMessage': message,
  };
}
```

### 3. High-Frequency Entries Edge Case

#### Current Situation
The current system uses timestamps to order entries but doesn't explicitly handle the case where multiple entries might occur within a short timeframe (same day or hour).

#### Proposed Solution
- **Timestamp Granularity**: Ensure all timestamps include time down to the second (ISO 8601 format)
- **UI Improvement**: When entries are close in time, display the full timestamp in the history view
- **Conflict Resolution**: Implement a conflict detection system that warns users when adding entries with timestamps very close to existing entries

#### Implementation Details
```dart
// Example timestamp conflict detection
Future<bool> hasTimestampConflict(DateTime timestamp) async {
  final entries = await dbHelper.getMeterEntries();
  
  // Check if any entry is within 5 minutes of the new timestamp
  return entries.any((entry) {
    final difference = entry.timestamp.difference(timestamp).abs();
    return difference.inMinutes < 5;
  });
}

// Usage in entry creation
if (await hasTimestampConflict(selectedDate)) {
  showDialog(
    context: context,
    builder: (context) => AlertDialog(
      title: Text('Nearby Entry Detected'),
      content: Text('There is already an entry within 5 minutes of this time. Are you sure you want to add another entry so close in time?'),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context, false),
          child: Text('Cancel'),
        ),
        TextButton(
          onPressed: () => Navigator.pop(context, true),
          child: Text('Continue'),
        ),
      ],
    ),
  );
}
```

### 4. Implied Linear Consumption Assumption

#### Current Situation
The validation system assumes relatively consistent usage patterns, which may not reflect real-world scenarios like vacations, seasonal changes, or unusual consumption events.

#### Proposed Solution
- **Adaptive Validation**: Implement a system that learns from user patterns and adjusts validation thresholds accordingly
- **Usage Pattern Recognition**: Detect and account for common patterns like weekday/weekend differences or seasonal variations
- **Contextual Validation**: Allow users to add context notes to entries that explain unusual patterns

#### Implementation Details
```dart
// Example of contextual validation with notes
class MeterEntry {
  final int? id;
  final double reading;
  final double amountToppedUp;
  final DateTime timestamp;
  final String? contextNote; // New field for explaining unusual readings
  
  // ...constructor and methods
}

// Usage in validation
if (!isValid && entry.contextNote != null) {
  // Show the context note along with the validation error
  return {
    'isValid': false,
    'errorMessage': '$baseErrorMessage\n\nUser note: ${entry.contextNote}',
    'hasContextNote': true,
  };
}
```

### 5. Severity Levels in Validation

#### Current Situation
The validation system currently treats all validation issues as errors of equal severity, which may lead to user frustration when minor inconsistencies block progress.

#### Proposed Solution
- **Tiered Validation System**: Implement three levels of validation issues:
  1. **Errors**: Impossible meter logic that must be fixed (e.g., negative consumption without top-ups)
  2. **Warnings**: Unusual but possible patterns that should be reviewed (e.g., very high consumption)
  3. **Notices**: Informational messages about potential optimizations (e.g., suggesting to add readings at regular intervals)
- **UI Differentiation**: Use distinct visual cues for each severity level (colors, icons)
- **Progressive Disclosure**: Allow users to proceed with warnings after acknowledgment, but require fixing errors

#### Implementation Details
```dart
enum ValidationSeverity {
  error,    // Must be fixed
  warning,  // Should be reviewed
  notice,   // Informational
}

// Enhanced validation result
class ValidationResult {
  final bool isValid;
  final String? message;
  final ValidationSeverity? severity;
  
  const ValidationResult({
    required this.isValid,
    this.message,
    this.severity,
  });
  
  // Helper for quick creation of results
  factory ValidationResult.valid() => ValidationResult(isValid: true);
  
  factory ValidationResult.error(String message) => ValidationResult(
    isValid: false,
    message: message,
    severity: ValidationSeverity.error,
  );
  
  factory ValidationResult.warning(String message) => ValidationResult(
    isValid: true, // Warnings don't invalidate the entry
    message: message,
    severity: ValidationSeverity.warning,
  );
  
  factory ValidationResult.notice(String message) => ValidationResult(
    isValid: true,
    message: message,
    severity: ValidationSeverity.notice,
  );
}
```

### 6. AI-Powered Suggestions

#### Current Situation
When validation fails, users must manually determine appropriate values, which can be challenging for those unfamiliar with their usage patterns.

#### Proposed Solution
- **Smart Correction Suggestions**: When validation fails, calculate and suggest valid values based on:
  - Historical average daily usage
  - Recent consumption patterns
  - Seasonal adjustments (if detected)
- **One-Tap Correction**: Allow users to apply suggested corrections with a single tap
- **Learning System**: Improve suggestions over time based on user acceptance/rejection

#### Implementation Details
```dart
// Example suggestion generation
Map<String, dynamic> generateSuggestion(MeterEntry invalidEntry, List<MeterEntry> history) {
  // For a reading that's too high
  if (invalidEntry.reading > maxPossibleReading) {
    // Calculate average daily usage from recent history
    final avgDailyUsage = calculateAverageDailyUsage(history, days: 30);
    
    // Suggest a reading based on previous reading minus expected usage
    final previousReading = findPreviousReading(history, invalidEntry.timestamp);
    final daysSincePrevious = invalidEntry.timestamp.difference(previousReading.timestamp).inDays;
    final expectedUsage = avgDailyUsage * daysSincePrevious;
    
    // Account for any top-ups
    final topUpsSincePrevious = findTopUpsBetween(
      history, 
      previousReading.timestamp, 
      invalidEntry.timestamp
    );
    
    final suggestedReading = previousReading.reading - expectedUsage + topUpsSincePrevious;
    
    return {
      'suggestedValue': suggestedReading.toStringAsFixed(2),
      'explanation': 'Based on your average daily usage of ${avgDailyUsage.toStringAsFixed(2)} ${currencySymbol} over the last 30 days',
    };
  }
  
  // Handle other validation cases...
  
  return {'suggestedValue': null, 'explanation': null};
}
```

### 7. Offline Import Warnings

#### Current Situation
Validation occurs immediately during import or entry creation, which may cause confusion during offline use or when importing historical data.

#### Proposed Solution
- **Deferred Validation**: For bulk imports or offline entries, store validation status separately from the entries
- **Sync-Aware Validation**: Delay showing validation errors until the app is online and all data is synced
- **Batch Validation UI**: Provide a dedicated interface for reviewing and fixing multiple validation issues after import

#### Implementation Details
```dart
// Example of deferred validation for imports
Future<void> importEntries(List<MeterEntry> entries, {bool isOffline = false}) async {
  // Store entries without immediate validation
  await dbHelper.bulkInsertEntries(entries);
  
  // If offline, mark for later validation
  if (isOffline) {
    await preferencesHelper.setNeedsValidation(true);
    return;
  }
  
  // Otherwise, validate immediately
  await validateAllEntries();
}

// Check on app startup
Future<void> checkPendingValidation() async {
  final needsValidation = await preferencesHelper.getNeedsValidation();
  
  if (needsValidation) {
    // Show notification about pending validation
    notificationService.show(
      title: 'Validation Needed',
      message: 'Some imported entries need validation. Tap to review.',
      onTap: () => navigateToValidationReview(),
    );
  }
}
```

### 8. Accessibility Enhancements

#### Current Situation
The current validation system relies heavily on color cues (red highlighting) to indicate invalid entries, which may not be accessible to all users.

#### Proposed Solution
- **Multi-Modal Feedback**: Supplement color-based cues with:
  - Distinctive icons (error, warning, notice)
  - Haptic feedback when encountering invalid entries
  - Screen reader-friendly descriptions
- **High Contrast Mode**: Ensure validation indicators work well in high contrast mode
- **Customizable Feedback**: Allow users to choose their preferred feedback methods

#### Implementation Details
```dart
// Example of accessible validation indicator
Widget buildValidationIndicator(ValidationResult result) {
  if (result.isValid && result.severity == null) {
    return SizedBox.shrink(); // No indicator needed
  }
  
  IconData icon;
  String semanticLabel;
  Color color;
  
  switch (result.severity) {
    case ValidationSeverity.error:
      icon = Icons.error;
      semanticLabel = 'Error: ${result.message}';
      color = Colors.red;
      break;
    case ValidationSeverity.warning:
      icon = Icons.warning;
      semanticLabel = 'Warning: ${result.message}';
      color = Colors.orange;
      break;
    case ValidationSeverity.notice:
    default:
      icon = Icons.info;
      semanticLabel = 'Notice: ${result.message}';
      color = Colors.blue;
      break;
  }
  
  return Semantics(
    label: semanticLabel,
    button: true,
    onTap: () {}, // Show details dialog
    child: Icon(
      icon,
      color: color,
      size: 18,
    ),
  );
}
```

## Integration Strategy

To ensure these proposals work together cohesively:

### 1. Phased Implementation

Implement the enhancements in three phases:

1. **Foundation Phase**:
   - Meter reading format clarification
   - Top-up granularity documentation
   - Timestamp precision improvements

2. **Enhancement Phase**:
   - Severity levels in validation
   - Contextual validation with notes
   - Accessibility improvements

3. **Advanced Phase**:
   - AI-powered suggestions
   - Adaptive validation
   - Offline import handling

### 2. Unified Validation Service

Create a centralized `ValidationService` that:
- Encapsulates all validation logic
- Provides consistent interfaces for different validation scenarios
- Manages severity levels and suggestions
- Handles both immediate and deferred validation

### 3. User Experience Flow

Design the validation experience to follow these principles:

1. **Progressive Disclosure**: Show only the most critical information first
2. **Non-Blocking When Possible**: Allow users to proceed with warnings
3. **Helpful Guidance**: Always provide clear explanations and suggestions
4. **Contextual Awareness**: Adapt validation based on user patterns and preferences

## Conclusion

By implementing these proposals, the Lekky app will provide a more robust, user-friendly validation system that:
- Handles edge cases gracefully
- Provides clear, accessible feedback
- Offers helpful suggestions
- Adapts to individual usage patterns

This enhanced validation system will improve data integrity while reducing user frustration, leading to a more positive overall experience.
