// File: lib/core/shared_modules/radio_selectors_adapter.dart
import 'package:flutter/material.dart';
import '../settings/widgets/radio_currency_selector.dart';
import '../settings/widgets/radio_region_selector.dart';

/// An adapter class that provides radio-based selectors for the app
/// This allows us to use the new radio-based selectors with the existing code
class RadioSelectorsAdapter {
  /// Shows a currency selection dialog with radio buttons
  static Future<void> showCurrencyDialog(
    BuildContext context,
    String currentValue,
    Function(String) onChanged,
  ) {
    return showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) {
          String selectedValue = currentValue;

          return AlertDialog(
            title: const Text('Select Currency'),
            content: SizedBox(
              width: double.maxFinite,
              child: RadioCurrencySelector(
                currentValue: selectedValue,
                onChanged: (value) {
                  setState(() {
                    selectedValue = value;
                  });
                },
                useDialog: false,
                showCard: false,
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () {
                  onChanged(selectedValue);
                  Navigator.of(context).pop();
                },
                child: const Text('Save'),
              ),
            ],
          );
        },
      ),
    );
  }

  /// Shows a region selection dialog with radio buttons
  static Future<void> showRegionDialog(
    BuildContext context,
    String currentValue,
    Function(String) onChanged,
  ) {
    return showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) {
          String selectedValue = currentValue;

          return AlertDialog(
            title: const Text('Select Region'),
            content: SizedBox(
              width: double.maxFinite,
              child: SingleChildScrollView(
                child: RadioRegionSelector(
                  currentValue: selectedValue,
                  onChanged: (value) {
                    setState(() {
                      selectedValue = value;
                    });
                  },
                  useDialog: false,
                  showCard: false,
                ),
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () {
                  onChanged(selectedValue);
                  Navigator.of(context).pop();
                },
                child: const Text('Save'),
              ),
            ],
          );
        },
      ),
    );
  }

  /// Creates a currency selector widget with radio buttons
  static Widget createCurrencySelector({
    required String currentValue,
    required Function(String) onChanged,
    bool useDialog = false,
    bool showCard = true,
    FocusNode? focusNode,
  }) {
    return RadioCurrencySelector(
      currentValue: currentValue,
      onChanged: onChanged,
      useDialog: useDialog,
      showCard: showCard,
      focusNode: focusNode,
    );
  }

  /// Creates a region selector widget with radio buttons
  static Widget createRegionSelector({
    required String currentValue,
    required Function(String) onChanged,
    bool useDialog = false,
    bool showCard = true,
  }) {
    return RadioRegionSelector(
      currentValue: currentValue,
      onChanged: onChanged,
      useDialog: useDialog,
      showCard: showCard,
    );
  }
}
