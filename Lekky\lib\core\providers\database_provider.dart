import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../database/database_helper.dart';
import '../../features/meter_readings/domain/repositories/meter_reading_repository.dart';
import '../../features/meter_readings/data/repositories/meter_reading_repository_impl.dart';
import '../../features/top_ups/domain/repositories/top_up_repository.dart';
import '../../features/top_ups/data/repositories/top_up_repository_impl.dart';
import '../../features/averages/domain/repositories/average_repository.dart';
import '../../features/averages/data/repositories/average_repository_impl.dart';
import '../../features/averages/domain/repositories/per_reading_average_repository.dart';
import '../../features/averages/data/repositories/per_reading_average_repository_impl.dart';

part 'database_provider.g.dart';

/// Database helper provider
@riverpod
DatabaseHelper databaseHelper(DatabaseHelperRef ref) {
  return DatabaseHelper();
}

/// Meter reading repository provider
@riverpod
MeterReadingRepository meterReadingRepository(MeterReadingRepositoryRef ref) {
  return MeterReadingRepositoryImpl(
    ref.watch(databaseHelperProvider),
    ref.container,
  );
}

/// Top-up repository provider
@riverpod
TopUpRepository topUpRepository(TopUpRepositoryRef ref) {
  return TopUpRepositoryImpl(
    ref.watch(databaseHelperProvider),
    ref.container,
  );
}

/// Average repository provider
@riverpod
AverageRepository averageRepository(AverageRepositoryRef ref) {
  return AverageRepositoryImpl(ref.watch(databaseHelperProvider));
}

/// Per-reading average repository provider
@riverpod
PerReadingAverageRepository perReadingAverageRepository(
    PerReadingAverageRepositoryRef ref) {
  return PerReadingAverageRepositoryImpl(ref.watch(databaseHelperProvider));
}
