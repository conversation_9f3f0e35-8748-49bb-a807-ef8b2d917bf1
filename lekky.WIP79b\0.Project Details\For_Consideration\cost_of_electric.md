# Cost of Electric Calculation Guide

This document explains how the Lekky app calculates electricity costs for different time periods using the "Cost of Electric" feature.

## Overview

The "Cost of Electric" feature allows users to estimate their electricity usage costs over specific time periods:
- Last Month
- Last 3 Months
- Last 6 Months
- Last Year
- Custom Range (user-selected dates)

All calculations are based on monetary meter readings (e.g., £50.00) and use interpolation for accurate estimates when readings don't align perfectly with the selected date range.

## Calculation Method

### Core Concept: Interpolation

Since meter readings may not perfectly align with the start and end dates of the selected period, the app uses linear interpolation to estimate readings at specific dates.

![Interpolation Diagram](https://i.imgur.com/example.png)

### Step-by-Step Calculation Process

1. **Find Relevant Readings**
   - Identify readings before and after the start date
   - Identify readings before and after the end date

2. **Interpolate Start Value**
   - If there's a reading exactly on the start date, use that value
   - Otherwise, interpolate between the closest readings before and after the start date:
     ```
     avgDailyUsage = (laterReading - earlierReading) / daysBetween
     startEstimate = earlierReading - (avgDailyUsage × daysFromEarlierToStartDate)
     ```

3. **Interpolate End Value**
   - If there's a reading exactly on the end date, use that value
   - Otherwise, interpolate between the closest readings before and after the end date using the same method

4. **Calculate Cost**
   - Subtract the end estimate from the start estimate:
     ```
     estimatedCost = startEstimate - endEstimate
     ```
   - This works because prepaid meter readings decrease over time as electricity is used

## Period-Specific Calculations

### Last Month

**Date Range:** First day to last day of the previous calendar month
- Example: If today is April 15, 2023, the range would be March 1-31, 2023

**Calculation:**
1. Find/interpolate the meter value on the first day of last month
2. Find/interpolate the meter value on the last day of last month
3. Calculate the difference (start - end)

### Last 3 Months

**Date Range:** 3 months ago from today to today
- Example: If today is April 15, 2023, the range would be January 15 to April 15, 2023

**Calculation:**
1. Find/interpolate the meter value 3 months ago
2. Find/interpolate the meter value today
3. Calculate the difference (start - end)

### Last 6 Months

**Date Range:** 6 months ago from today to today
- Example: If today is April 15, 2023, the range would be October 15, 2022 to April 15, 2023

**Calculation:**
1. Find/interpolate the meter value 6 months ago
2. Find/interpolate the meter value today
3. Calculate the difference (start - end)

### Last Year

**Date Range:** First day to last day of the previous calendar year
- Example: If it's 2023, the range would be January 1 to December 31, 2022

**Calculation:**
1. Find/interpolate the meter value on January 1 of last year
2. Find/interpolate the meter value on December 31 of last year
3. Calculate the difference (start - end)

### Custom Range

**Date Range:** User-selected start and end dates
- Constrained by the earliest and latest available meter readings

**Calculation:**
1. Find/interpolate the meter value on the user-selected start date
2. Find/interpolate the meter value on the user-selected end date
3. Calculate the difference (start - end)

## Example Calculation

Let's walk through a concrete example:

**Available Readings:**
- March 1, 2023: £100.00
- March 15, 2023: £85.00
- April 5, 2023: £70.00
- April 20, 2023: £55.00

**Calculating Cost for March 2023 (Last Month):**

1. **Start Date (March 1):**
   - We have an exact reading: £100.00

2. **End Date (March 31):**
   - We need to interpolate between March 15 (£85.00) and April 5 (£70.00)
   - Days between readings: 21 days
   - Usage between readings: £15.00
   - Daily usage: £15.00 ÷ 21 = £0.71 per day
   - Days from March 15 to March 31: 16 days
   - Estimated usage for this period: 16 × £0.71 = £11.36
   - Estimated reading on March 31: £85.00 - £11.36 = £73.64

3. **Calculate Cost:**
   - Start reading (March 1): £100.00
   - End reading (March 31): £73.64
   - Estimated cost: £100.00 - £73.64 = £26.36

## Validation Rules

The app enforces several validation rules to ensure accurate calculations:

1. **Date Range Constraints:**
   - Start date must be on or after the earliest available meter reading
   - End date must be on or before the latest available meter reading or today (whichever is earlier)
   - Start date must be before end date

2. **Data Sufficiency:**
   - At least two meter readings must exist to perform interpolation
   - The selected period must have readings before/after to enable interpolation

3. **Period Availability:**
   - Period options (Last 3 Months, Last 6 Months, etc.) are only shown if there's sufficient data
   - For example, "Last Year" is only available if readings span at least into the previous year

## Technical Implementation

The calculation is performed by the `UsageEstimator` utility class, which:

1. Finds readings around specific dates using `findReadingsAroundDate()`
2. Interpolates readings at arbitrary dates using `interpolateReading()`
3. Calculates costs between date ranges using `estimateCostBetweenDates()`

```dart
// Example of the interpolation logic
double interpolateReading(MeterEntry earlier, MeterEntry later, DateTime targetDate) {
  // Calculate days between readings
  final daysBetween = later.timestamp.difference(earlier.timestamp).inDays;
  if (daysBetween <= 0) return earlier.reading;

  // Calculate average daily usage
  final totalUsage = earlier.reading - later.reading;
  final avgDailyUsage = totalUsage / daysBetween;

  // Calculate days from earlier reading to target date
  final daysFromEarlier = targetDate.difference(earlier.timestamp).inDays;

  // Interpolate the reading at the target date
  return earlier.reading - (avgDailyUsage * daysFromEarlier);
}
```

## Limitations and Edge Cases

1. **Sparse Data:**
   - If readings are very far apart, interpolation becomes less accurate
   - The app will still calculate based on available data but may not reflect actual usage patterns

2. **Inconsistent Usage Patterns:**
   - The calculation assumes relatively consistent usage between readings
   - Seasonal variations or unusual events (vacations, etc.) may affect accuracy

3. **Top-Ups:**
   - The calculation accounts for top-ups between readings
   - However, multiple small top-ups close together might affect interpolation accuracy

## Best Practices for Accurate Estimates

1. **Regular Readings:**
   - Enter meter readings at consistent intervals (weekly or bi-weekly)
   - More frequent readings improve interpolation accuracy

2. **Record Significant Changes:**
   - Add readings before and after periods of unusual usage
   - Record readings before and after extended absences

3. **Use Custom Range:**
   - For the most accurate results, use Custom Range and select dates that closely align with actual readings
