// File: lib/core/theme/theme_service.dart
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'app_colors.dart';
import 'app_theme.dart';

/// A service class that manages the app's theme
class ThemeService {
  // Singleton instance
  static final ThemeService _instance = ThemeService._internal();
  factory ThemeService() => _instance;
  ThemeService._internal();

  // Theme mode
  ThemeMode _themeMode = ThemeMode.system;
  ThemeMode get themeMode => _themeMode;

  // Theme mode names for UI
  static const Map<ThemeMode, String> themeModeNames = {
    ThemeMode.light: 'Light',
    ThemeMode.dark: 'Dark',
    ThemeMode.system: 'System',
  };

  // Initialize the theme service
  Future<void> initialize() async {
    final prefs = await SharedPreferences.getInstance();
    final themeModeIndex = prefs.getInt('theme_mode') ?? 0;
    _themeMode = ThemeMode.values[themeModeIndex];
  }

  // Set the theme mode
  Future<void> setThemeMode(ThemeMode mode) async {
    if (_themeMode == mode) return;
    
    _themeMode = mode;
    
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt('theme_mode', mode.index);
  }

  // Set the theme mode from a string
  Future<void> setThemeModeFromString(String mode) async {
    switch (mode) {
      case 'light':
        await setThemeMode(ThemeMode.light);
        break;
      case 'dark':
        await setThemeMode(ThemeMode.dark);
        break;
      case 'system':
      default:
        await setThemeMode(ThemeMode.system);
        break;
    }
  }

  // Get the theme mode as a string
  String getThemeModeAsString() {
    switch (_themeMode) {
      case ThemeMode.light:
        return 'light';
      case ThemeMode.dark:
        return 'dark';
      case ThemeMode.system:
      default:
        return 'system';
    }
  }

  // Toggle between light and dark themes
  Future<void> toggleTheme() async {
    final newMode = _themeMode == ThemeMode.light 
        ? ThemeMode.dark 
        : ThemeMode.light;
    
    await setThemeMode(newMode);
  }

  // Check if dark mode is active
  bool isDarkMode(BuildContext context) {
    if (_themeMode == ThemeMode.system) {
      return MediaQuery.of(context).platformBrightness == Brightness.dark;
    }
    return _themeMode == ThemeMode.dark;
  }

  // Get the current theme data
  ThemeData getThemeData(BuildContext context) {
    final brightness = _themeMode == ThemeMode.system
        ? MediaQuery.of(context).platformBrightness
        : _themeMode == ThemeMode.light
            ? Brightness.light
            : Brightness.dark;
    
    return brightness == Brightness.light
        ? _getLightTheme()
        : _getDarkTheme();
  }

  // Get the light theme
  ThemeData _getLightTheme() {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,
      colorScheme: ColorScheme(
        brightness: Brightness.light,
        primary: AppColors.primary,
        onPrimary: AppColors.onPrimary,
        secondary: AppColors.secondary,
        onSecondary: AppColors.onSecondary,
        tertiary: AppColors.tertiary,
        onTertiary: AppColors.onTertiary,
        error: AppColors.error,
        onError: AppColors.onError,
        background: AppColors.background,
        onBackground: AppColors.onBackground,
        surface: AppColors.surface,
        onSurface: AppColors.onSurface,
      ),
      scaffoldBackgroundColor: AppColors.background,
      cardColor: AppColors.surface,
      dialogBackgroundColor: AppColors.surface,
      appBarTheme: const AppBarTheme(
        backgroundColor: Colors.transparent,
        elevation: 0,
        iconTheme: IconThemeData(color: AppColors.onPrimary),
      ),
      textTheme: TextTheme(
        bodyLarge: TextStyle(color: AppColors.onBackground),
        bodyMedium: TextStyle(color: AppColors.onBackground),
        bodySmall: TextStyle(color: AppColors.textSecondary),
        titleLarge: TextStyle(color: AppColors.onBackground, fontWeight: FontWeight.bold),
        titleMedium: TextStyle(color: AppColors.onBackground, fontWeight: FontWeight.bold),
        titleSmall: TextStyle(color: AppColors.onBackground, fontWeight: FontWeight.bold),
      ),
      iconTheme: IconThemeData(color: AppColors.onBackground),
      dividerTheme: DividerThemeData(color: AppColors.onBackground.withOpacity(0.1)),
    );
  }

  // Get the dark theme
  ThemeData _getDarkTheme() {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      colorScheme: ColorScheme(
        brightness: Brightness.dark,
        primary: AppColors.primaryDark,
        onPrimary: AppColors.onPrimaryDark,
        secondary: AppColors.secondaryDark,
        onSecondary: AppColors.onSecondaryDark,
        tertiary: AppColors.tertiaryDark,
        onTertiary: AppColors.onTertiaryDark,
        error: AppColors.errorDark,
        onError: AppColors.onErrorDark,
        background: AppColors.backgroundDark,
        onBackground: AppColors.onBackgroundDark,
        surface: AppColors.surfaceDark,
        onSurface: AppColors.onSurfaceDark,
      ),
      scaffoldBackgroundColor: AppColors.backgroundDark,
      cardColor: AppColors.surfaceDark,
      dialogBackgroundColor: AppColors.surfaceDark,
      appBarTheme: const AppBarTheme(
        backgroundColor: Colors.transparent,
        elevation: 0,
        iconTheme: IconThemeData(color: AppColors.onPrimaryDark),
      ),
      textTheme: TextTheme(
        bodyLarge: TextStyle(color: AppColors.onBackgroundDark),
        bodyMedium: TextStyle(color: AppColors.onBackgroundDark),
        bodySmall: TextStyle(color: AppColors.textSecondaryDark),
        titleLarge: TextStyle(color: AppColors.onBackgroundDark, fontWeight: FontWeight.bold),
        titleMedium: TextStyle(color: AppColors.onBackgroundDark, fontWeight: FontWeight.bold),
        titleSmall: TextStyle(color: AppColors.onBackgroundDark, fontWeight: FontWeight.bold),
      ),
      iconTheme: IconThemeData(color: AppColors.onBackgroundDark),
      dividerTheme: DividerThemeData(color: AppColors.onBackgroundDark.withOpacity(0.1)),
    );
  }
}
