// File: lib/core/utils/enhanced_logger.dart

import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:intl/intl.dart';

/// Log levels for different severity of messages
enum LogLevel {
  debug,
  info,
  warning,
  error,
  critical,
}

/// Enhanced logger with support for different log levels, file logging,
/// and structured log data.
class EnhancedLogger {
  static const String _tag = 'RooLekky';
  static bool _initialized = false;
  static bool _consoleLoggingEnabled = true;
  static bool _fileLoggingEnabled = false;
  static LogLevel _minimumLogLevel = LogLevel.info;
  static File? _logFile;
  static final DateFormat _timestampFormat =
      DateFormat('yyyy-MM-dd HH:mm:ss.SSS');
  static final Map<String, dynamic> _contextData = {};

  // Log retention settings
  static const int _maxLogFileSizeBytes = 5 * 1024 * 1024; // 5 MB
  static const int _maxLogFiles = 5;

  // Stream controller for log events
  static final StreamController<Map<String, dynamic>> _logStreamController =
      StreamController<Map<String, dynamic>>.broadcast();

  /// Initialize the logger
  static Future<void> initialize({
    bool consoleLoggingEnabled = true,
    bool fileLoggingEnabled = true,
    LogLevel minimumLogLevel = LogLevel.info,
    Map<String, dynamic>? initialContext,
  }) async {
    if (_initialized) return;

    _consoleLoggingEnabled = consoleLoggingEnabled;
    _fileLoggingEnabled = fileLoggingEnabled;
    _minimumLogLevel = minimumLogLevel;

    if (initialContext != null) {
      _contextData.addAll(initialContext);
    }

    // Add device info to context
    _contextData['platform'] = defaultTargetPlatform.toString();

    if (_fileLoggingEnabled) {
      await _initializeLogFile();
    }

    _initialized = true;

    info('Logger initialized', details: {
      'consoleLoggingEnabled': _consoleLoggingEnabled,
      'fileLoggingEnabled': _fileLoggingEnabled,
      'minimumLogLevel': _minimumLogLevel.toString(),
    });
  }

  /// Initialize the log file
  static Future<void> _initializeLogFile() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final logDir = Directory('${directory.path}/logs');

      if (!await logDir.exists()) {
        await logDir.create(recursive: true);
      }

      final now = DateTime.now();
      final timestamp = DateFormat('yyyyMMdd_HHmmss').format(now);
      _logFile = File('${logDir.path}/roolekky_$timestamp.log');

      // Create the file
      if (!await _logFile!.exists()) {
        await _logFile!.create();
      }

      // Clean up old log files
      await _cleanupOldLogFiles(logDir);
    } catch (e) {
      _consoleLog(LogLevel.error, 'Failed to initialize log file',
          details: {'error': e.toString()});
      _fileLoggingEnabled = false;
    }
  }

  /// Clean up old log files
  static Future<void> _cleanupOldLogFiles(Directory logDir) async {
    try {
      final files = await logDir.list().toList();
      if (files.length <= _maxLogFiles) return;

      // Sort files by creation time (oldest first)
      files.sort((a, b) {
        return a.statSync().changed.compareTo(b.statSync().changed);
      });

      // Delete oldest files
      for (int i = 0; i < files.length - _maxLogFiles; i++) {
        if (files[i] is File) {
          await (files[i] as File).delete();
        }
      }
    } catch (e) {
      _consoleLog(LogLevel.error, 'Failed to clean up old log files',
          details: {'error': e.toString()});
    }
  }

  /// Rotate log file if it exceeds the maximum size
  static Future<void> _rotateLogFileIfNeeded() async {
    if (_logFile == null) return;

    try {
      final fileStats = await _logFile!.stat();
      if (fileStats.size > _maxLogFileSizeBytes) {
        final directory = _logFile!.parent;
        final filename = _logFile!.path.split('/').last;
        final newTimestamp =
            DateFormat('yyyyMMdd_HHmmss').format(DateTime.now());
        final newFilename =
            filename.replaceAll(RegExp(r'\d{8}_\d{6}'), newTimestamp);

        _logFile = File('${directory.path}/$newFilename');
        await _logFile!.create();

        // Clean up old log files
        await _cleanupOldLogFiles(directory);
      }
    } catch (e) {
      _consoleLog(LogLevel.error, 'Failed to rotate log file',
          details: {'error': e.toString()});
    }
  }

  /// Log a debug message
  static void debug(String message,
      {Map<String, dynamic>? details, String? component}) {
    _log(LogLevel.debug, message, details: details, component: component);
  }

  /// Log an info message
  static void info(String message,
      {Map<String, dynamic>? details, String? component}) {
    _log(LogLevel.info, message, details: details, component: component);
  }

  /// Log a warning message
  static void warning(String message,
      {Map<String, dynamic>? details, String? component}) {
    _log(LogLevel.warning, message, details: details, component: component);
  }

  /// Log an error message
  static void error(String message,
      {Map<String, dynamic>? details,
      String? component,
      Object? exception,
      StackTrace? stackTrace}) {
    final Map<String, dynamic> errorDetails = details ?? {};

    if (exception != null) {
      errorDetails['exception'] = exception.toString();
    }

    if (stackTrace != null) {
      errorDetails['stackTrace'] = stackTrace.toString();
    }

    _log(LogLevel.error, message, details: errorDetails, component: component);
  }

  /// Log a critical message
  static void critical(String message,
      {Map<String, dynamic>? details,
      String? component,
      Object? exception,
      StackTrace? stackTrace}) {
    final Map<String, dynamic> errorDetails = details ?? {};

    if (exception != null) {
      errorDetails['exception'] = exception.toString();
    }

    if (stackTrace != null) {
      errorDetails['stackTrace'] = stackTrace.toString();
    }

    _log(LogLevel.critical, message,
        details: errorDetails, component: component);
  }

  /// Internal method to log a message with the specified level
  static void _log(LogLevel level, String message,
      {Map<String, dynamic>? details, String? component}) {
    if (!_initialized) {
      // Auto-initialize with defaults if not already initialized
      initialize();
    }

    // Skip logging if below minimum level
    if (level.index < _minimumLogLevel.index) {
      return;
    }

    final timestamp = DateTime.now();
    final formattedTimestamp = _timestampFormat.format(timestamp);
    final levelString = level.toString().split('.').last.toUpperCase();

    // Create structured log entry
    final Map<String, dynamic> logEntry = {
      'timestamp': formattedTimestamp,
      'level': levelString,
      'message': message,
      'component': component ?? 'app',
    };

    // Add context data
    if (_contextData.isNotEmpty) {
      logEntry['context'] = Map<String, dynamic>.from(_contextData);
    }

    // Add details
    if (details != null && details.isNotEmpty) {
      logEntry['details'] = details;
    }

    // Send to console
    if (_consoleLoggingEnabled) {
      _consoleLog(level, message, details: details, component: component);
    }

    // Write to file
    if (_fileLoggingEnabled && _logFile != null) {
      _fileLog(logEntry);
    }

    // Add to stream
    _logStreamController.add(logEntry);
  }

  /// Log to console
  static void _consoleLog(LogLevel level, String message,
      {Map<String, dynamic>? details, String? component}) {
    final levelString = level.toString().split('.').last.toUpperCase();
    final componentStr = component != null ? '[$component]' : '';

    String logMessage = '[$_tag][$levelString]$componentStr $message';

    if (details != null && details.isNotEmpty) {
      final prettyDetails = const JsonEncoder.withIndent('  ').convert(details);
      logMessage += '\n$prettyDetails';
    }

    switch (level) {
      case LogLevel.debug:
      case LogLevel.info:
        debugPrint(logMessage);
        break;
      case LogLevel.warning:
        debugPrint('\x1B[33m$logMessage\x1B[0m'); // Yellow
        break;
      case LogLevel.error:
        debugPrint('\x1B[31m$logMessage\x1B[0m'); // Red
        break;
      case LogLevel.critical:
        debugPrint('\x1B[31m\x1B[1m$logMessage\x1B[0m'); // Bold Red
        break;
    }
  }

  /// Log to file
  static void _fileLog(Map<String, dynamic> logEntry) async {
    try {
      await _rotateLogFileIfNeeded();

      final logLine = json.encode(logEntry);
      await _logFile!.writeAsString('$logLine\n', mode: FileMode.append);
    } catch (e) {
      _consoleLog(LogLevel.error, 'Failed to write to log file',
          details: {'error': e.toString()});
    }
  }

  /// Get a stream of log events
  static Stream<Map<String, dynamic>> get logStream =>
      _logStreamController.stream;

  /// Add context data
  static void addContext(String key, dynamic value) {
    _contextData[key] = value;
  }

  /// Remove context data
  static void removeContext(String key) {
    _contextData.remove(key);
  }

  /// Clear all context data
  static void clearContext() {
    _contextData.clear();
  }

  /// Set minimum log level
  static void setMinimumLogLevel(LogLevel level) {
    _minimumLogLevel = level;
  }

  /// Enable or disable console logging
  static void setConsoleLogging(bool enabled) {
    _consoleLoggingEnabled = enabled;
  }

  /// Enable or disable file logging
  static void setFileLogging(bool enabled) {
    _fileLoggingEnabled = enabled;
  }

  /// Get all logs as a string
  static Future<String> getLogsAsString() async {
    if (!_fileLoggingEnabled || _logFile == null) {
      return 'File logging is not enabled';
    }

    try {
      return await _logFile!.readAsString();
    } catch (e) {
      return 'Failed to read log file: ${e.toString()}';
    }
  }

  /// Get logs as a list of entries
  static Future<List<Map<String, dynamic>>> getLogEntries() async {
    if (!_fileLoggingEnabled || _logFile == null) {
      return [];
    }

    try {
      final content = await _logFile!.readAsString();
      final lines =
          content.split('\n').where((line) => line.isNotEmpty).toList();

      return lines.map((line) {
        try {
          return json.decode(line) as Map<String, dynamic>;
        } catch (e) {
          return {'raw': line, 'error': 'Failed to parse JSON'};
        }
      }).toList();
    } catch (e) {
      return [
        {'error': 'Failed to read log file: ${e.toString()}'}
      ];
    }
  }

  /// Get the path to the current log file
  static String? getLogFilePath() {
    return _logFile?.path;
  }

  /// Clear the current log file
  static Future<void> clearLogs() async {
    if (!_fileLoggingEnabled || _logFile == null) {
      return;
    }

    try {
      await _logFile!.writeAsString('');
    } catch (e) {
      _consoleLog(LogLevel.error, 'Failed to clear log file',
          details: {'error': e.toString()});
    }
  }

  /// Dispose the logger
  static void dispose() {
    _logStreamController.close();
  }
}
