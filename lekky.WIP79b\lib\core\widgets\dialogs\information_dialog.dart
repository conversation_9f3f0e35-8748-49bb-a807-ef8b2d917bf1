// File: lib/core/widgets/dialogs/information_dialog.dart
import 'package:flutter/material.dart';
import '../../theme/app_colors.dart';
import '../../theme/app_text_styles.dart';
import '../app_dialog.dart';

/// A specialized dialog for displaying important information to users.
/// 
/// This dialog presents a descriptive title, concise information,
/// and a single "OK" or "Close" button.
class InformationDialog {
  /// Shows an information dialog with the specified title and message.
  /// 
  /// Parameters:
  /// - [context]: The build context.
  /// - [title]: The title of the dialog, should be descriptive.
  /// - [message]: The information to display.
  /// - [closeText]: The text for the close button (default: "OK").
  /// - [icon]: An optional icon to display in the dialog.
  /// - [iconColor]: The color of the icon (default: primary color).
  /// - [scrollable]: Whether the content should be scrollable (default: false).
  /// - [barrierDismissible]: Whether the dialog can be dismissed by tapping outside (default: true).
  static Future<void> show({
    required BuildContext context,
    required String title,
    required String message,
    String closeText = 'OK',
    IconData? icon,
    Color? iconColor,
    bool scrollable = false,
    bool barrierDismissible = true,
  }) async {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    // Determine the icon color if not specified
    final Color effectiveIconColor = iconColor ?? 
        (isDarkMode ? AppColors.primaryDark : AppColors.primary);
    
    // Create the content widget with an optional icon
    Widget content = Text(
      message,
      style: AppTextStyles.bodyMedium.copyWith(
        color: isDarkMode
            ? AppColors.onSurfaceDark.withOpacity(0.8)
            : AppColors.onSurface.withOpacity(0.8),
      ),
      textAlign: TextAlign.center,
    );
    
    if (icon != null) {
      content = Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 48,
            color: effectiveIconColor,
          ),
          const SizedBox(height: 16),
          content,
        ],
      );
    }
    
    return AppDialog.show(
      context: context,
      title: title,
      content: content,
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          style: TextButton.styleFrom(
            foregroundColor: isDarkMode ? AppColors.primaryDark : AppColors.primary,
          ),
          child: Text(closeText),
        ),
      ],
      scrollable: scrollable,
      barrierDismissible: barrierDismissible,
    );
  }
  
  /// Shows a success information dialog.
  /// 
  /// This is a specialized version of the information dialog for success messages,
  /// with a check icon and success colors.
  static Future<void> showSuccess({
    required BuildContext context,
    required String title,
    required String message,
    String closeText = 'OK',
    bool scrollable = false,
    bool barrierDismissible = true,
  }) async {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return show(
      context: context,
      title: title,
      message: message,
      closeText: closeText,
      icon: Icons.check_circle,
      iconColor: isDarkMode ? AppColors.successDark : AppColors.success,
      scrollable: scrollable,
      barrierDismissible: barrierDismissible,
    );
  }
  
  /// Shows an error information dialog.
  /// 
  /// This is a specialized version of the information dialog for error messages,
  /// with an error icon and error colors.
  static Future<void> showError({
    required BuildContext context,
    required String title,
    required String message,
    String closeText = 'OK',
    bool scrollable = false,
    bool barrierDismissible = true,
  }) async {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return show(
      context: context,
      title: title,
      message: message,
      closeText: closeText,
      icon: Icons.error,
      iconColor: isDarkMode ? AppColors.errorDark : AppColors.error,
      scrollable: scrollable,
      barrierDismissible: barrierDismissible,
    );
  }
  
  /// Shows a warning information dialog.
  /// 
  /// This is a specialized version of the information dialog for warning messages,
  /// with a warning icon and warning colors.
  static Future<void> showWarning({
    required BuildContext context,
    required String title,
    required String message,
    String closeText = 'OK',
    bool scrollable = false,
    bool barrierDismissible = true,
  }) async {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return show(
      context: context,
      title: title,
      message: message,
      closeText: closeText,
      icon: Icons.warning,
      iconColor: isDarkMode ? AppColors.warningDark : AppColors.warning,
      scrollable: scrollable,
      barrierDismissible: barrierDismissible,
    );
  }
}
