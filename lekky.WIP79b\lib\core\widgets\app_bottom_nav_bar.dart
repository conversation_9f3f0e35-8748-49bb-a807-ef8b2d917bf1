// File: lib/core/widgets/app_bottom_nav_bar.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../shared_modules/settings_provider.dart';
import '../theme/app_colors.dart';
import 'bottom_nav_bar.dart';

/// The app's bottom navigation bar
class AppBottomNavBar extends StatefulWidget {
  final int currentIndex;
  final Function(int) onTap;

  const AppBottomNavBar({
    super.key,
    required this.currentIndex,
    required this.onTap,
  });

  @override
  State<AppBottomNavBar> createState() => _AppBottomNavBarState();
}

class _AppBottomNavBarState extends State<AppBottomNavBar> {
  // We no longer need to store the currency symbol as state
  // It will be provided by the SettingsProvider

  @override
  void initState() {
    super.initState();
    // No need to load currency symbol manually
  }

  @override
  void dispose() {
    super.dispose();
  }

  // Get the appropriate icon for the cost tab based on the currency
  IconData _getCostIcon(String currencySymbol, bool isActive) {
    // Default to dollar sign
    if (currencySymbol == '£') {
      return isActive ? Icons.currency_pound : Icons.currency_pound_outlined;
    } else if (currencySymbol == '€') {
      return isActive ? Icons.euro : Icons.euro_outlined;
    } else if (currencySymbol == '¥' || currencySymbol == 'CN¥') {
      return isActive ? Icons.currency_yen : Icons.currency_yen_outlined;
    } else if (currencySymbol.contains('\$')) {
      return isActive ? Icons.attach_money : Icons.attach_money_outlined;
    } else {
      // For other currencies, use a generic money icon
      return isActive ? Icons.payments : Icons.payments_outlined;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<SettingsProvider>(
      builder: (context, settingsProvider, _) {
        final currencySymbol = settingsProvider.currency;

        final l10n = AppLocalizations.of(context);

        return BottomNavBar(
          currentIndex: widget.currentIndex,
          onTap: widget.onTap,
          items: [
            BottomNavItem(
              label: l10n.home,
              icon: Icons.home_outlined,
              activeIcon: Icons.home,
              activeColor: AppColors.homeTab,
            ),
            BottomNavItem(
              label: l10n.cost,
              icon: _getCostIcon(currencySymbol, false),
              activeIcon: _getCostIcon(currencySymbol, true),
              activeColor: AppColors.costTab,
            ),
            BottomNavItem(
              label: l10n.history,
              icon: Icons.history_outlined,
              activeIcon: Icons.history,
              activeColor: AppColors.historyTab,
            ),
            // Settings icon removed from navigation bar
          ],
        );
      },
    );
  }
}
