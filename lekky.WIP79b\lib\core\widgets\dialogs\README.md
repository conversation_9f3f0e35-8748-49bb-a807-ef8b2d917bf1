# Lekky Dialog System

This document explains how to use the Lekky dialog system, which provides a set of specialized dialog classes for different use cases.

## Overview

The Lekky dialog system consists of the following specialized dialog classes:

1. **ConfirmationDialog**: For confirming user actions, especially irreversible ones
2. **InputDialog**: For collecting specific data from users
3. **InformationDialog**: For displaying important information to users
4. **SelectionDialog**: For allowing users to choose from multiple options
5. **DatePickerDialog**: For allowing users to select dates
6. **NotificationDialog**: For alerting users about important events
7. **ProgressDialog**: For indicating ongoing operations

Each specialized dialog class provides a set of static methods for showing different types of dialogs.

## Usage

### Importing

You can import all dialog classes at once using the index file:

```dart
import 'package:lekky/core/widgets/dialogs/index.dart';
```

Or import specific dialog classes as needed:

```dart
import 'package:lekky/core/widgets/dialogs/confirmation_dialog.dart';
import 'package:lekky/core/widgets/dialogs/input_dialog.dart';
// etc.
```

### Examples

#### Confirmation Dialog

```dart
// Show a confirmation dialog for a destructive action
final bool? confirmed = await ConfirmationDialog.show(
  context: context,
  title: 'Delete Entry',
  message: 'Are you sure you want to delete this entry? This action cannot be undone.',
  confirmText: 'Delete',
  cancelText: 'Cancel',
  isDestructive: true,
  icon: Icons.delete,
);

// Handle the result
if (confirmed == true) {
  // User confirmed the action
  // Perform the action
} else {
  // User cancelled the action
}
```

#### Input Dialog

```dart
// Show an input dialog for collecting a text value
final String? value = await InputDialog.show(
  context: context,
  title: 'Enter Name',
  labelText: 'Name',
  hintText: 'Enter your name',
  initialValue: 'John Doe',
  validator: (value) {
    if (value == null || value.isEmpty) {
      return 'Please enter a name';
    }
    return null;
  },
);

// Show a numeric input dialog for collecting a numeric value
final double? amount = await InputDialog.showNumeric(
  context: context,
  title: 'Enter Amount',
  labelText: 'Amount',
  hintText: 'Enter the amount',
  initialValue: 10.0,
  prefixText: '£',
  validator: (value) {
    if (value == null || value.isEmpty) {
      return 'Please enter an amount';
    }
    final double? amount = double.tryParse(value);
    if (amount == null || amount <= 0) {
      return 'Please enter a valid amount';
    }
    return null;
  },
);
```

#### Information Dialog

```dart
// Show an information dialog
await InformationDialog.show(
  context: context,
  title: 'Information',
  message: 'This is an information message.',
  closeText: 'OK',
  icon: Icons.info,
);

// Show a success information dialog
await InformationDialog.showSuccess(
  context: context,
  title: 'Success',
  message: 'The operation was successful.',
);

// Show an error information dialog
await InformationDialog.showError(
  context: context,
  title: 'Error',
  message: 'An error occurred.',
);

// Show a warning information dialog
await InformationDialog.showWarning(
  context: context,
  title: 'Warning',
  message: 'This is a warning message.',
);
```

#### Selection Dialog

```dart
// Show a single selection dialog
final String? selectedOption = await SelectionDialog.showSingleSelection<String>(
  context: context,
  title: 'Select an Option',
  options: ['Option 1', 'Option 2', 'Option 3'],
  selectedOption: 'Option 1',
  labelBuilder: (option) => option,
  subtitleBuilder: (option) => 'Description for $option',
);

// Show a multiple selection dialog
final List<String>? selectedOptions = await SelectionDialog.showMultiSelection<String>(
  context: context,
  title: 'Select Options',
  options: ['Option 1', 'Option 2', 'Option 3'],
  selectedOptions: ['Option 1'],
  labelBuilder: (option) => option,
  subtitleBuilder: (option) => 'Description for $option',
);
```

#### Date Picker Dialog

```dart
// Show a date picker dialog
final DateTime? selectedDate = await DatePickerDialog.showDatePicker(
  context: context,
  title: 'Select Date',
  initialDate: DateTime.now(),
  firstDate: DateTime(2020),
  lastDate: DateTime(2030),
);

// Show a date range picker dialog
final DateTimeRange? selectedDateRange = await DatePickerDialog.showDateRangePicker(
  context: context,
  title: 'Select Date Range',
  initialDateRange: DateTimeRange(
    start: DateTime.now(),
    end: DateTime.now().add(const Duration(days: 7)),
  ),
  firstDate: DateTime(2020),
  lastDate: DateTime(2030),
);

// Show a custom date picker dialog with a preview
final DateTime? selectedDate = await DatePickerDialog.showCustomDatePicker(
  context: context,
  title: 'Select Date',
  initialDate: DateTime.now(),
  firstDate: DateTime(2020),
  lastDate: DateTime(2030),
  dateFormat: 'dd-MM-yyyy',
);
```

#### Notification Dialog

```dart
// Show a notification dialog
await NotificationDialog.show(
  context: context,
  title: 'Notification',
  message: 'This is a notification message.',
  priority: NotificationPriority.medium,
  actionText: 'Action',
  onAction: () {
    // Perform an action
  },
);

// Show an info notification dialog
await NotificationDialog.showInfo(
  context: context,
  title: 'Information',
  message: 'This is an information message.',
);

// Show a warning notification dialog
await NotificationDialog.showWarning(
  context: context,
  title: 'Warning',
  message: 'This is a warning message.',
);

// Show an error notification dialog
await NotificationDialog.showError(
  context: context,
  title: 'Error',
  message: 'This is an error message.',
);
```

#### Progress Dialog

```dart
// Show an indeterminate progress dialog
await ProgressDialog.show(
  context: context,
  title: 'Loading',
  message: 'Please wait...',
  cancelText: 'Cancel',
  onCancel: () {
    // Cancel the operation
  },
);

// Show a determinate progress dialog
await ProgressDialog.showDeterminate(
  context: context,
  title: 'Uploading',
  progress: 0.5, // 50%
  message: 'Uploading file...',
);

// Show a progress dialog with a stream
final progressController = StreamController<double>();
final messageController = StreamController<String>();

// Start the operation
ProgressDialog.showWithStream(
  context: context,
  title: 'Downloading',
  progressStream: progressController.stream,
  messageStream: messageController.stream,
  cancelText: 'Cancel',
  onCancel: () {
    // Cancel the operation
    progressController.close();
    messageController.close();
  },
  onComplete: () {
    // Operation completed
  },
);

// Update progress and message
progressController.add(0.2); // 20%
messageController.add('Downloading file...');

// Later
progressController.add(0.5); // 50%
messageController.add('Processing file...');

// When done
progressController.add(1.0); // 100%
messageController.add('Download complete');
progressController.close();
messageController.close();
```

## Best Practices

1. **Use the appropriate dialog type for each use case**:
   - Use ConfirmationDialog for confirming user actions
   - Use InputDialog for collecting data from users
   - Use InformationDialog for displaying information
   - Use SelectionDialog for allowing users to choose from options
   - Use DatePickerDialog for date selection
   - Use NotificationDialog for alerts and notifications
   - Use ProgressDialog for indicating ongoing operations

2. **Keep dialog content concise and focused**:
   - Use clear, descriptive titles
   - Keep messages short and to the point
   - Use icons to enhance understanding

3. **Follow platform conventions**:
   - Use platform-appropriate button order
   - Use appropriate colors for different actions

4. **Ensure accessibility**:
   - Use sufficient contrast
   - Make touch targets large enough
   - Support screen readers

5. **Handle dialog results appropriately**:
   - Check for null results (user dismissed the dialog)
   - Validate user input before proceeding

## Customization

Each dialog class provides parameters for customizing the appearance and behavior of the dialog. Common customization options include:

- **Title and message**: Set the title and message text
- **Button text**: Customize the text of the buttons
- **Colors**: Set custom colors for buttons and icons
- **Icons**: Add icons to enhance understanding
- **Validation**: Add validation for user input
- **Callbacks**: Provide callbacks for button actions
- **Dismissibility**: Control whether the dialog can be dismissed by tapping outside
