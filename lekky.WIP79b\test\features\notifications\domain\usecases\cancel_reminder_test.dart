// File: test/features/notifications/domain/usecases/cancel_reminder_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:lekky/features/notifications/domain/repositories/notification_repository.dart';
import 'package:lekky/features/notifications/domain/usecases/cancel_reminder.dart';

// Manual mock implementation
class MockNotificationRepository implements NotificationRepository {
  bool cancelMeterReadingReminderResult = true;
  int cancelMeterReadingReminderCallCount = 0;

  @override
  Future<bool> cancelMeterReadingReminder() async {
    cancelMeterReadingReminderCallCount++;
    return cancelMeterReadingReminderResult;
  }

  // Stub implementation of other required methods
  @override
  noSuchMethod(Invocation invocation) => super.noSuchMethod(invocation);
}

void main() {
  late CancelReminder useCase;
  late MockNotificationRepository mockRepository;

  setUp(() {
    mockRepository = MockNotificationRepository();
    useCase = CancelReminder(mockRepository);
  });

  group('CancelReminder', () {
    test('should cancel a meter reading reminder', () async {
      // Arrange
      mockRepository.cancelMeterReadingReminderResult = true;
      mockRepository.cancelMeterReadingReminderCallCount = 0;

      // Act
      final result = await useCase.execute();

      // Assert
      expect(result, true);
      expect(mockRepository.cancelMeterReadingReminderCallCount, 1);
    });

    test('should handle cancellation failure', () async {
      // Arrange
      mockRepository.cancelMeterReadingReminderResult = false;
      mockRepository.cancelMeterReadingReminderCallCount = 0;

      // Act
      final result = await useCase.execute();

      // Assert
      expect(result, false);
      expect(mockRepository.cancelMeterReadingReminderCallCount, 1);
    });
  });
}
