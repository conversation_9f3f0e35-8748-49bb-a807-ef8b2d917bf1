// File: lib/core/widgets/notification_button.dart
import 'package:flutter/material.dart';
import 'notification_dialog.dart';

/// A button that displays a notification icon with a badge for unread notifications
class NotificationButton extends StatelessWidget {
  const NotificationButton({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Simple IconButton without badge
    return IconButton(
      icon: const Icon(Icons.notifications_outlined),
      onPressed: () => NotificationDialog.show(context),
      color: Colors.white,
      tooltip: 'Notifications',
    );
  }
}
