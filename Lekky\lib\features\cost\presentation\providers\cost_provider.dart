import 'dart:async';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../../../core/providers/database_provider.dart';
import '../../../../core/providers/error_provider.dart';
import '../../../../core/utils/logger.dart';
import '../../../../core/utils/event_bus.dart';
import '../../../../core/di/service_locator.dart';
import '../../../averages/presentation/providers/averages_provider.dart';
import '../../domain/models/cost_state.dart';
import '../../domain/models/cost_period.dart';
import '../../domain/models/cost_mode.dart';
import '../../domain/models/cost_result.dart';
import '../../domain/models/date_range.dart';
import '../../data/cost_repository.dart';
import '../models/chart_data.dart';
import '../models/chart_data_provider.dart';
import '../../../../core/models/meter_entry.dart';

part 'cost_provider.g.dart';

/// Cost repository provider
@riverpod
CostRepository costRepository(CostRepositoryRef ref) {
  return serviceLocator<CostRepository>();
}

/// Cost analysis provider with comprehensive cost calculation and chart management
@riverpod
class Cost extends _$Cost {
  StreamSubscription<EventType>? _eventSubscription;

  @override
  Future<CostState> build() async {
    // Set up event listener for data updates
    _setupEventListener();

    // Dispose event subscription when provider is disposed
    ref.onDispose(() {
      _eventSubscription?.cancel();
    });

    return await _loadInitialState();
  }

  /// Set up event listener for data updates
  void _setupEventListener() {
    _eventSubscription?.cancel(); // Cancel any existing subscription
    _eventSubscription = EventBus().stream.listen((event) {
      Logger.info('CostProvider: Received event: $event');
      if (event == EventType.dataUpdated) {
        Logger.info(
            'CostProvider: Received data update event, refreshing cost analysis');
        // Refresh the cost analysis when data is updated
        refresh();
      } else if (event == EventType.averagesCalculating) {
        Logger.info(
            'CostProvider: Averages calculating, showing loading state');
        final currentState = state.value ?? CostState.initial();
        state = AsyncValue.data(currentState.copyWith(isLoading: true));
      } else if (event == EventType.averageCalculationFailed) {
        Logger.error('CostProvider: Average calculation failed');
        final currentState = state.value ?? CostState.initial();
        state = AsyncValue.data(currentState.copyWith(
          isLoading: false,
          errorMessage: 'Failed to calculate averages. Please try again.',
        ));
      }
    });
  }

  /// Load initial state
  Future<CostState> _loadInitialState() async {
    try {
      final initialState = CostState.initial();
      await _updatePeriod(initialState.selectedPeriod, initialState);
      return state.value ?? initialState;
    } catch (error, stackTrace) {
      ref.read(globalErrorProvider.notifier).handleError(
            error,
            stackTrace,
            context: 'Cost analysis initialization',
          );
      return CostState.initial().copyWith(
        isLoading: false,
        errorMessage: 'Failed to initialize cost analysis: ${error.toString()}',
      );
    }
  }

  /// Toggle cost mode between past and future
  Future<void> toggleCostMode() async {
    final currentState = state.value ?? CostState.initial();
    final newMode = currentState.costMode == CostMode.past
        ? CostMode.future
        : CostMode.past;

    // Update the selected period based on the new mode
    final periodName = currentState.selectedPeriod.name;
    final newPeriod =
        CostPeriod.getPeriodByNameAndMode(periodName, newMode == CostMode.past);

    state = AsyncValue.data(currentState.copyWith(
      costMode: newMode,
      selectedPeriod: newPeriod,
      isLoading: true,
    ));

    // Recalculate cost with new mode
    await _updatePeriod(newPeriod, state.value!);
  }

  /// Update selected period
  Future<void> updatePeriod(CostPeriod period) async {
    final currentState = state.value ?? CostState.initial();
    if (currentState.selectedPeriod == period) return;

    // Handle custom period selection with default dates
    if (period == CostPeriod.custom) {
      await _handleCustomPeriodSelection(currentState);
      return;
    }

    state = AsyncValue.data(currentState.copyWith(
      selectedPeriod: period,
      isLoading: true,
    ));

    await _updatePeriod(period, state.value!);
  }

  /// Set custom date range
  Future<void> setCustomDateRange(DateTime? fromDate, DateTime? toDate) async {
    final currentState = state.value ?? CostState.initial();

    // Validate date range
    String? dateRangeError;
    if (fromDate != null && toDate != null) {
      if (fromDate.isAfter(toDate)) {
        dateRangeError = 'From date must be before to date';
      } else if (toDate.isAfter(DateTime.now())) {
        dateRangeError = 'To date cannot be in the future';
      }
    }

    state = AsyncValue.data(currentState.copyWith(
      fromDate: fromDate,
      toDate: toDate,
      dateRangeError: dateRangeError,
      selectedPeriod: CostPeriod.custom,
      isLoading: dateRangeError == null,
    ));

    // Only calculate if date range is valid
    if (dateRangeError == null) {
      await _calculateCost(state.value!);
    }
  }

  /// Refresh cost analysis
  Future<void> refresh() async {
    final currentState = state.value ?? CostState.initial();
    state = AsyncValue.data(currentState.copyWith(isLoading: true));
    await _updatePeriod(currentState.selectedPeriod, state.value!);
  }

  /// Clear error
  void clearError() {
    final currentState = state.value ?? CostState.initial();
    state = AsyncValue.data(currentState.copyWith(errorMessage: null));
  }

  /// Handle custom period selection with default dates and insufficient data check
  Future<void> _handleCustomPeriodSelection(CostState currentState) async {
    try {
      final costRepo = ref.read(costRepositoryProvider);

      // Check if there are sufficient meter readings
      final hasSufficientData = await costRepo.hasSufficientMeterReadings();

      if (!hasSufficientData) {
        // Set insufficient data state
        state = AsyncValue.data(currentState.copyWith(
          selectedPeriod: CostPeriod.custom,
          hasInsufficientData: true,
          insufficientDataMessage: 'Not enough data',
          isLoading: false,
          fromDate: null,
          toDate: null,
        ));
        return;
      }

      // Calculate default dates
      final defaultFromDate = await _calculateDefaultFromDate();
      final defaultToDate = await _calculateDefaultToDate();

      // Set default dates and trigger calculation
      state = AsyncValue.data(currentState.copyWith(
        selectedPeriod: CostPeriod.custom,
        fromDate: defaultFromDate,
        toDate: defaultToDate,
        hasInsufficientData: false,
        insufficientDataMessage: null,
        isLoading: true,
        dateRangeError: null,
      ));

      // Auto-trigger cost calculation with default dates
      await _calculateCost(state.value!);
    } catch (error, stackTrace) {
      Logger.error(
          'Failed to handle custom period selection: $error', stackTrace);

      state = AsyncValue.data(currentState.copyWith(
        selectedPeriod: CostPeriod.custom,
        hasInsufficientData: true,
        insufficientDataMessage: 'Error loading data',
        isLoading: false,
      ));
    }
  }

  /// Calculate default from date (previous meter reading)
  Future<DateTime?> _calculateDefaultFromDate() async {
    try {
      final costRepo = ref.read(costRepositoryProvider);
      return await costRepo.getPreviousMeterReadingDate();
    } catch (e) {
      Logger.error('Failed to calculate default from date: $e');
      return null;
    }
  }

  /// Calculate default to date (last meter reading)
  Future<DateTime?> _calculateDefaultToDate() async {
    try {
      final costRepo = ref.read(costRepositoryProvider);
      return await costRepo.getLastMeterReadingDate();
    } catch (e) {
      Logger.error('Failed to calculate default to date: $e');
      return null;
    }
  }

  /// Update period and recalculate cost
  Future<void> _updatePeriod(CostPeriod period, CostState currentState) async {
    try {
      if (period != CostPeriod.custom) {
        // Update date range based on the selected period
        final dateRange = DateRange.forPeriod(period);
        final fromDate = dateRange.startDate;
        final toDate = dateRange.endDate;

        // Update state with new date range
        state = AsyncValue.data(currentState.copyWith(
          dateRange: dateRange,
          fromDate: fromDate,
          toDate: toDate,
          dateRangeError: null,
        ));

        // Calculate projected cost based on the period
        await _calculateProjectedCost(period, state.value!);
      } else {
        // For custom period, use existing date range
        await _calculateCost(state.value!);
      }
    } catch (error, stackTrace) {
      Logger.error('Failed to update period: $error', stackTrace);

      state = AsyncValue.data(currentState.copyWith(
        isLoading: false,
        errorMessage: 'Failed to calculate cost: ${error.toString()}',
      ));

      ref.read(globalErrorProvider.notifier).handleError(
            error,
            stackTrace,
            context: 'Cost period update',
          );
    }
  }

  /// Calculate cost for custom date range
  Future<void> _calculateCost(CostState currentState) async {
    try {
      final costRepo = ref.read(costRepositoryProvider);

      final costResult = await costRepo.calculateCostForPeriod(
        currentState.selectedPeriod,
        currentState.fromDate,
        currentState.toDate,
      );

      // Generate chart data
      final chartData = await _generateChartData(currentState);

      // Load recent average chart data
      final recentAverageChartData =
          await _loadRecentAverageChartData(currentState);

      // Determine average type (Recent Average for past/custom periods)
      const averageType = "Recent Average";

      state = AsyncValue.data(currentState.copyWith(
        costResult: costResult,
        chartData: chartData,
        recentAverageChartData: recentAverageChartData,
        averageType: averageType,
        isLoading: false,
        errorMessage: null,
      ));
    } catch (error, stackTrace) {
      Logger.error('Failed to calculate cost: $error', stackTrace);

      state = AsyncValue.data(currentState.copyWith(
        isLoading: false,
        errorMessage: 'Failed to calculate cost: ${error.toString()}',
      ));

      ref.read(globalErrorProvider.notifier).handleError(
            error,
            stackTrace,
            context: 'Cost calculation',
          );
    }
  }

  /// Calculate projected cost based on total average usage
  Future<void> _calculateProjectedCost(
      CostPeriod period, CostState currentState) async {
    try {
      final costRepo = ref.read(costRepositoryProvider);

      // Get the total average usage
      final totalAverage = await costRepo.getTotalAverageUsage();

      // Get the meter unit
      final meterUnit = await costRepo.getMeterUnit();

      // Calculate cost
      final costPerPeriod = totalAverage * period.days;

      // Create cost result
      final costResult = CostResult(
        averageUsage: totalAverage,
        costPerPeriod: costPerPeriod,
        period: period,
        meterUnit: meterUnit,
        fromDate: currentState.fromDate,
        toDate: currentState.toDate,
      );

      // Generate chart data
      final chartData = await _generateChartData(currentState);

      // Load recent average chart data
      final recentAverageChartData =
          await _loadRecentAverageChartData(currentState);

      // Determine average type (Total Average for future periods)
      const averageType = "Total Average";

      state = AsyncValue.data(currentState.copyWith(
        costResult: costResult,
        chartData: chartData,
        recentAverageChartData: recentAverageChartData,
        averageType: averageType,
        isLoading: false,
        errorMessage: null,
      ));
    } catch (error, stackTrace) {
      Logger.error('Failed to calculate projected cost: $error', stackTrace);

      state = AsyncValue.data(currentState.copyWith(
        isLoading: false,
        errorMessage: 'Failed to calculate projected cost: ${error.toString()}',
      ));

      ref.read(globalErrorProvider.notifier).handleError(
            error,
            stackTrace,
            context: 'Projected cost calculation',
          );
    }
  }

  /// Generate chart data for visualization
  Future<List<ChartData>> _generateChartData(CostState currentState) async {
    try {
      // Get actual meter entries from repositories
      final meterReadingRepo = ref.read(meterReadingRepositoryProvider);
      final topUpRepo = ref.read(topUpRepositoryProvider);

      // Get all historical data
      final meterReadings = await meterReadingRepo.getAllMeterReadings();
      final topUps = await topUpRepo.getAllTopUps();

      // Convert to MeterEntry format for chart generation
      final List<MeterEntry> entries = [];

      // Add meter readings
      for (final reading in meterReadings) {
        entries.add(MeterEntry(
          id: reading.id,
          date: reading.date,
          reading: reading.value,
          amountToppedUp: 0.0,
          typeCode: 0,
          notes: reading.notes,
        ));
      }

      // Add top-ups
      for (final topUp in topUps) {
        entries.add(MeterEntry(
          id: topUp.id,
          date: topUp.date,
          reading: 0.0,
          amountToppedUp: topUp.amount,
          typeCode: 1,
          notes: topUp.notes,
        ));
      }

      if (entries.isEmpty) {
        return [];
      }

      // Sort entries by date
      entries.sort((a, b) => a.date.compareTo(b.date));
      final earliestDate = entries.first.date;
      final latestDate = entries.last.date;

      // Generate chart data using ChartDataProvider
      return ChartDataProvider.generateChartData(
        entries: entries,
        period: currentState.selectedPeriod,
        fromDate: earliestDate,
        toDate: latestDate,
      );
    } catch (error) {
      Logger.error('Failed to generate chart data: $error');
      return [];
    }
  }

  /// Load recent average chart data from repository
  Future<List<ChartData>> _loadRecentAverageChartData(
      CostState currentState) async {
    try {
      final costRepo = ref.read(costRepositoryProvider);

      // Check if custom period with valid date range
      if (currentState.selectedPeriod == CostPeriod.custom &&
          currentState.fromDate != null &&
          currentState.toDate != null) {
        // Use date-filtered chart data for custom periods
        return await costRepo.getRecentAverageChartData(
            currentState.fromDate, currentState.toDate);
      } else {
        // Use all averages (last 100) for standard periods
        return await costRepo.getRecentAverageChartDataForAllAverages();
      }
    } catch (error) {
      Logger.error('Failed to load recent average chart data: $error');
      return [];
    }
  }
}
