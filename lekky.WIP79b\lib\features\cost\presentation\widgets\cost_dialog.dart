// File: lib/features/cost/presentation/widgets/cost_dialog.dart
import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/widgets/dialogs/date_picker_dialog.dart'
    as custom_date_picker;
import '../../domain/models/cost_period.dart';
import '../controllers/cost_controller.dart';

/// A dialog for selecting cost period and date range
class CostDialog extends StatefulWidget {
  final CostController controller;

  const CostDialog({
    Key? key,
    required this.controller,
  }) : super(key: key);

  /// Show the dialog
  static Future<void> show({
    required BuildContext context,
    required CostController controller,
  }) async {
    // Simple dialog with fixed size
    await showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: SizedBox(
            width: 350,
            height: 420, // Fixed height that fits content without extra space
            child: CostDialog(
              controller: controller,
            ),
          ),
        );
      },
    );
  }

  @override
  State<CostDialog> createState() => _CostDialogState();
}

class _CostDialogState extends State<CostDialog> {
  final FocusNode _fromDateFocusNode = FocusNode();
  final FocusNode _toDateFocusNode = FocusNode();

  DateTime? _fromDate;
  DateTime? _toDate;
  bool _isDateRangeActive = false;

  @override
  void initState() {
    super.initState();
    // Set default dates
    _toDate = widget.controller.toDate ?? DateTime.now();
    _fromDate = widget.controller.fromDate ?? widget.controller.earliestDate;

    // Add focus listeners to ensure fields are visible when they receive focus
    _fromDateFocusNode.addListener(_onFromDateFocusChange);
    _toDateFocusNode.addListener(_onToDateFocusChange);
  }

  // Handle from date field focus changes
  void _onFromDateFocusChange() {
    if (_fromDateFocusNode.hasFocus) {
      // Wait for keyboard to appear before scrolling
      Future.delayed(const Duration(milliseconds: 200), () {
        if (mounted && _fromDateFocusNode.hasFocus) {
          // Ensure the field is visible
          Scrollable.ensureVisible(
            _fromDateFocusNode.context!,
            alignment: 0.2, // Align closer to the top
            duration: const Duration(milliseconds: 300),
          );
        }
      });
    }
  }

  // Handle to date field focus changes
  void _onToDateFocusChange() {
    if (_toDateFocusNode.hasFocus) {
      // Wait for keyboard to appear before scrolling
      Future.delayed(const Duration(milliseconds: 200), () {
        if (mounted && _toDateFocusNode.hasFocus) {
          // Ensure the field is visible
          Scrollable.ensureVisible(
            _toDateFocusNode.context!,
            alignment: 0.2, // Align closer to the top
            duration: const Duration(milliseconds: 300),
          );
        }
      });
    }
  }

  @override
  void dispose() {
    // Remove listeners before disposing focus nodes
    _fromDateFocusNode.removeListener(_onFromDateFocusChange);
    _toDateFocusNode.removeListener(_onToDateFocusChange);

    // Dispose focus nodes
    _fromDateFocusNode.dispose();
    _toDateFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // Header
        Padding(
          padding: const EdgeInsets.fromLTRB(24, 16, 24, 8),
          child: Row(
            children: [
              Icon(
                Icons.attach_money,
                color: Theme.of(context).brightness == Brightness.dark
                    ? AppColors.primaryDark
                    : AppColors.primary,
              ),
              const SizedBox(width: 10),
              Text(
                'Cost Settings',
                style: AppTextStyles.titleMedium.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).brightness == Brightness.dark
                      ? AppColors.primaryDark
                      : AppColors.primary,
                ),
              ),
            ],
          ),
        ),

        // Content - Make it scrollable with auto-scroll when keyboard appears
        Flexible(
          child: SingleChildScrollView(
            // Add controller to auto-scroll when keyboard appears
            controller: ScrollController(),
            physics: const AlwaysScrollableScrollPhysics(),
            child: Padding(
              padding: const EdgeInsets.fromLTRB(24, 16, 24, 16),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildPeriodSelector(),
                  const SizedBox(height: 16),
                  _buildDateRangeSelector(),
                  // Add extra space at the bottom to ensure content is visible above keyboard
                  const SizedBox(height: 16),
                ],
              ),
            ),
          ),
        ),

        // Actions
        Padding(
          padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              // Close button
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                style: TextButton.styleFrom(
                  foregroundColor: AppColors.primary,
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                ),
                child: const Text('Close'),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildPeriodSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Select Period',
          style: AppTextStyles.labelLarge,
        ),
        const SizedBox(height: 8),
        // Period buttons in a horizontal row with equal sizing
        Row(
          children: CostPeriod.all.map((period) {
            final isSelected = widget.controller.selectedPeriod == period;
            return Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 4.0),
                child: SizedBox(
                  height: 44,
                  child: isSelected
                      ? ElevatedButton(
                          onPressed: () {
                            setState(() {
                              _isDateRangeActive = false;
                            });
                            widget.controller.updatePeriod(period);
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.primary,
                            foregroundColor: Colors.white,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(10),
                            ),
                            padding: EdgeInsets.zero,
                          ),
                          child: Text(
                            period.name,
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 14,
                            ),
                          ),
                        )
                      : OutlinedButton(
                          onPressed: () {
                            setState(() {
                              _isDateRangeActive = false;
                            });
                            widget.controller.updatePeriod(period);
                          },
                          style: OutlinedButton.styleFrom(
                            foregroundColor: AppColors.onSurface,
                            side: BorderSide(
                              color: AppColors.outline.withOpacity(0.3),
                              width: 1,
                            ),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(10),
                            ),
                            padding: EdgeInsets.zero,
                          ),
                          child: Text(
                            period.name,
                            style: const TextStyle(
                              fontWeight: FontWeight.normal,
                              fontSize: 14,
                            ),
                          ),
                        ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildDateRangeSelector() {
    // Check if there are enough readings for custom date range
    if (!widget.controller.hasEnoughReadingsForCustomRange) {
      return Padding(
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 8),
        child: Center(
          child: Column(
            children: [
              const Icon(
                Icons.info_outline,
                color: AppColors.tertiary,
                size: 24,
              ),
              const SizedBox(height: 8),
              Text(
                'Custom date range requires at least 2 meter readings',
                textAlign: TextAlign.center,
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.tertiary,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Custom Date Range',
          style: AppTextStyles.labelLarge,
        ),
        const SizedBox(height: 8),
        // Date range selectors side by side
        Row(
          children: [
            // From date
            Expanded(
              child: Card(
                elevation: 0,
                margin: EdgeInsets.zero,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                  side: BorderSide(
                    color: _isDateRangeActive
                        ? AppColors.primary.withOpacity(0.7)
                        : AppColors.tertiary.withOpacity(0.3),
                    width: _isDateRangeActive ? 2 : 1,
                  ),
                ),
                child: _buildDateInput(
                  context,
                  'From:',
                  _fromDate,
                  widget.controller.earliestDate,
                  widget.controller.latestDate,
                  _fromDateFocusNode,
                  (date) {
                    setState(() {
                      _fromDate = date;
                      _isDateRangeActive = true;
                    });
                    // This will automatically set the period to custom
                    widget.controller.fromDate = date;
                  },
                  isDisabledMessage: 'No meter history available',
                  isActive: _isDateRangeActive,
                ),
              ),
            ),
            const SizedBox(width: 14),
            // To date
            Expanded(
              child: Card(
                elevation: 0,
                margin: EdgeInsets.zero,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                  side: BorderSide(
                    color: _isDateRangeActive
                        ? AppColors.primary.withOpacity(0.7)
                        : AppColors.tertiary.withOpacity(0.3),
                    width: _isDateRangeActive ? 2 : 1,
                  ),
                ),
                child: _buildDateInput(
                  context,
                  'To:',
                  _toDate,
                  widget.controller.earliestDate,
                  widget.controller.latestDate,
                  _toDateFocusNode,
                  (date) {
                    setState(() {
                      _toDate = date;
                      _isDateRangeActive = true;
                    });
                    // This will automatically set the period to custom
                    widget.controller.toDate = date;
                  },
                  isDisabledMessage: 'No meter history available',
                  isActive: _isDateRangeActive,
                ),
              ),
            ),
          ],
        ),
        if (widget.controller.dateRangeError != null)
          Padding(
            padding: const EdgeInsets.only(top: 12, left: 4, right: 4),
            child: Card(
              elevation: 0,
              color: AppColors.error.withOpacity(0.1),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
                side: BorderSide(
                  color: AppColors.error.withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                child: Row(
                  children: [
                    const Stack(
                      alignment: Alignment.center,
                      children: [
                        Icon(
                          Icons.calendar_today,
                          color: AppColors.error,
                          size: 18,
                        ),
                        Icon(
                          Icons.close,
                          color: AppColors.error,
                          size: 14,
                        ),
                      ],
                    ),
                    const SizedBox(width: 10),
                    Expanded(
                      child: Text(
                        widget.controller.dateRangeError!,
                        style: AppTextStyles.bodyMedium.copyWith(
                          color: AppColors.error,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildDateInput(
      BuildContext context,
      String label,
      DateTime? selectedDate,
      DateTime? firstDate,
      DateTime? lastDate,
      FocusNode focusNode,
      Function(DateTime?) onDateSelected,
      {String isDisabledMessage = 'Disabled',
      bool isActive = false}) {
    // Determine if the date should be disabled
    final bool isDisabled = firstDate == null || lastDate == null;

    return Focus(
      focusNode: focusNode,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 2, vertical: 4),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            // Label with icon
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.calendar_today,
                  size: 14,
                  color: isDisabled
                      ? AppColors.outline.withOpacity(0.5)
                      : AppColors.tertiary,
                ),
                const SizedBox(width: 4),
                Text(
                  label,
                  style: AppTextStyles.labelMedium.copyWith(
                    color: isDisabled ? AppColors.outline : AppColors.tertiary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 4),
            // Date display with tap to select
            InkWell(
              onTap: isDisabled
                  ? null
                  : () async {
                      // Check the Date Info setting
                      final dateInfo = await widget.controller.getDateInfo();
                      final includeTime = dateInfo == 'Date and time';

                      // Check if the widget is still mounted before proceeding
                      if (!mounted) return;

                      // Store the current context in a local variable
                      final currentContext = context;

                      // Use date time picker with proper time inclusion based on settings
                      final DateTime? picked =
                          await Future<DateTime?>.sync(() async {
                        if (!mounted) return null;
                        return await custom_date_picker.DatePickerDialog
                            .showDateTimePicker(
                          context: currentContext,
                          title: 'Select Date and Time',
                          initialDate: selectedDate ?? DateTime.now(),
                          firstDate: firstDate,
                          lastDate: lastDate,
                          helpText:
                              'Select the date${includeTime ? ' and time' : ''}',
                          includeTime:
                              includeTime, // This will show time picker only if includeTime is true
                        );
                      });
                      if (picked != null) {
                        onDateSelected(picked);
                      }
                    },
              child: Container(
                padding:
                    const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                decoration: BoxDecoration(
                  color: isDisabled
                      ? AppColors.surfaceVariant.withOpacity(0.1)
                      : isActive
                          ? AppColors.primary.withOpacity(0.1)
                          : AppColors.surfaceVariant.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Center(
                  child: Text(
                    isDisabled
                        ? isDisabledMessage
                        : selectedDate != null
                            ? '${selectedDate.day}/${selectedDate.month}/${selectedDate.year}'
                            : 'Select date',
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: isDisabled
                          ? AppColors.outline.withOpacity(0.5)
                          : isActive
                              ? AppColors.primary
                              : AppColors.onSurface,
                      fontWeight:
                          isActive ? FontWeight.bold : FontWeight.normal,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
