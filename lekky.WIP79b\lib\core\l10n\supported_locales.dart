// File: lib/core/l10n/supported_locales.dart
import 'package:flutter/material.dart';

/// Class that defines the supported locales for the app
class SupportedLocales {
  // Private constructor to prevent instantiation
  SupportedLocales._();

  /// List of supported locales
  static const List<Locale> locales = [
    Locale('en'), // English
    Locale('es'), // Spanish
    Locale('fr'), // French
    Locale('de'), // German
    Locale('it'), // Italian
    Locale('pt'), // Portuguese
    Locale('ru'), // Russian
    Locale('zh'), // Chinese
    Locale('ja'), // Japanese
    // Add more locales as needed
  ];

  /// Map of language codes to their display names
  static const Map<String, String> languageNames = {
    'en': 'English',
    'es': 'Español',
    'fr': 'Français',
    'de': 'Deutsch',
    'it': 'Italiano',
    'pt': 'Português',
    'ru': 'Русский',
    'zh': '中文',
    'ja': '日本語',
    // Add more language names as needed
  };

  /// Get the display name for a language code
  static String getDisplayName(String languageCode) {
    return languageNames[languageCode] ?? languageCode;
  }

  /// Check if a locale is supported
  static bool isSupported(Locale locale) {
    return locales.contains(locale) || 
           locales.any((supportedLocale) => 
             supportedLocale.languageCode == locale.languageCode);
  }

  /// Get the RTL languages
  static const List<String> rtlLanguages = [
    'ar', // Arabic
    'fa', // Persian
    'he', // Hebrew
    'ur', // Urdu
    // Add more RTL languages as needed
  ];

  /// Check if a language is RTL
  static bool isRtl(String languageCode) {
    return rtlLanguages.contains(languageCode);
  }
}
