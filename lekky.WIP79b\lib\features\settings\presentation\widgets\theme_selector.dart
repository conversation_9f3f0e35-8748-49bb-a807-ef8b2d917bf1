// File: lib/features/settings/presentation/widgets/theme_selector.dart
import 'package:flutter/material.dart';

/// A widget for selecting the theme mode
class ThemeSelector extends StatelessWidget {
  final String selectedTheme;
  final Function(String) onThemeChanged;

  const ThemeSelector({
    super.key,
    required this.selectedTheme,
    required this.onThemeChanged,
  });

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final primaryColor = Theme.of(context).colorScheme.primary;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ListTile(
          title: const Text(
            'Appearance',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
          ),
          subtitle: const Text('Choose how <PERSON><PERSON><PERSON> looks to you'),
          leading: Icon(
            Icons.palette_outlined,
            color: primaryColor,
          ),
        ),
        const SizedBox(height: 8),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildThemeOption(
                context: context,
                label: 'Light',
                icon: Icons.light_mode,
                value: 'light',
                isSelected: selectedTheme == 'light',
                backgroundColor: isDarkMode
                    ? const Color(0xFF2C2C2C)
                    : const Color(0xFFF5F5F5),
                iconColor: const Color(0xFFFFA000), // Amber for light mode
              ),
              _buildThemeOption(
                context: context,
                label: 'Dark',
                icon: Icons.dark_mode,
                value: 'dark',
                isSelected: selectedTheme == 'dark',
                backgroundColor: isDarkMode
                    ? const Color(0xFF2C2C2C)
                    : const Color(0xFFF5F5F5),
                iconColor: const Color(0xFF90CAF9), // Blue for dark mode
              ),
              _buildThemeOption(
                context: context,
                label: 'System',
                icon: Icons.brightness_auto,
                value: 'system',
                isSelected: selectedTheme == 'system',
                backgroundColor: isDarkMode
                    ? const Color(0xFF2C2C2C)
                    : const Color(0xFFF5F5F5),
                iconColor: primaryColor,
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
      ],
    );
  }

  Widget _buildThemeOption({
    required BuildContext context,
    required String label,
    required IconData icon,
    required String value,
    required bool isSelected,
    required Color backgroundColor,
    required Color iconColor,
  }) {
    final textTheme = Theme.of(context).textTheme;
    final selectedColor = Theme.of(context).colorScheme.primary;

    return GestureDetector(
      onTap: () => onThemeChanged(value),
      child: Container(
        width: 90,
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? selectedColor : Colors.transparent,
            width: 2,
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              color: isSelected ? selectedColor : iconColor,
              size: 32,
            ),
            const SizedBox(height: 8),
            Text(
              label,
              style: textTheme.bodyMedium?.copyWith(
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                color: isSelected ? selectedColor : null,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
