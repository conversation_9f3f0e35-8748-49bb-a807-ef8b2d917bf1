// File: lib/features/cost/presentation/widgets/period_selector.dart
import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/widgets/gradient_button.dart';
import '../../domain/models/cost_period.dart';

/// A widget for selecting a time period for cost calculation
class PeriodSelector extends StatelessWidget {
  /// The currently selected period
  final CostPeriod selectedPeriod;

  /// The list of available periods
  final List<CostPeriod> periods;

  /// Callback when a period is selected
  final ValueChanged<CostPeriod> onPeriodSelected;

  /// Whether to show the custom period option
  final bool showCustom;

  /// Callback when the custom period is selected
  final VoidCallback? onCustomSelected;

  const PeriodSelector({
    Key? key,
    required this.selectedPeriod,
    required this.periods,
    required this.onPeriodSelected,
    this.showCustom = true,
    this.onCustomSelected,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Row of period buttons with more compact layout
        Row(
          children: periods.map((period) {
            return Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 2.0),
                child: _buildPeriodButton(period),
              ),
            );
          }).toList(),
        ),
        if (showCustom) ...[
          const SizedBox(height: 4), // Reduced spacing
          _buildCustomButton(),
        ],
      ],
    );
  }

  Widget _buildPeriodButton(CostPeriod period) {
    final isSelected = selectedPeriod == period;

    return SizedBox(
      height: 36, // Reduced height
      child: isSelected
          ? GradientButton(
              text: period.name,
              onPressed: () => onPeriodSelected(period),
              gradientColors: AppColors.primaryGradient,
              elevation: 1, // Reduced elevation
              borderRadius: BorderRadius.circular(6), // Smaller radius
              padding: EdgeInsets.zero,
              textStyle: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 12, // Smaller font size
              ),
            )
          : OutlinedButton(
              onPressed: () => onPeriodSelected(period),
              style: OutlinedButton.styleFrom(
                side: const BorderSide(
                    color: AppColors.primary, width: 1), // Thinner border
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(6), // Smaller radius
                ),
                padding: EdgeInsets.zero,
              ),
              child: Text(
                period.name,
                style: const TextStyle(
                  color: AppColors.primary,
                  fontSize: 12, // Smaller font size
                ),
              ),
            ),
    );
  }

  Widget _buildCustomButton() {
    final isSelected = selectedPeriod == CostPeriod.custom;

    return SizedBox(
      height: 36, // Reduced height
      child: isSelected
          ? GradientButton(
              text: 'Custom',
              onPressed:
                  onCustomSelected ?? () => onPeriodSelected(CostPeriod.custom),
              gradientColors: AppColors.primaryGradient,
              elevation: 1, // Reduced elevation
              borderRadius: BorderRadius.circular(6), // Smaller radius
              padding: EdgeInsets.zero,
              textStyle: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 12, // Smaller font size
              ),
            )
          : OutlinedButton(
              onPressed:
                  onCustomSelected ?? () => onPeriodSelected(CostPeriod.custom),
              style: OutlinedButton.styleFrom(
                side: const BorderSide(
                    color: AppColors.primary, width: 1), // Thinner border
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(6), // Smaller radius
                ),
                padding: EdgeInsets.zero,
              ),
              child: const Text(
                'Custom',
                style: TextStyle(
                  color: AppColors.primary,
                  fontSize: 12, // Smaller font size
                ),
              ),
            ),
    );
  }
}
