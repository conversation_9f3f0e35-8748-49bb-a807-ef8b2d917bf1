{"@@locale": "en", "appTitle": "<PERSON><PERSON><PERSON>", "@appTitle": {"description": "The title of the application"}, "welcomeTitle": "Welcome to Lekky", "@welcomeTitle": {"description": "Title shown on the welcome screen"}, "getStarted": "Get Started", "@getStarted": {"description": "Button text to start using the app"}, "restorePreviousData": "Restore Previous Data", "@restorePreviousData": {"description": "Button text to restore previous data"}, "trackYourUsage": "Track Your Usage", "@trackYourUsage": {"description": "Feature title on welcome screen"}, "trackYourUsageDesc": "Monitor your electricity consumption and spending", "@trackYourUsageDesc": {"description": "Feature description on welcome screen"}, "getTimelyAlerts": "Get <PERSON><PERSON>s", "@getTimelyAlerts": {"description": "Feature title on welcome screen"}, "getTimelyAlertsDesc": "Receive notifications when your balance is running low", "@getTimelyAlertsDesc": {"description": "Feature description on welcome screen"}, "viewHistory": "View History", "@viewHistory": {"description": "Feature title on welcome screen"}, "viewHistoryDesc": "See your past meter readings and top-ups", "@viewHistoryDesc": {"description": "Feature description on welcome screen"}, "calculateCosts": "Calculate Costs", "@calculateCosts": {"description": "Feature title on welcome screen"}, "calculateCostsDesc": "Estimate your electricity costs over different periods", "@calculateCostsDesc": {"description": "Feature description on welcome screen"}, "setupTitle": "Setup Your Meter", "@setupTitle": {"description": "Title shown on the setup screen"}, "setupDescription": "Configure your regional preferences", "@setupDescription": {"description": "Description shown on the setup screen"}, "selectLanguage": "Select your preferred language", "@selectLanguage": {"description": "Label for language selection"}, "currency": "<PERSON><PERSON><PERSON><PERSON>", "@currency": {"description": "Label for currency selection"}, "selectCurrency": "Select the currency", "@selectCurrency": {"description": "Label for currency selection"}, "dateFormat": "Date Format", "@dateFormat": {"description": "Label for date format selection"}, "selectDateFormat": "Select your preferred date format", "@selectDateFormat": {"description": "Label for date format selection"}, "dateInfo": "Date Information", "@dateInfo": {"description": "Label for date information selection"}, "selectDateInfo": "Select how dates should be displayed", "@selectDateInfo": {"description": "Label for date information selection"}, "alertThreshold": "<PERSON><PERSON>", "@alertThreshold": {"description": "Label for alert threshold setting"}, "alertThresholdDesc": "Get notified when your balance falls below this amount", "@alertThresholdDesc": {"description": "Description for alert threshold setting"}, "daysInAdvance": "Days in Advance", "@daysInAdvance": {"description": "Label for days in advance setting"}, "daysInAdvanceDesc": "Get notified this many days before you're likely to run out", "@daysInAdvanceDesc": {"description": "Description for days in advance setting"}, "enableNotifications": "Enable Notifications", "@enableNotifications": {"description": "Label for notifications toggle"}, "enableNotificationsDesc": "Get alerts about your meter balance", "@enableNotificationsDesc": {"description": "Description for notifications toggle"}, "initialMeterCredit": "Initial Meter Credit", "@initialMeterCredit": {"description": "Label for initial meter credit input"}, "initialMeterCreditDesc": "Enter your current meter balance", "@initialMeterCreditDesc": {"description": "Description for initial meter credit input"}, "next": "Next", "@next": {"description": "<PERSON><PERSON> text to proceed to the next step"}, "back": "Back", "@back": {"description": "<PERSON><PERSON> text to go back to the previous step"}, "finish": "Finish", "@finish": {"description": "Button text to complete setup"}, "cancel": "Cancel", "@cancel": {"description": "Button text to cancel an action"}, "save": "Save", "@save": {"description": "Button text to save changes"}, "delete": "Delete", "@delete": {"description": "Button text to delete something"}, "edit": "Edit", "@edit": {"description": "Button text to edit something"}, "editNow": "Edit Now", "@editNow": {"description": "Button text to edit something immediately"}, "home": "Home", "@home": {"description": "Label for home tab"}, "cost": "Cost", "@cost": {"description": "Label for cost tab"}, "history": "History", "@history": {"description": "Label for history tab"}, "settings": "Settings", "@settings": {"description": "Label for settings tab"}, "addEntry": "Add Entry", "@addEntry": {"description": "Button text to add a new entry"}, "editEntry": "Edit Entry", "@editEntry": {"description": "Title for edit entry dialog"}, "meterReading": "Meter Reading", "@meterReading": {"description": "Label for meter reading input"}, "topUp": "Top Up", "@topUp": {"description": "Label for top up input"}, "amount": "Amount", "@amount": {"description": "Label for amount input"}, "date": "Date", "@date": {"description": "Label for date input"}, "time": "Time", "@time": {"description": "Label for time input"}, "notes": "Notes", "@notes": {"description": "Label for notes input"}, "optional": "Optional", "@optional": {"description": "Label for optional fields"}, "currentBalance": "Current Balance", "@currentBalance": {"description": "Label for current balance"}, "recentAverage": "Recent Average", "@recentAverage": {"description": "Label for recent average usage"}, "totalAverage": "Total Average", "@totalAverage": {"description": "Label for total average usage"}, "perDay": "per day", "@perDay": {"description": "Label for per day usage"}, "estimatedDaysRemaining": "Estimated Days Remaining", "@estimatedDaysRemaining": {"description": "Label for estimated days remaining"}, "costOfElectricity": "Cost of Electricity", "@costOfElectricity": {"description": "Title for cost of electricity dialog"}, "dailyUsage": "Daily Usage", "@dailyUsage": {"description": "Label for daily usage"}, "weeklyUsage": "Weekly Usage", "@weeklyUsage": {"description": "Label for weekly usage"}, "monthlyUsage": "Monthly Usage", "@monthlyUsage": {"description": "Label for monthly usage"}, "yearlyUsage": "Yearly Usage", "@yearlyUsage": {"description": "Label for yearly usage"}, "noData": "No Data", "@noData": {"description": "Text shown when there is no data to display"}, "noDataDesc": "Add meter readings to see your usage", "@noDataDesc": {"description": "Description shown when there is no data to display"}, "allEntries": "All Entries", "@allEntries": {"description": "Label for all entries filter"}, "readings": "Readings", "@readings": {"description": "Label for readings filter"}, "topUps": "Top Ups", "@topUps": {"description": "Label for top ups filter"}, "invalidEntries": "Invalid Entries", "@invalidEntries": {"description": "Label for invalid entries filter"}, "region": "Region", "@region": {"description": "Label for region settings section"}, "alertsAndNotifications": "Alerts & Notifications", "@alertsAndNotifications": {"description": "Label for alerts and notifications settings section"}, "dateSettings": "Date Settings", "@dateSettings": {"description": "Label for date settings section"}, "appearance": "Appearance", "@appearance": {"description": "Label for appearance settings section"}, "dataManagement": "Data Management", "@dataManagement": {"description": "Label for data management settings section"}, "about": "About", "@about": {"description": "Label for about settings section"}, "donate": "Donate", "@donate": {"description": "Label for donate settings section"}, "language": "Language", "@language": {"description": "Label for language setting"}, "current": "Current", "@current": {"description": "Label for current selection"}, "theme": "Theme", "@theme": {"description": "Label for theme setting"}, "systemDefault": "System Default", "@systemDefault": {"description": "Label for system default theme"}, "lightMode": "Light Mode", "@lightMode": {"description": "Label for light mode theme"}, "darkMode": "Dark Mode", "@darkMode": {"description": "Label for dark mode theme"}, "backupData": "Backup Data", "@backupData": {"description": "Label for backup data option"}, "restoreData": "Restore Data", "@restoreData": {"description": "Label for restore data option"}, "exportData": "Export Data", "@exportData": {"description": "Label for export data option"}, "importData": "Import Data", "@importData": {"description": "Label for import data option"}, "clearAllData": "Clear All Data", "@clearAllData": {"description": "Label for clear all data option"}, "version": "Version", "@version": {"description": "Label for app version"}, "privacyPolicy": "Privacy Policy", "@privacyPolicy": {"description": "Label for privacy policy link"}, "termsOfService": "Terms of Service", "@termsOfService": {"description": "Label for terms of service link"}, "contactUs": "Contact Us", "@contactUs": {"description": "Label for contact us option"}, "rateApp": "Rate App", "@rateApp": {"description": "Label for rate app option"}, "shareApp": "Share App", "@shareApp": {"description": "Label for share app option"}, "donateToSupport": "Donate to Support Development", "@donateToSupport": {"description": "Label for donate to support development"}, "donateDesc": "Help us improve Lekky with your contribution", "@donateDesc": {"description": "Description for donate option"}, "selectAmount": "Select Amount", "@selectAmount": {"description": "Label for select donation amount"}, "customAmount": "Custom Amount", "@customAmount": {"description": "Label for custom donation amount"}, "proceedToPayment": "Proceed to Payment", "@proceedToPayment": {"description": "Button text to proceed to payment"}, "thankYou": "Thank You!", "@thankYou": {"description": "Thank you message after donation"}, "thankYouDesc": "Your support helps us continue developing Lekky", "@thankYouDesc": {"description": "Description for thank you message after donation"}, "errorOccurred": "An error occurred", "@errorOccurred": {"description": "Generic error message"}, "tryAgain": "Try Again", "@tryAgain": {"description": "<PERSON><PERSON> text to try an action again"}, "loading": "Loading...", "@loading": {"description": "Loading indicator text"}, "processing": "Processing...", "@processing": {"description": "Processing indicator text"}, "success": "Success!", "@success": {"description": "Success message"}, "error": "Error", "@error": {"description": "Error message title"}, "warning": "Warning", "@warning": {"description": "Warning message title"}, "info": "Information", "@info": {"description": "Information message title"}, "confirmation": "Confirmation", "@confirmation": {"description": "Confirmation dialog title"}, "areYouSure": "Are you sure?", "@areYouSure": {"description": "Confirmation question"}, "thisActionCannot": "This action cannot be undone.", "@thisActionCannot": {"description": "Warning about irreversible action"}, "yes": "Yes", "@yes": {"description": "Yes button text"}, "no": "No", "@no": {"description": "No button text"}, "ok": "OK", "@ok": {"description": "OK button text"}, "close": "Close", "@close": {"description": "Close button text"}, "done": "Done", "@done": {"description": "Done button text"}, "today": "Today", "@today": {"description": "Label for today"}, "yesterday": "Yesterday", "@yesterday": {"description": "Label for yesterday"}, "tomorrow": "Tomorrow", "@tomorrow": {"description": "Label for tomorrow"}, "now": "Now", "@now": {"description": "Label for current time"}, "daily": "Daily", "@daily": {"description": "Label for daily frequency"}, "weekly": "Weekly", "@weekly": {"description": "Label for weekly frequency"}, "monthly": "Monthly", "@monthly": {"description": "Label for monthly frequency"}, "custom": "Custom", "@custom": {"description": "Label for custom option"}, "day": "Day", "@day": {"description": "Label for day"}, "week": "Week", "@week": {"description": "Label for week"}, "month": "Month", "@month": {"description": "Label for month"}, "year": "Year", "@year": {"description": "Label for year"}, "days": "Days", "@days": {"description": "Label for multiple days"}, "weeks": "Weeks", "@weeks": {"description": "Label for multiple weeks"}, "months": "Months", "@months": {"description": "Label for multiple months"}, "years": "Years", "@years": {"description": "Label for multiple years"}, "monday": "Monday", "@monday": {"description": "Label for Monday"}, "tuesday": "Tuesday", "@tuesday": {"description": "Label for Tuesday"}, "wednesday": "Wednesday", "@wednesday": {"description": "Label for Wednesday"}, "thursday": "Thursday", "@thursday": {"description": "Label for Thursday"}, "friday": "Friday", "@friday": {"description": "Label for Friday"}, "saturday": "Saturday", "@saturday": {"description": "Label for Saturday"}, "sunday": "Sunday", "@sunday": {"description": "Label for Sunday"}, "mon": "Mon", "@mon": {"description": "Short label for Monday"}, "tue": "<PERSON><PERSON>", "@tue": {"description": "Short label for Tuesday"}, "wed": "Wed", "@wed": {"description": "Short label for Wednesday"}, "thu": "<PERSON>hu", "@thu": {"description": "Short label for Thursday"}, "fri": "<PERSON><PERSON>", "@fri": {"description": "Short label for Friday"}, "sat": "Sat", "@sat": {"description": "Short label for Saturday"}, "sun": "Sun", "@sun": {"description": "Short label for Sunday"}, "january": "January", "@january": {"description": "Label for January"}, "february": "February", "@february": {"description": "Label for February"}, "march": "March", "@march": {"description": "Label for March"}, "april": "April", "@april": {"description": "Label for April"}, "may": "May", "@may": {"description": "Label for May"}, "june": "June", "@june": {"description": "Label for June"}, "july": "July", "@july": {"description": "Label for July"}, "august": "August", "@august": {"description": "Label for August"}, "september": "September", "@september": {"description": "Label for September"}, "october": "October", "@october": {"description": "Label for October"}, "november": "November", "@november": {"description": "Label for November"}, "december": "December", "@december": {"description": "Label for December"}, "jan": "Jan", "@jan": {"description": "Short label for January"}, "feb": "Feb", "@feb": {"description": "Short label for February"}, "mar": "Mar", "@mar": {"description": "Short label for March"}, "apr": "Apr", "@apr": {"description": "Short label for April"}, "mayShort": "May", "@mayShort": {"description": "Short label for May"}, "jun": "Jun", "@jun": {"description": "Short label for June"}, "jul": "Jul", "@jul": {"description": "Short label for July"}, "aug": "Aug", "@aug": {"description": "Short label for August"}, "sep": "Sep", "@sep": {"description": "Short label for September"}, "oct": "Oct", "@oct": {"description": "Short label for October"}, "nov": "Nov", "@nov": {"description": "Short label for November"}, "dec": "Dec", "@dec": {"description": "Short label for December"}}