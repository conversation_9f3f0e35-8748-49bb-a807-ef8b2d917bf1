# Notifications Feature Module

This module contains all notification-related functionality for the RooLekky app, following a clean architecture approach.

## Structure

- **data**: Data sources and repository implementations
  - **repositories**: Repository implementations
  - **sources**: Data sources (local and platform)

- **domain**: Business logic and models
  - **models**: Domain models
  - **repositories**: Repository interfaces
  - **usecases**: Use cases for notification operations

- **presentation**: UI components and providers
  - **providers**: State management for notifications
  - **screens**: Notification-related screens
  - **widgets**: Notification-related widgets

## Features

- Meter reading reminders
- Low balance alerts
- Top-up reminders
- Notification history and management