// File: test/features/history/presentation/controllers/history_controller_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:lekky/core/models/meter_entry.dart';
import 'package:lekky/features/history/data/history_repository.dart';
import 'package:lekky/features/history/presentation/controllers/history_controller.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

import 'history_controller_test.mocks.dart';

@GenerateMocks([HistoryRepository])
void main() {
  group('HistoryController', () {
    late MockHistoryRepository mockRepository;
    late HistoryController controller;
    
    setUp(() {
      // Create a mock HistoryRepository
      mockRepository = MockHistoryRepository();
      
      // Create a new instance of HistoryController with the mock repository
      controller = HistoryController(repository: mockRepository);
    });
    
    test('bulkAddEntries should call repository.bulkAddEntries', () async {
      // Create test entries
      final entries = [
        MeterEntry(
          reading: 100.0,
          amountToppedUp: 0.0,
          timestamp: DateTime(2023, 1, 1),
          shortAverageAfterTopUp: null,
          totalAverageUpToThisPoint: null,
        ),
        MeterEntry(
          reading: 0.0,
          amountToppedUp: 50.0,
          timestamp: DateTime(2023, 1, 2),
          shortAverageAfterTopUp: null,
          totalAverageUpToThisPoint: null,
        ),
      ];
      
      // Set up the mock to return a successful result
      when(mockRepository.bulkAddEntries(
        any,
        replace: anyNamed('replace'),
        onProgress: anyNamed('onProgress'),
      )).thenAnswer((_) async => true);
      
      // Set up the mock for refresh
      when(mockRepository.getAllEntries()).thenAnswer((_) async => []);
      
      // Call the method
      final result = await controller.bulkAddEntries(entries);
      
      // Verify that the method was called with the correct parameters
      verify(mockRepository.bulkAddEntries(
        entries,
        replace: false,
        onProgress: anyNamed('onProgress'),
      )).called(1);
      
      // Verify that refresh was called
      verify(mockRepository.getAllEntries()).called(1);
      
      // Verify that the method returns true on success
      expect(result, isTrue);
    });
    
    test('bulkAddEntries should handle progress updates', () async {
      // Create test entries
      final entries = [
        MeterEntry(
          reading: 100.0,
          amountToppedUp: 0.0,
          timestamp: DateTime(2023, 1, 1),
          shortAverageAfterTopUp: null,
          totalAverageUpToThisPoint: null,
        ),
      ];
      
      // Set up the mock to call the progress callback
      when(mockRepository.bulkAddEntries(
        any,
        replace: anyNamed('replace'),
        onProgress: anyNamed('onProgress'),
      )).thenAnswer((invocation) async {
        final onProgress = invocation.namedArguments[Symbol('onProgress')] as Function(double)?;
        if (onProgress != null) {
          onProgress(0.5);
          onProgress(1.0);
        }
        return true;
      });
      
      // Set up the mock for refresh
      when(mockRepository.getAllEntries()).thenAnswer((_) async => []);
      
      // Track progress updates
      final progressUpdates = <double>[];
      
      // Call the method with progress tracking
      await controller.bulkAddEntries(
        entries,
        onProgress: (progress) {
          progressUpdates.add(progress);
        },
      );
      
      // Verify that progress updates were received
      expect(progressUpdates, isNotEmpty);
      
      // Verify that progress updates were scaled correctly
      expect(progressUpdates[0], equals(0.5 * 0.8)); // 0.5 scaled to 80%
      expect(progressUpdates[1], equals(1.0 * 0.8)); // 1.0 scaled to 80%
      // Should also have 0.9 (after refresh) and 1.0 (completion)
      expect(progressUpdates.length, greaterThanOrEqualTo(4));
    });
    
    test('bulkAddEntries should handle repository errors', () async {
      // Create test entries
      final entries = [
        MeterEntry(
          reading: 100.0,
          amountToppedUp: 0.0,
          timestamp: DateTime(2023, 1, 1),
          shortAverageAfterTopUp: null,
          totalAverageUpToThisPoint: null,
        ),
      ];
      
      // Set up the mock to return a failure result
      when(mockRepository.bulkAddEntries(
        any,
        replace: anyNamed('replace'),
        onProgress: anyNamed('onProgress'),
      )).thenAnswer((_) async => false);
      
      // Call the method
      final result = await controller.bulkAddEntries(entries);
      
      // Verify that the method returns false on repository error
      expect(result, isFalse);
      
      // Verify that the controller has an error message
      expect(controller.error, isNotEmpty);
    });
    
    test('bulkAddEntries should handle exceptions', () async {
      // Create test entries
      final entries = [
        MeterEntry(
          reading: 100.0,
          amountToppedUp: 0.0,
          timestamp: DateTime(2023, 1, 1),
          shortAverageAfterTopUp: null,
          totalAverageUpToThisPoint: null,
        ),
      ];
      
      // Set up the mock to throw an exception
      when(mockRepository.bulkAddEntries(
        any,
        replace: anyNamed('replace'),
        onProgress: anyNamed('onProgress'),
      )).thenThrow(Exception('Test error'));
      
      // Call the method
      final result = await controller.bulkAddEntries(entries);
      
      // Verify that the method returns false on exception
      expect(result, isFalse);
      
      // Verify that the controller has an error message
      expect(controller.error, isNotEmpty);
    });
  });
}
