// File: lib/features/history/presentation/widgets/parallax_line_painter.dart
import 'package:flutter/material.dart';

/// A custom painter that draws a vertical parallax line
class ParallaxLinePainter extends CustomPainter {
  final double scrollOffset;
  final double maxScrollOffset;
  final Color lineColor;
  final double lineWidth;
  final double parallaxFactor;
  final bool isDarkMode;
  final double tableHeight;

  ParallaxLinePainter({
    required this.scrollOffset,
    required this.maxScrollOffset,
    required this.lineColor,
    this.lineWidth = 4.0, // Thicker line for better visibility
    this.parallaxFactor = 0.8, // Line moves at 80% of scroll speed
    required this.isDarkMode,
    required this.tableHeight,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // Calculate the line height (33% of table height)
    final double lineHeight = tableHeight * 0.33;

    // Calculate the parallax offset for vertical movement
    // When scrollOffset is 0, the line is at the top
    // When scrollOffset is maxScrollOffset, the line is at the bottom
    final double maxVerticalTravel = tableHeight - lineHeight;

    // Calculate the vertical position - start at top, move down as user scrolls down
    double verticalPosition;
    if (maxScrollOffset <= 0) {
      // If there's no scrolling, position at the top
      verticalPosition = 0;
    } else {
      // Move down as user scrolls down (normal direction)
      verticalPosition = (scrollOffset / maxScrollOffset) * maxVerticalTravel;

      // Ensure the line stays within the table bounds
      verticalPosition = verticalPosition.clamp(0.0, tableHeight - lineHeight);
    }

    // Position the line outside the table on the right side
    final double linePosition =
        size.width - 16; // 16px from the right edge of the table

    // Create a solid grey line
    final Paint paint = Paint()
      ..color = Colors.grey.shade300 // Light grey as requested
      ..strokeWidth = lineWidth
      ..strokeCap = StrokeCap.round;

    // Draw the line
    canvas.drawLine(
      Offset(linePosition, verticalPosition),
      Offset(linePosition, verticalPosition + lineHeight),
      paint,
    );
  }

  @override
  bool shouldRepaint(ParallaxLinePainter oldDelegate) {
    return oldDelegate.scrollOffset != scrollOffset ||
        oldDelegate.lineColor != lineColor ||
        oldDelegate.lineWidth != lineWidth ||
        oldDelegate.parallaxFactor != parallaxFactor ||
        oldDelegate.isDarkMode != isDarkMode ||
        oldDelegate.tableHeight != tableHeight;
  }
}
