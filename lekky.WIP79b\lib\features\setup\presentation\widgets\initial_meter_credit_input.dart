// File: lib/features/setup/presentation/widgets/initial_meter_credit_input.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/extensions/context_extensions.dart';
import '../../../../core/utils/input_validator.dart';
import '../../../../core/widgets/app_card.dart';
import '../../../../core/widgets/app_text_field.dart';

/// A widget for inputting the first meter reading
class InitialMeterCreditInput extends StatefulWidget {
  final double? initialMeterCredit;
  final String meterUnit;
  final Function(double?) onInitialMeterCreditChanged;

  const InitialMeterCreditInput({
    super.key,
    this.initialMeterCredit,
    required this.meterUnit,
    required this.onInitialMeterCreditChanged,
  });

  @override
  State<InitialMeterCreditInput> createState() =>
      _InitialMeterCreditInputState();
}

class _InitialMeterCreditInputState extends State<InitialMeterCreditInput> {
  late TextEditingController _controller;
  String? _errorText;

  @override
  void initState() {
    super.initState();
    // Format initial value to avoid trailing .0 for whole numbers
    _controller = TextEditingController(
      text: _formatInitialValue(widget.initialMeterCredit),
    );
  }

  @override
  void didUpdateWidget(InitialMeterCreditInput oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.initialMeterCredit != widget.initialMeterCredit) {
      // Update text without triggering onChanged
      _controller.text = _formatInitialValue(widget.initialMeterCredit);
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  // Format initial value to display whole numbers without .0
  String _formatInitialValue(double? value) {
    if (value == null) return '';
    // Check if the number is effectively a whole number
    if (value % 1 == 0) {
      return value.toInt().toString(); // e.g., 5.0 -> "5"
    }
    // For decimals, use toStringAsFixed to avoid trailing zeros
    return value
        .toStringAsFixed(2)
        .replaceAll(RegExp(r'0*$'), '')
        .replaceAll(RegExp(r'\.$'), '');
  }

  @override
  Widget build(BuildContext context) {
    return AppCard(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'First Meter Reading',
            style: AppTextStyles.titleMedium.copyWith(
              color: context.textColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Enter your first meter reading in monetary value (leave empty if not applicable)',
            style: AppTextStyles.bodyMedium.copyWith(
              color: context.textColor,
            ),
          ),
          const SizedBox(height: 16),
          AppTextField(
            controller: _controller,
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
            inputFormatters: [
              FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*$')),
            ],
            prefixText: widget.meterUnit,
            errorText: _errorText,
            onChanged: _validateAndUpdate,
            hintText: 'Optional',
            selectAllOnFocus: true,
          ),
          const SizedBox(height: 8),
          Text(
            'This value must be positive and will be used to calculate your initial meter balance',
            style: AppTextStyles.bodySmall.copyWith(
              fontStyle: FontStyle.italic,
              color: context.secondaryTextColor,
            ),
          ),
        ],
      ),
    );
  }

  void _validateAndUpdate(String value) {
    // Handle empty input (optional)
    if (value.isEmpty) {
      setState(() {
        _errorText = null;
      });
      widget.onInitialMeterCreditChanged(null);
      return;
    }

    // Validate using InputValidator (aligned with ThresholdInput)
    final validation = InputValidator.validateMonetaryAmount(value);

    setState(() {
      _errorText = validation['isValid'] ? null : validation['errorMessage'];
    });

    if (!validation['isValid']) {
      return;
    }

    // Parse the value
    final initialCredit = double.tryParse(value);

    // Additional validation for positive values
    if (initialCredit != null && initialCredit <= 0) {
      setState(() {
        _errorText = 'Value must be greater than zero';
      });
      return;
    }

    // Update parent with valid value (rounded to 2 decimal places)
    if (initialCredit != null) {
      final roundedValue = double.parse(initialCredit.toStringAsFixed(2));
      widget.onInitialMeterCreditChanged(roundedValue);
    }
  }
}
