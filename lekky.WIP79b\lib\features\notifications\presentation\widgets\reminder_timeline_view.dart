// File: lib/features/notifications/presentation/widgets/reminder_timeline_view.dart

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../domain/models/notification_model.dart';
import '../../domain/models/timezone_aware_reminder_time.dart';

/// A model representing a reminder event for the timeline
class ReminderEvent {
  final String id;
  final String title;
  final DateTime scheduledDate;
  final NotificationPriority priority;
  final String? status; // 'scheduled', 'triggered', 'dismissed', 'missed'

  const ReminderEvent({
    required this.id,
    required this.title,
    required this.scheduledDate,
    required this.priority,
    this.status = 'scheduled',
  });
}

/// A widget that displays upcoming reminders in a timeline view
class ReminderTimelineView extends StatelessWidget {
  final List<ReminderEvent> reminders;
  final Function(ReminderEvent)? onReminderTap;

  const ReminderTimelineView({
    Key? key,
    required this.reminders,
    this.onReminderTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Sort reminders by date
    final sortedReminders = List<ReminderEvent>.from(reminders)
      ..sort((a, b) => a.scheduledDate.compareTo(b.scheduledDate));

    return reminders.isEmpty
        ? _buildEmptyState()
        : _buildTimelineList(context, sortedReminders);
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.event_available,
            size: 64,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            'No upcoming reminders',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Scheduled reminders will appear here',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTimelineList(
      BuildContext context, List<ReminderEvent> sortedReminders) {
    // Group reminders by month
    final Map<String, List<ReminderEvent>> remindersByMonth = {};

    for (final reminder in sortedReminders) {
      final monthKey = DateFormat('MMMM yyyy').format(reminder.scheduledDate);
      if (!remindersByMonth.containsKey(monthKey)) {
        remindersByMonth[monthKey] = [];
      }
      remindersByMonth[monthKey]!.add(reminder);
    }

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: remindersByMonth.length,
      itemBuilder: (context, index) {
        final monthKey = remindersByMonth.keys.elementAt(index);
        final monthReminders = remindersByMonth[monthKey]!;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Text(
                monthKey,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: monthReminders.length,
              itemBuilder: (context, reminderIndex) {
                final reminder = monthReminders[reminderIndex];
                return _buildReminderTimelineItem(context, reminder,
                    reminderIndex == monthReminders.length - 1);
              },
            ),
            if (index < remindersByMonth.length - 1)
              const Divider(height: 32, thickness: 1),
          ],
        );
      },
    );
  }

  Widget _buildReminderTimelineItem(
      BuildContext context, ReminderEvent reminder, bool isLast) {
    final now = DateTime.now();
    final isUpcoming = reminder.scheduledDate.isAfter(now);
    final isPast = reminder.scheduledDate.isBefore(now);
    final isToday = reminder.scheduledDate.day == now.day &&
        reminder.scheduledDate.month == now.month &&
        reminder.scheduledDate.year == now.year;

    // Determine the status color
    Color statusColor;
    IconData statusIcon;

    if (reminder.status == 'triggered') {
      statusColor = Colors.green;
      statusIcon = Icons.check_circle;
    } else if (reminder.status == 'missed') {
      statusColor = Colors.red;
      statusIcon = Icons.cancel;
    } else if (reminder.status == 'dismissed') {
      statusColor = Colors.orange;
      statusIcon = Icons.remove_circle;
    } else {
      // Scheduled
      statusColor =
          isUpcoming ? Colors.blue : (isToday ? Colors.amber : Colors.grey);
      statusIcon = isUpcoming
          ? Icons.schedule
          : (isToday ? Icons.today : Icons.event_available);
    }

    return InkWell(
      onTap: onReminderTap != null ? () => onReminderTap!(reminder) : null,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Timeline dot and line
            Column(
              children: [
                Container(
                  width: 20,
                  height: 20,
                  decoration: BoxDecoration(
                    color: statusColor,
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    statusIcon,
                    size: 12,
                    color: Colors.white,
                  ),
                ),
                if (!isLast)
                  Container(
                    width: 2,
                    height: 40,
                    color: Colors.grey.shade300,
                  ),
              ],
            ),
            const SizedBox(width: 12),

            // Reminder content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    reminder.title,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Icon(
                        Icons.access_time,
                        size: 14,
                        color: Colors.grey.shade600,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        _formatReminderDateTime(reminder.scheduledDate),
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  _buildReminderStatusChip(reminder),
                ],
              ),
            ),

            // Actions
            Icon(
              Icons.chevron_right,
              color: Colors.grey.shade400,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReminderStatusChip(ReminderEvent reminder) {
    String statusText;
    Color statusColor;

    if (reminder.status == 'triggered') {
      statusText = 'Triggered';
      statusColor = Colors.green;
    } else if (reminder.status == 'missed') {
      statusText = 'Missed';
      statusColor = Colors.red;
    } else if (reminder.status == 'dismissed') {
      statusText = 'Dismissed';
      statusColor = Colors.orange;
    } else {
      // Calculate time difference for scheduled reminders
      final now = DateTime.now();
      final difference = reminder.scheduledDate.difference(now);

      if (difference.inDays > 0) {
        statusText =
            'In ${difference.inDays} ${difference.inDays == 1 ? 'day' : 'days'}';
      } else if (difference.inHours > 0) {
        statusText =
            'In ${difference.inHours} ${difference.inHours == 1 ? 'hour' : 'hours'}';
      } else if (difference.inMinutes > 0) {
        statusText =
            'In ${difference.inMinutes} ${difference.inMinutes == 1 ? 'minute' : 'minutes'}';
      } else if (difference.inMinutes >= -30) {
        // Within the last 30 minutes
        statusText = 'Just now';
      } else {
        statusText = 'Scheduled';
      }

      statusColor = Colors.blue;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        color: statusColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: statusColor.withOpacity(0.3)),
      ),
      child: Text(
        statusText,
        style: TextStyle(
          fontSize: 12,
          color: statusColor,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  String _formatReminderDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final reminderDate = DateTime(dateTime.year, dateTime.month, dateTime.day);

    final timeFormat = DateFormat('h:mm a');

    if (reminderDate.difference(today).inDays == 0) {
      return 'Today, ${timeFormat.format(dateTime)}';
    } else if (reminderDate.difference(today).inDays == 1) {
      return 'Tomorrow, ${timeFormat.format(dateTime)}';
    } else if (reminderDate.difference(today).inDays == -1) {
      return 'Yesterday, ${timeFormat.format(dateTime)}';
    } else if (reminderDate.difference(today).inDays >= -6 &&
        reminderDate.difference(today).inDays < 0) {
      // Within the last week
      return '${DateFormat('EEEE').format(dateTime)}, ${timeFormat.format(dateTime)}';
    } else {
      return DateFormat('MMM d, yyyy').format(dateTime) +
          ' at ' +
          timeFormat.format(dateTime);
    }
  }
}
