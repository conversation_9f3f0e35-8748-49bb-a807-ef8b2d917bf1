// File: lib/features/notifications/domain/usecases/migrate_notification_storage.dart

import '../repositories/notification_repository.dart';

/// Use case for migrating notification storage from SharedPreferences to SQLite
class MigrateNotificationStorage {
  final NotificationRepository _repository;

  MigrateNotificationStorage({
    required NotificationRepository repository,
  }) : _repository = repository;

  /// Execute the migration
  /// Returns true if the migration was successful, false otherwise
  Future<bool> execute({
    Function(double progress)? onProgress,
  }) async {
    return await _repository.migrateToSQLite(
      onProgress: onProgress,
    );
  }

  /// Check if the migration has already been completed
  Future<bool> isMigrationCompleted() async {
    return await _repository.isMigratedToSQLite();
  }

  /// Get the current storage type
  Future<String> getCurrentStorageType() async {
    return await _repository.getStorageType();
  }
}
