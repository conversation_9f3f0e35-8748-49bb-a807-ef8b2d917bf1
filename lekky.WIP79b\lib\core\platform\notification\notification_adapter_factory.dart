// File: lib/core/platform/notification/notification_adapter_factory.dart

import 'notification_adapter.dart';
import 'default_notification_adapter.dart';

/// Factory for creating notification adapters
class NotificationAdapterFactory {
  /// Create a notification adapter
  static NotificationAdapter createAdapter() {
    // For now, we just return a default implementation
    // In the future, we could create platform-specific adapters
    return DefaultNotificationAdapter();
  }
}
