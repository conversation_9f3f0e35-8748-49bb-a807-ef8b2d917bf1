// File: lib/features/welcome/presentation/screens/welcome_screen.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/models/meter_entry.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/widgets/gradient_button.dart';
import '../../../../features/backup/backup_service.dart';
import '../../../../features/settings/presentation/controllers/settings_controller.dart';
import '../widgets/welcome_feature_item.dart';

/// The welcome screen of the app
class WelcomeScreen extends StatelessWidget {
  WelcomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _buildBody(context),
    );
  }

  Widget _buildBody(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Color(0xFF003087),
            Color(0xFF0057B8)
          ], // Primary gradient from lekky_pallet.md
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: SingleChildScrollView(
            physics: const ClampingScrollPhysics(),
            child: ConstrainedBox(
              constraints: BoxConstraints(
                minHeight: MediaQuery.of(context).size.height -
                    MediaQuery.of(context).padding.top -
                    MediaQuery.of(context).padding.bottom -
                    48.0, // Account for padding
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    children: [
                      const SizedBox(height: 24),
                      _buildHeader(),
                      const SizedBox(height: 48),
                      _buildFeaturesList(),
                    ],
                  ),
                  Padding(
                    padding: const EdgeInsets.only(top: 32.0, bottom: 16.0),
                    child: _buildGetStartedButton(context),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        const Icon(
          Icons.electric_meter,
          size: 80,
          color: Colors.white,
        ),
        const SizedBox(height: 16),
        Text(
          'Welcome to Lekky',
          style: AppTextStyles.headlineLarge.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 8),
        Text(
          'Your personal prepaid meter assistant',
          style: AppTextStyles.titleMedium.copyWith(
            color: Colors.white,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildFeaturesList() {
    return Column(
      children: const [
        WelcomeFeatureItem(
          icon: Icons.track_changes,
          title: 'Track Your Usage',
          description: 'Monitor your electricity consumption and spending',
        ),
        SizedBox(height: 24),
        WelcomeFeatureItem(
          icon: Icons.notifications,
          title: 'Get Timely Alerts',
          description: 'Receive notifications when your balance is running low',
        ),
        SizedBox(height: 24),
        WelcomeFeatureItem(
          icon: Icons.history,
          title: 'View History',
          description: 'See your past meter readings and top-ups',
        ),
        SizedBox(height: 24),
        WelcomeFeatureItem(
          icon: Icons.attach_money,
          title: 'Calculate Costs',
          description: 'Estimate your electricity costs over different periods',
        ),
      ],
    );
  }

  Widget _buildGetStartedButton(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Always show restore option
        Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            OutlinedButton.icon(
              onPressed: () => _restoreFromManualSelection(context),
              icon: const Icon(Icons.restore),
              label: const Text('Restore Previous Data'),
              style: OutlinedButton.styleFrom(
                foregroundColor: Colors.white,
                side: const BorderSide(color: Colors.white),
                padding:
                    const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'Have a backup from another device?',
              style: TextStyle(color: Colors.white70, fontSize: 14),
            ),
            const SizedBox(height: 16),
          ],
        ),
        // Get Started button
        GradientButton(
          text: 'Get Started',
          onPressed: () {
            Navigator.of(context).pushReplacementNamed(AppConstants.routeSetup);
          },
          gradientColors: const [
            Color(0xFF1976D2),
            Color(0xFF42A5F5)
          ], // Primary color from lekky_pallet.md
          icon: const Icon(
            Icons.arrow_forward,
            color: Colors.white,
          ),
          width: double.infinity,
        ),
      ],
    );
  }

  // Create an instance of the BackupService
  final BackupService _backupService = BackupService();

  /// Restore data from a manually selected file
  Future<void> _restoreFromManualSelection(BuildContext context) async {
    try {
      // Get the settings controller
      final settingsController =
          Provider.of<SettingsController>(context, listen: false);

      // Show loading indicator
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Select your backup file...'),
          duration: Duration(seconds: 1),
        ),
      );

      // Use the backup service to pick a file
      final fileResult = await _backupService.pickBackupFile();

      if (fileResult.isFailure) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content:
                  Text('File selection failed: ${fileResult.error.message}'),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 3),
            ),
          );
        }
        return;
      }

      // Show loading indicator
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Restoring data...'),
            duration: Duration(seconds: 1),
          ),
        );
      }

      // Import the selected file
      final importResult =
          await _backupService.importMeterEntries(fileResult.value);

      if (importResult.isFailure) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Import failed: ${importResult.error.message}'),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 3),
            ),
          );
        }
        return;
      }

      // Process the imported entries
      if (context.mounted) {
        // Clear existing data
        await settingsController.clearAllData(silent: true);

        // Save entries to database
        for (final entry in importResult.value) {
          // Use the repository through the controller
          await settingsController.addMeterEntry(entry);
        }

        // Set setup completed to true
        await settingsController.completeSetup();

        // Show success message
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                  'Data restored successfully: ${importResult.value.length} entries'),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 2),
            ),
          );
        }

        // Navigate to home screen after a short delay
        Future.delayed(const Duration(seconds: 1), () {
          if (context.mounted) {
            Navigator.of(context).pushReplacementNamed(AppConstants.routeHome);
          }
        });
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }
}
