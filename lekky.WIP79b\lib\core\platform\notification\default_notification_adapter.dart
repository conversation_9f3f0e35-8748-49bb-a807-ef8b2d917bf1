// File: lib/core/platform/notification/default_notification_adapter.dart

import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:timezone/data/latest.dart' as tz_data;

import 'notification_adapter.dart';

/// Default implementation of the notification adapter
class DefaultNotificationAdapter extends NotificationAdapter {
  final FlutterLocalNotificationsPlugin _notificationsPlugin =
      FlutterLocalNotificationsPlugin();
  bool _isInitialized = false;

  @override
  Future<void> initialize() async {
    if (_isInitialized) return;

    // Initialize timezone database
    tz_data.initializeTimeZones();

    // Initialize notification plugin
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');

    final DarwinInitializationSettings initializationSettingsIOS =
        DarwinInitializationSettings(
      requestAlertPermission: false,
      requestBadgePermission: false,
      requestSoundPermission: false,
      onDidReceiveLocalNotification: (id, title, body, payload) {
        // Handle iOS notification
      },
    );

    final InitializationSettings initializationSettings =
        InitializationSettings(
      android: initializationSettingsAndroid,
      iOS: initializationSettingsIOS,
    );

    await _notificationsPlugin.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: (NotificationResponse details) {
        // Handle notification tap
      },
    );

    _isInitialized = true;
  }

  @override
  Future<bool> checkNotificationPermissions() async {
    await initialize();

    // Check permissions
    final status = await _notificationsPlugin
            .resolvePlatformSpecificImplementation<
                AndroidFlutterLocalNotificationsPlugin>()
            ?.areNotificationsEnabled() ??
        false;

    return status;
  }

  @override
  Future<bool> requestNotificationPermissions() async {
    await initialize();

    // Request permissions on iOS
    final iOS = _notificationsPlugin.resolvePlatformSpecificImplementation<
        IOSFlutterLocalNotificationsPlugin>();
    if (iOS != null) {
      await iOS.requestPermissions(
        alert: true,
        badge: true,
        sound: true,
      );
    }

    // Request permissions on Android
    final android = _notificationsPlugin.resolvePlatformSpecificImplementation<
        AndroidFlutterLocalNotificationsPlugin>();
    if (android != null) {
      await android.requestPermission();
    }

    // Check if permissions were granted
    return await checkNotificationPermissions();
  }

  @override
  Future<bool> isNotificationPermissionPermanentlyDenied() async {
    // This would require platform-specific code
    // For now, return false
    return false;
  }

  @override
  Future<bool> openNotificationSettings() async {
    await initialize();

    final android = _notificationsPlugin.resolvePlatformSpecificImplementation<
        AndroidFlutterLocalNotificationsPlugin>();
    if (android != null) {
      return await android.openNotificationSettings() ?? false;
    }

    return false;
  }

  @override
  Future<void> showNotification({
    required int id,
    required String title,
    required String body,
    String? payload,
  }) async {
    await initialize();

    const AndroidNotificationDetails androidDetails =
        AndroidNotificationDetails(
      'default_channel',
      'Default Channel',
      channelDescription: 'Default notification channel',
      importance: Importance.high,
      priority: Priority.high,
    );

    const DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const NotificationDetails details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _notificationsPlugin.show(
      id,
      title,
      body,
      details,
      payload: payload,
    );
  }

  @override
  Future<bool> scheduleNotification({
    required int id,
    required String title,
    required String body,
    required DateTime scheduledDate,
    String? payload,
    int maxRetries = 3,
  }) async {
    await initialize();

    try {
      const AndroidNotificationDetails androidDetails =
          AndroidNotificationDetails(
        'scheduled_channel',
        'Scheduled Channel',
        channelDescription: 'Channel for scheduled notifications',
        importance: Importance.high,
        priority: Priority.high,
      );

      const DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      const NotificationDetails details = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      final tzDateTime = tz.TZDateTime.from(scheduledDate, tz.local);

      await _notificationsPlugin.zonedSchedule(
        id,
        title,
        body,
        tzDateTime,
        details,
        androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
        uiLocalNotificationDateInterpretation:
            UILocalNotificationDateInterpretation.absoluteTime,
        payload: payload,
      );

      return true;
    } catch (e) {
      debugPrint('Error scheduling notification: $e');
      return false;
    }
  }

  @override
  Future<bool> rescheduleNotification(int id) async {
    // This would require storing notification details
    // For now, return false
    return false;
  }

  @override
  Future<int> rescheduleAllNotifications() async {
    // This would require storing all notification details
    // For now, return 0
    return 0;
  }

  @override
  Future<void> cancelNotification(int id) async {
    await initialize();
    await _notificationsPlugin.cancel(id);
  }

  @override
  Future<void> cancelAllNotifications() async {
    await initialize();
    await _notificationsPlugin.cancelAll();
  }

  @override
  Future<void> createNotificationChannels() async {
    await initialize();

    // Create channels on Android
    final android = _notificationsPlugin.resolvePlatformSpecificImplementation<
        AndroidFlutterLocalNotificationsPlugin>();
    if (android != null) {
      const List<AndroidNotificationChannel> channels = [
        AndroidNotificationChannel(
          'default_channel',
          'Default Channel',
          description: 'Default notification channel',
          importance: Importance.high,
        ),
        AndroidNotificationChannel(
          'scheduled_channel',
          'Scheduled Channel',
          description: 'Channel for scheduled notifications',
          importance: Importance.high,
        ),
        AndroidNotificationChannel(
          'meter_reading_channel',
          'Meter Reading Reminders',
          description: 'Channel for meter reading reminder notifications',
          importance: Importance.high,
        ),
      ];

      for (final channel in channels) {
        await android.createNotificationChannel(channel);
      }
    }
  }

  @override
  Future<bool> hasTimeZoneChanged() async {
    // This would require storing the last known timezone
    // For now, return false
    return false;
  }

  @override
  String getCurrentTimeZone() {
    return tz.local.name;
  }

  @override
  Future<Map<String, dynamic>> getChannelSettings(String channelId) async {
    // This would require platform-specific code
    // For now, return default settings
    return {
      'enabled': true,
      'importance': 'high',
      'sound': true,
      'vibration': true,
    };
  }

  @override
  Future<bool> canScheduleExactAlarms() async {
    // This would require platform-specific code for Android 12+
    // For now, return true
    return true;
  }

  @override
  Future<void> requestExactAlarmPermission() async {
    // This would require platform-specific code for Android 12+
    // For now, do nothing
  }
}
