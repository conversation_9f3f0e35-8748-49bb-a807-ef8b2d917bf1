// File: test/core/platform/timezone/timezone_change_service_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:lekky/core/platform/timezone/timezone_change_service.dart';
import 'package:shared_preferences/shared_preferences.dart';

void main() {
  setUp(() {
    // Initialize SharedPreferences for testing
    SharedPreferences.setMockInitialValues({});

    // Reset service state
    TimezoneChangeService.dispose();
  });

  tearDown(() {
    // Clean up after each test
    TimezoneChangeService.dispose();
  });

  group('TimezoneChangeService', () {
    test('should mark and check timezone change detection', () async {
      // Act - mark that a timezone change was detected
      await TimezoneChangeService.markTimezoneChangeDetected(true);

      // Assert
      final detected =
          await TimezoneChangeService.hasTimezoneChangeBeenDetected();
      expect(detected, true);
    });

    test('should clear timezone change detection flag', () async {
      // Arrange - first mark that a timezone change was detected
      await TimezoneChangeService.markTimezoneChangeDetected(true);
      final detectedBefore =
          await TimezoneChangeService.hasTimezoneChangeBeenDetected();
      expect(detectedBefore, true);

      // Act
      await TimezoneChangeService.clearTimezoneChangeDetection();

      // Assert
      final detectedAfter =
          await TimezoneChangeService.hasTimezoneChangeBeenDetected();
      expect(detectedAfter, false);
    });

    test('should add and remove timezone change listeners', () {
      // Arrange
      int listener1CallCount = 0;
      int listener2CallCount = 0;

      final listener1 = () {
        listener1CallCount++;
      };

      final listener2 = () {
        listener2CallCount++;
      };

      // Add both listeners
      TimezoneChangeService.addTimezoneChangeListener(listener1);
      TimezoneChangeService.addTimezoneChangeListener(listener2);

      // We can't directly test the listeners are called without modifying the service
      // So we'll just test that adding and removing doesn't throw exceptions

      // Act & Assert - removing a listener should not throw
      expect(
          () => TimezoneChangeService.removeTimezoneChangeListener(listener1),
          returnsNormally);
      expect(
          () => TimezoneChangeService.removeTimezoneChangeListener(listener2),
          returnsNormally);
    });

    test('should initialize without errors', () async {
      // Act & Assert
      expect(() => TimezoneChangeService.initialize(), returnsNormally);
    });

    test('should start and stop monitoring without errors', () {
      // Arrange
      TimezoneChangeService.initialize();

      // Act & Assert
      expect(() => TimezoneChangeService.startMonitoring(), returnsNormally);
      expect(() => TimezoneChangeService.stopMonitoring(), returnsNormally);
    });
  });
}
