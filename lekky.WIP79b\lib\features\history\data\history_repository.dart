// File: lib/features/history/data/history_repository.dart
import '../../../core/data/repositories/meter_entry_repository.dart';
import '../../../core/data/repositories/settings_repository.dart';
import '../../../core/models/meter_entry.dart';
import '../domain/models/history_filter.dart';
import '../domain/models/validation_result.dart';
import '../domain/usecases/filter_entries.dart';
import '../domain/usecases/validate_entries.dart';
import '../domain/usecases/calculate_averages.dart';

/// Repository for the history screen
class HistoryRepository {
  final MeterEntryRepository _meterEntryRepository;
  final SettingsRepository _settingsRepository;
  final FilterEntries _filterEntries;
  final ValidateEntries _validateEntries;
  final CalculateAverages _calculateAverages;

  HistoryRepository({
    required MeterEntryRepository meterEntryRepository,
    required SettingsRepository settingsRepository,
  })  : _meterEntryRepository = meterEntryRepository,
        _settingsRepository = settingsRepository,
        _filterEntries = FilterEntries(),
        _validateEntries = ValidateEntries(meterEntryRepository),
        _calculateAverages = CalculateAverages();

  /// Get all meter entries
  Future<List<MeterEntry>> getAllEntries() async {
    return await _meterEntryRepository.getAllEntriesWithCache();
  }

  /// Get filtered meter entries
  Future<List<MeterEntry>> getFilteredEntries(HistoryFilter filter) async {
    final entries = await _meterEntryRepository.getAllEntriesWithCache();
    return _filterEntries.execute(entries, filter);
  }

  /// Validate a meter entry
  Future<ValidationResult> validateEntry(MeterEntry entry) async {
    return await _validateEntries.execute(entry);
  }

  /// Validate multiple meter entries
  Future<Map<int, ValidationResult>> validateEntries(
      List<MeterEntry> entries) async {
    return await _validateEntries.executeMultiple(entries);
  }

  /// Calculate averages for meter entries
  List<MeterEntry> calculateAverages(List<MeterEntry> entries) {
    return _calculateAverages.execute(entries);
  }

  /// Calculate the total average up to a specific entry
  double calculateTotalAverageUpToEntry(
      List<MeterEntry> entries, int entryIndex) {
    return _calculateAverages.calculateTotalAverageUpToEntry(
        entries, entryIndex);
  }

  /// Calculate the short average for a specific entry
  double calculateShortAverageForEntry(
      List<MeterEntry> entries, int entryIndex) {
    return _calculateAverages.calculateShortAverageForEntry(
        entries, entryIndex);
  }

  /// Add a new meter entry
  Future<void> addEntry(MeterEntry entry) async {
    await _meterEntryRepository.addEntry(entry);
  }

  /// Add multiple meter entries in bulk
  ///
  /// @param entries The list of MeterEntry objects to add
  /// @param replace Whether to replace existing entries (true) or append (false)
  /// @param onProgress Optional callback for progress updates (0.0 to 1.0)
  Future<bool> bulkAddEntries(
    List<MeterEntry> entries, {
    bool replace = false,
    Function(double progress)? onProgress,
  }) async {
    return await _meterEntryRepository.bulkAddEntries(
      entries,
      replace: replace,
      onProgress: onProgress,
    );
  }

  /// Delete a meter entry
  Future<void> deleteEntry(int id) async {
    await _meterEntryRepository.deleteEntry(id);
  }

  /// Get the meter unit
  Future<String> getMeterUnit() async {
    return await _settingsRepository.getMeterUnit();
  }

  /// Get the date format
  Future<String> getDateFormat() async {
    return await _settingsRepository.getDateFormat();
  }

  /// Get the date info setting
  Future<String> getDateInfo() async {
    return await _settingsRepository.getDateInfo();
  }
}
