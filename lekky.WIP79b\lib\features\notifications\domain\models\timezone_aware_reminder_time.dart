// File: lib/features/notifications/domain/models/timezone_aware_reminder_time.dart

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../../core/platform/timezone/timezone_adapter.dart';
import 'reminder_time_model.dart';

/// A timezone-aware extension of the ReminderTimeModel
class TimezoneAwareReminderTime {
  /// The time of day in the user's local timezone
  final TimeOfDay timeOfDay;

  /// The selected date in the user's local timezone (optional)
  final DateTime? selectedDate;

  /// The timezone in which this reminder was created
  final String creationTimezone;

  /// Whether to keep the reminder at the same local time when timezone changes
  final bool adjustForTimezoneChanges;

  const TimezoneAwareReminderTime({
    required this.timeOfDay,
    this.selectedDate,
    required this.creationTimezone,
    this.adjustForTimezoneChanges = true,
  });

  /// Create from a ReminderTimeModel and current timezone
  factory TimezoneAwareReminderTime.fromReminderTimeModel({
    required ReminderTimeModel model,
    required String currentTimezone,
    bool adjustForTimezoneChanges = true,
  }) {
    return TimezoneAwareReminderTime(
      timeOfDay: model.timeOfDay,
      selectedDate: model.selectedDate,
      creationTimezone: currentTimezone,
      adjustForTimezoneChanges: adjustForTimezoneChanges,
    );
  }

  /// Convert to a ReminderTimeModel
  ReminderTimeModel toReminderTimeModel() {
    return ReminderTimeModel(
      timeOfDay: timeOfDay,
      selectedDate: selectedDate,
    );
  }

  /// Convert to a string representation for storage
  Map<String, dynamic> toJson() {
    return {
      'hour': timeOfDay.hour,
      'minute': timeOfDay.minute,
      'selectedDate': selectedDate?.toIso8601String(),
      'creationTimezone': creationTimezone,
      'adjustForTimezoneChanges': adjustForTimezoneChanges,
    };
  }

  /// Create from a JSON representation
  factory TimezoneAwareReminderTime.fromJson(Map<String, dynamic> json) {
    DateTime? selectedDate;
    if (json['selectedDate'] != null) {
      selectedDate = DateTime.parse(json['selectedDate']);
    }

    return TimezoneAwareReminderTime(
      timeOfDay: TimeOfDay(
        hour: json['hour'],
        minute: json['minute'],
      ),
      selectedDate: selectedDate,
      creationTimezone: json['creationTimezone'],
      adjustForTimezoneChanges: json['adjustForTimezoneChanges'] ?? true,
    );
  }

  /// Calculate the next occurrence of this reminder time
  DateTime getNextOccurrence({
    required DateTime fromDate,
    required int frequencyInDays,
    required TimezoneAdapter timezoneAdapter,
  }) {
    final now = timezoneAdapter.convertToLocal(DateTime.now().toUtc());

    // Start with the base date (today or fromDate)
    final baseDate = fromDate.isAfter(now) ? fromDate : now;

    // Create a DateTime with the reminder time
    DateTime reminderDateTime;

    if (selectedDate != null) {
      // If a specific date is selected, use that
      reminderDateTime = DateTime(
        selectedDate!.year,
        selectedDate!.month,
        selectedDate!.day,
        timeOfDay.hour,
        timeOfDay.minute,
      );

      // If the selected date is in the past, add frequency days until it's in the future
      while (reminderDateTime.isBefore(baseDate)) {
        reminderDateTime =
            reminderDateTime.add(Duration(days: frequencyInDays));
      }
    } else {
      // Otherwise, calculate based on the current date + frequency
      reminderDateTime = DateTime(
        baseDate.year,
        baseDate.month,
        baseDate.day,
        timeOfDay.hour,
        timeOfDay.minute,
      );

      // Add the frequency
      reminderDateTime = reminderDateTime.add(Duration(days: frequencyInDays));

      // If the calculated time is still in the past, add one more frequency period
      if (reminderDateTime.isBefore(baseDate)) {
        reminderDateTime =
            reminderDateTime.add(Duration(days: frequencyInDays));
      }
    }

    // If adjusting for timezone changes is enabled and the current timezone
    // is different from the creation timezone, adjust the time
    final currentTimezone = timezoneAdapter.getCurrentTimezone();
    if (adjustForTimezoneChanges && currentTimezone != creationTimezone) {
      // Calculate the difference in offset between the creation timezone and current timezone
      final creationOffset = _getOffsetForTimezone(
        timezoneAdapter,
        creationTimezone,
        reminderDateTime,
      );
      final currentOffset = timezoneAdapter.getTimezoneOffset(reminderDateTime);
      final offsetDifference = currentOffset - creationOffset;

      // Adjust the time by the offset difference
      if (offsetDifference != 0) {
        reminderDateTime = reminderDateTime.subtract(
          Duration(minutes: (offsetDifference * 60).round()),
        );
      }
    }

    return reminderDateTime;
  }

  /// Get the timezone offset for a specific timezone
  double _getOffsetForTimezone(
    TimezoneAdapter timezoneAdapter,
    String timezone,
    DateTime dateTime,
  ) {
    // For now, we'll use the current timezone's offset as we don't have
    // a way to get the offset for a specific timezone
    // In a real implementation, you would use timezone data to calculate this
    return timezoneAdapter.getTimezoneOffset(dateTime);
  }

  /// Format the time for display based on the context (12/24 hour format)
  String format(BuildContext context, TimezoneAdapter timezoneAdapter) {
    final timeStr = timeOfDay.format(context);

    // Check if we're in a different timezone than when the reminder was created
    final currentTimezone = timezoneAdapter.getCurrentTimezone();
    final isDifferentTimezone = currentTimezone != creationTimezone;

    // Get timezone abbreviation
    final timezoneAbbr =
        timezoneAdapter.getTimezoneAbbreviation(DateTime.now());

    if (selectedDate != null) {
      // Format with day and date
      final dayFormat = DateFormat('EEEE'); // Full day name
      final dateFormat = DateFormat('MMM d'); // Month and day

      final day = dayFormat.format(selectedDate!);
      final date = dateFormat.format(selectedDate!);

      if (isDifferentTimezone && adjustForTimezoneChanges) {
        return '$day, $date at $timeStr ($timezoneAbbr)';
      } else {
        return '$day, $date at $timeStr';
      }
    } else {
      // Just time
      if (isDifferentTimezone && adjustForTimezoneChanges) {
        return '$timeStr ($timezoneAbbr)';
      } else {
        return timeStr;
      }
    }
  }

  /// Create a copy with updated values
  TimezoneAwareReminderTime copyWith({
    TimeOfDay? timeOfDay,
    DateTime? selectedDate,
    bool clearDate = false,
    String? creationTimezone,
    bool? adjustForTimezoneChanges,
  }) {
    return TimezoneAwareReminderTime(
      timeOfDay: timeOfDay ?? this.timeOfDay,
      selectedDate: clearDate ? null : (selectedDate ?? this.selectedDate),
      creationTimezone: creationTimezone ?? this.creationTimezone,
      adjustForTimezoneChanges:
          adjustForTimezoneChanges ?? this.adjustForTimezoneChanges,
    );
  }
}
