import '../../../../core/models/average.dart';

/// Immutable state for the Averages feature
class AverageState {
  /// Recent average daily usage
  final double? recentAverage;

  /// Total average daily usage
  final double? totalAverage;

  /// Loading state
  final bool isLoading;

  /// Error message
  final String? errorMessage;

  /// Whether data is from cache
  final bool fromCache;

  /// Cache timestamp
  final DateTime? cacheTimestamp;

  /// Constructor
  const AverageState({
    this.recentAverage,
    this.totalAverage,
    this.isLoading = false,
    this.errorMessage,
    this.fromCache = false,
    this.cacheTimestamp,
  });

  /// Initial average state
  factory AverageState.initial() => const AverageState(isLoading: true);

  /// Loading state
  factory AverageState.loading() => const AverageState(isLoading: true);

  /// Error state
  factory AverageState.error(String message) => AverageState(
        isLoading: false,
        errorMessage: message,
      );

  /// Success state
  factory AverageState.success({
    required double? recentAverage,
    required double? totalAverage,
    bool fromCache = false,
    DateTime? cacheTimestamp,
  }) =>
      AverageState(
        recentAverage: recentAverage,
        totalAverage: totalAverage,
        isLoading: false,
        fromCache: fromCache,
        cacheTimestamp: cacheTimestamp,
      );

  /// Create from Average model
  factory AverageState.fromAverage(Average average, {bool fromCache = false}) =>
      AverageState(
        recentAverage: average.recentAverage,
        totalAverage: average.totalAverage,
        isLoading: false,
        fromCache: fromCache,
        cacheTimestamp: average.lastUpdated,
      );

  /// Create a copy with some fields changed
  AverageState copyWith({
    double? recentAverage,
    double? totalAverage,
    bool? isLoading,
    String? errorMessage,
    bool? fromCache,
    DateTime? cacheTimestamp,
  }) {
    return AverageState(
      recentAverage: recentAverage ?? this.recentAverage,
      totalAverage: totalAverage ?? this.totalAverage,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage ?? this.errorMessage,
      fromCache: fromCache ?? this.fromCache,
      cacheTimestamp: cacheTimestamp ?? this.cacheTimestamp,
    );
  }

  /// Check if state has valid averages
  bool get hasValidAverages =>
      recentAverage != null && totalAverage != null && !isLoading;

  /// Check if state has error
  bool get hasError => errorMessage != null;

  /// Check if cache is valid (less than 5 minutes old)
  bool get isCacheValid {
    if (cacheTimestamp == null) return false;
    final now = DateTime.now();
    final ageInMinutes = now.difference(cacheTimestamp!).inMinutes;
    return ageInMinutes < 5;
  }

  @override
  String toString() {
    return 'AverageState(recentAverage: $recentAverage, totalAverage: $totalAverage, isLoading: $isLoading, errorMessage: $errorMessage, fromCache: $fromCache, cacheTimestamp: $cacheTimestamp)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AverageState &&
        other.recentAverage == recentAverage &&
        other.totalAverage == totalAverage &&
        other.isLoading == isLoading &&
        other.errorMessage == errorMessage &&
        other.fromCache == fromCache &&
        other.cacheTimestamp == cacheTimestamp;
  }

  @override
  int get hashCode {
    return Object.hash(
      recentAverage,
      totalAverage,
      isLoading,
      errorMessage,
      fromCache,
      cacheTimestamp,
    );
  }
}
