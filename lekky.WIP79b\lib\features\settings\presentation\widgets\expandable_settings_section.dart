// File: lib/features/settings/presentation/widgets/expandable_settings_section.dart
import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/widgets/app_card.dart';

/// A controller for an [ExpandableSettingsSection].
class SettingsExpansionTileController implements ExpansionTileController {
  _ExpandableSettingsSectionState? _state;

  @override
  void expand() => _state?.expand();

  @override
  void collapse() => _state?.collapse();

  @override
  bool get isExpanded => _state?.isExpanded ?? false;
}

/// An expandable settings section that can be toggled with a switch
class ExpandableSettingsSection extends StatefulWidget {
  /// The title of the section
  final String title;

  /// The icon to display next to the title
  final IconData icon;

  /// The color of the icon
  final Color? iconColor;

  /// The children to display when expanded
  final List<Widget> children;

  /// Whether the section is initially expanded
  final bool initiallyExpanded;

  /// Callback when expansion state changes
  final Function(bool)? onExpansionChanged;

  /// Controller to programmatically control the expansion state
  final ExpansionTileController? controller;

  const ExpandableSettingsSection({
    super.key,
    required this.title,
    required this.icon,
    this.iconColor,
    required this.children,
    this.initiallyExpanded = false,
    this.onExpansionChanged,
    this.controller,
  });

  @override
  State<ExpandableSettingsSection> createState() =>
      _ExpandableSettingsSectionState();
}

class _ExpandableSettingsSectionState extends State<ExpandableSettingsSection>
    with SingleTickerProviderStateMixin
    implements ExpansionTileController {
  late bool _isExpanded;
  late AnimationController _animationController;
  late Animation<double> _expandAnimation;

  @override
  void initState() {
    super.initState();
    _isExpanded = widget.initiallyExpanded;

    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    _expandAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );

    if (_isExpanded) {
      _animationController.value = 1.0;
    }

    // Register with controller if provided
    if (widget.controller != null) {
      final controller = widget.controller!;
      if (controller is SettingsExpansionTileController) {
        controller._state = this;
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    // When disposed (leaving settings page), reset to default state
    _isExpanded = widget.initiallyExpanded;

    // Unregister from controller if provided
    if (widget.controller != null) {
      final controller = widget.controller!;
      if (controller is SettingsExpansionTileController) {
        controller._state = null;
      }
    }

    super.dispose();
  }

  // Method to force close the section
  void forceClose() {
    if (_isExpanded) {
      setState(() {
        _isExpanded = false;
        _animationController.reverse();
      });
    }
  }

  void _toggleExpanded() {
    setState(() {
      _isExpanded = !_isExpanded;
      if (_isExpanded) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }

      // Call the callback if provided
      if (widget.onExpansionChanged != null) {
        widget.onExpansionChanged!(_isExpanded);
      }
    });
  }

  // ExpansionTileController implementation
  @override
  void expand() {
    if (!_isExpanded) {
      _toggleExpanded();
    }
  }

  @override
  void collapse() {
    if (_isExpanded) {
      _toggleExpanded();
    }
  }

  @override
  bool get isExpanded => _isExpanded;

  @override
  Widget build(BuildContext context) {
    final primaryColor = Theme.of(context).colorScheme.primary;

    return AppCard(
      margin: const EdgeInsets.symmetric(vertical: 4.0), // Reduced by 25%
      padding: const EdgeInsets.all(0), // Override default padding
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with switch - LARGER for parent menu
          InkWell(
            onTap: _toggleExpanded,
            child: Padding(
              padding: const EdgeInsets.symmetric(
                  horizontal: 12.0,
                  vertical: 10.0), // INCREASED for parent menu
              child: Row(
                children: [
                  Icon(
                    widget.icon,
                    color: widget.iconColor ?? primaryColor,
                    size: 22, // INCREASED for parent menu
                  ),
                  const SizedBox(width: 10), // INCREASED for parent menu
                  Expanded(
                    child: Text(
                      widget.title,
                      style: const TextStyle(
                          fontSize: 16, // INCREASED for parent menu
                          fontWeight: FontWeight.bold),
                    ),
                  ),
                  // LARGER switch for parent menu
                  Switch(
                    value: _isExpanded,
                    onChanged: (_) => _toggleExpanded(),
                    activeColor: primaryColor,
                    materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                ],
              ),
            ),
          ),

          // Expandable content
          AnimatedSize(
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
            child: SizeTransition(
              sizeFactor: _expandAnimation,
              child: _isExpanded
                  ? Padding(
                      padding: const EdgeInsets.fromLTRB(
                          10.0, 0.0, 10.0, 9.0), // Reduced by 25%
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min, // Ensure minimum height
                        children: widget.children,
                      ),
                    )
                  : const SizedBox.shrink(),
            ),
          ),
        ],
      ),
    );
  }
}
