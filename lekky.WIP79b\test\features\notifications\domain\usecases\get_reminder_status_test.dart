// File: test/features/notifications/domain/usecases/get_reminder_status_test.dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:lekky/core/platform/permissions/permission_adapter.dart';
import 'package:lekky/features/notifications/domain/models/reminder_time_model.dart';
import 'package:lekky/features/notifications/domain/repositories/notification_repository.dart';
import 'package:lekky/features/notifications/domain/usecases/get_reminder_status.dart';

// Mock implementation of NotificationRepository
class MockNotificationRepository implements NotificationRepository {
  bool _remindersEnabled = true;
  bool _notificationsEnabled = true;
  int _reminderFrequency = 30;
  ReminderTimeModel _reminderTime = ReminderTimeModel(
    timeOfDay: TimeOfDay(hour: 9, minute: 0),
  );
  DateTime? _lastReminderDate;
  DateTime? _nextReminderDate = DateTime.now().add(Duration(days: 7));
  PermissionStatus _permissionStatus = PermissionStatus.granted;
  String _currentTimezone = 'Europe/London';

  void setRemindersEnabled(bool enabled) {
    _remindersEnabled = enabled;
  }

  void setNotificationsEnabledSync(bool enabled) {
    _notificationsEnabled = enabled;
  }

  void setPermissionStatus(PermissionStatus status) {
    _permissionStatus = status;
  }

  void setReminderFrequency(int days) {
    _reminderFrequency = days;
  }

  void setReminderTime(ReminderTimeModel time) {
    _reminderTime = time;
  }

  void setNextReminderDate(DateTime? date) {
    _nextReminderDate = date;
  }

  void setCurrentTimezone(String timezone) {
    _currentTimezone = timezone;
  }

  @override
  Future<bool> areMeterReadingRemindersEnabled() async {
    return _remindersEnabled;
  }

  @override
  Future<bool> areNotificationsEnabled() async {
    return _notificationsEnabled;
  }

  @override
  Future<PermissionStatus> getPermissionStatus() async {
    return _permissionStatus;
  }

  @override
  Future<int> getMeterReadingReminderFrequency() async {
    return _reminderFrequency;
  }

  @override
  Future<ReminderTimeModel> getMeterReadingReminderTime() async {
    return _reminderTime;
  }

  @override
  Future<DateTime?> getLastMeterReadingReminderDate() async {
    return _lastReminderDate;
  }

  @override
  Future<DateTime?> getNextMeterReadingReminderDate() async {
    return _nextReminderDate;
  }

  @override
  Future<void> setNotificationsEnabled(bool enabled) async {
    _notificationsEnabled = enabled;
  }

  // Additional methods needed for timezone handling
  String getCurrentTimezone() {
    return _currentTimezone;
  }

  Map<String, dynamic> getTimezoneInfo(DateTime dateTime) {
    return {
      'timezone': _currentTimezone,
      'offset': 0.0,
      'abbreviation': 'GMT',
      'isDST': false,
    };
  }

  // Stub implementations of other required methods
  @override
  noSuchMethod(Invocation invocation) => super.noSuchMethod(invocation);
}

// Special mock that throws exceptions for testing error handling
class MockThrowingRepository extends MockNotificationRepository {
  @override
  Future<bool> areMeterReadingRemindersEnabled() async {
    throw Exception('Test exception');
  }
}

void main() {
  late GetReminderStatus useCase;
  late MockNotificationRepository mockRepository;

  setUp(() {
    mockRepository = MockNotificationRepository();
    useCase = GetReminderStatus(mockRepository);
  });

  group('GetReminderStatus', () {
    test('should get active reminder status when all conditions are met',
        () async {
      // Arrange
      mockRepository.setRemindersEnabled(true);
      mockRepository.setNotificationsEnabledSync(true);
      mockRepository.setPermissionStatus(PermissionStatus.granted);
      mockRepository.setNextReminderDate(DateTime.now().add(Duration(days: 7)));

      // Act
      final status = await useCase.execute();

      // Assert
      expect(status.isEnabled, true);
      expect(status.nextReminderDate, isNotNull);
      expect(status.arePermissionsGranted, true);
      expect(status.frequency, 30);
      expect(status.isFullyFunctional, true);
      expect(status.getStatusDescription(), contains('enabled'));
    });

    test('should get inactive status when reminders are disabled', () async {
      // Arrange
      mockRepository.setRemindersEnabled(false);
      mockRepository.setNotificationsEnabledSync(true);
      mockRepository.setPermissionStatus(PermissionStatus.granted);

      // Act
      final status = await useCase.execute();

      // Assert
      expect(status.isEnabled, false);
      expect(status.isFullyFunctional, false);
      expect(status.getStatusDescription(), contains('disabled'));
    });

    test('should get inactive status when notifications are disabled',
        () async {
      // Arrange
      mockRepository.setRemindersEnabled(true);
      mockRepository.setNotificationsEnabledSync(false);
      mockRepository.setPermissionStatus(PermissionStatus.granted);

      // Act
      final status = await useCase.execute();

      // Assert
      expect(status.areNotificationsEnabled, false);
      expect(status.isFullyFunctional, false);
      expect(status.getStatusDescription(), contains('Notifications'));
    });

    test('should get inactive status when permission is denied', () async {
      // Arrange
      mockRepository.setRemindersEnabled(true);
      mockRepository.setNotificationsEnabledSync(true);
      mockRepository.setPermissionStatus(PermissionStatus.denied);

      // Act
      final status = await useCase.execute();

      // Assert
      expect(status.arePermissionsGranted, false);
      expect(status.isFullyFunctional, false);
      expect(status.getStatusDescription(), contains('permission'));
    });

    test('should include reminder details in status', () async {
      // Arrange
      final reminderTime = ReminderTimeModel(
        timeOfDay: TimeOfDay(hour: 15, minute: 30),
      );
      final nextReminderDate = DateTime(2025, 6, 1, 15, 30);

      mockRepository.setReminderTime(reminderTime);
      mockRepository.setReminderFrequency(14);
      mockRepository.setNextReminderDate(nextReminderDate);

      // Act
      final status = await useCase.execute();

      // Assert
      expect(status.frequency, 14);
      expect(status.reminderTime.timeOfDay.hour, 15);
      expect(status.reminderTime.timeOfDay.minute, 30);
      expect(status.nextReminderDate, nextReminderDate);
    });

    test('should include timezone information', () async {
      // Arrange
      mockRepository.setCurrentTimezone('Europe/London');

      // Act
      final status = await useCase.execute();

      // Assert
      expect(status.currentTimezone, 'Europe/London');
      expect(status.getTimezoneStatusDescription(), contains('Europe/London'));
    });

    test('should handle exceptions gracefully', () async {
      // Arrange
      final throwingRepository = MockThrowingRepository();
      final useCaseWithException = GetReminderStatus(throwingRepository);

      // Act & Assert
      expect(() => useCaseWithException.execute(), throwsException);
    });
  });
}
