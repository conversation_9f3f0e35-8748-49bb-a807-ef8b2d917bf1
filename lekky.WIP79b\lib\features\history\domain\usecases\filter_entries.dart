// File: lib/features/history/domain/usecases/filter_entries.dart
import '../../../../core/models/meter_entry.dart';
import '../../../../core/utils/date_time_utils.dart';
import '../models/history_filter.dart';

/// Use case for filtering meter entries
class FilterEntries {
  /// Execute the use case
  List<MeterEntry> execute(List<MeterEntry> entries, HistoryFilter filter) {
    // Create a copy of the entries list to avoid modifying the original
    List<MeterEntry> filteredEntries = List.from(entries);

    // Filter by date range
    if (filter.startDate != null) {
      // Use startOfDay to normalize the start date to beginning of day (00:00:00)
      final startOfDay = DateTimeUtils.startOfDay(filter.startDate!);

      filteredEntries = filteredEntries.where((entry) {
        return entry.timestamp.isAfter(startOfDay) ||
            entry.timestamp.isAtSameMomentAs(startOfDay);
      }).toList();
    }

    if (filter.endDate != null) {
      // Use endOfDay to normalize the end date to end of day (23:59:59)
      final endOfDay = DateTimeUtils.endOfDay(filter.endDate!);

      filteredEntries = filteredEntries.where((entry) {
        return entry.timestamp.isBefore(endOfDay) ||
            entry.timestamp.isAtSameMomentAs(endOfDay);
      }).toList();
    }

    // Filter by entry type
    if (!filter.showMeterReadings) {
      filteredEntries = filteredEntries.where((entry) {
        return entry.amountToppedUp > 0;
      }).toList();
    }

    if (!filter.showTopUps) {
      filteredEntries = filteredEntries.where((entry) {
        return entry.amountToppedUp == 0;
      }).toList();
    }

    // Sort entries
    if (filter.sortBy == 'date') {
      filteredEntries.sort((a, b) {
        return filter.sortAscending
            ? a.timestamp.compareTo(b.timestamp)
            : b.timestamp.compareTo(a.timestamp);
      });
    } else if (filter.sortBy == 'amount') {
      filteredEntries.sort((a, b) {
        final aValue = a.amountToppedUp > 0 ? a.amountToppedUp : a.reading;
        final bValue = b.amountToppedUp > 0 ? b.amountToppedUp : b.reading;
        return filter.sortAscending
            ? aValue.compareTo(bValue)
            : bValue.compareTo(aValue);
      });
    }

    return filteredEntries;
  }
}
