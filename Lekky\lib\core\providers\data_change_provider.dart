import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../utils/logger.dart';

part 'data_change_provider.g.dart';

/// Provider that tracks data changes to trigger reactive updates
@riverpod
class DataChangeNotifier extends _$DataChangeNotifier {
  @override
  int build() {
    Logger.info('DataChangeNotifier: Initialized with value 0');
    return 0;
  }

  /// Notify that data has changed (meter readings, top-ups, etc.)
  void notifyDataChanged() {
    Logger.info('DataChangeNotifier: Data changed, incrementing from $state to ${state + 1}');
    state = state + 1;
  }

  /// Reset the change counter (for testing purposes)
  void reset() {
    Logger.info('DataChangeNotifier: Resetting counter to 0');
    state = 0;
  }

  /// Get current change count
  int get changeCount => state;
}
