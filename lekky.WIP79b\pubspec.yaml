name: lekky
description: "A new Flutter project."
publish_to: 'none'

version: 1.0.1+1

environment:
  sdk: '>=3.3.3 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  shared_preferences: ^2.2.0
  sqflite: ^2.3.0
  flutter_local_notifications: ^17.2.3
  path_provider: ^2.1.0
  file_picker: ^8.0.0
  cupertino_icons: ^1.0.6
  intl: ^0.18.1
  provider: ^6.1.2
  permission_handler: ^11.3.0
  timezone: ^0.9.2
  csv: ^5.1.1
  url_launcher: ^6.2.5
  fl_chart: ^0.66.2
  get_it: ^7.6.7
  flutter_paypal: ^0.2.0
  table_calendar: ^3.0.9

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_launcher_icons: ^0.13.1
  flutter_lints: ^3.0.0
  mockito: ^5.4.4
  build_runner: ^2.4.8
  plugin_platform_interface: ^2.1.8

flutter:
  uses-material-design: true
  generate: true
  assets:
    - assets/icon.png
    - assets/background.png
    - assets/splash.png
    - assets/dark_mode_background.png

flutter_launcher_icons:
  android: true
  ios: true
  image_path: "assets/icon.png"
  min_sdk_android: 21