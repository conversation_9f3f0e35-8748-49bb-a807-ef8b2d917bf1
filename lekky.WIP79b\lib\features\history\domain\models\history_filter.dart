// File: lib/features/history/domain/models/history_filter.dart

/// Filter options for the history screen
class HistoryFilter {
  final DateTime? startDate;
  final DateTime? endDate;
  final bool showMeterReadings;
  final bool showTopUps;
  final bool showInvalidEntries;
  final String? sortBy;
  final bool sortAscending;
  final bool
      isInvalidFilter; // Flag to indicate if this is specifically an invalid entries filter

  const HistoryFilter({
    this.startDate,
    this.endDate,
    this.showMeterReadings = true,
    this.showTopUps = true,
    this.showInvalidEntries = true,
    this.sortBy = 'date',
    this.sortAscending = false,
    this.isInvalidFilter = false, // Default to false
  });

  /// Creates a copy of this HistoryFilter with the given fields replaced with the new values
  HistoryFilter copyWith({
    DateTime? startDate,
    DateTime? endDate,
    bool? showMeterReadings,
    bool? showTopUps,
    bool? showInvalidEntries,
    String? sortBy,
    bool? sortAscending,
    bool? isInvalidFilter,
  }) {
    return HistoryFilter(
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      showMeterReadings: showMeterReadings ?? this.showMeterReadings,
      showTopUps: showTopUps ?? this.showTopUps,
      showInvalidEntries: showInvalidEntries ?? this.showInvalidEntries,
      sortBy: sortBy ?? this.sortBy,
      sortAscending: sortAscending ?? this.sortAscending,
      isInvalidFilter: isInvalidFilter ?? this.isInvalidFilter,
    );
  }

  /// Reset the filter to default values
  HistoryFilter reset() {
    return const HistoryFilter(
      // Explicitly set all values to defaults
      startDate: null,
      endDate: null,
      showMeterReadings: true,
      showTopUps: true,
      showInvalidEntries: true,
      sortBy: 'date',
      sortAscending: false,
      isInvalidFilter: false, // Ensure invalid filter flag is reset
    );
  }
}
