// File: lib/features/notifications/data/migration/storage_migration_service.dart

import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../domain/usecases/migrate_notification_storage.dart';
import '../../presentation/providers/notification_provider.dart';
import '../../presentation/providers/notification_provider_extensions.dart';

/// Service for managing the migration from SharedPreferences to SQLite
class StorageMigrationService {
  final MigrateNotificationStorage _migrateStorageUseCase;
  final NotificationProvider _notificationProvider;

  // Key for storing migration status
  static const String _migrationAttemptedKey =
      'notification_migration_attempted';
  static const String _migrationCompletedKey =
      'notification_migration_completed';

  StorageMigrationService({
    required MigrateNotificationStorage migrateStorageUseCase,
    required NotificationProvider notificationProvider,
  })  : _migrateStorageUseCase = migrateStorageUseCase,
        _notificationProvider = notificationProvider;

  /// Check if migration is needed
  Future<bool> isMigrationNeeded() async {
    // Check if migration is already completed
    final isCompleted = await _migrateStorageUseCase.isMigrationCompleted();
    if (isCompleted) {
      return false;
    }

    // Check if migration has been attempted before
    final prefs = await SharedPreferences.getInstance();
    final hasAttempted = prefs.getBool(_migrationAttemptedKey) ?? false;

    // If migration has been attempted but not completed, we need to try again
    return !hasAttempted || !isCompleted;
  }

  /// Execute the migration
  Future<bool> executeMigration({
    Function(double progress)? onProgress,
    Function(String)? onStatusUpdate,
  }) async {
    try {
      // Mark migration as attempted
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_migrationAttemptedKey, true);

      // Check if migration is already completed
      final isAlreadyMigrated =
          await _migrateStorageUseCase.isMigrationCompleted();
      if (isAlreadyMigrated) {
        onStatusUpdate?.call('Migration already completed');
        return true;
      }

      onStatusUpdate?.call('Starting migration...');

      // Execute migration
      final result = await _notificationProvider.migrateStorageToSQLite(
        migrateStorageUseCase: _migrateStorageUseCase,
        onProgress: (progress) {
          onProgress?.call(progress);

          // Provide status updates based on progress
          if (progress < 0.3) {
            onStatusUpdate?.call('Preparing data for migration...');
          } else if (progress < 0.6) {
            onStatusUpdate?.call('Transferring notification data...');
          } else if (progress < 0.9) {
            onStatusUpdate?.call('Transferring settings data...');
          } else {
            onStatusUpdate?.call('Finalizing migration...');
          }
        },
      );

      // Mark migration as completed if successful
      if (result) {
        await prefs.setBool(_migrationCompletedKey, true);
        onStatusUpdate?.call('Migration completed successfully');
      } else {
        onStatusUpdate?.call('Migration failed');
      }

      return result;
    } catch (e) {
      debugPrint('Error during migration: $e');
      onStatusUpdate?.call('Error during migration: $e');
      return false;
    }
  }

  /// Get the current storage type
  Future<String> getCurrentStorageType() async {
    return await _notificationProvider.getCurrentStorageType(
      migrateStorageUseCase: _migrateStorageUseCase,
    );
  }
}
