// File: lib/core/settings/validators/settings_validator.dart

/// A utility class for validating settings inputs
class SettingsValidator {
  /// Validate a threshold value
  static Map<String, dynamic> validateThreshold(String value) {
    if (value.isEmpty) {
      return {
        'isValid': false,
        'errorMessage': 'Threshold cannot be empty',
      };
    }

    final threshold = double.tryParse(value);
    if (threshold == null) {
      return {
        'isValid': false,
        'errorMessage': 'Please enter a valid number',
      };
    }

    if (threshold < 1) {
      return {
        'isValid': false,
        'errorMessage': 'Threshold must be at least 1',
      };
    }

    if (threshold > 999) {
      return {
        'isValid': false,
        'errorMessage': 'Threshold must be 999 or less',
      };
    }

    // Check if it has more than 2 decimal places
    final decimalPlaces = value.contains('.') ? value.split('.')[1].length : 0;
    if (decimalPlaces > 2) {
      return {
        'isValid': false,
        'errorMessage': 'Maximum 2 decimal places allowed',
      };
    }

    return {
      'isValid': true,
      'errorMessage': null,
    };
  }

  /// Validate days in advance
  static Map<String, dynamic> validateDaysInAdvance(String value) {
    if (value.isEmpty) {
      return {
        'isValid': false,
        'errorMessage': 'Days cannot be empty',
      };
    }

    final days = int.tryParse(value);
    if (days == null) {
      return {
        'isValid': false,
        'errorMessage': 'Please enter a valid number',
      };
    }

    if (days < 1) {
      return {
        'isValid': false,
        'errorMessage': 'Days must be at least 1',
      };
    }

    if (days > 99) {
      return {
        'isValid': false,
        'errorMessage': 'Days must be 99 or less',
      };
    }

    return {
      'isValid': true,
      'errorMessage': null,
    };
  }

  /// Validate meter unit
  static Map<String, dynamic> validateMeterUnit(String value) {
    if (value.isEmpty) {
      return {
        'isValid': false,
        'errorMessage': 'Currency symbol cannot be empty',
      };
    }

    if (value.length > 4) {
      return {
        'isValid': false,
        'errorMessage': 'Currency symbol must be 4 characters or less',
      };
    }

    return {
      'isValid': true,
      'errorMessage': null,
    };
  }

  /// Validate initial meter credit
  static Map<String, dynamic> validateInitialCredit(String value) {
    if (value.isEmpty) {
      // Empty is valid for initial credit (it's optional)
      return {
        'isValid': true,
        'errorMessage': null,
      };
    }

    final credit = double.tryParse(value);
    if (credit == null) {
      return {
        'isValid': false,
        'errorMessage': 'Please enter a valid number',
      };
    }

    // Initial credit can be any value (positive or negative)
    // But we should check for decimal places
    final decimalPlaces = value.contains('.') ? value.split('.')[1].length : 0;
    if (decimalPlaces > 2) {
      return {
        'isValid': false,
        'errorMessage': 'Maximum 2 decimal places allowed',
      };
    }

    return {
      'isValid': true,
      'errorMessage': null,
    };
  }
}
