// File: lib/features/notifications/presentation/providers/notification_provider.dart

import 'dart:async';
import 'package:flutter/material.dart';
import '../../../../core/platform/permissions/permission_adapter.dart';
import '../../../../core/platform/timezone/timezone_adapter_factory.dart';
import '../../../../core/platform/timezone/timezone_adapter.dart';
import '../../../../core/platform/timezone/timezone_change_service.dart';
import '../../domain/models/notification_model.dart';
import '../../domain/models/reminder_time_model.dart';
import '../../domain/models/timezone_aware_reminder_time.dart';
import '../../domain/repositories/notification_repository.dart';
import '../../domain/usecases/get_reminder_status.dart';
import '../../domain/usecases/schedule_meter_reading_reminder.dart';
import '../widgets/permission_education_dialog.dart';
import '../widgets/permission_denied_dialog.dart';

/// Provider for managing notifications in the app
class NotificationProvider extends ChangeNotifier {
  final NotificationRepository _repository;
  final GetReminderStatus _getReminderStatus;
  final ScheduleMeterReadingReminder _scheduleMeterReadingReminder;

  // Timezone adapter for timezone operations
  late TimezoneAdapter _timezoneAdapter;

  List<NotificationModel> _notifications = [];
  bool _isInitialized = false;
  ReminderStatus? _reminderStatus;
  PermissionStatus _permissionStatus = PermissionStatus.unknown;
  StreamSubscription<PermissionStatus>? _permissionStatusSubscription;

  // Timezone change handling
  bool _processingTimezoneChange = false;

  NotificationProvider({
    required NotificationRepository repository,
  })  : _repository = repository,
        _getReminderStatus = GetReminderStatus(repository),
        _scheduleMeterReadingReminder =
            ScheduleMeterReadingReminder(repository);

  /// Get all notifications
  List<NotificationModel> get notifications =>
      List.unmodifiable(_notifications);

  /// Get unread notifications count
  int get unreadCount => _notifications.where((n) => !n.isRead).length;

  /// Check if there are any unread notifications
  bool get hasUnread => _notifications.any((n) => !n.isRead);

  /// Get the current reminder status
  ReminderStatus? get reminderStatus => _reminderStatus;

  /// Get the current permission status
  PermissionStatus get permissionStatus => _permissionStatus;

  /// Initialize the provider
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Initialize the repository
      await _repository.initialize();

      // Initialize timezone adapter
      _timezoneAdapter = TimezoneAdapterFactory.create();
      await _timezoneAdapter.initialize();

      // Initialize timezone change service
      await TimezoneChangeService.initialize();
      await TimezoneChangeService.startMonitoring();

      // Register for timezone change notifications
      TimezoneChangeService.addTimezoneChangeListener(_onTimezoneChanged);

      // Load existing notifications
      await _loadNotifications();

      // Check if time zone has changed since last app run
      final timeZoneChanged = await _repository.hasTimeZoneChanged();
      if (timeZoneChanged) {
        await _handleTimezoneChange();
      }

      // Load reminder status
      await _loadReminderStatus();

      // Get initial permission status
      _permissionStatus = await _repository.getPermissionStatus();

      // Subscribe to permission status changes
      _permissionStatusSubscription =
          _repository.getPermissionStatusStream().listen((status) {
        _permissionStatus = status;
        // Reload reminder status when permission status changes
        _loadReminderStatus();
        notifyListeners();
      });

      // Check if reminders are enabled and reschedule if needed
      if (_reminderStatus?.isEnabled == true) {
        await _scheduleMeterReadingReminder.executeWithReschedule();
      }

      _isInitialized = true;
    } catch (e) {
      // Handle initialization error
      debugPrint('Error initializing NotificationProvider: $e');
    }
  }

  /// Load notifications from the repository
  Future<void> _loadNotifications() async {
    _notifications = await _repository.getNotifications();
    notifyListeners();
  }

  /// Load reminder status from the repository
  Future<void> _loadReminderStatus() async {
    _reminderStatus = await _getReminderStatus.execute();
    notifyListeners();
  }

  /// Add a notification
  Future<void> addNotification({
    required String title,
    required String message,
    required NotificationPriority priority,
    String? actionType,
  }) async {
    await _repository.addNotification(
      title: title,
      message: message,
      priority: priority,
      actionType: actionType,
    );
    await _loadNotifications();
  }

  /// Mark a notification as read
  Future<void> markAsRead(String id) async {
    await _repository.markAsRead(id);
    await _loadNotifications();
  }

  /// Mark all notifications as read
  Future<void> markAllAsRead() async {
    await _repository.markAllAsRead();
    await _loadNotifications();
  }

  /// Remove a notification
  Future<void> removeNotification(String id) async {
    await _repository.removeNotification(id);
    await _loadNotifications();
  }

  /// Clear all notifications
  Future<void> clearAll() async {
    await _repository.clearAll();
    await _loadNotifications();
  }

  /// Refresh notifications
  Future<void> refresh() async {
    await _loadNotifications();
    await _loadReminderStatus();
  }

  /// Check if notifications are enabled
  Future<bool> areNotificationsEnabled() async {
    return await _repository.areNotificationsEnabled();
  }

  /// Enable or disable notifications
  Future<void> setNotificationsEnabled(bool enabled) async {
    await _repository.setNotificationsEnabled(enabled);
    await _loadReminderStatus();
    notifyListeners();
  }

  /// Check if meter reading reminders are enabled
  Future<bool> areMeterReadingRemindersEnabled() async {
    return await _repository.areMeterReadingRemindersEnabled();
  }

  /// Enable or disable meter reading reminders
  Future<void> setMeterReadingRemindersEnabled(bool enabled) async {
    await _repository.setMeterReadingRemindersEnabled(enabled);
    await _loadReminderStatus();
    notifyListeners();
  }

  /// Get the meter reading reminder frequency in days
  Future<int> getMeterReadingReminderFrequency() async {
    return await _repository.getMeterReadingReminderFrequency();
  }

  /// Set the meter reading reminder frequency in days
  Future<void> setMeterReadingReminderFrequency(int days) async {
    await _repository.setMeterReadingReminderFrequency(days);
    await _loadReminderStatus();
    notifyListeners();
  }

  /// Get the last date a meter reading reminder was shown
  Future<DateTime?> getLastMeterReadingReminderDate() async {
    return await _repository.getLastMeterReadingReminderDate();
  }

  /// Get the time of day for meter reading reminders
  Future<ReminderTimeModel> getMeterReadingReminderTime() async {
    return await _repository.getMeterReadingReminderTime();
  }

  /// Set the time of day for meter reading reminders
  Future<void> setMeterReadingReminderTime(ReminderTimeModel time) async {
    await _repository.setMeterReadingReminderTime(time);

    // Also save as timezone-aware reminder time
    final repo =
        _repository as dynamic; // Cast to access the additional methods
    try {
      final currentTimezone = _timezoneAdapter.getCurrentTimezone();
      final timezoneAwareTime = TimezoneAwareReminderTime.fromReminderTimeModel(
        model: time,
        currentTimezone: currentTimezone,
      );
      await repo.setTimezoneAwareReminderTime(timezoneAwareTime);
    } catch (e) {
      debugPrint('Error saving timezone-aware reminder time: $e');
    }

    await _loadReminderStatus();
    notifyListeners();
  }

  /// Set timezone preference for reminders
  Future<void> setTimezoneAdjustmentPreference(
      bool adjustForTimezoneChanges) async {
    try {
      final repo =
          _repository as dynamic; // Cast to access the additional methods
      final timezoneAwareTime = await repo.getTimezoneAwareReminderTime();

      if (timezoneAwareTime != null) {
        final updatedTime = timezoneAwareTime.copyWith(
          adjustForTimezoneChanges: adjustForTimezoneChanges,
        );
        await repo.setTimezoneAwareReminderTime(updatedTime);
        await _loadReminderStatus();
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error setting timezone adjustment preference: $e');
    }
  }

  /// Schedule a meter reading reminder
  Future<bool> scheduleMeterReadingReminder() async {
    final result = await _scheduleMeterReadingReminder.execute();
    await _loadReminderStatus();
    return result;
  }

  /// Reschedule meter reading reminders
  Future<bool> rescheduleMeterReadingReminders(
      {bool forceReschedule = false}) async {
    final result = await _scheduleMeterReadingReminder.executeWithReschedule(
      forceReschedule: forceReschedule,
    );
    await _loadReminderStatus();
    return result;
  }

  /// Get the next scheduled meter reading reminder date
  Future<DateTime?> getNextMeterReadingReminderDate() async {
    return await _repository.getNextMeterReadingReminderDate();
  }

  /// Show a meter reading reminder notification immediately
  Future<void> showMeterReadingReminderNotification() async {
    await _repository.showMeterReadingReminderNotification();
    await _loadReminderStatus();
  }

  /// Get the current permission status
  Future<PermissionStatus> getPermissionStatus() async {
    final status = await _repository.getPermissionStatus();
    _permissionStatus = status;
    notifyListeners();
    return status;
  }

  /// Open the app settings page
  Future<bool> openAppSettings() async {
    return await _repository.openAppSettings();
  }

  /// Request notification permission with educational UI
  Future<bool> requestPermissionWithEducation(BuildContext context) async {
    // First check if notifications are enabled in the app
    final isEnabled = await areNotificationsEnabled();
    if (!isEnabled) {
      await setNotificationsEnabled(true);
    }

    // Check current permission status
    final currentStatus = await getPermissionStatus();
    if (currentStatus == PermissionStatus.granted) {
      return true;
    }

    // Show educational UI before requesting permission
    final shouldProceed = await showPermissionEducationDialog(context);
    if (!shouldProceed) {
      return false;
    }

    // Request permission
    final permissionGranted =
        await _repository.getPermissionStatus() == PermissionStatus.granted;

    // If permission was denied, show alternatives
    if (!permissionGranted) {
      final isPermanentlyDenied = await isPermissionPermanentlyDenied();
      final alternative = await showPermissionDeniedDialog(
        context,
        isPermanentlyDenied: isPermanentlyDenied,
      );

      // Handle the selected alternative
      if (alternative != null) {
        await _handlePermissionAlternative(context, alternative);
      }
    }

    return permissionGranted;
  }

  /// Handle the selected permission alternative
  Future<void> _handlePermissionAlternative(
      BuildContext context, PermissionAlternative alternative) async {
    switch (alternative) {
      case PermissionAlternative.openSettings:
        await openAppSettings();
        break;
      case PermissionAlternative.calendarReminders:
        // Implementation would depend on platform-specific calendar integration
        // For now, we'll just show a snackbar
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
              content: Text('Calendar reminders will be implemented soon')),
        );
        break;
      case PermissionAlternative.inAppReminders:
        // Enable in-app reminders only
        await setNotificationsEnabled(false);
        await setMeterReadingRemindersEnabled(true);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('In-app reminders enabled')),
        );
        break;
      case PermissionAlternative.skip:
        // Disable reminders
        await setMeterReadingRemindersEnabled(false);
        break;
    }
  }

  /// Get a string description of the current permission status
  String getPermissionStatusDescription() {
    switch (_permissionStatus) {
      case PermissionStatus.granted:
        return 'Notification permissions are granted';
      case PermissionStatus.denied:
        return 'Notification permissions are denied';
      case PermissionStatus.permanentlyDenied:
        return 'Notification permissions are permanently denied';
      case PermissionStatus.restricted:
        return 'Notification permissions are restricted';
      case PermissionStatus.limited:
        return 'Notification permissions are limited';
      case PermissionStatus.unknown:
        return 'Notification permission status is unknown';
    }
  }

  /// Check if permission is permanently denied
  Future<bool> isPermissionPermanentlyDenied() async {
    return _permissionStatus == PermissionStatus.permanentlyDenied;
  }

  /// Handle timezone change
  Future<void> _handleTimezoneChange() async {
    // Prevent re-entry
    if (_processingTimezoneChange) return;

    _processingTimezoneChange = true;
    try {
      debugPrint('Handling timezone change in provider');

      // Clear the timezone change detection flag
      await TimezoneChangeService.clearTimezoneChangeDetection();

      // Reschedule reminders
      await _scheduleMeterReadingReminder.executeWithReschedule(
          forceReschedule: true);

      // Reload reminder status
      await _loadReminderStatus();
      notifyListeners();
    } finally {
      _processingTimezoneChange = false;
    }
  }

  /// Callback for timezone changes
  void _onTimezoneChanged() {
    debugPrint('Timezone change detected in provider');
    _handleTimezoneChange();
  }

  /// Get current timezone information
  Map<String, dynamic> getTimezoneInfo() {
    try {
      final repo =
          _repository as dynamic; // Cast to access the additional methods
      return repo.getTimezoneInfo(DateTime.now());
    } catch (e) {
      debugPrint('Error getting timezone info: $e');
      return {
        'timezone': _timezoneAdapter.getCurrentTimezone(),
        'offset': _timezoneAdapter.getTimezoneOffset(DateTime.now()),
        'isDST': _timezoneAdapter.isInDaylightSavingTime(DateTime.now()),
      };
    }
  }

  /// Check if device is in a different timezone than when reminders were set
  Future<bool> isInDifferentTimezone() async {
    try {
      final repo =
          _repository as dynamic; // Cast to access the additional methods
      final timezoneAwareTime = await repo.getTimezoneAwareReminderTime();

      if (timezoneAwareTime != null) {
        final currentTimezone = _timezoneAdapter.getCurrentTimezone();
        return timezoneAwareTime.creationTimezone != currentTimezone;
      }
    } catch (e) {
      debugPrint('Error checking if in different timezone: $e');
    }
    return false;
  }

  /// Dispose of resources
  @override
  void dispose() {
    _permissionStatusSubscription?.cancel();
    TimezoneChangeService.removeTimezoneChangeListener(_onTimezoneChanged);
    TimezoneChangeService.dispose();
    _repository.dispose();
    super.dispose();
  }
}
