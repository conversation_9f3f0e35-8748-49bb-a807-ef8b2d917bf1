// File: lib/core/constants/preference_keys.dart
/// Constants for shared preference keys used across the app
class PreferenceKeys {
  // Private constructor to prevent instantiation
  PreferenceKeys._();

  // Setup and Settings shared keys

  // Theme settings
  static const String themeMode = 'theme_mode';

  // Date settings
  static const String dateFormat = 'date_format';
  static const String showTimeWithDate = 'show_time_with_date';

  // Alert settings
  static const String alertThreshold = 'alert_threshold';
  static const String daysInAdvance = 'days_in_advance';

  // Notification settings
  static const String notificationsEnabled = 'notifications_enabled';
  static const String lowBalanceAlertsEnabled = 'low_balance_alerts_enabled';
  static const String timeToTopUpAlertsEnabled =
      'time_to_top_up_alerts_enabled';
  static const String invalidRecordAlertsEnabled =
      'invalid_record_alerts_enabled';

  // Reminder settings
  static const String reminderFrequency = 'reminder_frequency';
  static const String reminderTime = 'reminder_time';
  static const String remindersEnabled = 'reminders_enabled';
  static const String reminderStartDateTime = 'reminder_start_date_time';

  // Notification deduplication tracking
  static const String lastLowBalanceNotificationDate =
      'last_low_balance_notification_date';
  static const String lastTimeToTopUpNotificationDate =
      'last_time_to_top_up_notification_date';

  // Region settings
  static const String language = 'language';
  static const String currency = 'currency';
  static const String currencySymbol = 'currency_symbol';

  // Setup specific keys
  static const String initialMeterReading = 'initial_meter_reading';
  static const String setupCompleted = 'setup_completed';
  static const String firstLaunch = 'first_launch';

  // First run flow keys
  static const String isFirstRun = 'is_first_run';
  static const String firstRunCompleted = 'first_run_completed';
  static const String welcomeNotificationTriggered =
      'welcome_notification_triggered';

  // Additional format settings
  static const String use24HourFormat = 'use_24_hour_format';
  static const String showDecimalPlaces = 'show_decimal_places';
  static const String decimalPlaces = 'decimal_places';
}
