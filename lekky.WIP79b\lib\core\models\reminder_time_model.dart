// File: lib/core/models/reminder_time_model.dart
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

/// Model for storing reminder time settings
class ReminderTimeModel {
  final TimeOfDay timeOfDay;
  final DateTime? selectedDate;

  const ReminderTimeModel({
    required this.timeOfDay,
    this.selectedDate,
  });

  /// Create a model from a string representation
  factory ReminderTimeModel.fromString(String timeString) {
    try {
      // Check if the string contains date information
      if (timeString.contains('|')) {
        final parts = timeString.split('|');
        if (parts.length == 2) {
          final timeParts = parts[0].split(':');
          if (timeParts.length == 2) {
            final hour = int.parse(timeParts[0]);
            final minute = int.parse(timeParts[1]);

            // Parse the date
            final date = DateTime.tryParse(parts[1]);

            return ReminderTimeModel(
              timeOfDay: TimeOfDay(hour: hour, minute: minute),
              selectedDate: date,
            );
          }
        }
      } else {
        // Legacy format - just time
        final parts = timeString.split(':');
        if (parts.length == 2) {
          final hour = int.parse(parts[0]);
          final minute = int.parse(parts[1]);
          return ReminderTimeModel(
            timeOfDay: TimeOfDay(hour: hour, minute: minute),
          );
        }
      }
    } catch (e) {
      // If parsing fails, return default time
    }

    // Default to 7:00 PM
    return const ReminderTimeModel(
      timeOfDay: TimeOfDay(hour: 19, minute: 0),
    );
  }

  /// Convert to a string representation for storage
  String toTimeString() {
    final timeStr =
        '${timeOfDay.hour.toString().padLeft(2, '0')}:${timeOfDay.minute.toString().padLeft(2, '0')}';

    if (selectedDate != null) {
      // Include date information
      return '$timeStr|${selectedDate!.toIso8601String()}';
    }

    // Legacy format - just time
    return timeStr;
  }

  /// Format the time for display based on the context (12/24 hour format)
  String format(BuildContext context) {
    final timeStr = timeOfDay.format(context);

    if (selectedDate != null) {
      // Format with day and date
      final dayFormat = DateFormat('EEEE'); // Full day name
      final dateFormat = DateFormat('MMM d'); // Month and day

      final day = dayFormat.format(selectedDate!);
      final date = dateFormat.format(selectedDate!);

      return '$day, $date at $timeStr';
    }

    // Just time
    return timeStr;
  }

  /// Create a copy with updated values
  ReminderTimeModel copyWith({
    TimeOfDay? timeOfDay,
    DateTime? selectedDate,
    bool clearDate = false,
  }) {
    return ReminderTimeModel(
      timeOfDay: timeOfDay ?? this.timeOfDay,
      selectedDate: clearDate ? null : (selectedDate ?? this.selectedDate),
    );
  }
}
