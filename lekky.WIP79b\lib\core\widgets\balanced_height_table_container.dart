// File: lib/core/widgets/balanced_height_table_container.dart
import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';

/// A container widget that ensures balanced spacing above and below its child.
///
/// This widget calculates the appropriate height for its child to ensure
/// equal spacing at the top and bottom of the screen, while coordinating
/// scrolling behavior between the parent and child scrollable widgets.
class BalancedHeightTableContainer extends StatefulWidget {
  /// The child widget to display, typically a scrollable table.
  final Widget child;

  /// The desired spacing above the container.
  final double topSpacing;

  /// The desired spacing below the container.
  final double bottomSpacing;

  /// Optional minimum height for the container.
  final double? minHeight;

  /// Optional maximum height for the container.
  final double? maxHeight;

  /// Whether to coordinate scrolling between parent and child.
  final bool coordinateScrolling;

  /// The scroll controller of the parent scrollable widget.
  /// Required if coordinateScrolling is true.
  final ScrollController? parentScrollController;

  /// The scroll controller to pass to the child widget.
  /// If null, a new controller will be created.
  final ScrollController? childScrollController;

  const BalancedHeightTableContainer({
    super.key,
    required this.child,
    required this.topSpacing,
    required this.bottomSpacing,
    this.minHeight,
    this.maxHeight,
    this.coordinateScrolling = true,
    this.parentScrollController,
    this.childScrollController,
  });

  @override
  State<BalancedHeightTableContainer> createState() =>
      _BalancedHeightTableContainerState();
}

class _BalancedHeightTableContainerState
    extends State<BalancedHeightTableContainer> {
  late ScrollController _childScrollController;
  bool _isHandlingScroll = false;

  @override
  void initState() {
    super.initState();
    _childScrollController = widget.childScrollController ?? ScrollController();

    if (widget.coordinateScrolling && widget.parentScrollController != null) {
      // Add listeners to coordinate scrolling
      _childScrollController.addListener(_handleChildScroll);
    }
  }

  @override
  void dispose() {
    if (widget.childScrollController == null) {
      // Only dispose the controller if we created it
      _childScrollController.dispose();
    } else {
      // Remove listener if we're using an external controller
      _childScrollController.removeListener(_handleChildScroll);
    }
    super.dispose();
  }

  // Handle scroll events from the child to coordinate with parent
  void _handleChildScroll() {
    if (!widget.coordinateScrolling || widget.parentScrollController == null) {
      return;
    }
    if (_isHandlingScroll) {
      return; // Prevent recursive handling
    }

    _isHandlingScroll = true;

    // If child is at the top and user is scrolling up, let parent scroll
    if (_childScrollController.position.pixels <= 0 &&
        _childScrollController.position.userScrollDirection ==
            ScrollDirection.idle) {
      // Allow parent to scroll
    }

    // If child is at the bottom and user is scrolling down, let parent scroll
    if (_childScrollController.position.pixels >=
            _childScrollController.position.maxScrollExtent &&
        _childScrollController.position.userScrollDirection ==
            ScrollDirection.idle) {
      // Allow parent to scroll
    }

    _isHandlingScroll = false;
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Get screen dimensions
        final screenHeight = MediaQuery.of(context).size.height;

        // Calculate available height
        final availableHeight =
            screenHeight - widget.topSpacing - widget.bottomSpacing;

        // Apply min/max constraints
        // Use a height that extends close to the pagination controls
        final minHeight = widget.minHeight ?? (availableHeight * 0.95);
        final maxHeight = widget.maxHeight ?? double.infinity;

        // Calculate the final height
        final calculatedHeight =
            math.min(math.max(minHeight, availableHeight), maxHeight);

        // Create a notification listener to intercept scroll events
        return NotificationListener<ScrollNotification>(
          onNotification: (notification) {
            if (!widget.coordinateScrolling) return false;

            // Handle scroll coordination logic
            if (notification is ScrollEndNotification) {
              // When scrolling ends in the child, check if we need to pass control to parent
              if (_childScrollController.position.pixels <= 0 ||
                  _childScrollController.position.pixels >=
                      _childScrollController.position.maxScrollExtent) {
                // At edge of child scroll, allow parent to scroll
                return false;
              }
            }

            // Prevent parent from scrolling while child is handling scroll
            return _childScrollController.position.pixels > 0 &&
                _childScrollController.position.pixels <
                    _childScrollController.position.maxScrollExtent;
          },
          child: SizedBox(
            height: calculatedHeight,
            width: constraints.maxWidth,
            child: RepaintBoundary(
              child: widget.child,
            ),
          ),
        );
      },
    );
  }
}
