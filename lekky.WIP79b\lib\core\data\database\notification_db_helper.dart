// File: lib/core/data/database/notification_db_helper.dart

import 'dart:async';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../../utils/logger.dart';
import '../../../features/notifications/domain/models/notification_model.dart';

/// A database helper class for notification-related data storage
class NotificationDBHelper {
  // Private constructor
  NotificationDBHelper._privateConstructor();

  // Static instance of NotificationDBHelper
  static final NotificationDBHelper _instance =
      NotificationDBHelper._privateConstructor();

  // Factory constructor to return the same instance
  factory NotificationDBHelper() {
    return _instance;
  }

  // Database instance
  Database? _database;

  // Database name and version
  static const String _databaseName = 'roolekky_notifications.db';
  static const int _databaseVersion = 1;

  // Table names
  static const String notificationsTable = 'notifications';
  static const String notificationSettingsTable = 'notification_settings';
  static const String reminderSettingsTable = 'reminder_settings';
  static const String meterReminderHistoryTable = 'meter_reminder_history';

  // Get database instance
  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  // Initialize the database
  Future<Database> _initDatabase() async {
    final dbPath = await getDatabasesPath();
    final path = join(dbPath, _databaseName);

    logger.d('NotificationDBHelper: Initializing database at $path');

    return await openDatabase(
      path,
      version: _databaseVersion,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }

  // Create database tables
  Future<void> _onCreate(Database db, int version) async {
    logger.d('NotificationDBHelper: Creating database tables');

    // Notifications table
    await db.execute('''
      CREATE TABLE $notificationsTable (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        message TEXT NOT NULL,
        timestamp TEXT NOT NULL,
        priority INTEGER NOT NULL,
        is_read INTEGER NOT NULL DEFAULT 0,
        action_type TEXT
      )
    ''');

    // Notification settings table
    await db.execute('''
      CREATE TABLE $notificationSettingsTable (
        key TEXT PRIMARY KEY,
        value TEXT NOT NULL
      )
    ''');

    // Reminder settings table
    await db.execute('''
      CREATE TABLE $reminderSettingsTable (
        key TEXT PRIMARY KEY,
        value TEXT NOT NULL,
        timestamp TEXT NOT NULL
      )
    ''');

    // Meter reminder history table
    await db.execute('''
      CREATE TABLE $meterReminderHistoryTable (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        scheduled_date TEXT NOT NULL,
        actual_date TEXT,
        status TEXT NOT NULL,
        timezone TEXT NOT NULL,
        timezone_offset INTEGER NOT NULL,
        is_dst INTEGER NOT NULL DEFAULT 0
      )
    ''');

    // Create indexes for better performance
    await db.execute(
        'CREATE INDEX idx_notifications_timestamp ON $notificationsTable (timestamp)');
    await db.execute(
        'CREATE INDEX idx_notifications_priority ON $notificationsTable (priority)');
    await db.execute(
        'CREATE INDEX idx_notifications_is_read ON $notificationsTable (is_read)');
    await db.execute(
        'CREATE INDEX idx_meter_reminder_history_scheduled_date ON $meterReminderHistoryTable (scheduled_date)');
    await db.execute(
        'CREATE INDEX idx_meter_reminder_history_status ON $meterReminderHistoryTable (status)');
  }

  // Handle database upgrades
  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    logger.d(
        'NotificationDBHelper: Upgrading database from $oldVersion to $newVersion');

    if (oldVersion < 2) {
      // For future upgrades
    }
  }

  // Insert a notification
  Future<void> insertNotification(NotificationModel notification) async {
    final db = await database;
    await db.insert(
      notificationsTable,
      {
        'id': notification.id,
        'title': notification.title,
        'message': notification.message,
        'timestamp': notification.timestamp.toIso8601String(),
        'priority': notification.priority.index,
        'is_read': notification.isRead ? 1 : 0,
        'action_type': notification.actionType,
      },
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  // Get all notifications
  Future<List<NotificationModel>> getNotifications() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      notificationsTable,
      orderBy: 'timestamp DESC',
    );

    return List.generate(maps.length, (i) {
      return NotificationModel(
        id: maps[i]['id'],
        title: maps[i]['title'],
        message: maps[i]['message'],
        timestamp: DateTime.parse(maps[i]['timestamp']),
        priority: NotificationPriority.values[maps[i]['priority']],
        isRead: maps[i]['is_read'] == 1,
        actionType: maps[i]['action_type'],
      );
    });
  }

  // Mark a notification as read
  Future<void> markNotificationAsRead(String id) async {
    final db = await database;
    await db.update(
      notificationsTable,
      {'is_read': 1},
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Mark all notifications as read
  Future<void> markAllNotificationsAsRead() async {
    final db = await database;
    await db.update(
      notificationsTable,
      {'is_read': 1},
    );
  }

  // Delete a notification
  Future<void> deleteNotification(String id) async {
    final db = await database;
    await db.delete(
      notificationsTable,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Delete all notifications
  Future<void> deleteAllNotifications() async {
    final db = await database;
    await db.delete(notificationsTable);
  }

  // Get a setting value
  Future<String?> getSetting(String key, String table) async {
    final db = await database;
    final List<Map<String, dynamic>> result = await db.query(
      table,
      columns: ['value'],
      where: 'key = ?',
      whereArgs: [key],
    );

    if (result.isNotEmpty) {
      return result.first['value'];
    }
    return null;
  }

  // Save a setting value
  Future<void> saveSetting(String key, String value, String table) async {
    final db = await database;
    await db.insert(
      table,
      {
        'key': key,
        'value': value,
        if (table == reminderSettingsTable)
          'timestamp': DateTime.now().toIso8601String(),
      },
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  // Save a reminder history entry
  Future<int> saveReminderHistory({
    required DateTime scheduledDate,
    DateTime? actualDate,
    required String status,
    required String timezone,
    required int timezoneOffset,
    required bool isDST,
  }) async {
    final db = await database;
    return await db.insert(
      meterReminderHistoryTable,
      {
        'scheduled_date': scheduledDate.toIso8601String(),
        'actual_date': actualDate?.toIso8601String(),
        'status': status,
        'timezone': timezone,
        'timezone_offset': timezoneOffset,
        'is_dst': isDST ? 1 : 0,
      },
    );
  }

  // Update a reminder history entry
  Future<void> updateReminderHistory({
    required int id,
    DateTime? actualDate,
    required String status,
  }) async {
    final db = await database;
    await db.update(
      meterReminderHistoryTable,
      {
        'actual_date': actualDate?.toIso8601String(),
        'status': status,
      },
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Get the most recent reminder history entry
  Future<Map<String, dynamic>?> getLatestReminderHistory() async {
    final db = await database;
    final List<Map<String, dynamic>> result = await db.query(
      meterReminderHistoryTable,
      orderBy: 'scheduled_date DESC',
      limit: 1,
    );

    if (result.isNotEmpty) {
      return result.first;
    }
    return null;
  }

  // Get all reminder history entries
  Future<List<Map<String, dynamic>>> getReminderHistory() async {
    final db = await database;
    return await db.query(
      meterReminderHistoryTable,
      orderBy: 'scheduled_date DESC',
    );
  }

  // Delete old reminder history entries (older than 3 months)
  Future<int> deleteOldReminderHistory() async {
    final db = await database;
    final cutoffDate = DateTime.now().subtract(const Duration(days: 90));
    return await db.delete(
      meterReminderHistoryTable,
      where: 'scheduled_date < ?',
      whereArgs: [cutoffDate.toIso8601String()],
    );
  }

  // Execute a batch of operations
  Future<void> executeBatch(Function(Batch batch) operations) async {
    final db = await database;
    final batch = db.batch();
    operations(batch);
    await batch.commit(noResult: true);
  }

  // Close the database
  Future<void> close() async {
    if (_database != null) {
      await _database!.close();
      _database = null;
    }
  }
}
