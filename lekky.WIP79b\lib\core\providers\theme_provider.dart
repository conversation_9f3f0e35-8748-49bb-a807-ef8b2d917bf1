// File: lib/core/providers/theme_provider.dart
import 'package:flutter/material.dart';
import '../theme/theme_service.dart';

/// Provider for managing the app's theme
class ThemeProvider extends ChangeNotifier {
  final ThemeService _themeService = ThemeService();

  // Flag to track initialization status
  bool _initialized = false;
  bool get initialized => _initialized;

  // Theme mode
  ThemeMode get themeMode => _themeService.themeMode;

  // Theme mode as string
  String get themeModeString => _themeService.getThemeModeAsString();

  // Initialize the theme provider
  Future<void> initialize() async {
    try {
      await _themeService.initialize();
      _initialized = true;
      notifyListeners();
    } catch (e) {
      debugPrint('Error initializing ThemeProvider: $e');
      // Set default theme mode to system
      _initialized = true; // Mark as initialized anyway to avoid hanging
    }
  }

  // Set the theme mode
  Future<void> setThemeMode(ThemeMode mode) async {
    await _themeService.setThemeMode(mode);
    notifyListeners();
  }

  // Set the theme mode from a string
  Future<void> setThemeModeFromString(String mode) async {
    await _themeService.setThemeModeFromString(mode);
    notifyListeners();
  }

  // Toggle between light and dark themes
  Future<void> toggleTheme() async {
    await _themeService.toggleTheme();
    notifyListeners();
  }

  // Check if dark mode is active
  bool isDarkMode(BuildContext context) {
    return _themeService.isDarkMode(context);
  }

  // Get the current theme data
  ThemeData getThemeData(BuildContext context) {
    return _themeService.getThemeData(context);
  }
}
