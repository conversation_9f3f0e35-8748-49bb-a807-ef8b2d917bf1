// File: lib/features/history/presentation/widgets/fixed_header_history_table.dart
import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../core/models/meter_entry.dart';
import '../../../../core/shared_modules/settings_provider.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/extensions/context_extensions.dart';
import '../controllers/history_controller.dart';

/// A table with a fixed header that displays meter entries
/// The table width matches the Meter Total dialog on the Homepage
/// The table header has the same height as the top of the Meter Total dialog
class FixedHeaderHistoryTable extends StatefulWidget {
  final List<MeterEntry> entries;
  final HistoryController controller;
  final Function(MeterEntry) onEntryTap;
  final Function(MeterEntry)? onInvalidEntryTap;

  /// Optional scroll controller to use for the table content
  /// If not provided, an internal controller will be created
  final ScrollController? scrollController;

  /// Optional fixed height for the table content
  /// If not provided, the height will be calculated based on the screen size
  final double? fixedHeight;

  const FixedHeaderHistoryTable({
    super.key,
    required this.entries,
    required this.controller,
    required this.onEntryTap,
    this.onInvalidEntryTap,
    this.scrollController,
    this.fixedHeight,
  });

  @override
  State<FixedHeaderHistoryTable> createState() =>
      _FixedHeaderHistoryTableState();
}

class _FixedHeaderHistoryTableState extends State<FixedHeaderHistoryTable> {
  // Constants for table dimensions
  static const double headerHeight = 60.0; // Height for header cells
  static const double rowHeight = 60.0; // Height for data rows

  // Scroll controller for the table content
  late ScrollController _scrollController;
  bool _isInternalController = false;

  @override
  void initState() {
    super.initState();

    // Use the provided controller or create a new one
    if (widget.scrollController != null) {
      _scrollController = widget.scrollController!;
    } else {
      _scrollController = ScrollController();
      _isInternalController = true;
    }
  }

  // Calculate the table height to ensure equal spacing at top and bottom with no overflow
  double _calculateTableHeight(BuildContext context) {
    // If a fixed height is provided, use it
    if (widget.fixedHeight != null) {
      return widget.fixedHeight!;
    }

    final screenHeight = MediaQuery.of(context).size.height;

    // Calculate the space at the top of the table
    const appBarHeight = 96.0; // Fixed height of the app bar
    const messageBarHeight = 32.0; // Height of the message bar
    const topPadding = 16.0; // Padding from SliverPadding
    const topSpacing = appBarHeight + messageBarHeight + topPadding;

    // Calculate the space at the bottom
    const bottomBarHeight = 60.0; // Height of the bottom control bar
    const bottomPadding = 8.0; // Adjusted padding for consistent 8px gap
    const bottomSpacing = bottomBarHeight + bottomPadding;

    // Calculate the available height for the table
    final availableHeight = screenHeight - topSpacing - bottomSpacing;

    // Reduce the height by 4px to fix the overflow issue
    final adjustedHeight = availableHeight - 4.0;

    // Set height to show 5.5 rows plus header
    // This clearly shows scrollability while maintaining proper spacing
    const targetHeight = (rowHeight * 5.5) + headerHeight;

    // Calculate the number of rows that would fit in the target height
    final numberOfVisibleRows = (targetHeight / rowHeight).floor();

    // Ensure we have at least 3 rows visible
    const int minRows = 3;
    final visibleRows = math.max(numberOfVisibleRows, minRows);

    // Calculate the height based on the number of rows plus a partial row to indicate scrolling
    // Add 0.5 of a row height to show a partial row at the bottom to indicate scrolling
    final heightWithPartialRow = (visibleRows * rowHeight) + (rowHeight * 0.5);

    // Use the minimum of:
    // 1. The adjusted available height
    // 2. The height with a partial row to indicate scrolling
    return math.min(adjustedHeight, heightWithPartialRow);
  }

  @override
  void dispose() {
    // Only dispose the controller if we created it
    if (_isInternalController) {
      _scrollController.dispose();
    }
    super.dispose();
  }

  List<MeterEntry> get _currentPageEntries {
    final startIndex =
        widget.controller.currentPage * widget.controller.itemsPerPage;

    // No need to limit items per page since we have scrolling now
    final endIndex =
        (startIndex + widget.controller.itemsPerPage <= widget.entries.length)
            ? startIndex + widget.controller.itemsPerPage
            : widget.entries.length;

    if (startIndex >= widget.entries.length) {
      return [];
    }

    return widget.entries.sublist(startIndex, endIndex);
  }

  @override
  Widget build(BuildContext context) {
    if (widget.entries.isEmpty) {
      return const Center(
        child: Text('No entries found'),
      );
    }

    return Column(
      children: [
        _buildTable(),
        // No need for additional spacing here as we've calculated it in the table height
      ],
    );
  }

  Widget _buildTable() {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Calculate a width that fits within the screen bounds
        // The parent now has 8px padding on each side from SliverPadding in the history_screen.dart
        final screenWidth = MediaQuery.of(context).size.width;

        // Use 92% of screen width to ensure no overflow and proper centering
        final tableWidth = math.min(constraints.maxWidth, screenWidth * 0.92);

        // No need to subtract additional padding as we'll handle it with the Center widget
        final actualTableWidth = tableWidth;

        // Calculate content height (without header)
        final contentHeight = _calculateTableHeight(context);

        return Center(
          child: Padding(
            padding: EdgeInsets.zero,
            child: Card(
              elevation: 2,
              margin:
                  EdgeInsets.zero, // Remove margin to match Meter Total dialog
              shape: RoundedRectangleBorder(
                borderRadius:
                    BorderRadius.circular(12), // Rounded corners on all sides
                side: widget.controller.isEditMode
                    ? const BorderSide(color: AppColors.warning, width: 2)
                    : BorderSide.none,
              ),
              child: SizedBox(
                width: actualTableWidth,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // Fixed header
                    Container(
                      height: headerHeight,
                      decoration: BoxDecoration(
                        color: context.isDarkMode
                            ? AppColors.tableHeaderDark
                            : AppColors.tableHeaderLight,
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(12),
                          topRight: Radius.circular(12),
                        ),
                      ),
                      child: Row(
                        children: _buildHeaderCells(actualTableWidth),
                      ),
                    ),
                    // Non-scrollable content with fixed height
                    ClipRRect(
                      borderRadius: const BorderRadius.only(
                        bottomLeft: Radius.circular(12),
                        bottomRight: Radius.circular(12),
                      ),
                      child: Container(
                        // Use a fixed height that's large enough to show several rows, reduced by 31px (16px + 7px + 8px)
                        height: contentHeight - 31.0,
                        decoration: BoxDecoration(
                          color: context.isDarkMode
                              ? AppColors.surfaceDark
                              : Colors.white, // Theme-aware background color
                        ),
                        child: GestureDetector(
                          onHorizontalDragEnd: (details) {
                            final currentPage = widget.controller.currentPage;
                            final totalPages = widget.controller.totalPages;

                            // Swipe left to go to next page
                            if (details.primaryVelocity != null &&
                                details.primaryVelocity! < 0) {
                              if (currentPage < totalPages - 1) {
                                widget.controller.goToPage(currentPage + 1);
                              }
                            }
                            // Swipe right to go to previous page
                            else if (details.primaryVelocity != null &&
                                details.primaryVelocity! > 0) {
                              if (currentPage > 0) {
                                widget.controller.goToPage(currentPage - 1);
                              }
                            }
                          },
                          // Use RawScrollbar for better visibility
                          child: RawScrollbar(
                            controller: _scrollController,
                            thumbVisibility: true,
                            thickness: 6,
                            thumbColor: context.isDarkMode
                                ? Colors.grey.withOpacity(0.9)
                                : Colors.grey.withOpacity(0.8),
                            radius: const Radius.circular(3.0),
                            padding: const EdgeInsets.only(
                                right:
                                    0), // Moved 2px towards the right edge of screen
                            child: SingleChildScrollView(
                              controller: _scrollController,
                              physics: const AlwaysScrollableScrollPhysics(),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.stretch,
                                children: [
                                  ...(_buildRows(actualTableWidth)),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  List<Widget> _buildHeaderCells(double tableWidth) {
    // Calculate column widths based on the table width
    // Date column takes 39% of the width, Amount 22%, others 18% each, with 3% spacer
    // These percentages ensure the table matches the width of the Meter Total dialog
    final dateWidth = tableWidth * 0.39; // Reduced from 0.41
    final amountWidth = tableWidth * 0.22;
    final shortAvgWidth = tableWidth * 0.18;
    final totalAvgWidth = tableWidth * 0.18;
    final spacerWidth = tableWidth * 0.03; // Increased from 1% to 3%

    // Use the same style for all column headers with theme-aware colors
    final isDarkMode = context.isDarkMode;
    final headerStyle = AppTextStyles.titleMedium.copyWith(
      fontWeight: FontWeight.w600,
      color: isDarkMode
          ? AppColors.tableHeaderTextDark
          : AppColors.tableHeaderText,
      fontSize: 16.0, // Consistent font size for all table elements
    );

    return [
      // Space for warning icon on the left
      const SizedBox(width: 24), // Reduced from 28 to 24
      // Date column
      SizedBox(
        width: dateWidth -
            24, // Adjust width to account for the reduced icon space
        child: Padding(
          padding: const EdgeInsets.only(
              left: 2.0, right: 4.0), // Reduced right padding
          child: Container(
            height: headerHeight,
            alignment: Alignment.center, // Center-align the header
            child: Text(
              'Date',
              style: headerStyle,
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ),
      // Amount column (£)
      SizedBox(
        width: amountWidth,
        child: Padding(
          padding: const EdgeInsets.only(left: 2.0, right: 4.0),
          child: Container(
            height: headerHeight,
            alignment: Alignment.center,
            child: Consumer<SettingsProvider>(
              builder: (context, settingsProvider, _) {
                return Text(
                  settingsProvider.currency,
                  style: headerStyle,
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                );
              },
            ),
          ),
        ),
      ),
      // Short average column (£/day)
      SizedBox(
        width: shortAvgWidth,
        child: Padding(
          padding: const EdgeInsets.only(left: 2.0, right: 4.0),
          child: Container(
            height: headerHeight,
            alignment: Alignment.center,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  'Rcnt.',
                  style: headerStyle,
                  textAlign: TextAlign.center,
                ),
                Text(
                  'Avg',
                  style: headerStyle,
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
      // Total average column (£/day)
      SizedBox(
        width: totalAvgWidth,
        child: Padding(
          padding: const EdgeInsets.only(left: 2.0, right: 4.0),
          child: Container(
            height: headerHeight,
            alignment: Alignment.center,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  'Total',
                  style: headerStyle,
                  textAlign: TextAlign.center,
                ),
                Text(
                  'Avg',
                  style: headerStyle,
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
      // Add spacer/buffer to the right of the Total Average column
      SizedBox(width: spacerWidth),
    ];
  }

  List<Widget> _buildRows(double tableWidth) {
    // Calculate column widths based on the table width
    // Date column takes 39% of the width, Amount 22%, others 18% each, with 3% spacer
    // These percentages ensure the table matches the width of the Meter Total dialog
    final dateWidth = tableWidth * 0.39; // Reduced from 0.41
    final amountWidth = tableWidth * 0.22;
    final shortAvgWidth = tableWidth * 0.18;
    final totalAvgWidth = tableWidth * 0.18;
    final spacerWidth = tableWidth * 0.03; // Increased from 1% to 3%

    return _currentPageEntries.asMap().entries.map((entry) {
      final index = entry.key;
      final meterEntry = entry.value;
      final isValid = widget.controller.isEntryValid(meterEntry.id ?? -1);
      // We can use severity for additional styling if needed in the future
      // final severity = widget.controller.getValidationSeverity(meterEntry.id ?? -1);
      final isTopUp = meterEntry.amountToppedUp > 0;

      // Determine row color based on the entry type and theme
      final isDarkMode = Theme.of(context).brightness == Brightness.dark;
      Color rowColor;

      if (!isValid) {
        rowColor = isDarkMode
            ? AppColors.tableRowInvalidDark
            : AppColors.tableRowInvalid;
      } else if (isTopUp) {
        rowColor =
            isDarkMode ? AppColors.tableRowTopUpDark : AppColors.tableRowTopUp;
      } else if (index % 2 == 0) {
        rowColor =
            isDarkMode ? AppColors.tableRowEvenDark : AppColors.tableRowEven;
      } else {
        rowColor =
            isDarkMode ? AppColors.tableRowOddDark : AppColors.tableRowOdd;
      }

      return Container(
        height: rowHeight,
        color: rowColor,
        child: Row(
          children: [
            // Warning icon or empty space
            SizedBox(
              width: 24, // Reduced from 28 to 24
              child: !isValid
                  ? Center(
                      child: GestureDetector(
                        onTap: widget.onInvalidEntryTap != null
                            ? () => widget.onInvalidEntryTap!(meterEntry)
                            : null,
                        child: Container(
                          width: 24,
                          height: 24,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            shape: BoxShape.circle,
                            border: Border.all(color: Colors.amber, width: 1),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.1),
                                blurRadius: 2,
                                spreadRadius: 1,
                              ),
                            ],
                          ),
                          child: const Center(
                            child: Icon(
                              Icons.warning,
                              color: Colors.amber,
                              size: 20,
                            ),
                          ),
                        ),
                      ),
                    )
                  : null,
            ),
            // Date cell
            SizedBox(
              width: dateWidth -
                  24, // Adjust width to account for the reduced icon space
              child: _buildCell(
                widget.controller.formatDate(meterEntry.timestamp),
                AppTextStyles.bodyMedium.copyWith(
                  fontSize: 16.0,
                  color: isDarkMode
                      ? AppColors.tableDateTextDark
                      : AppColors.tableDateText,
                ),
                meterEntry,
                isValid,
              ),
            ),
            // Amount cell
            SizedBox(
              width: amountWidth,
              child: _buildCell(
                (isTopUp ? meterEntry.amountToppedUp : meterEntry.reading)
                    .toStringAsFixed(2),
                AppTextStyles.bodyMedium.copyWith(
                  fontSize: 16.0,
                  fontWeight: FontWeight.w600,
                  color: isTopUp
                      ? (isDarkMode
                          ? AppColors.tableTopUpTextDark
                          : AppColors.tableTopUpText)
                      : (isDarkMode
                          ? AppColors.tableReadingTextDark
                          : AppColors.tableReadingText),
                ),
                meterEntry,
                isValid,
              ),
            ),
            // Short average cell
            SizedBox(
              width: shortAvgWidth,
              child: _buildCell(
                isTopUp || meterEntry.shortAverageAfterTopUp == null
                    ? 'n/a'
                    : meterEntry.shortAverageAfterTopUp!.toStringAsFixed(2),
                isTopUp || meterEntry.shortAverageAfterTopUp == null
                    ? AppTextStyles.bodyMedium.copyWith(
                        fontSize: 16.0,
                        color: isDarkMode
                            ? AppColors.textSecondaryDark
                            : Colors.grey)
                    : AppTextStyles.bodyMedium.copyWith(
                        fontSize: 16.0,
                        color: isDarkMode
                            ? AppColors.tableAverageTextDark
                            : AppColors.tableAverageText),
                meterEntry,
                isValid,
              ),
            ),
            // Total average cell
            SizedBox(
              width: totalAvgWidth,
              child: _buildCell(
                isTopUp || meterEntry.totalAverageUpToThisPoint == null
                    ? 'n/a'
                    : meterEntry.totalAverageUpToThisPoint!.toStringAsFixed(2),
                isTopUp || meterEntry.totalAverageUpToThisPoint == null
                    ? AppTextStyles.bodyMedium.copyWith(
                        fontSize: 16.0,
                        color: isDarkMode
                            ? AppColors.textSecondaryDark
                            : Colors.grey)
                    : AppTextStyles.bodyMedium.copyWith(
                        fontSize: 16.0,
                        color: isDarkMode
                            ? AppColors.tableAverageTextDark
                            : AppColors.tableAverageText),
                meterEntry,
                isValid,
              ),
            ),
            // Add spacer/buffer to the right of the Total Average column
            SizedBox(width: spacerWidth),
          ],
        ),
      );
    }).toList();
  }

  Widget _buildCell(
    String text,
    TextStyle style,
    MeterEntry entry,
    bool isValid,
  ) {
    // Determine text alignment based on content type
    // Date cells and text -> left aligned
    // Numbers -> right aligned
    final bool isDateCell = text.contains('-'); // Updated for DD-MM-YYYY format
    final bool isNumeric =
        double.tryParse(text.replaceAll('£', '').trim()) != null;

    final TextAlign textAlignment = isDateCell
        ? TextAlign.left
        : (isNumeric || text == 'n/a')
            ? TextAlign.right
            : TextAlign.left;

    return InkWell(
      onTap: widget.controller.isEditMode
          ? () => widget.onEntryTap(entry)
          : (!isValid && widget.onInvalidEntryTap != null)
              ? () => widget.onInvalidEntryTap!(entry)
              : () => widget.onEntryTap(
                  entry), // Always allow tapping entries when not in edit mode
      child: Padding(
        // Use consistent padding for all cells
        padding: const EdgeInsets.only(left: 2.0, right: 4.0),
        child: Container(
          height: rowHeight,
          // Align date cells to the left, numeric values and 'n/a' to the right
          alignment: isDateCell
              ? Alignment.centerLeft
              : (isNumeric || text == 'n/a')
                  ? Alignment.centerRight
                  : Alignment.center,
          child: Text(
            text,
            style: style,
            textAlign: textAlignment,
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
          ),
        ),
      ),
    );
  }

  // Scroll bar has been removed as requested
}
