// File: lib/features/notifications/presentation/widgets/notification_preview_widget.dart

import 'package:flutter/material.dart';
import 'dart:async';
import '../../domain/models/notification_model.dart';

/// A widget that shows what a notification will look like
class NotificationPreviewWidget extends StatefulWidget {
  final String title;
  final String body;
  final NotificationPriority priority;
  final bool showControls;
  final VoidCallback? onSoundTest;
  final VoidCallback? onVibrationTest;
  final VoidCallback? onPermissionCheck;

  const NotificationPreviewWidget({
    Key? key,
    required this.title,
    required this.body,
    this.priority = NotificationPriority.info,
    this.showControls = true,
    this.onSoundTest,
    this.onVibrationTest,
    this.onPermissionCheck,
  }) : super(key: key);

  @override
  State<NotificationPreviewWidget> createState() =>
      _NotificationPreviewWidgetState();
}

class _NotificationPreviewWidgetState extends State<NotificationPreviewWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _slideAnimation;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );

    _slideAnimation = Tween<double>(begin: -100.0, end: 0.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeOut,
      ),
    );

    _opacityAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeIn,
      ),
    );

    // Start the animation after a short delay to simulate a notification appearing
    Timer(const Duration(milliseconds: 300), () {
      _controller.forward();
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Padding(
          padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Text(
            'Notification Preview',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        const SizedBox(height: 8),
        _buildDeviceFrame(context),
        if (widget.showControls) _buildControls(context),
      ],
    );
  }

  Widget _buildDeviceFrame(BuildContext context) {
    // Simulate a phone frame
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade200,
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Status bar
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.black,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Icon(Icons.signal_cellular_alt, color: Colors.white, size: 14),
                const SizedBox(width: 4),
                Icon(Icons.wifi, color: Colors.white, size: 14),
                const SizedBox(width: 4),
                Icon(Icons.battery_full, color: Colors.white, size: 14),
                const SizedBox(width: 4),
                Text(
                  '9:41',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),

          // Lock screen background
          Container(
            height: 200,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [Colors.blue.shade200, Colors.purple.shade200],
              ),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Stack(
              children: [
                // Clock
                Positioned(
                  top: 40,
                  left: 0,
                  right: 0,
                  child: Column(
                    children: [
                      Text(
                        '9:41',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 40,
                          fontWeight: FontWeight.w300,
                        ),
                      ),
                      Text(
                        'Monday, May 5',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                ),

                // Notification
                Positioned(
                  bottom: 20,
                  left: 16,
                  right: 16,
                  child: AnimatedBuilder(
                    animation: _controller,
                    builder: (context, child) {
                      return Transform.translate(
                        offset: Offset(0, _slideAnimation.value),
                        child: Opacity(
                          opacity: _opacityAnimation.value,
                          child: child,
                        ),
                      );
                    },
                    child: _buildNotificationCard(context),
                  ),
                ),
              ],
            ),
          ),

          // Home indicator
          const SizedBox(height: 16),
          Center(
            child: Container(
              width: 100,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.black,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationCard(BuildContext context) {
    final Color priorityColor = _getPriorityColor(widget.priority);

    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.9),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // App bar
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: priorityColor.withOpacity(0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: priorityColor.withOpacity(0.2),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.bolt,
                    color: priorityColor,
                    size: 12,
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  'Lekky',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                    color: priorityColor,
                  ),
                ),
                const Spacer(),
                Text(
                  'now',
                  style: TextStyle(
                    fontSize: 10,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),

          // Content
          Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.title,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  widget.body,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade800,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildControls(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Test Notification Settings',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 16),

          // Sound test
          _buildControlButton(
            icon: Icons.volume_up,
            label: 'Test Sound',
            onTap: widget.onSoundTest,
          ),

          const SizedBox(height: 12),

          // Vibration test
          _buildControlButton(
            icon: Icons.vibration,
            label: 'Test Vibration',
            onTap: widget.onVibrationTest,
          ),

          const SizedBox(height: 12),

          // Permission check
          _buildControlButton(
            icon: Icons.verified_user,
            label: 'Verify Permissions',
            onTap: widget.onPermissionCheck,
          ),

          const SizedBox(height: 24),

          // Priority selector
          _buildPrioritySelector(context),
        ],
      ),
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required String label,
    required VoidCallback? onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.blue.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: Colors.blue,
                size: 20,
              ),
            ),
            const SizedBox(width: 16),
            Text(
              label,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
            const Spacer(),
            Icon(
              Icons.arrow_forward_ios,
              color: Colors.grey.shade400,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPrioritySelector(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Notification Priority',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Higher priority notifications are more likely to be delivered immediately',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildPriorityOption(
                context,
                'Low',
                NotificationPriority.info,
                Colors.blue,
              ),
              _buildPriorityOption(
                context,
                'Medium',
                NotificationPriority.warning,
                Colors.orange,
              ),
              _buildPriorityOption(
                context,
                'High',
                NotificationPriority.alert,
                Colors.red,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPriorityOption(
    BuildContext context,
    String label,
    NotificationPriority priority,
    Color color,
  ) {
    final isSelected = widget.priority == priority;

    return Column(
      children: [
        Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            color: isSelected ? color.withOpacity(0.2) : Colors.transparent,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: isSelected ? color : Colors.grey.shade300,
              width: isSelected ? 2 : 1,
            ),
          ),
          child: Center(
            child: Icon(
              _getPriorityIcon(priority),
              color: isSelected ? color : Colors.grey,
              size: 24,
            ),
          ),
        ),
        const SizedBox(height: 8),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
            color: isSelected ? color : Colors.grey.shade700,
          ),
        ),
      ],
    );
  }

  Color _getPriorityColor(NotificationPriority priority) {
    switch (priority) {
      case NotificationPriority.info:
        return Colors.blue;
      case NotificationPriority.warning:
        return Colors.orange;
      case NotificationPriority.alert:
        return Colors.red;
    }
  }

  IconData _getPriorityIcon(NotificationPriority priority) {
    switch (priority) {
      case NotificationPriority.info:
        return Icons.info_outline;
      case NotificationPriority.warning:
        return Icons.warning_amber_outlined;
      case NotificationPriority.alert:
        return Icons.priority_high;
    }
  }
}

/// Shows a notification preview dialog
Future<void> showNotificationPreviewDialog(
  BuildContext context, {
  required String title,
  required String body,
  NotificationPriority priority = NotificationPriority.info,
  VoidCallback? onSoundTest,
  VoidCallback? onVibrationTest,
  VoidCallback? onPermissionCheck,
}) async {
  await showDialog<void>(
    context: context,
    builder: (context) => Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Notification Preview',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.of(context).pop(),
                ),
              ],
            ),
            const SizedBox(height: 16),
            NotificationPreviewWidget(
              title: title,
              body: body,
              priority: priority,
              onSoundTest: onSoundTest,
              onVibrationTest: onVibrationTest,
              onPermissionCheck: onPermissionCheck,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
              ),
              child: const Text('Done'),
            ),
          ],
        ),
      ),
    ),
  );
}
