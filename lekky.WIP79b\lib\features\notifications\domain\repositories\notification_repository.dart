// File: lib/features/notifications/domain/repositories/notification_repository.dart

import '../../../../core/platform/permissions/permission_adapter.dart';
import '../models/notification_model.dart';
import '../models/reminder_time_model.dart';

/// Repository interface for notification operations
abstract class NotificationRepository {
  /// Initialize the repository
  Future<void> initialize();

  /// Get all notifications
  Future<List<NotificationModel>> getNotifications();

  /// Add a notification
  Future<void> addNotification({
    required String title,
    required String message,
    required NotificationPriority priority,
    String? actionType,
  });

  /// Mark a notification as read
  Future<void> markAsRead(String id);

  /// Mark all notifications as read
  Future<void> markAllAsRead();

  /// Remove a notification
  Future<void> removeNotification(String id);

  /// Clear all notifications
  Future<void> clearAll();

  /// Check if notifications are enabled
  Future<bool> areNotificationsEnabled();

  /// Enable or disable notifications
  Future<void> setNotificationsEnabled(bool enabled);

  /// Check if meter reading reminders are enabled
  Future<bool> areMeterReadingRemindersEnabled();

  /// Enable or disable meter reading reminders
  Future<void> setMeterReadingRemindersEnabled(bool enabled);

  /// Get the meter reading reminder frequency in days
  Future<int> getMeterReadingReminderFrequency();

  /// Set the meter reading reminder frequency in days
  Future<void> setMeterReadingReminderFrequency(int days);

  /// Get the last date a meter reading reminder was shown
  Future<DateTime?> getLastMeterReadingReminderDate();

  /// Set the last date a meter reading reminder was shown
  Future<void> setLastMeterReadingReminderDate(DateTime date);

  /// Get the time of day for meter reading reminders
  Future<ReminderTimeModel> getMeterReadingReminderTime();

  /// Set the time of day for meter reading reminders
  Future<void> setMeterReadingReminderTime(ReminderTimeModel time);

  /// Schedule a meter reading reminder
  Future<bool> scheduleMeterReadingReminder();

  /// Reschedule meter reading reminders
  Future<bool> rescheduleMeterReadingReminders({bool forceReschedule = false});

  /// Get the next scheduled meter reading reminder date
  Future<DateTime?> getNextMeterReadingReminderDate();

  /// Show a meter reading reminder notification immediately
  Future<void> showMeterReadingReminderNotification();

  /// Check if the time zone has changed since the last app run
  Future<bool> hasTimeZoneChanged();

  /// Get the current permission status
  Future<PermissionStatus> getPermissionStatus();

  /// Open the app settings page
  Future<bool> openAppSettings();

  /// Get a stream of permission status changes
  Stream<PermissionStatus> getPermissionStatusStream();

  /// Dispose of any resources
  Future<void> dispose();

  /// Migrate from SharedPreferences to SQLite
  Future<bool> migrateToSQLite({
    Function(double progress)? onProgress,
  });

  /// Get the current storage type (SQLite or SharedPreferences)
  Future<String> getStorageType();

  /// Check if data has been migrated to SQLite
  Future<bool> isMigratedToSQLite();
}
