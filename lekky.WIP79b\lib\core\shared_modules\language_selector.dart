// File: lib/core/shared_modules/language_selector.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../l10n/supported_locales.dart';
import '../providers/localization_provider.dart';
import '../theme/app_colors.dart';
import '../theme/app_text_styles.dart';
import 'base_settings_widget.dart';
import 'settings_model.dart';

class LanguageSelector extends BaseSettingsWidget {
  final String currentValue;
  final Function(String) onChanged;
  final String? errorText;

  const LanguageSelector({
    super.key,
    required this.currentValue,
    required this.onChanged,
    this.errorText,
    super.displayMode = SettingsDisplayMode.compact,
    super.showHelperText = false,
    super.showTitle = true,
  });

  // Get language options from SupportedLocales
  static List<Map<String, String>> getLanguages() {
    return SupportedLocales.locales.map((locale) {
      return {
        'code': locale.languageCode,
        'name': SupportedLocales.getDisplayName(locale.languageCode)
      };
    }).toList();
  }

  @override
  Widget buildExpandedView(BuildContext context) {
    final primaryColor = Theme.of(context).colorScheme.primary;
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final languages = getLanguages();
    final l10n = AppLocalizations.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (showTitle)
          Row(
            children: [
              const Icon(Icons.language, size: 20),
              const SizedBox(width: 8),
              Text(
                l10n.language,
                style: AppTextStyles.titleMedium.copyWith(
                  color: isDarkMode
                      ? AppColors.primaryTextDark
                      : AppColors.primary,
                ),
              ),
            ],
          ),

        if (showHelperText) ...[
          const SizedBox(height: 8),
          Text(
            l10n.selectLanguage,
            style: TextStyle(
              fontSize: 14,
              color: isDarkMode ? Colors.grey[300] : Colors.grey[700],
            ),
          ),
        ],

        const SizedBox(height: 16),

        // Language options as radio buttons
        ...languages.map((language) {
          return RadioListTile<String>(
            title: Text(language['name']!),
            value: language['code']!,
            groupValue: currentValue,
            onChanged: (value) {
              if (value != null) {
                onChanged(value);
              }
            },
            dense: true,
            activeColor: primaryColor,
          );
        }),

        buildErrorText(context, errorText),
      ],
    );
  }

  @override
  Widget buildCompactView(BuildContext context) {
    final primaryColor = Theme.of(context).colorScheme.primary;
    final languages = getLanguages();
    final l10n = AppLocalizations.of(context);

    // Find the current language name
    final currentLanguageName = languages.firstWhere(
        (l) => l['code'] == currentValue,
        orElse: () => {'name': 'English', 'code': 'en'})['name'];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (showTitle) buildLabel(context, l10n.language),

        if (showTitle)
          Text(
            '${l10n.current}: $currentLanguageName',
            style: const TextStyle(
              fontSize: 12,
              color: Colors.grey,
            ),
          ),

        if (showHelperText) buildHelperText(context, l10n.selectLanguage),

        const SizedBox(height: 8),

        // Language options as more compact radio buttons
        ...languages.map((language) {
          return RadioListTile<String>(
            title: Text(language['name']!),
            value: language['code']!,
            groupValue: currentValue,
            onChanged: (value) {
              if (value != null) {
                onChanged(value);
              }
            },
            dense: true,
            contentPadding: const EdgeInsets.symmetric(horizontal: 0),
            activeColor: primaryColor,
          );
        }),

        buildErrorText(context, errorText),
      ],
    );
  }
}
