User defined range cost caluculation

import 'package:postgres/postgres.dart';
import 'package:intl/intl.dart';

/// Model for a meter reading record
class MeterReading {
  final DateTime date;
  final double amountLeft;
  final double topUp;

  MeterReading({required this.date, required this.amountLeft, required this.topUp});
}

/// Connect to PostgreSQL database (adjust parameters as needed)
Future<PostgreSQLConnection> getDatabaseConnection() async {
  final connection = PostgreSQLConnection(
    'your_host', // e.g. 'localhost'
    5432,         // port
    'your_database',
    username: 'your_username',
    password: 'your_password',
  );
  await connection.open();
  return connection;
}

/// Fetch all meter readings ordered by date ascending
Future<List<MeterReading>> fetchReadings(PostgreSQLConnection conn) async {
  final results = await conn.query('''
    SELECT date_of_meter_reading, amount_left, amount_topped_up
    FROM meter_readings
    ORDER BY date_of_meter_reading ASC
  ''');

  return results.map((row) {
    return MeterReading(
      date: row[0] as DateTime,
      amountLeft: (row[1] as num).toDouble() + (row[2] as num).toDouble(),
      topUp: (row[2] as num).toDouble(),
    );
  }).toList();
}

/// Calculate usage rates for each interval between readings
List<double> computeUsageRates(List<MeterReading> readings) {
  List<double> rates = [];
  for (var i = 0; i < readings.length - 1; i++) {
    final current = readings[i];
    final next = readings[i + 1];
    final days = next.date.difference(current.date).inDays;
    final delta = current.amountLeft - next.amountLeft + next.topUp;
    rates.add(delta / days);
  }
  return rates;
}

/// Compute total cost between two dates
Future<double> computeCost(
  List<MeterReading> readings,
  List<double> rates,
  DateTime start,
  DateTime end,
) async {
  if (start.isAfter(end)) {
    throw ArgumentError('Start date must be before end date');
  }

  // find k_start
  int kStart = readings.lastIndexWhere((r) => !r.date.isAfter(start));
  // find k_end
  int kEnd = readings.lastIndexWhere((r) => !r.date.isAfter(end));

  // cost at start
  final nextReadingAfterStart = readings[kStart + 1];
  final daysAtStart = nextReadingAfterStart.date.difference(start).inDays;
  final costAtStart = rates[kStart] * daysAtStart;

  // cost at end
  final readingAtEnd = readings[kEnd];
  final daysAtEnd = end.difference(readingAtEnd.date).inDays;
  final costAtEnd = rates[kEnd] * daysAtEnd;

  // cost in middle
  double costInMiddle = 0;
  for (var k = kStart + 1; k < kEnd; k++) {
    // full interval consumption = rate * days
    final days = readings[k + 1].date.difference(readings[k].date).inDays;
    costInMiddle += rates[k] * days;
  }

  return costAtStart + costInMiddle + costAtEnd;
}

void main() async {
  // parse input dates (adjust format if needed)
  final dateFormat = DateFormat('dd/MM/yyyy');
  final start = dateFormat.parse('20/02/2025');
  final end = dateFormat.parse('20/03/2025');

  final conn = await getDatabaseConnection();
  final readings = await fetchReadings(conn);
  final rates = computeUsageRates(readings);

  final totalCost = await computeCost(readings, rates, start, end);
  print('Total cost from ${dateFormat.format(start)} to ${dateFormat.format(end)}: £\${totalCost.toStringAsFixed(2)}');

  await conn.close();
}
