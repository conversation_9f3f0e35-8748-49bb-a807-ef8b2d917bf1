// File: lib/core/widgets/dialogs/entry_edit_dialog.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../models/meter_entry.dart';
import '../../theme/app_colors.dart';
import '../../theme/app_text_styles.dart';
import '../../utils/date_time_utils.dart';
import '../../utils/input_validator.dart';
import '../app_text_field.dart';
import 'confirmation_dialog.dart';
import 'date_picker_dialog.dart' as custom_date_picker;
import 'input_dialog.dart';
import '../../extensions/context_extensions.dart';
import '../../../features/history/presentation/controllers/history_controller.dart';

/// A modular dialog for adding or editing a meter entry
/// This dialog can be used from both the Home screen and the History screen
class EntryEditDialog extends StatefulWidget {
  final MeterEntry? entry;
  final HistoryController controller;
  final Function(MeterEntry) onSave;
  final Function(int)? onDelete; // Add delete callback

  const EntryEditDialog({
    super.key,
    this.entry,
    required this.controller,
    required this.onSave,
    this.onDelete,
  });

  /// Show the dialog
  static Future<void> show({
    required BuildContext context,
    MeterEntry? entry,
    required HistoryController controller,
    required Function(MeterEntry) onSave,
    Function(int)? onDelete,
  }) async {
    return InputDialog.showCustomForm(
      context: context,
      title: entry != null ? 'Edit Entry' : 'Add Entry',
      widget: EntryEditDialog(
        entry: entry,
        controller: controller,
        onSave: onSave,
        onDelete: onDelete,
      ),
      icon: entry != null ? Icons.edit : Icons.add_circle,
    );
  }

  @override
  State<EntryEditDialog> createState() => _EntryEditDialogState();
}

class _EntryEditDialogState extends State<EntryEditDialog> {
  final _formKey = GlobalKey<FormState>();
  final _readingController = TextEditingController();
  final _scrollController = ScrollController();
  DateTime _selectedDate = DateTime.now();
  bool _isTopUp = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _initializeValues();
  }

  void _initializeValues() {
    if (widget.entry != null) {
      _isTopUp = widget.entry!.isTopUp;
      _selectedDate = widget.entry!.timestamp;
      if (_isTopUp) {
        _readingController.text = widget.entry!.amountToppedUp.toString();
      } else {
        _readingController.text = widget.entry!.reading.toString();
      }
    }
  }

  @override
  void dispose() {
    _readingController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  Widget _buildEntryTypeSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.business,
              color: AppColors.primary,
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              'Entry Type',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.primary,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(30),
            border: Border.all(color: AppColors.primary.withOpacity(0.5)),
          ),
          child: Row(
            children: [
              Expanded(
                child: GestureDetector(
                  onTap: () {
                    if (_isTopUp) {
                      setState(() {
                        _isTopUp = false;
                        _readingController.text = '';
                      });
                    }
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    decoration: BoxDecoration(
                      color: !_isTopUp
                          ? AppColors.primary.withOpacity(0.1)
                          : Colors.transparent,
                      borderRadius: const BorderRadius.horizontal(
                        left: Radius.circular(30),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          !_isTopUp ? Icons.check : Icons.remove,
                          color: AppColors.primary,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Meter\nReading',
                          style: AppTextStyles.bodyMedium.copyWith(
                            color: AppColors.primary,
                            fontWeight:
                                !_isTopUp ? FontWeight.bold : FontWeight.normal,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              Expanded(
                child: GestureDetector(
                  onTap: () {
                    if (!_isTopUp) {
                      setState(() {
                        _isTopUp = true;
                        _readingController.text = '';
                      });
                    }
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    decoration: BoxDecoration(
                      color: _isTopUp
                          ? AppColors.primary.withOpacity(0.1)
                          : Colors.transparent,
                      borderRadius: const BorderRadius.horizontal(
                        right: Radius.circular(30),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          _isTopUp ? Icons.check : Icons.add,
                          color: _isTopUp ? AppColors.primary : Colors.green,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Top-up',
                          style: AppTextStyles.bodyMedium.copyWith(
                            color: _isTopUp ? AppColors.primary : Colors.green,
                            fontWeight:
                                _isTopUp ? FontWeight.bold : FontWeight.normal,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDatePicker() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.calendar_today,
              color: AppColors.primary,
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              'Date',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.primary,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        GestureDetector(
          onTap: _showDatePicker,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    DateTimeUtils.formatDateTime(_selectedDate),
                    style: AppTextStyles.bodyMedium,
                  ),
                ),
                Icon(
                  Icons.calendar_today,
                  color: AppColors.primary,
                  size: 20,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _showDatePicker() async {
    try {
      final DateTime? pickedDate = await custom_date_picker.DatePickerDialog.show(
        context: context,
        initialDate: _selectedDate,
        firstDate: DateTime(2020),
        lastDate: DateTime.now().add(const Duration(days: 365)),
      );

      if (pickedDate != null && mounted) {
        setState(() {
          _selectedDate = pickedDate;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error selecting date: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Widget _buildReadingField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.water_drop,
              color: AppColors.primary,
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              'Meter Reading',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.primary,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        AppTextField(
          controller: _readingController,
          labelText: '',
          hintText: '0.00',
          prefixText: '£',
          keyboardType: const TextInputType.numberWithOptions(decimal: true),
          inputFormatters: [
            FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
          ],
          validator: (value) => InputValidator.validateAmount(value),
          errorText: _errorMessage,
          onChanged: (value) {
            if (_errorMessage != null) {
              setState(() {
                _errorMessage = null;
              });
            }
          },
        ),
      ],
    );
  }

  Widget _buildTopUpField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.add_circle,
              color: Colors.green,
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              'Top-up Amount',
              style: AppTextStyles.bodyMedium.copyWith(
                color: Colors.green,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        AppTextField(
          controller: _readingController,
          labelText: '',
          hintText: '0.00',
          prefixText: '£',
          keyboardType: const TextInputType.numberWithOptions(decimal: true),
          inputFormatters: [
            FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
          ],
          validator: (value) => InputValidator.validateAmount(value),
          errorText: _errorMessage,
          onChanged: (value) {
            if (_errorMessage != null) {
              setState(() {
                _errorMessage = null;
              });
            }
          },
        ),
      ],
    );
  }

  void _submit() {
    if (_formKey.currentState!.validate()) {
      final amount = double.tryParse(_readingController.text) ?? 0.0;
      
      // Create a MeterEntry from the form data
      final entry = MeterEntry.fromTypeCodeAndAmount(
        id: widget.entry?.id,
        typeCode: _isTopUp ? MeterEntry.typeTopUp : MeterEntry.typeMeterReading,
        amount: amount,
        timestamp: _selectedDate,
      );
      
      widget.onSave(entry);
      Navigator.of(context).pop();
    }
  }

  void _confirmDelete() {
    if (widget.entry?.id == null) return;
    
    ConfirmationDialog.show(
      context: context,
      title: 'Delete Entry',
      message: 'Are you sure you want to delete this entry? This action cannot be undone.',
      confirmText: 'Delete',
      cancelText: 'Cancel',
      confirmColor: Colors.red,
      onConfirm: () {
        widget.onDelete?.call(widget.entry!.id!);
        Navigator.of(context).pop(); // Close the confirmation dialog
        Navigator.of(context).pop(); // Close the edit dialog
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Form(
      key: _formKey,
      child: SingleChildScrollView(
        controller: _scrollController,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildEntryTypeSelector(),
            const SizedBox(height: 16),
            _buildDatePicker(),
            const SizedBox(height: 16),
            if (_isTopUp) _buildTopUpField() else _buildReadingField(),
            const SizedBox(height: 24),
            // Evenly spaced buttons in a row
            Container(
              alignment: Alignment.center,
              child: LayoutBuilder(
                builder: (context, constraints) {
                  // Calculate button width based on available space
                  // If we have delete button, divide by 3, otherwise by 2
                  final hasDeleteButton = widget.entry != null &&
                      widget.entry!.id != null &&
                      widget.onDelete != null;
                  final buttonWidth = hasDeleteButton
                      ? (constraints.maxWidth - 16) / 3
                      : (constraints.maxWidth - 8) / 2;

                  return Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      if (hasDeleteButton) ...[
                        SizedBox(
                          width: buttonWidth,
                          child: ElevatedButton(
                            onPressed: _confirmDelete,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.red,
                              foregroundColor: Colors.white,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            child: const Text('Delete'),
                          ),
                        ),
                        const SizedBox(width: 8),
                      ],
                      SizedBox(
                        width: buttonWidth,
                        child: OutlinedButton(
                          onPressed: () => Navigator.of(context).pop(),
                          style: OutlinedButton.styleFrom(
                            foregroundColor: AppColors.primary,
                            side: BorderSide(color: AppColors.primary),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: const Text('Cancel'),
                        ),
                      ),
                      const SizedBox(width: 8),
                      SizedBox(
                        width: buttonWidth,
                        child: ElevatedButton(
                          onPressed: _submit,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.primary,
                            foregroundColor: Colors.white,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: const Text('Save'),
                        ),
                      ),
                    ],
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
