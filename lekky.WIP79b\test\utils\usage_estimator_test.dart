// File: test/utils/usage_estimator_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:lekky/models/meter_entry.dart';
import 'package:lekky/utils/usage_estimator.dart';

void main() {
  group('UsageEstimator', () {
    // Test data
    final testEntries = [
      MeterEntry(
        id: 1,
        reading: 100.0,
        amountToppedUp: 0.0,
        timestamp: DateTime(2023, 1, 1),
      ),
      MeterEntry(
        id: 2,
        reading: 0.0,
        amountToppedUp: 50.0,
        timestamp: DateTime(2023, 1, 5),
      ),
      MeterEntry(
        id: 3,
        reading: 90.0,
        amountToppedUp: 0.0,
        timestamp: DateTime(2023, 1, 10),
      ),
      MeterEntry(
        id: 4,
        reading: 0.0,
        amountToppedUp: 30.0,
        timestamp: DateTime(2023, 1, 15),
      ),
      MeterEntry(
        id: 5,
        reading: 70.0,
        amountToppedUp: 0.0,
        timestamp: DateTime(2023, 1, 20),
      ),
    ];

    test('findReadingsAroundDate finds correct readings', () {
      // Test finding readings around a date in the middle
      final middleResult = UsageEstimator.findReadingsAroundDate(
        testEntries,
        DateTime(2023, 1, 15),
      );
      expect(middleResult['before']?.id, 3);
      expect(middleResult['after']?.id, 5);

      // Test finding readings around a date before all entries
      final earlyResult = UsageEstimator.findReadingsAroundDate(
        testEntries,
        DateTime(2022, 12, 1),
      );
      expect(earlyResult['before'], null);
      expect(earlyResult['after']?.id, 1);

      // Test finding readings around a date after all entries
      final lateResult = UsageEstimator.findReadingsAroundDate(
        testEntries,
        DateTime(2023, 2, 1),
      );
      expect(lateResult['before']?.id, 5);
      expect(lateResult['after'], null);
    });

    test('interpolateReading calculates correct values', () {
      // Test interpolation between two readings
      final earlier = MeterEntry(
        id: 1,
        reading: 100.0,
        amountToppedUp: 0.0,
        timestamp: DateTime(2023, 1, 1),
      );
      final later = MeterEntry(
        id: 2,
        reading: 90.0,
        amountToppedUp: 0.0,
        timestamp: DateTime(2023, 1, 11),
      );

      // Interpolate at the midpoint (should be 95.0)
      final midpointResult = UsageEstimator.interpolateReading(
        earlier,
        later,
        DateTime(2023, 1, 6),
      );
      expect(midpointResult, 95.0);

      // Interpolate at 1/4 of the way (should be 97.5)
      final quarterResult = UsageEstimator.interpolateReading(
        earlier,
        later,
        DateTime(2023, 1, 3, 12),
      );
      expect(quarterResult, closeTo(97.5, 0.1));

      // Interpolate at the same date as earlier (should be 100.0)
      final sameAsEarlierResult = UsageEstimator.interpolateReading(
        earlier,
        later,
        DateTime(2023, 1, 1),
      );
      expect(sameAsEarlierResult, 100.0);
    });

    test('estimateCostBetweenDates calculates correct cost', () {
      // Test estimation for a period with sufficient data
      final validResult = UsageEstimator.estimateCostBetweenDates(
        testEntries,
        DateTime(2023, 1, 5),
        DateTime(2023, 1, 15),
      );

      expect(validResult['isValid'], true);
      expect(validResult['estimatedCost'], closeTo(20.0, 0.1));

      // Test estimation for a period with insufficient data
      final insufficientDataResult = UsageEstimator.estimateCostBetweenDates(
        [testEntries.first],
        DateTime(2023, 1, 5),
        DateTime(2023, 1, 15),
      );

      expect(insufficientDataResult['isValid'], false);
      expect(insufficientDataResult['errorMessage'],
          contains('Not enough meter readings'));

      // Test estimation with invalid date range
      final invalidDateRangeResult = UsageEstimator.estimateCostBetweenDates(
        testEntries,
        DateTime(2023, 1, 15),
        DateTime(2023, 1, 5),
      );

      expect(invalidDateRangeResult['isValid'], false);
      expect(invalidDateRangeResult['errorMessage'],
          contains('Start date must be before end date'));
    });

    test('getLastMonthDates returns correct date range', () {
      // Mock current date to make test deterministic
      final now = DateTime(2023, 3, 15);
      final lastMonth = DateTime(now.year, now.month - 1, 1);
      final endOfLastMonth = DateTime(now.year, now.month, 0);

      // Test with fixed date
      final result = UsageEstimator.getLastMonthDates();

      // We can't directly test with fixed dates since it depends on current date
      // Instead, verify the structure and relationship between dates
      expect(result['startDate'], isNotNull);
      expect(result['endDate'], isNotNull);
      expect(result['startDate']!.isBefore(result['endDate']!), true);
      expect(result['startDate']!.day, 1); // Should start on the 1st
    });

    test('getLastYearDates returns correct date range', () {
      // Mock current date to make test deterministic
      final now = DateTime(2023, 3, 15);
      final lastYear = DateTime(now.year - 1, 1, 1);
      final endOfLastYear = DateTime(now.year, 1, 0);

      // Test with fixed date
      final result = UsageEstimator.getLastYearDates();

      // Verify the structure and relationship between dates
      expect(result['startDate'], isNotNull);
      expect(result['endDate'], isNotNull);
      expect(result['startDate']!.isBefore(result['endDate']!), true);
      expect(result['startDate']!.day, 1); // Should start on the 1st
      expect(result['startDate']!.month, 1); // Should start in January
    });
  });
}
