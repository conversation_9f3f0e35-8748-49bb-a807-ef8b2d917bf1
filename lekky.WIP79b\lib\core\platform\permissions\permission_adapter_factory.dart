// File: lib/core/platform/permissions/permission_adapter_factory.dart

import 'dart:io';
import 'package:flutter/foundation.dart';

import 'permission_adapter.dart';
import 'android_permission_adapter.dart';
import 'ios_permission_adapter.dart';

/// Factory for creating platform-specific permission adapters
class PermissionAdapterFactory {
  /// Create a permission adapter for the current platform
  static PermissionAdapter createAdapter() {
    if (Platform.isAndroid) {
      // Android implementation
      return AndroidPermissionAdapter();
    } else if (Platform.isIOS) {
      // iOS implementation
      return IOSPermissionAdapter();
    } else {
      // Default implementation for other platforms
      // For web or other platforms, use Android implementation as fallback
      return AndroidPermissionAdapter();
    }
  }
}
